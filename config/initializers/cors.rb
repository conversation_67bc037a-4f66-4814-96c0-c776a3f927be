Rails.application.config.middleware.insert_before 0, Rack::Cors do
  allow do
    origins { |origin|
      if Rails.env.dev_hmg? || Rails.env.homologation?
        "*"
      else
        web_domain = URI(origin).host
        if Rails.env.dev_hmg? ||
           web_domain.include?(".lecupon.com") ||
           web_domain.include?(".alloyal.com.br") ||
           ProjectConfig.where(web_domain:).exists? ||
           web_domain.include?("doctorvip.store") ||
           web_domain.include?("clicrbs.com.br") ||
           web_domain.include?("clubedefidelidade.com") ||
           WebApplication.where(cloudfront_distribution_url: web_domain).exists?
          origin
        end
      end
    }
    resource "*",
      headers: :any,
      methods: [:get, :post, :patch, :put, :delete, :options, :head],
      expose: ["access-token", "token-type", "client", "expiry", "uid", "Authorization"]
  end
end
