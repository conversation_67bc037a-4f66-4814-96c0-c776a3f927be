# frozen_string_literal: true

class Rack::Attack
  Rack::Attack.enabled = Rails.env.production?

  if Rails.env.production?
    RACK_ATTACK_REDIS_URL = ENV.fetch("RACK_ATTACK_REDIS_URL", "redis://localhost:6379/0")

    Rack::Attack.cache.store = ActiveSupport::Cache::RedisCacheStore.new(url: RACK_ATTACK_REDIS_URL)
  elsif Rails.env.test?
    Rack::Attack.cache.store = ActiveSupport::Cache::MemoryStore.new
  end

  Rack::Attack.safelist_ip("************")
  Rack::Attack.safelist_ip("*************")
  Rack::Attack.safelist_ip("*************")
  Rack::Attack.safelist_ip("**************")
  Rack::Attack.safelist_ip("**************")
  Rack::Attack.safelist_ip("**************")
  Rack::Attack.safelist_ip("**************")
  Rack::Attack.safelist_ip("**************")
  Rack::Attack.safelist_ip("**************")
  Rack::Attack.safelist_ip("*************")

  Rack::Attack.safelist("mark Swile safe") do |req|
    req.path.start_with?("/client/v2/") && (req.path.include?("/912/") || req.path.include?("/37374538000176/"))
  end

  throttle("api/ip/headers", limit: 300, period: 3.minutes) do |req|
    if req.path.start_with?("/api/")
      api_discriminator(req)
    elsif req.path.start_with?("/client/")
      client_discriminator(req)
    end
  end

  throttle("cashback_transfer_requests/ip/headers", limit: 1, period: 10.seconds) do |req|
    if req.path == "/api/v1/public_integration/cashback_transfer_requests"
      api_discriminator(req)
    end
  end

  throttle("health_check", limit: 4, period: 60.seconds) do |req|
    if req.path == "/health_check"
      req.ip
    end
  end

  ### Custom Throttle Response ###

  # By default, Rack::Attack returns an HTTP 429 for throttled responses,
  # which is just fine.
  #
  # If you want to return 503 so that the attacker might be fooled into
  # believing that they've successfully broken your app (or you just want to
  # customize the response), then uncomment these lines.
  # self.throttled_response = lambda do |env|
  #  [ 503,  # status
  #    {},   # headers
  #    ['']] # body
  # end

  def self.api_discriminator(req)
    ip = req.ip
    access_token = req.env["HTTP_ACCESS_TOKEN"]
    client = req.env["HTTP_CLIENT"]
    uid = req.env["HTTP_UID"]
    api_secret = req.env["HTTP_API_SECRET"]

    "#{ip}|#{access_token}|#{client}|#{uid}|#{api_secret}"
  end

  def self.client_discriminator(req)
    ip = req.ip
    email = req.env["HTTP_X_CLIENTEMPLOYEE_EMAIL"]
    token = req.env["HTTP_X_CLIENTEMPLOYEE_TOKEN"]
    "#{ip}|#{email}|#{token}"
  end
end
