{"ignored_warnings": [{"warning_type": "Redirect", "warning_code": 18, "fingerprint": "1a4598e76cace1c1cd8d734d561ca023e41babe926103cc63a3d27598f7d1b3e", "check_name": "Redirect", "message": "Possible unprotected redirect", "file": "app/controllers/api/v2/navigations/redirect_controller.rb", "line": 26, "link": "https://brakemanscanner.org/docs/warning_types/redirect/", "code": "redirect_to((\"#{\"https://#{(Navigation.active.where(\"provider = ? OR smart_link = ?\", Navigation.providers[:external_link], true).find(params[:id]).business.project_config.web_domain or \"giftcard.lecupon.com\")}/login?token=#{User.find(params[:mref]).smart_token}&embedded=true&redirect_to=#{Navigation.active.where(\"provider = ? OR smart_link = ?\", Navigation.providers[:external_link], true).find(params[:id]).destination}?hide_back=true\"}&api_secret=#{(nil or Navigation.active.where(\"provider = ? OR smart_link = ?\", Navigation.providers[:external_link], true).find(params[:id]).business.api_secret)}\" or \"https://#{(Navigation.active.where(\"provider = ? OR smart_link = ?\", Navigation.providers[:external_link], true).find(params[:id]).business.project_config.web_domain or \"giftcard.lecupon.com\")}/login?token=#{User.find(params[:mref]).smart_token}&embedded=true&redirect_to=#{Navigation.active.where(\"provider = ? OR smart_link = ?\", Navigation.providers[:external_link], true).find(params[:id]).destination}?hide_back=true\"), :allow_other_host => true)", "render_path": null, "location": {"type": "method", "class": "Api::V2::Navigations::RedirectController", "method": "redirect_to_smart_link"}, "user_input": "Navigation.active.where(\"provider = ? OR smart_link = ?\", Navigation.providers[:external_link], true).find(params[:id]).business.project_config.web_domain", "confidence": "Weak", "cwe_id": [601], "note": ""}, {"warning_type": "Redirect", "warning_code": 18, "fingerprint": "1f395080ba70a79b24e785f97b06207d635824580aedeee2ecd06e98d0d74bf6", "check_name": "Redirect", "message": "Possible unprotected redirect", "file": "app/controllers/api/v1/tickets_controller.rb", "line": 30, "link": "https://brakemanscanner.org/docs/warning_types/redirect/", "code": "redirect_to(Order.joins(:voucher, :user).where(:cupon => cupon, :user => user).where(:cupon => cupon, :user => ({ :cpf => user.cpf })).last.deep_link, :allow_other_host => true)", "render_path": null, "location": {"type": "method", "class": "Api::V1::<PERSON><PERSON>etsController", "method": "redirect_to_voucher_order_deep_link"}, "user_input": "Order.joins(:voucher, :user).where(:cupon => cupon, :user => user).where(:cupon => cupon, :user => ({ :cpf => user.cpf })).last.deep_link", "confidence": "Weak", "cwe_id": [601], "note": ""}, {"warning_type": "Redirect", "warning_code": 18, "fingerprint": "8194a9306fd0ac5102c5b6accd856ab54643777cc243f2cb46eba57a322abaa8", "check_name": "Redirect", "message": "Possible unprotected redirect", "file": "app/controllers/api/v2/navigations/redirect_controller.rb", "line": 35, "link": "https://brakemanscanner.org/docs/warning_types/redirect/", "code": "redirect_to(Navigation.active.where(\"provider = ? OR smart_link = ?\", Navigation.providers[:external_link], true).find(params[:id]).call_external_request(:user => User.find(params[:mref])).headers[\"location\"], :allow_other_host => true)", "render_path": null, "location": {"type": "method", "class": "Api::V2::Navigations::RedirectController", "method": "redirect_to_external_link"}, "user_input": "Navigation.active.where(\"provider = ? OR smart_link = ?\", Navigation.providers[:external_link], true).find(params[:id]).call_external_request(:user => User.find(params[:mref])).headers[\"location\"]", "confidence": "Weak", "cwe_id": [601], "note": ""}, {"warning_type": "Redirect", "warning_code": 18, "fingerprint": "8303a2dd534920ae7146b685c148d2f0a92352e74011687f595e9daf672d2b47", "check_name": "Redirect", "message": "Possible unprotected redirect", "file": "app/controllers/api/v2/navigations/redirect_controller.rb", "line": 31, "link": "https://brakemanscanner.org/docs/warning_types/redirect/", "code": "redirect_to(Navigation.active.where(\"provider = ? OR smart_link = ?\", Navigation.providers[:external_link], true).find(params[:id]).url_with_query_params(:user => User.find(params[:mref])), :allow_other_host => true)", "render_path": null, "location": {"type": "method", "class": "Api::V2::Navigations::RedirectController", "method": "redirect_to_external_link"}, "user_input": "Navigation.active.where(\"provider = ? OR smart_link = ?\", Navigation.providers[:external_link], true).find(params[:id]).url_with_query_params(:user => User.find(params[:mref]))", "confidence": "Weak", "cwe_id": [601], "note": ""}], "brakeman_version": "7.0.2"}