# Use this file to easily define all of your cron jobs.
#
# It"s helpful, but not entirely necessary to understand cron before proceeding.
# http://en.wikipedia.org/wiki/Cron

# Example:
#
# set :output, "/path/to/my/cron_log.log"
#
# every 2.hours do
#   command "/usr/bin/some_great_command"
#   runner "MyModel.some_method"
#   rake "some:great:rake:task"
# end
#
# every 4.days do
#   runner "AnotherModel.prune_old_records"
# end

# Learn more: http://github.com/javan/whenever

set :output, "./log/whenever_cron.log"

set :runner_command,  if Whenever.bin_rails?
                        "rails runner"
                      elsif Whenever.script_rails?
                        "script/rails runner"
                      else
                        "script/runner"
                      end

every 1.day, at: ["03:00 am", "03:00 pm"] do
  runner "Promotion::Integration.import_all"
end

every 1.day, at: "03:00 am" do
  runner "User.process_soft_delete_requests"
end

every 1.day, at: "03:30 am" do
  runner "Awin::AllTransactionsProcessService.call"
end

every 1.day, at: "04:00 am" do
  runner "MagazineLuiza::CancelExpiredCashbacks.call"
end

every 1.day, at: "04:30 am" do
  runner "OrganizationIntegrationPartner.import_transactions"
end

every 1.day, at: "05:30 am" do
  runner "Admitad::AllTransactionsProcessService.call"
end

every 1.day, at: "06:00 am" do
  runner "MailerConfig.refresh_all"
end

every :monday, at: "08:00 am" do
  runner "Drogasil::EmailIntegration.new.call"
end

every 1.day, at: "02:00 am" do
  runner "Ixc::ScheduleUsersImportService.call"
end

every 1.day, at: "02:00 am" do
  runner "Unifacisa::ScheduleUsersImportService.call"
  runner "Tenex::ScheduleUsersImportService.call"
end

every 1.day, at: "02:00 am" do
  runner "MkSolution::ScheduleUserImportService.call"
end

every 1.day, at: "02:00 am" do
  runner "Hinova::ScheduleUsersImportService.call"
end

every 1.day, at: "02:10 am" do
  runner "IlevaClient.import_for_all_projects"
end

every 1.day, at: "04:20 am" do
  runner "Cashback::Transferable::Request.call"
end

every 1.day, at: "05:00 am" do
  runner "ProjectConfig::VerifyEmailSendersService.call"
end

every 1.day, at: "05:01 am" do
  runner "ProjectConfig::VerifyGoogleKeyFilesService.call"
end

every 1.day, at: "05:02 am" do
  runner "ProjectConfig::VerifyExternalUrlsService.call"
end

every 1.day, at: "03:30 am" do
  runner "Metrics::UserRegistration.generate_daily"
  runner "Metrics::UserLastSeen.generate_daily"
  runner "Metrics::Order.generate_daily"
  runner "Metrics::Gmv.generate_daily"
  runner "Metrics::UsersGmv.generate_daily"
  runner "Metrics::AuthorizedUsersGmv.generate_daily"
  runner "Metrics::Cashback.generate_daily"
end

every :sunday, at: "04:00 am" do
  runner "Metrics::UserRegistration.generate_weekly"
  runner "Metrics::UserLastSeen.generate_weekly"
  runner "Metrics::Order.generate_weekly"
  runner "Metrics::Gmv.generate_weekly"
  runner "Metrics::UsersGmv.generate_weekly"
  runner "Metrics::AuthorizedUsersGmv.generate_weekly"
  runner "Metrics::Cashback.generate_weekly"
end

every :sunday, at: "01:00 am" do
  runner "ExataArena::Workers::SelloutWorker.perform_async"
end

every 1.month, at: "start of month at 04:30 am" do
  runner "Metrics::UserRegistration.generate_monthly"
  runner "Metrics::UserLastSeen.generate_monthly"
  runner "Metrics::Order.generate_monthly"
  runner "Metrics::Gmv.generate_monthly"
  runner "Metrics::UsersGmv.generate_monthly"
  runner "Metrics::AuthorizedUsersGmv.generate_monthly"
  runner "Metrics::Cashback.generate_monthly"
end

every 1.day, at: "03:50 am" do
  runner "Metrics::BusinessCounts.update_all"
end

every 1.day, at: "04:20 am" do
  runner "Metrics::OrganizationOrderCount.generate_metrics"
end

every 1.day, at: "04:25 am" do
  runner "Metrics::CategoryOrderCount.generate_metrics"
end

every 1.day, at: "05:15 am" do
  runner "Giftcard.update_availability"
end

every 1.day, at: "05:20 am" do
  runner "Branch.update_giftcard_redeemable"
end

every 1.day, at: "05:25 am" do
  runner "Organization.update_giftcard_redeemable"
end

every 1.day, at: "05:30 am" do
  runner "Giftcard::UsageExpiration.update"
end

every 1.day, at: "05:35 am" do
  runner "Order::UsageExpiration.update"
end

every 1.day, at: "05:40 am" do
  runner "Promotion::PropagateChanges.call"
end

every 1.day, at: "06:00 am" do
  rake "giftcard:make_cashback_records_available"
end

every 1.day, at: "09:00 am" do
  runner "Subscription.charge_due_installments"
end

every 2.hours do
  runner "User.associate_to_authorized_user_orders"
  runner "User.associate_to_authorized_user_cashback_records"
end

every 15.minutes do
  runner "Fund.credit_due_points!"
  runner "Fund.expire_points!"
end

every 6.hours do
  runner "WebApplication.validate_all_distribution_certificates"
end

every 15.minutes do
  runner "HomeWidgetsCache.generate_all"
end
