default: &defaults
  ignore_actions:
    - HealthChecksController#show

  # Your push api key, it is possible to set this dynamically using ERB:
  # push_api_key: "<%= ENV['APPSIGNAL_PUSH_API_KEY'] %>"
  push_api_key: "5a5ff5f3-2934-4088-884c-a6b5147bed04"

  # Your app's name
  name: "lecupon-rails"

  # Actions that should not be monitored by AppSignal
  # ignore_actions:
  #   - ApplicationController#isup

  # Errors that should not be recorded by AppSignal
  # For more information see our docs:
  # https://docs.appsignal.com/ruby/configuration/ignore-errors.html
  # ignore_errors:
  #   - Exception
  #   - NoMemoryError
  #   - ScriptError
  #   - LoadError
  #   - NotImplementedError
  #   - SyntaxError
  #   - SecurityError
  #   - SignalException
  #   - Interrupt
  #   - SystemExit
  #   - SystemStackError

  # See http://docs.appsignal.com/ruby/configuration/options.html for
  # all configuration options.

# Configuration per environment, leave out an environment or set active
# to false to not push metrics for that environment.
dev_hmg:
  <<: *defaults
  active: true

homologation:
  <<: *defaults
  active: true

production:
  <<: *defaults
  active: true

staging:
  <<: *defaults
  active: true
