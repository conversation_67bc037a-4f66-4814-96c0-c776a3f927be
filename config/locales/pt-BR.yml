---
pt-BR:
  will_paginate:
    next_label: Próximos &#8594;
    page_entries_info:
      multi_page: Exibindo %{model} %{from} - %{to} de %{count} no total
      multi_page_html: Exibindo %{model} <b>%{from}&nbsp;-&nbsp;%{to}</b> de <b>%{count}</b> no total
      single_page:
        one: Exibindo 1 %{model}
        other: Exibindo todos os %{count} %{model}
        zero: Nenhum %{model} encontrado
      single_page_html:
        one: Exibindo <b>1</b> %{model}
        other: Exibindo <b>todos os&nbsp;%{count}</b> %{model}
        zero: Nenhum %{model} encontrado
    page_gap: "&hellip;"
    previous_label: "&#8592; Anteriores"
  paginate:
    range_error:
      per_page: "Parâmetro per_page deve conter um valor entre %{min_per_page} e %{max_per_page}"
      page: "Parâmetro per_page deve ser maior ou igual a %{min_page}"

  devise_token_auth:
    sessions:
      not_confirmed: "Uma mensagem com um link de confirmação foi enviado para seu endereço de e-mail. Você precisa confirmar sua conta antes de continuar."
      bad_credentials: "Informações inválidas."
      not_supported: "Use POST /sign_in para efetuar o login. GET não é suportado."
      user_not_found: "Usuário não existe ou não está logado."
    token_validations:
      invalid: "Dados de login inválidos."
    registrations:
      missing_confirm_success_url: "Parâmetro 'confirm_success_url' não informado."
      redirect_url_not_allowed: "Redirecionamento para '%{redirect_url}' não permitido."
      email_already_exists: "Já existe uma conta com o email '%{email}'."
      account_with_uid_destroyed: "A conta com uid '%{uid}' foi excluída."
      account_to_destroy_not_found: "Não foi possível encontrar a conta para exclusão."
      user_not_found: "Usuário não encontrado."
    passwords:
      missing_email: "Informe o endereço de e-mail."
      missing_redirect_url: "URL para redirecionamento não informada."
      not_allowed_redirect_url: "Redirecionamento para '%{redirect_url}' não permitido."
      sended: "Você receberá um e-mail com instruções sobre como redefinir sua senha."
      user_not_found: "Não existe um usuário com o e-mail '%{email}'."
      password_not_required: "Esta conta não necessita de uma senha. Faça login utilizando '%{provider}'."
      missing_passwords: "Preencha a senha e a confirmação de senha."
      successfully_updated: "Senha atualizada com sucesso."
  devise:
    confirmations:
      confirmed: "Sua conta foi confirmada com sucesso. Você está logado."
      send_instructions: "Dentro de minutos, você receberá um e-mail com instruções para a confirmação da sua conta."
      send_paranoid_instructions: "Se o seu endereço de e-mail estiver cadastrado, você receberá uma mensagem com instruções para confirmação da sua conta."
    failure:
      already_authenticated: "Você já está logado."
      inactive: "Sua conta ainda não foi ativada."
      invalid: "Credenciais inválidas."
      locked: "Sua conta está bloqueada."
      last_attempt: "Você tem mais uma tentativa antes de bloquear sua conta."
      not_found_in_database: "Credenciais inválidas."
      timeout: "Sua sessão expirou, por favor, efetue login novamente para continuar."
      unauthenticated: "Para continuar, efetue login ou registre-se."
      unconfirmed: "Antes de continuar, confirme a sua conta."
      missing_business_headers: "Preencha o cabeçalho de autenticação do Business (Api-Secret ou Web-Domain)"
      invalid_aud_header: "Preencha o cabeçalho 'aud' corretamente (api ou web)"
      invalid_business_headers: "Business não encontrado"
      invalid_smart_token: Smart Link inválido
      sso_error: Erro na integração Single Sign On
      wrong_aud: "Sua sessão expirou. Por favor, faça login novamente."
      revoked_token: "Sua sessão expirou. Por favor, faça login novamente."
      unexpected_invalid_integration_data: "Houve um erro inesperado. Se persistir, contate seu gestor."
      invalid_password_recovery_token: "Token de recuperação de senha inválido"
      invalid_current_password: "Senha atual informada é inválida"
    omniauth_callbacks:
      failure: 'Não foi possível autenticá-lo como %{kind} porque "%{reason}".'
      success: "Autenticado com sucesso com uma conta de %{kind}."
    passwords:
      no_token: "Você só pode acessar essa página através de um e-mail de troca de senha. Se já estiver acessando por um e-mail, verifique se a URL fornecida está completa."
      send_instructions: "Dentro de minutos, você receberá um e-mail com instruções para a troca da sua senha."
      send_paranoid_instructions: "Se o seu endereço de e-mail estiver cadastrado, você receberá um link de recuperação da senha via e-mail."
      updated: "Sua senha foi alterada com sucesso. Você está logado."
      updated_not_active: "Sua senha foi alterada com sucesso."
    registrations:
      destroyed: "Sua conta foi cancelada com sucesso. Esperamos vê-lo novamente em breve."
      signed_up: "Login efetuado com sucesso. Se não foi autorizado, a confirmação será enviada por e-mail."
      signed_up_but_inactive: "Você foi cadastrado com sucesso. No entanto, não foi possível efetuar login, pois sua conta não foi ativada."
      signed_up_but_locked: "Você foi cadastrado com sucesso. No entanto, não foi possível efetuar login, pois sua conta está bloqueada."
      signed_up_but_unconfirmed: "Uma mensagem com um link de confirmação foi enviada para o seu endereço de e-mail. Por favor, abra o link para confirmar a sua conta."
      update_needs_confirmation: "Você atualizou a sua conta com sucesso, mas o seu novo endereço de e-mail precisa ser confirmado. Por favor, acesse-o e clique no link de confirmação que enviamos."
      updated: "Sua conta foi atualizada com sucesso."
    sessions:
      signed_in: "Login efetuado com sucesso!"
      signed_out: "Saiu com sucesso."
      already_signed_out: "Saiu com sucesso."
    unlocks:
      send_instructions: "Dentro de minutos, você receberá um email com instruções para o desbloqueio da sua conta."
      send_paranoid_instructions: "Se sua conta existir, você receberá um e-mail com instruções para desbloqueá-la em alguns minutos."
      unlocked: "Sua conta foi desbloqueada com sucesso. Efetue login para continuar."
  activerecord:
    errors:
      messages:
        record_invalid: "A validação falhou: %{errors}"
        restrict_dependent_destroy:
          has_one: Não é possível excluir o registro pois existe um %{record} dependente
          has_many: Não é possível excluir o registro pois existem %{record} dependentes
      models:
        telemedicine_config:
          unknown_limit: "não está em um limite permitido para o plano"
          below_limit: "está abaixo do limite mínimo permitido para o plano"
          above_limit: "está acima do limite máximo permitido para o plano"
    attributes:
      project_config:
        clever_tap_account_id: Identificador do Projeto CleverTap
        clever_tap_passcode: Senha CleverTap
        clever_tap_project_token: Token do Projeto CleverTap
        clever_tap_region: Região CleverTap
      branch:
        branch_type: Tipo de filial
      member_referral_config:
        referrer_benefit: "Benefício de quem indicou"
        referred_benefit: "Benefício do indicado"
        share_message: "Mensagem de compartilhamento"
        register_page_content: "Conteúdo da pagina compartilhada"
      lead:
        entry_type: "Tipo de Indicação"
        status: "Status"
        name: "Nome"
        email: "E-mail"
        phone: "Número de telefone"
      promotion:
        cashback_value: Valor do cashback
      telemedicine_config:
        contracted_beneficiaries: "Número de vidas contratadas"
  activemodel:
    attributes:
      giftcard/charge/request:
        anti_fraud_session_id: ID de sessão antifraude
        credit_card: Cartão de crédito
        address_city: Cidade
        address_complement: Complemento
        address_complement: Complemento
        address_neighborhood: Bairro
        address_number: Número
        address_state: Estado
        address_street: Rua
        address_zip_code: Cep
        document: CPF
        name: Nome
        phones: Telefone
        payment_method: Meio de pagamento
        card_holder: Nome impresso no cartão
        card_number: Número do cartão
        card_cvv: Código de segurança do cartão
        card_expires_at: Data de validade do cartão
      metrics/user_registration/search:
        diff_in_days: "Intervalo de dias"
        start_date: "Data inicial"
        end_date: "Data final"
        business_id: "ID do business"
        period: "Período"
      metrics/gmv/search:
        diff_in_days: "Intervalo de dias"
        start_date: "Data inicial"
        end_date: "Data final"
        business_id: "ID do business"
        period: "Período"
      metrics/cashback/search:
        diff_in_days: "Intervalo de dias"
        start_date: "Data inicial"
        end_date: "Data final"
        business_id: "ID do business"
        period: "Período"
      metrics/users_gmv/search:
        diff_in_days: "Intervalo de dias"
        start_date: "Data inicial"
        end_date: "Data final"
        business_id: "ID do business"
        period: "Período"
      metrics/authorized_users_gmv/search:
        diff_in_days: "Intervalo de dias"
        start_date: "Data inicial"
        end_date: "Data final"
        business_id: "ID do business"
        period: "Período"
      purchase/create:
        business: "Business"
        business_id: "ID do business"
        batch: "Identificador do lote"
        promotion: "Promoção"
        promotion_id: "ID da promoção"
        branch: "Filial"
        branch_id: "ID da filial"
        coupon: "Cupom"
        cpf: "CPF"
        idempotency_key: "Chave de idempotência"
        initial_cashback_status: "Status inicial do cashback"
        transaction_value: "Valor da transação"
        user: "Usuário"
        user_id: "ID do usuário"
        authorized_user: "Usuário autorizado"
        authorized_user_id: "ID do usuário autorizado"
        coupon: "Cupom"
        coupon_id: "ID do cupom"
        order: "Order"
        order_id: "ID da order"
        created_at: "Data/hora da criação"
      purchase/update:
        cashback: "Cashback"
        status: "Status do cashback"
  date:
    abbr_day_names:
      - Dom
      - Seg
      - Ter
      - Qua
      - Qui
      - Sex
      - Sáb
    abbr_month_names:
      -
      - Jan
      - Fev
      - Mar
      - Abr
      - Mai
      - Jun
      - Jul
      - Ago
      - Set
      - Out
      - Nov
      - Dez
    day_names:
      - Domingo
      - Segunda-feira
      - Terça-feira
      - Quarta-feira
      - Quinta-feira
      - Sexta-feira
      - Sábado
    formats:
      default: "%d/%m/%Y"
      long: "%d de %B de %Y"
      short: "%d de %B"
      en_default: "%Y-%m-%d"
    month_names:
      -
      - Janeiro
      - Fevereiro
      - Março
      - Abril
      - Maio
      - Junho
      - Julho
      - Agosto
      - Setembro
      - Outubro
      - Novembro
      - Dezembro
    order:
      - :day
      - :month
      - :year
  datetime:
    distance_in_words:
      about_x_hours:
        one: aproximadamente 1 hora
        other: aproximadamente %{count} horas
      about_x_months:
        one: aproximadamente 1 mês
        other: aproximadamente %{count} meses
      about_x_years:
        one: aproximadamente 1 ano
        other: aproximadamente %{count} anos
      almost_x_years:
        one: quase 1 ano
        other: quase %{count} anos
      half_a_minute: meio minuto
      less_than_x_minutes:
        one: menos de um minuto
        other: menos de %{count} minutos
      less_than_x_seconds:
        one: menos de 1 segundo
        other: menos de %{count} segundos
      over_x_years:
        one: mais de 1 ano
        other: mais de %{count} anos
      x_days:
        one: 1 dia
        other: "%{count} dias"
      x_minutes:
        one: 1 minuto
        other: "%{count} minutos"
      x_months:
        one: 1 mês
        other: "%{count} meses"
      x_years:
        one: 1 ano
        other: "%{count} anos"
      x_seconds:
        one: 1 segundo
        other: "%{count} segundos"
    prompts:
      day: Dia
      hour: Hora
      minute: Minuto
      month: Mês
      second: Segundo
      year: Ano
  errors:
    format: "%{attribute} %{message}"
    params:
      inclusion: "Parâmetro %{param_name} inválido. Os valores aceitos são: '%{accepted_params}'."
    messages:
      forbidden: "Acesso negado"
      invalid_url: "URL inválida"
      parameter_missing: "A requisição falhou devido a ausência de parâmetro: %{param}"
      accepted: deve ser aceito
      blank: não pode ficar em branco
      present: deve ficar em branco
      confirmation: não é igual a %{attribute}
      empty: não pode ficar vazio
      equal_to: deve ser igual a %{count}
      even: deve ser par
      exclusion: não está disponível
      greater_than: deve ser maior que %{count}
      greater_than_or_equal_to: deve ser maior ou igual a %{count}
      inclusion: não está incluído na lista
      invalid: não é válido
      less_than: deve ser menor que %{count}
      less_than_or_equal_to: deve ser menor ou igual a %{count}
      model_invalid: "A validação falhou: %{errors}"
      not_a_number: não é um número
      not_an_integer: não é um número inteiro
      odd: deve ser ímpar
      required: é obrigatório(a)
      taken: já está em uso
      too_long:
        one: "é muito longo (máximo: 1 caracter)"
        other: "é muito longo (máximo: %{count} caracteres)"
      too_short:
        one: "é muito curto (mínimo: 1 caracter)"
        other: "é muito curto (mínimo: %{count} caracteres)"
      wrong_length:
        one: não possui o tamanho esperado (1 caracter)
        other: não possui o tamanho esperado (%{count} caracteres)
      other_than: deve ser diferente de %{count}
      already_confirmed: "já foi confirmado"
      confirmation_period_expired: "precisa ser confirmada em até %{period}. Por favor, solicite uma nova"
      expired: "expirou, por favor, solicite uma nova"
      not_found: "não encontrado"
      not_locked: "não foi bloqueado"
      not_saved:
        one: "Não foi possível salvar %{resource}: 1 erro"
        other: "Não foi possível salvar %{resource}: %{count} erros."
      carrierwave_processing_error: falhou em ser processado
      carrierwave_integrity_error: não é um tipo de arquivo permitido
      carrierwave_download_error: não pôde ser baixado
      extension_whitelist_error: "não permite arquivos %{extension}. Tipos permitidos: %{allowed_types}"
      extension_blacklist_error: "não permite arquivos %{extension}. Tipos proibidos: %{prohibited_types}"
      rmagick_processing_error: "Falha ao manipular com RMagick. Talvez arquivo não seja uma imagem? Erro original: %{e}"
      mini_magick_processing_error: "Falha ao manipular com MiniMagick. Talvez arquivo não seja uma imagem? Erro original: %{e}"
      min_size_error: "O tamanho do arquivo deve ser maior do que %{min_size}"
      max_size_error: "O tamanho do arquivo deve ser inferior a %{max_size}"
    template:
      body: "Por favor, verifique o(s) seguinte(s) campo(s):"
      header:
        one: "Não foi possível gravar %{model}: 1 erro"
        other: "Não foi possível gravar %{model}: %{count} erros"
  helpers:
    select:
      prompt: Por favor selecione
    submit:
      create: Criar %{model}
      submit: Salvar %{model}
      update: Atualizar %{model}
  number:
    currency:
      format:
        delimiter: "."
        format: "%u %n"
        precision: 2
        separator: ","
        significant: false
        strip_insignificant_zeros: false
        unit: R$
    format:
      delimiter: "."
      precision: 3
      separator: ","
      significant: false
      strip_insignificant_zeros: false
    human:
      decimal_units:
        format: "%n %u"
        units:
          billion:
            one: bilhão
            other: bilhões
          million:
            one: milhão
            other: milhões
          quadrillion:
            one: quatrilhão
            other: quatrilhões
          thousand: mil
          trillion:
            one: trilhão
            other: trilhões
          unit: ""
      format:
        delimiter: ""
        precision: 3
        significant: true
        strip_insignificant_zeros: true
      storage_units:
        format: "%n %u"
        units:
          byte:
            one: Byte
            other: Bytes
          gb: GB
          kb: KB
          mb: MB
          tb: TB
    percentage:
      format:
        delimiter: "."
        format: "%n%"
    precision:
      format:
        delimiter: "."
  support:
    array:
      last_word_connector: " e "
      two_words_connector: " e "
      words_connector: ", "
  time:
    am: ""
    formats:
      default: "%a, %d de %B de %Y, %H:%M:%S %z"
      long: "%d de %B de %Y, %H:%M"
      short: "%d de %B, %H:%M"
      en_default: "%Y-%m-%d"
    pm: ""
  integration_partners:
    does_not_import_promotions: "Esta integração não importa promoções"
    scheduled_promotions_import: "Importação de promoções agendada com sucesso!"
  organizations:
    type:
      invalid: "Tipo de marca inválido"
    highlights:
      create:
        blocklisted: "Não é permitido destacar uma marca bloqueada"
      type:
        invalid: "Tipo de marca inválido"
  authorized_user_import_file:
    errors:
      wrong_format: Arquivo com formato inválido
      invalid_columns: "Colunas inválidas: %{errors}"
      invalid_separator: "As colunas devem ser separadas por Vírgula(,) ou Ponto e Vírgula(;)"
      invalid_content: "Formatação inválida do conteudo CSV: %{errors}"
  inactive_cashback: "Cashback desativado"
  inactive_cashback_import: "Importação de cashback desativado"
  user:
    sign_up:
      registration_denied: "O cadastro de usuários está desativado"
      banned_cpf: "Este CPF está banido"
      validate:
        no_signup: "Módulo de cadastro não ativado"
        already_registered: "Usuário já cadastrado. Faça login."
        not_email: "não é um e-mail"
    params:
      invalid:
        password: "Ops! A senha para o usuário logado está incorreta"
    devices:
      token_succesfully_created: "Device registrado com sucesso"
    destroy_request:
      created: Pedido de cancelamento criado com sucesso
      canceled: Pedido de cancelamento encerrado com sucesso
      unrequested: Você não solicitou o cancelamento ou não tem permissão para encerrá-lo.
  client_employee:
    bad_credentials: "Credenciais inválidas"
    current_business:
      has_more_than_one: Usuário Logado possui dois ou mais businesses. Parâmetro business_identifier é obrigatório
  authorized_user:
    unrelated_authorized_user_group: "Tag indefinida no projeto"
    invalid_email_format: "e-mail inválido"
  smart_link:
    setup_required: "Você precisa acessar o menu Configurações do App no painel de gestão Alloyal e preencher as configurações de Web Domain e/ou Dynamic Link"
    missing_user: "Não foi possível criar o Smart Link pois não encontrou um usuário ou convite"
  smart_login:
    inactive_user: "Usuário inativo"
  project_config:
    errors:
      invalid_auth_integration_for_user_manager: Esta integração não funciona com esse tipo de gestão de usuário.
      child_business_cannot_edit: "Este subprojeto não pode alterar configurações do projeto."
      version_incorrect: "incorreta"
      unrelated_open_signup_business: "Sem autorização para cadastrar nesse projeto"
      invalid_google_key_file: O google_key_file fornecido é inválido.
      invalid_apple_key_file: O apple_key_file fornecido é inválido.
      invalid_firebase_google_file: O firebase_google_file fornecido é inválido.
      invalid_firebase_apple_file: O firebase_apple_file fornecido é inválido.
      invalid_scope: Parâmetro scope deve ser 'mobile' ou 'web'
  voucher:
    unredeemable: "Este voucher não pode ser resgatado"
    redeem:
      lbc_giftcard:
        deactivated: "Esta promoção não está ativa pois a opção Promoção de postos foi desabilitada pelo seu gestor"
      not_available: "Voucher não disponível para resgate"
  city:
    errors:
      invalid_city: "A cidade fornecida é inválida ou inexistente"
  orders:
    usage_instruction:
      with_code: Você resgatou seu cupom com sucesso!
      with_cpf_code: Para validar a promoção, solicitamos que você apresente o seu CPF durante a compra. Fique atento às regras da promoção e boas compras!
      without_code: Você concluiu o resgate do seu cupom com sucesso! Finalize seu pedido e economize em mais uma compra.
    errors:
      giftcard_redeem_limit: "Você já atingiu o limite de resgates deste giftcard"
      business_redeem_limit: "Você já atingiu o seu limite de resgates de giftcard"
      not_redeemed_voucher: "O voucher deve ser resgatado primeiro"
      outside_the_limit_in_a_period: "Esta promoção só pode ser ativada novamente a cada %{frequency_in_days} dia(s)."
  payment:
    BRL:
      create:
        pending: "Sua compra foi confirmada e seu pagamento está sob análise. Assim que for confirmada, certifique-se de ler as instruções e seguir as etapas indicadas para resgatar seu gift card com sucesso."
        authorized: "Sua compra foi confirmada e seu pagamento está sob análise. Assim que for confirmada, certifique-se de ler as instruções e seguir as etapas indicadas para resgatar seu gift card com sucesso."
        captured: "Sua compra foi confirmada e seu pagamento está sob análise. Assim que for confirmada, certifique-se de ler as instruções e seguir as etapas indicadas para resgatar seu gift card com sucesso."
        denied: "Ocorreu algum erro na sua tentativa de compra do Gift Card. Por favor, tente novamente"
    points:
      create:
        captured: "Sua troca de pontos foi confirmada e você recebeu seu Gift Card. Certifique-se de ler as instruções e seguir as etapas indicadas para resgatar seu gift card com sucesso."
        denied: "Ocorreu algum erro na sua tentativa de troca de pontos. Por favor, tente novamente"
    errors:
      charge: "Ocorreu algum erro na sua tentativa de compra do Gift Card. Por favor, tente novamente"
      params: "Preencha todos os dados de pagamento para efetuar a compra"
      cpf_credit_card: "O CPF do Cartão deve ser o mesmo do usuário logado."
  aws_cloud_front:
    error:
      certificate_update_in_progress: Foi realizado uma atualização de certificado recentemente! Tente novamente mais tarde.
  shopkeeper_business:
    invalid_business: "Business inválido"
  shopkeepers:
    branch_added: "Filial adicionada com sucesso"
    organization_branches_added: "%{count} filiais adicionadas com sucesso"
    branch_removed: "Filial removida com sucesso"
  page:
    invalid: "Página inválida"
  promotions:
    redeem_type:
      invalid: "Tipo de resgate inválido"
    rules:
      default: |-
        • As compras devem ser realizadas com o mesmo CPF cadastrado em seu clube de benefícios.
        • Usar cupom, vale presente ou código de desconto que não esteja disponível como promoção neste aplicativo anula esse benefício que oferecemos.
        • A marca pode desativar os cupons e ofertas sem aviso prévio.
        • A promoção pode ser válida apenas em produtos selecionados, sujeito a alteração de condições e não acumulativo com outras promoções do site.
    cashback_rules:
      default: |-
        • Esvazie seu carrinho de compras antes de ativar a promoção.
        • Após a ativação, aceite os cookies e finalize a compra na página que será aberta para garantir que sua compra seja registrada corretamente.

        Compras elegíveis
        • O resgate da promoção deve ser realizado dentro do aplicativo do seu programa de fidelidade. Clicando em "Ativar Promoção", será direcionado para o site da marca, onde a compra deve ocorrer com o mesmo CPF cadastrado no app. Compras feitas diretamente pelo aplicativo da loja ou da marca podem invalidar a recompensa.

        Restrições
        • A promoção não é cumulativa com outros programas de fidelidade da marca e/ou gift cards.
        • O uso de cupons, vales-presente ou códigos de desconto que não estejam disponíveis como promoção neste aplicativo anulam o benefício.
        • Se você utilizar o Google Shopping e/ou sites de comparação de preços, seu benefício pode ser cancelado.

        Funcionamento
        • A loja parceira é responsável por nos informar o valor da compra, o status da venda (cancelada, devolvida ou trocada) e a validação do benefício.
        • A marca pode cancelar o benefício sem aviso prévio.

        Seguindo corretamente esse passo a passo, a loja parceira tem até 7 dias para registrar sua compra, e seu saldo será exibido como “pendente” por até 9 semanas até a confirmação da marca. Após a aprovação, ele será exibido como “disponível”.
    segment:
      invalid: "Segmento inválido"
  gift_card:
    inactive: "Gift Card desativado"
    request:
      charge:
        authorized: "Sua compra foi finalizada com sucesso! Em breve, você receberá as informações sobre o seu Gift Card no seu aplicativo ou e-mail."
        pending: "Recebemos sua solicitação de compra! Em breve, você receberá as informações sobre o seu Gift Card no seu aplicativo ou e-mail."
    errors:
      charge: Ocorreu algum erro na sua tentativa de compra do Gift Card. Por favor, tente novamente
      suspicious_request: "Sua requisição parece suspeita. Por favor, tente novamente ou entre em contato com o suporte se o problema persistir."
      credit_card: deve ter o mesmo CPF do usuário logado.
      order: não foi encontrada!
      payment: "Já existe uma requisição de pagamento para este Gift card. Acesso o menu: Meus gift cards"
      denied: "Sua compra foi negada na operadora"
  business:
    invalid_contact_email: "e-mail de contato inválido"
  auth_integration:
    user_not_created: "Cadastro inválido na integração, contate seu administrador."
  search:
    term:
      invalid: "Termo de busca inválido"
    resource:
      invalid: "Recurso inválido"
    coordinates:
      required: "Latitude e longitude são obrigatórios"
  branch:
    type:
      invalid: "Tipo de filial inválido"
      uniqueness: já existe uma online para essa marca
    params:
      invalid: "Ops! Parâmetro inválido. Os valores aceitos são: '%{accepted_params}'."
  csv:
    malformed_error: "Não foi possível processar o arquivo CSV"
  organization_category:
    position:
      import:
        organization_category_not_found: "Categoria não encontrada na marca"
        scheduled: "A importação de ordenação foi agendada. Os detalhes serão enviados para o seu email: %{email}"
  redeem:
    import:
      scheduled: "Sucesso! A importação de resgate foi agendada. Os detalhes serão enviados para o seu email: %{email}"
      user_not_found: Usuário não encontrado com o CPF informado
  category:
    errors:
      sub_category_cant_be_main_category: "Não é possível definir uma subcategoria como categoria principal"
      sub_category_cant_be_itself: "Não é possível definir a propria categoria como subcategoria"
    home_pinned:
      invalid: "A categoria precisa estar ativa e com ícone para ser fixada na Home"
  purchase:
    import:
      scheduled: "Sucesso! A importação de compra foi agendada. Os detalhes serão enviados para o seu email: %{email}"
  banned_cpf:
    reasons:
      payment_rate_limit: "Sua conta foi temporariamente bloqueada devido a várias tentativas de compra. Entre em contato conosco para que possamos ajudá-lo a resolver esse problema."
      default: "Sua conta foi temporariamente bloqueada. Entre em contato conosco para que possamos ajudá-lo a resolver esse problema."
  webhook_sender:
    application:
      errors:
        already_exists: "Este projeto já possui a aplicação para enviar Webhooks"
    message:
      errors:
        missing_application: "Este projeto não possui a aplicação para enviar Webhooks"
        event_disabled_for_project: "Este projeto desabilitou o envio deste tipo de evento"
        inactive_webhook_config: "Este projeto desabilitou o envio de webhooks"
  lead:
    business:
      must_be_main_business: deve ser o business principal
  search_user_integration:
    status:
      eligible_message: "Elegível"
      not_eligible_message: "Não elegível"
  organization_profile:
    cashback_manage:
      forbidden: Gerenciamento de cashback não permitido
  import_file:
    csv:
      invalid_header: Cabeçalho inválido
      invalid_csv: CSV inválido
  telemedicine:
    warnings:
      already_subscribed: "Você já se cadastrou para usar Telemedicina."
    errors:
      cant_subscribe: "Erro ao se inscrever para telemedicina. Se persistir, contate seu gestor."
      couldnt_create_smart_link: "Não foi possível acessar o serviço de Telemedicina"
      waiting_sync: "Atualizando seus dados na telemedicina, volte em breve!"
  you_safer:
    errors:
      import_users_error: "Erro ao se inscrever para telemedicina. Se persistir, contate seu gestor."
      deactivate_users_error: "Erro ao desabilitar usuários de telemedicina inativos."
  conexa:
    errors:
      create_error: "Erro na plataforma de telemedicina. Se persistir, contate seu gestor."
      activate_error: "Erro reativar telemedicina para o usuário."
      deactivate_error: "Erro ao desativar telemedicina para o usuário."
      smart_link_error: "Erro ao gerar smart link de telemedicina"
