# mobile app backwards compatibility
post "api/v1/auth/sign_in", to: "api/v1/sessions#create"
get "api/v1/cashback_records", to: "api/v1/cashback_records#index"

namespace :api do # deprecated
  namespace :v1 do
    resources :cupons, only: [:show] do
      get "tickets" => "tickets#create"
      resources :tickets, only: [:create]
    end

    get :user, to: "users#show"
    post :user, to: "users#create"
    patch "user/recovery_password", to: "users/reset_password#update"

    namespace :external do
      resources :categories, only: :none do
        resources :coupons, only: :index, controller: "categories/coupons"
      end

      resources :branches, only: :none do
        resources :coupons, only: :index, controller: "branches/coupons"
      end

      resources :promotions, only: :index
    end
  end
  scope module: :integration, path: :integration, defaults: {format: :json} do
    resources :vouchers, only: %i[show create]
  end
end

namespace :api do
  scope path: "v1/public_integration", module: :v1 do
    put "/auth/password", to: "passwords#update"
    post "/auth/sign_in", to: "sessions#create"

    resources :branches, only: :none do
      resources :coupons, only: :index, controller: "branches/coupons"
    end
    resource :business_apps, only: :show, controller: :project_configs do
      get :app_config, on: :collection, to: "project_configs#show"
    end # deprecated

    resources :cashback_discount_requests, only: [:index, :create]
    resources :cashback_records, only: [:index, :show] do
      resources :history, only: :index, controller: "cashback_records/histories"
    end
    resources :cashback_transfer_requests, only: [:index, :create]
    delete :cashback_transfer_requests, to: "cashback_transfer_requests#destroy"
    resources :categories, only: :none do
      resources :coupons, only: :index, controller: "categories/coupons"
      resources :organizations, only: :index, controller: "categories/organizations"
    end
    resources :categories, only: :index
    resources :cupons, only: :show
    resources :custom_fields, only: [:index]
    resources :custom_messages, only: :index, controller: :custom_texts # deprecated
    resources :custom_texts, only: :index

    resources :geolocations, only: :index do
      get :search, to: "geolocations#show", on: :collection
    end
    resources :giftcards, only: [:show]

    resources :menus, only: :index, controller: "navigations/menus"

    resources :orders, only: [:index, :create, :destroy] do
      member do
        resources :charges, only: [:create], controller: "orders/charges"
      end
    end
    resources :organizations, only: [:index, :show] do
      collection do # Everything on this colection will be moved to organizations_controller#index with defaults params
        resources :favorites, only: :index, controller: "favorites"
        resources :highlights, only: :index, controller: "organizations/highlights"
        resources :highlights_nearby, only: :index, controller: "organizations/highlights"
        resources :highlights_online, only: :index, controller: "organizations/highlights"
        resources :nearest, only: :index, controller: "organizations"
        resources :recent, only: :index, controller: "organizations/recent"
        resources :recent_nearby, only: :index, controller: "organizations/recent"
        resources :recent_online, only: :index, controller: "organizations/recent"
      end
      member do
        resources :giftcards, only: :index, controller: "organizations/giftcards"
      end
      resources :branches, only: [:index, :show], controller: "organizations/branches" do
        resources :coupons, only: :index, controller: "branches/coupons" # deprecated
      end
      resources :coupons, only: [:index, :show], controller: "organizations/coupons"
      resources :favorite, only: :create, controller: "favorites"
      delete :favorite, to: "favorites#destroy"
      resources :orders, only: :create, controller: "organizations/orders"
    end

    resources :project_configs, only: :show
    resources :promotions, only: [:index]

    resource :users, only: :update
    resources :users, only: :destroy, to: "users/deletions#create"
    resources :users, only: :create do
      collection do
        resource :token, only: :create, controller: "users/token"
        patch :register_device, to: "users/devices#create"
        put :register_device, to: "users/devices#create"
        resource :reset_password, only: :update, controller: "users/reset_password"
        post :cancel_destroy, to: "users/deletions#destroy"
        get :me, to: "users#show"
        resource :passwords, only: :none do
          resource :validation, only: :show, controller: "users/passwords/validations"
        end
        resource :cpfs, only: :none do
          resource :validation, only: :show, controller: "users/cpfs/validations"
        end
      end
    end

    resources :coupons, only: :show
  end
end
