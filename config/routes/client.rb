devise_for :client_employees,
  only: [:sessions],
  path: "client/v2",
  controllers: {sessions: "client/v2/sessions"}

namespace :client do
  namespace :v2, defaults: {format: :json} do
    devise_scope :client_employee do
      resources :authorized_users, controller: "authorized_users" do
        collection do
          post :sync
          resources :custom_fields, only: [:index], controller: "authorized_users/custom_fields"
          resources :tags, only: [:index], controller: "authorized_users/tags"

          resource :bulk, only: [:destroy], controller: "authorized_users/bulk" do
            post :activate, controller: "authorized_users/bulk", on: :collection
            patch :transfer_business, controller: "authorized_users/business", action: :update, on: :collection
          end
          resources :file_imports, except: [:destroy, :update], controller: "authorized_user_import_files" do
            resources :errors, only: :index, controller: "authorized_user_import_files/errors"
          end
          resources :file_exports, only: [:index, :create], controller: "authorized_users/file_exports"
          resources :total_records, only: [:index], controller: "authorized_users/total_records"
        end
      end

      namespace :businesses do
        resources :business_status, only: :index, controller: "statuses"
      end
      resources :businesses, except: :destroy
      resource :business_app, only: [:show, :update], controller: "project_configs", on: :collection # Will be Deprecated

      scope :cashback_batches do
        resources :export, only: :create, controller: "payouts/export"
        resources :in_transfer, only: :index, controller: "payouts/in_transfer"
        resources :locked, only: :index, controller: "payouts/locked"
        resources :process, only: :create, controller: "payouts/process"
        resources :transferred, only: :index, controller: "payouts/transferred"
      end
      resources :cashback_transfer_frequencies, only: [:index]
      resources :cashback_transfer_receivers, only: [:index]
      resources :campaigns, only: [:index], controller: "promotions"
      resources :cashback_records, only: [:index], controller: "cashback_records"
      resources :cashbacks, only: [:index, :update], controller: "cashbacks"
      resources :categories, except: [:destroy], controller: "categories" do
        delete :blocklists, to: "categories/blocklists#destroy"

        collection do
          resources :available_organizations, only: :index, controller: "categories/available_organizations"
          resources :available_promotions, only: :index, controller: "categories/available_promotions"
        end
        resources :blocklists, only: [:create], controller: "categories/blocklists"
        resources :promotions, only: [:index, :create, :destroy], controller: "categories/promotions", param: :promotion_id
      end
      resources :client_employees do
        resources :businesses, only: [:index], on: :member, controller: "client_employees/businesses"
      end
      resources :custom_fields, controller: "custom_fields"
      resources :custom_texts, only: [:index], controller: "custom_texts"
      put "custom_texts", to: "custom_texts#upsert", controller: "custom_texts"

      resources :deposits, only: :create, controller: "wallets/deposits"

      resources :exclusive_categories, controller: "categories" do
        resources :organizations, except: [:update, :show], controller: "exclusive_categories/organizations", on: :member
      end

      resource :giftcard_config, only: [:show, :update], controller: "giftcard_configs", on: :collection

      resources :hinova_tokens, controller: "hinova_tokens", on: :collection
      resource :hubsoft_tokens, except: [:index], controller: "hubsoft_tokens", on: :collection
      resources :hubsoft_services, except: [:show, :update], controller: "hubsoft_services", on: :collection

      resource :ixc_tokens, except: [:index], controller: "ixc_tokens", on: :collection
      resources :ixc_contracts, controller: "ixc_contracts", on: :collection

      resource :mailer_configs, only: [:show], controller: "mailer_configs", on: :collection
      resources :menu_icons, only: :index, controller: "navigations/menu_icons"
      resources :menu_items, except: [:show, :new, :edit], controller: "navigations/menus"
      resource :metrics, only: :none do
        resources :categories, only: :none do
          collection do
            resources :redeems, only: :index, controller: "metrics/categories/redeems"
          end
        end
        resources :orders, only: :index, controller: "metrics/orders"
        resources :gmv, only: :index, controller: "metrics/gmv"
        resources :cashback, only: :index, controller: "metrics/cashback"

        resource :authorized_users, only: :none do
          resources :gmv, only: :index, controller: "metrics/authorized_users/gmv"
        end
        resources :organizations, only: :none do
          collection do
            resources :redeems, only: :index, controller: "metrics/organizations/redeems"
          end
        end
        resource :users, only: :none do
          resources :gmv, only: :index, controller: "metrics/users/gmv"
          resources :registrations, only: :index, controller: "metrics/users/registrations"
        end
      end
      resource :mk_solution_tokens, except: [:index], controller: "mk_solution_tokens", on: :collection
      resources :mk_solution_plans, controller: "mk_solution_plans", on: :collection

      resources :orders, only: :create, controller: "orders" do
        resources :cashbacks, only: :index, controller: "orders/cashbacks"
      end
      resources :organizations, only: :none do
        resources :admins, only: [:index], controller: "organizations/admins" do
          resource :active, only: [:update], controller: "organizations/admins/active"
          resource :inactive, only: [:update], controller: "organizations/admins/inactive"
          collection do
            resource :invitations, only: [:create, :update], controller: "organizations/admins/invitations"
          end
        end
        resources :coupons, only: [:index], controller: "organizations/coupons"
        resources :branches, only: [:index, :show, :create, :update], controller: "organizations/branches" do
          resource :active, only: [:update], controller: "organizations/branches/active"
          resource :inactive, only: [:update], controller: "organizations/branches/inactive"
        end
        resource :highlights, only: [:create, :destroy], controller: "organizations/highlights"
        resources :promotions, only: [:index, :show, :create, :destroy], controller: "organizations/promotions" do
          resource :branches, only: [:update], controller: "organizations/promotions/branches"
          resources :branches, only: :index, controller: "organizations/promotions/branches"
          resource :descriptions, only: [:update], controller: "organizations/promotions/descriptions"
          resources :vouchers, only: [:index, :create, :destroy], controller: "organizations/promotions/vouchers"
          resource :provider_type, only: :update, controller: "organizations/promotions/provider_type"
          resource :rules, only: :update, controller: "organizations/promotions/rules"
          resource :work_schedule, only: :update, controller: "organizations/promotions/work_schedule"
          resource :voucher_type, only: :update, controller: "organizations/promotions/voucher_type"
        end
        get :highlights, on: :collection, to: "organizations/highlights#index"
      end
      resources :organizations, only: [:index], controller: "organizations" do
        resources :blocklists, only: [:create], controller: "organizations/blocklists"
        delete :blocklists, to: "organizations/blocklists#destroy"
      end
      resources :organization_profiles, only: [:index, :show, :create, :update, :destroy], controller: "organization_profiles" do
        resource :cashback_imports, only: [:update], controller: "organization_profiles/cashback_imports"
      end

      resources :payments, only: :create, controller: "wallets/payments"
      resource :playhub_clients, except: [:index], controller: "playhub_clients", on: :collection
      resources :funds, only: [:create, :destroy], controller: "funds"
      resources :prize_draws, only: [:show, :index], controller: "prize_draws" do
        collection do
          get ":id/entries", to: "prize_draws/entries#index"
          get ":id/winner", to: "prize_draws/winner#show"
          post "draft", to: "prize_draws/draft#create"
          put ":id/cancel", to: "prize_draws/cancel#update"
          put ":id/draft", to: "prize_draws/draft#update"
          put ":id/publish", to: "prize_draws/publish#update"
        end
      end
      resource :project_config, only: [:show, :update], controller: "project_configs", on: :collection

      scope :reports do
        resources :users, only: :none do
          collection do
            get :overall_businesses_info, controller: "reports/users"
          end
        end
      end
      resources :redeem_templates, only: [:index], controller: "redeem_templates", as: :redeem_templates
      resources :related_businesses, only: [:index], controller: "related_businesses", on: :collection
      resource :reports, only: :none do
        resources :cashback_records, only: [:index], controller: "reports/cashback_records"
        resources :users, only: :none do
          collection do
            get :overall_info, controller: "reports/users"
          end
        end
        resources :cashback_transfers, only: :index, controller: "reports/cashback_transfers"
      end

      resources :project_configs, only: [:index] do
        collection do
          resource :bulk, only: :update, controller: "project_configs/bulk"
        end
      end

      post "sign_in", to: "sessions#create"
      resource :signature_secret, only: [:update], controller: "signature_secrets"

      resources :telemedicine_plans, only: [:index]

      resources :users, only: :none do
        get :me, on: :collection
        post :token, on: :collection
      end
      resources :users, controller: "users", on: :collection do
        resource :user_destroy_requests, except: [:update, :index], controller: "users/user_destroy_requests", on: :member
        resource :smart_link, only: [:create], controller: "users/smart_link", on: :collection
        resource :smart_login, only: [:create], controller: "users/smart_login", on: :collection
        collection do
          resource :sync, only: [:create], controller: "users/sync"
        end
        resources :wallets, only: :index, controller: "users/wallets"
      end
      resources :user_destroy_requests, only: [:index], controller: "user_destroy_requests", on: :collection
      resources :user_managers, only: [:index]
      resources :user_tags, only: [:index, :create], controller: "authorized_user_groups"
      resources :user_wallets, only: :none, controller: "user_wallets" do
        collection do
          resources :balances, only: :index, controller: "user_wallets/balances"
          resources :entries, only: [:index, :create], controller: "user_wallets/entries"
        end
      end

      resources :voalle_plans, controller: "voalle_plans", on: :collection
      resource :voalle_tokens, except: [:index], controller: "voalle_tokens", on: :collection

      resource :web_applications, only: [:show], controller: "web_applications", on: :collection do
        resource :certificate, only: [:create], controller: "web_applications/certificate", on: :member
        resource :dns_validation, only: [:update], controller: "web_applications/dns_validation", on: :member
        resource :setup, only: [:create], controller: "web_applications/setup", on: :member
      end

      resources :wallets, only: :show, controller: "wallets" do
        collection do
          resources :deposits, only: :create, controller: "wallets/deposits"
          resources :payments, only: :create, controller: "wallets/payments"
        end
      end
    end

    resources :addresses, only: :none do
      get :search, to: "addresses#show", on: :collection
    end

    resources :category_types, only: :index
    resources :cities, only: :index
    resources :coupon_types, only: [:index]
    resources :custom_text_types, only: :index

    resources :federation_units, only: :index

    resources :integration_types, only: :index

    resources :passwords, only: :update do
      post :forgot, on: :collection
      post :reset, on: :collection
    end
    namespace :promotions do
      resources :provider_types, only: :index
    end
    resources :prize_drawn_numbers, only: :create
  end
end
