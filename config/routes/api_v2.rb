devise_for :users, path: "api/v2/users",
  controllers: {
    sessions: "api/v2/users/sessions",
    registrations: "api/v2/users/registrations",
    passwords: "api/v2/users/passwords"
  },
  defaults: {
    format: :json
  }

namespace :api do
  namespace :v2, defaults: {format: :json} do
    resources :addresses, only: :index

    resources :banners, only: :index
    resources :branches, only: :none do
      resources :coupons, only: :index, controller: "branches/coupons"
    end

    namespace :cashbacks, path: :wallet do
      resource :balance, only: :show, controller: "balance"
      resources :transactions, only: :index
      scope "transactions/:cashback_record_id" do
        resources :histories, only: :index, controller: "transaction_histories"
      end
      resources :withdrawals, only: :create
    end
    namespace :cashbacks do
      resource :balance, only: :show, controller: "balance"
      resources :transactions, only: :index
      scope "transactions/:cashback_record_id" do
        resources :histories, only: :index, controller: "transaction_histories"
      end
    end
    resources :categories, only: [:show, :index] do
      resources :organizations, only: :index, controller: "categories/organizations"
    end
    resources :coupons, only: :show do
      resource :redeem, only: :create, controller: "redeem"
    end
    resources :custom_messages, only: :index

    resources :gift_cards, only: :none do
      collection do
        resources :organizations, only: :index, controller: "gift_cards/organizations"
      end
    end

    resources :leads, only: [:create, :index]

    resource :member_referral_configs, only: :show
    resource :menus, only: :none do
      get "/:menu_type", to: "navigations/menus#index"
    end
    resources :menu_items, only: :none do
      get "/redirect", to: "navigations/redirect#show", on: :member
    end

    resources :orders, only: [:index, :show, :create, :destroy] do
      resources :payments, only: :create, controller: "orders/payments"
    end

    resources :organizations, only: :show do
      resources :branches, only: :index, controller: "organizations/branches"
      collection do
        resources :favorites, only: :index, controller: "organizations/favorites"
      end
      resource :favorite, only: [:create, :destroy], controller: "organizations/favorites"
      resources :coupons, only: :index, controller: "organizations/coupons"
      resources :gift_cards, only: :index, controller: "organizations/gift_cards"
    end

    resources :policies, param: :kind, only: :show
    resources :prize_draws, only: :show do
      collection do
        resources :oldests, only: :index, controller: "prize_draws/oldests", as: :prize_draws_oldests
        resources :recents, only: :index, controller: "prize_draws/recents", as: :prize_draws_recents
        resources :wons, only: :index, controller: "prize_draws/wons", as: :prize_draws_wons
      end
      resources :entries, only: :create, controller: "prize_draws/entries"
    end
    resource :project_config, only: :show

    resources :search, only: :index
    resources :sign_in_custom_fields, only: :index
    resources :sign_up_custom_fields, only: :index

    namespace :users do
      get :me, action: :show
      resource :address, only: [:show, :update]
      resource :devices, only: :create
      resource :deletion_requests, only: [:create, :destroy]
      namespace :password do
        resource :option_previews, only: :show
      end
      resource :password_validation, only: :show
      resource :taxpayer_number_validation, only: :show
      namespace :telemedicine do
        resource :access, only: :create
        resource :subscription, only: :create
      end
    end

    resources :wallets, only: :index do
      collection do
        resource :balance, only: :show, controller: "wallets/balance"
        resources :transactions, only: [:index, :show], controller: "wallets/transactions"
        resources :withdrawals, only: :create, controller: "wallets/withdrawals"
        resources :order_history, only: :index, controller: "wallets/order_history" do
          resources :events, only: :index, controller: "wallets/order_history/events"
        end
        resources :conversions, only: :index, controller: "wallets/conversions"
      end
      member do
        resources :recent_transactions, only: :index, controller: "wallets/recent_transactions"
      end
    end

    namespace :earn_points do
      resources :popular_organizations, only: :index
      resources :highest_points_organizations, only: :index
      resources :new_organizations, only: :index
    end

    resources :widgets, only: :index

    resource :fund_redemption_config, only: :show

    resources :subscriptions, only: [:index, :create] do
      member do
        resource :credit_card, only: :update, controller: "subscriptions/credit_card"
      end
    end
    resource :subscription, only: [:show, :destroy], controller: "subscription"

    resources :credit_cards, only: [:index, :create, :destroy]

    namespace :checkout do
      resources :plans, only: [:show]
    end
  end
end
