get "api/integration/users/:cpf" => "api/integration/users#show"
get "api/lecupon/:cpf" => "api/integration/users#show", :defaults => {integration: "extrafarma"}
get "checkcpf/:cpf" => "api/integration/users#show", :defaults => {integration: "pague_menos"}
get "empresas/:cpf" => "api/integration/users#show", :defaults => {integration: "araujo"}

namespace :api do
  namespace :integration do
    resources :sign_in, only: :create
    resources :voucher_availability, only: :create

    scope module: :web_postos, path: "webhooks/web_postos" do
      resources :venda, only: :create
      resources :cancelamento_venda, only: :create
    end
  end
end
