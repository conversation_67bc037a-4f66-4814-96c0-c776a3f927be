<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="328" height="160" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 328 160">
  <defs>
    <clipPath id="clippath">
      <rect width="328" height="160" rx="16" ry="16" style="fill: none;"/>
    </clipPath>
    <linearGradient id="Gradiente_sem_nome" data-name="Gradiente sem nome" x1="232.23" y1="32.59" x2="290.19" y2=".04" gradientTransform="translate(0 161) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".8"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="Gradiente_sem_nome_2" data-name="Gradiente sem nome 2" x1="172.95" y1="32.83" x2="213.54" y2="10.03" gradientTransform="translate(0 161) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".8"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="Gradiente_sem_nome_3" data-name="Gradiente sem nome 3" x1="275.84" y1="160.53" x2="330.86" y2="129.63" gradientTransform="translate(0 161) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".8"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="Gradiente_sem_nome_4" data-name="Gradiente sem nome 4" x1="181.76" y1="174.77" x2="262.94" y2="129.18" gradientTransform="translate(0 161) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".8"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="Gradiente_sem_nome_5" data-name="Gradiente sem nome 5" x1="2909.66" y1="-2572.91" x2="-2639.82" y2="2949.84" gradientTransform="translate(0 161) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#fff" stop-opacity=".2"/>
    </linearGradient>
    <linearGradient id="Gradiente_sem_nome_6" data-name="Gradiente sem nome 6" x1="100.6" y1="37.79" x2="249.62" y2="4.15" gradientTransform="translate(86.32 106.18) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".47"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <radialGradient id="Gradiente_sem_nome_7" data-name="Gradiente sem nome 7" cx="23.83" cy="219.85" fx="23.83" fy="219.85" r="1" gradientTransform="translate(-68187.45 26434.31) rotate(64.2) scale(250.24 -332.42)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".7"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="Gradiente_sem_nome_8" data-name="Gradiente sem nome 8" x1="100.8" y1="36.76" x2="236.09" y2="6.97" gradientTransform="translate(86.36 105.58) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".4"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <radialGradient id="Gradiente_sem_nome_9" data-name="Gradiente sem nome 9" cx="23.83" cy="219.82" fx="23.83" fy="219.82" r="1" gradientTransform="translate(-58397.69 19095.41) rotate(66.73) scale(234.93 -279.24)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
    <mask id="mask" x="148.7" y="14.56" width="208.92" height="137.71" maskUnits="userSpaceOnUse">
      <g id="mask0_6461_7015" data-name="mask0 6461 7015">
        <rect x="204.1" y="-9.48" width="100.09" height="186.09" rx="10.78" ry="10.78" transform="translate(138.13 324.64) rotate(-82.5)" style="fill: url(#Gradiente_sem_nome_8); stroke: url(#Gradiente_sem_nome_9); stroke-miterlimit: 4; stroke-width: .65px;"/>
      </g>
    </mask>
    <linearGradient id="Gradiente_sem_nome_10" data-name="Gradiente sem nome 10" x1="183.53" y1="101" x2="270.09" y2="64.56" gradientTransform="translate(125.61 -144.82) rotate(90) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".77"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <radialGradient id="Gradiente_sem_nome_11" data-name="Gradiente sem nome 11" cx="24.33" cy="219.7" fx="24.33" fy="219.7" r="1" gradientTransform="translate(-24096.77 -30792.88) rotate(137.96) scale(105.85 -178.43)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".7"/>
      <stop offset="1" stop-color="#fff" stop-opacity=".23"/>
    </radialGradient>
    <linearGradient id="Gradiente_sem_nome_12" data-name="Gradiente sem nome 12" x1="279.53" y1="104.32" x2="294.27" y2="86.74" gradientTransform="translate(-18.66 121.65) rotate(7.5) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".47"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <radialGradient id="Gradiente_sem_nome_13" data-name="Gradiente sem nome 13" cx="24.28" cy="217.94" fx="24.28" fy="217.94" r="1" gradientTransform="translate(-3530.85 5603.23) rotate(28.87) scale(26.68 -30.7)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".7"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="Gradiente_sem_nome_14" data-name="Gradiente sem nome 14" x1="300.49" y1="85.68" x2="308.59" y2="58.11" gradientTransform="translate(222.2 -214.83) rotate(90) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".47"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <radialGradient id="Gradiente_sem_nome_15" data-name="Gradiente sem nome 15" cx="23.89" cy="218.06" fx="23.89" fy="218.06" r="1" gradientTransform="translate(-3265.39 6498.33) rotate(15.31) scale(71.54 -32.66)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".7"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="Gradiente_sem_nome_16" data-name="Gradiente sem nome 16" x1="307.17" y1="58.35" x2="318.41" y2="33.49" gradientTransform="translate(256.49 -198.3) rotate(90) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".47"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <radialGradient id="Gradiente_sem_nome_17" data-name="Gradiente sem nome 17" cx="23.94" cy="218.03" fx="23.94" fy="218.03" r="1" gradientTransform="translate(-3117.56 6364.04) rotate(19.43) scale(47.05 -32.25)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".7"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="Gradiente_sem_nome_18" data-name="Gradiente sem nome 18" x1="195.88" y1="102.37" x2="224.37" y2="66.21" gradientTransform="translate(0 161) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".8"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="Gradiente_sem_nome_19" data-name="Gradiente sem nome 19" x1="225.37" y1="57.05" x2="197.97" y2="108" gradientTransform="translate(0 161) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#fff" stop-opacity=".2"/>
    </linearGradient>
    <linearGradient id="Gradiente_sem_nome_20" data-name="Gradiente sem nome 20" x1="285.45" y1="113.22" x2="326.04" y2="90.42" gradientTransform="translate(0 161) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".8"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <g style="isolation: isolate;">
    <g id="Camada_1" data-name="Camada 1">
      <g style="clip-path: url(#clippath);">
        <g>
          <rect width="328" height="160" rx="16" ry="16" style="fill: #922c2c;"/>
          <path d="M267.93,145.68c-.45.99-1.25,1.79-2.24,2.24l-2.69,1.22,2.69,1.22c.99.45,1.79,1.25,2.24,2.24l1.22,2.69,1.22-2.69c.45-.99,1.25-1.79,2.24-2.24l2.69-1.22-2.69-1.22c-.99-.45-1.79-1.25-2.24-2.24l-1.22-2.68-1.22,2.69h0Z" style="fill: url(#Gradiente_sem_nome);"/>
          <path d="M197.94,140.27c-.31.69-.87,1.25-1.57,1.57l-1.88.85,1.88.85c.69.31,1.25.87,1.57,1.57l.85,1.88.85-1.88c.31-.69.87-1.25,1.57-1.57l1.88-.85-1.88-.85c-.69-.31-1.25-.87-1.57-1.57l-.85-1.88-.85,1.88h0Z" style="fill: url(#Gradiente_sem_nome_2); fill-opacity: .5;"/>
          <path d="M309.72,16.87c-.42.94-1.18,1.7-2.12,2.13l-2.55,1.15,2.55,1.15c.94.43,1.7,1.18,2.12,2.13l1.15,2.55,1.15-2.55c.42-.94,1.18-1.7,2.13-2.13l2.55-1.15-2.55-1.15c-.94-.43-1.7-1.18-2.13-2.13l-1.16-2.55-1.15,2.55h0Z" style="fill: url(#Gradiente_sem_nome_3); fill-opacity: .5;"/>
          <path d="M231.76,10.42c-.63,1.39-1.75,2.5-3.14,3.14l-3.76,1.7,3.76,1.7c1.39.63,2.5,1.75,3.14,3.14l1.7,3.76,1.7-3.76c.63-1.39,1.75-2.5,3.14-3.14l3.76-1.7-3.76-1.7c-1.39-.63-2.5-1.75-3.14-3.14l-1.71-3.76-1.7,3.76h0Z" style="fill: url(#Gradiente_sem_nome_4);"/>
          <path d="M219.16-39.91c-19.45,32.86-50.39,57.56-86.71,69.57l-95.11,31.18c-2.52.83-2.89,4.24-.61,5.58l86.19,50.89h0c32.98,19.44,57.78,50.45,69.82,86.86l31.18,95.11c.83,2.52,4.24,2.89,5.58.61l50.89-86.19h0c19.44-32.98,50.45-57.78,86.86-69.82l95.11-31.18c2.52-.83,2.89-4.24.61-5.58l-86.19-50.89h0c-32.98-19.44-57.78-50.45-69.82-86.86l-87.81-9.28ZM219.16-39.91s.04-.08.06-.11l50.9-86.2c1.35-2.28,4.75-1.91,5.58.6l31.27,94.98-87.81-9.27ZM200.65-117.5C169.24-64.38,119.24-24.45,60.56-5.03L-95.26,46.07c-2.52.83-2.89,4.24-.61,5.58L45.33,135.05h0c53.28,31.41,93.33,81.52,112.79,140.35l51.07,155.86c.83,2.52,4.24,2.89,5.58.61l83.38-141.24h0c31.4-53.3,81.49-93.37,140.31-112.83L200.65-117.5ZM200.65-117.5s.04-.1.07-.14l83.38-141.25c1.35-2.28,4.76-1.91,5.58.6l51.22,155.64c19.45,58.84,59.51,108.94,112.79,140.35h0s141.19,83.4,141.19,83.4c2.28,1.35,1.91,4.76-.61,5.58l-155.81,51.11L200.65-117.5ZM221.17-39.06s.04-.07.06-.11l50.75-85.94c.4-.68,1.41-.57,1.66.18l31.27,94.98h0c12.2,36.9,37.34,68.34,70.78,88.05h0s86.19,50.89,86.19,50.89c.68.4.57,1.42-.18,1.66l-95.11,31.18h0c-36.9,12.21-68.34,37.34-88.04,70.78l-50.89,86.19c-.4.68-1.42.57-1.66-.18l-31.18-95.11h0c-12.21-36.91-37.34-68.34-70.78-88.05l-86.19-50.89c-.68-.4-.57-1.42.18-1.66l95.11-31.18h0c36.9-12.21,68.34-37.34,88.04-70.78ZM202.74-116.77s.05-.1.07-.14l83.16-140.88c.4-.68,1.42-.57,1.66.18l51.22,155.65h0c19.61,59.33,60,109.86,113.74,141.54l141.19,83.4c.68.4.57,1.42-.18,1.66l-155.82,51.11h0c-59.31,19.62-109.83,60.03-141.5,113.79l-83.38,141.24c-.4.68-1.42.57-1.66-.18l-51.08-155.86h0c-19.61-59.33-60-109.86-113.74-141.54h0L-94.76,49.79c-.68-.4-.57-1.42.18-1.66L61.24-2.98h0c59.31-19.62,109.83-60.03,141.5-113.79ZM179.1-217.79c.03-.06.06-.12.08-.18l120.39-203.95c2.4-4.06,8.46-3.41,9.94,1.07l74.14,225.29h0c29.29,88.61,89.62,164.09,169.88,211.4l204.37,120.71c4.06,2.4,3.4,8.47-1.08,9.94l-225.54,73.97h0c-88.58,29.31-164.04,89.67-211.34,169.96l-120.68,204.44c-2.4,4.07-8.47,3.4-9.94-1.08l-73.93-225.6h0c-29.29-88.61-89.62-164.09-169.88-211.4,0,0,0,0,0,0l-204.37-120.71c-4.06-2.4-3.4-8.47,1.08-9.94L-32.24-47.83h0c88.58-29.31,164.04-89.66,211.34-169.96Z" style="fill: none; stroke: url(#Gradiente_sem_nome_5); stroke-linejoin: round; stroke-opacity: .05; stroke-width: 2.17px;"/>
          <g>
            <rect x="204.43" y="-9.15" width="99.44" height="185.43" rx="10.46" ry="10.46" transform="translate(138.13 324.64) rotate(-82.5)" style="fill: url(#Gradiente_sem_nome_6);"/>
            <rect x="204.43" y="-9.15" width="99.44" height="185.43" rx="10.46" ry="10.46" transform="translate(138.13 324.64) rotate(-82.5)" style="fill: none; stroke: url(#Gradiente_sem_nome_7); stroke-miterlimit: 4; stroke-width: 1.16px;"/>
          </g>
          <g style="mask: url(#mask);">
            <g style="mix-blend-mode: soft-light; opacity: .2;">
              <rect x="195.46" y="-15.64" width="115.4" height="198.11" rx="10.46" ry="10.46" transform="translate(137.42 323.52) rotate(-82.5)" style="fill: none;"/>
            </g>
          </g>
          <g style="opacity: .5;">
            <rect x="177.82" y="45.41" width="64.7" height="64.7" rx="15.69" ry="15.69" transform="translate(105.65 275.98) rotate(-82.5)" style="fill: url(#Gradiente_sem_nome_10);"/>
            <rect x="178.76" y="46.34" width="62.83" height="62.83" rx="14.75" ry="14.75" transform="translate(105.65 275.98) rotate(-82.5)" style="fill: none; stroke: url(#Gradiente_sem_nome_11); stroke-miterlimit: 4; stroke-width: 1.88px;"/>
          </g>
          <g>
            <path d="M270.9,58.5l14.55,1.92c2.19.29,3.73,2.3,3.45,4.49h0c-.29,2.19-2.3,3.73-4.49,3.45l-14.55-1.92c-2.19-.29-3.73-2.3-3.45-4.49h0c.29-2.19,2.3-3.73,4.49-3.45Z" style="fill: url(#Gradiente_sem_nome_12);"/>
            <path d="M270.94,58.18l14.55,1.92c2.37.31,4.04,2.49,3.73,4.86h0c-.31,2.37-2.49,4.04-4.86,3.73l-14.55-1.92c-2.37-.31-4.04-2.49-3.73-4.86h0c.31-2.37,2.49-4.04,4.86-3.73Z" style="fill: none; stroke: url(#Gradiente_sem_nome_13); stroke-miterlimit: 4; stroke-width: .65px;"/>
          </g>
          <g style="opacity: .5;">
            <rect x="291.44" y="56.97" width="8" height="64.7" rx="4" ry="4" transform="translate(168.33 370.58) rotate(-82.5)" style="fill: url(#Gradiente_sem_nome_14);"/>
            <path d="M267.9,81.33l56.21,7.4c2.37.31,4.04,2.49,3.73,4.86h0c-.31,2.37-2.49,4.04-4.86,3.73l-56.21-7.4c-2.37-.31-4.04-2.49-3.73-4.86h0c.31-2.37,2.49-4.04,4.86-3.73Z" style="fill: none; stroke: url(#Gradiente_sem_nome_15); stroke-miterlimit: 4; stroke-width: .65px;"/>
          </g>
          <g style="opacity: .5;">
            <rect x="299.63" y="92.93" width="8" height="42.02" rx="4" ry="4" transform="translate(151.04 400.11) rotate(-82.5)" style="fill: url(#Gradiente_sem_nome_16);"/>
            <path d="M287.33,107.43l33.73,4.44c2.37.31,4.04,2.49,3.73,4.86h0c-.31,2.37-2.49,4.04-4.86,3.73l-33.73-4.44c-2.37-.31-4.04-2.49-3.73-4.86h0c.31-2.37,2.49-4.04,4.86-3.73Z" style="fill: none; stroke: url(#Gradiente_sem_nome_17); stroke-miterlimit: 4; stroke-width: .65px;"/>
          </g>
          <path d="M223.4,68.52c-4.64-.61-8.81,2.54-10.72,4.29-.57-.75-1.43-1.28-2.43-1.42-1.01-.13-1.98.16-2.72.74-1.39-2.18-4.6-6.3-9.24-6.91-6.27-.83-9.07,3.24-9.56,6.97-.49,3.73,1.16,8.38,7.43,9.2,1.56.21,3.07-.02,4.45-.47-1.11,2.65-2,6.09-2.18,10.44l2.41-1.43,2.26,1.63c.33-7.84,3.22-11.92,4.68-13.49.45.29.96.49,1.52.57.56.07,1.11.01,1.61-.16,1,1.89,2.73,6.58,1.03,14.24l2.61-.98,1.95,2.01c.95-4.25.98-7.81.59-10.65,1.22.8,2.62,1.4,4.18,1.6,6.27.83,9.07-3.24,9.56-6.97.49-3.73-1.16-8.38-7.43-9.21ZM193.36,72.79c.3-2.29,1.76-3.28,4.32-2.94,2.27.3,4.32,2.53,5.53,4.24-1.6,1.33-4.17,2.96-6.44,2.66-2.57-.33-3.71-1.67-3.41-3.96ZM221.88,80.06c-2.27-.3-4.32-2.53-5.53-4.24,1.6-1.33,4.17-2.96,6.44-2.66,2.56.34,3.71,1.67,3.41,3.96-.3,2.29-1.76,3.28-4.32,2.94Z" style="fill: url(#Gradiente_sem_nome_18);"/>
          <path d="M212.61,73.62l-.36-.48c-.49-.64-1.21-1.09-2.07-1.2-.86-.11-1.68.14-2.31.63l-.47.37-.32-.51c-.68-1.06-1.8-2.6-3.3-3.93-1.51-1.33-3.37-2.45-5.55-2.74-3-.39-5.1.38-6.51,1.64-1.43,1.27-2.2,3.09-2.43,4.85-.23,1.77.05,3.72,1.1,5.32,1.04,1.58,2.87,2.87,5.87,3.27,1.47.19,2.89-.01,4.21-.45l1.14-.37-.46,1.1c-.99,2.38-1.82,5.43-2.09,9.24l1.83-1.09,1.74,1.25c.53-7.33,3.29-11.25,4.76-12.84l.31-.34.39.25c.38.25.82.42,1.29.48.48.06.94.01,1.37-.13l.44-.15.22.41c1.01,1.91,2.66,6.41,1.28,13.63l2.01-.76,1.49,1.53c.73-3.75.72-6.9.38-9.46l-.16-1.18,1,.65c1.17.76,2.49,1.33,3.96,1.52,3,.39,5.1-.38,6.51-1.64,1.43-1.27,2.2-3.09,2.43-4.85.23-1.77-.05-3.72-1.1-5.32-1.04-1.58-2.87-2.88-5.86-3.27-2.18-.29-4.27.31-6.07,1.21-1.8.9-3.27,2.09-4.21,2.94l-.44.41ZM216.63,93.1c-.04.21-.09.43-.14.64l-1.95-2.01-2.61.98c.05-.21.09-.43.14-.64,1.47-7.28-.19-11.76-1.16-13.6-.51.17-1.05.23-1.61.16-.56-.07-1.07-.28-1.52-.57-1.41,1.53-4.18,5.43-4.64,12.84-.01.21-.02.43-.03.65l-2.26-1.63-2.41,1.43c0-.22.02-.44.03-.66.22-3.7.96-6.72,1.89-9.13.09-.22.17-.44.26-.65-.22.07-.45.14-.67.2-1.19.31-2.47.44-3.78.27-6.27-.83-7.92-5.47-7.43-9.2.49-3.73,3.29-7.79,9.56-6.97,4.34.57,7.42,4.2,8.94,6.46.11.16.21.31.3.46.14-.11.3-.21.46-.3.66-.37,1.45-.54,2.26-.44.81.11,1.53.47,2.07,1.01.13.13.25.27.36.41.13-.12.26-.24.41-.37,2.05-1.78,5.97-4.49,10.31-3.92,6.27.83,7.92,5.48,7.43,9.21-.49,3.73-3.29,7.79-9.56,6.97-1.31-.17-2.51-.63-3.59-1.24-.2-.12-.4-.24-.6-.36.03.23.06.46.09.7.27,2.57.21,5.68-.54,9.31ZM203.95,74.19l-.39.32c-.82.68-1.9,1.45-3.08,2.02-1.17.56-2.5.94-3.78.77-1.35-.18-2.43-.63-3.12-1.43-.7-.82-.92-1.9-.76-3.14.16-1.25.66-2.23,1.55-2.84.88-.6,2.04-.75,3.39-.58,1.29.17,2.46.87,3.45,1.72.99.85,1.84,1.87,2.46,2.75l.29.41ZM196.77,76.75l-.07.54s0,0,0,0c0,0,0,0,0,0l.07-.54ZM203.21,74.09c-1.6,1.33-4.17,2.96-6.44,2.66-2.57-.33-3.71-1.67-3.41-3.96.3-2.29,1.76-3.28,4.32-2.94,2.27.3,4.32,2.53,5.53,4.24ZM215.61,75.72l.39-.32c.82-.68,1.9-1.45,3.08-2.01,1.17-.56,2.5-.94,3.78-.77,1.35.18,2.43.63,3.12,1.43.7.81.92,1.89.76,3.14-.16,1.25-.66,2.23-1.55,2.84-.88.6-2.03.76-3.39.58-1.29-.17-2.46-.87-3.45-1.72-.99-.85-1.84-1.87-2.46-2.75l-.29-.41ZM216.35,75.82c1.21,1.71,3.26,3.94,5.53,4.24,2.56.34,4.02-.66,4.32-2.94.3-2.29-.85-3.62-3.41-3.96-2.27-.3-4.84,1.34-6.44,2.66Z" style="fill: url(#Gradiente_sem_nome_19); fill-rule: evenodd;"/>
          <path d="M310.45,59.88c-.31.69-.87,1.25-1.57,1.57l-1.88.85,1.88.85c.69.31,1.25.87,1.57,1.57l.85,1.88.85-1.88c.31-.69.87-1.25,1.57-1.57l1.88-.85-1.88-.85c-.69-.31-1.25-.87-1.57-1.57l-.85-1.88-.85,1.88h0Z" style="fill: url(#Gradiente_sem_nome_20);"/>
          <path d="M36,37.17c-.11-.38-.26-.72-.46-1.01-.2-.3-.44-.55-.73-.75-.28-.21-.6-.37-.97-.48-.36-.11-.76-.16-1.2-.16-.82,0-1.54.2-2.16.61-.61.41-1.09,1-1.44,1.77-.34.77-.52,1.71-.52,2.83s.17,2.06.51,2.84c.34.78.82,1.38,1.44,1.79.62.41,1.35.61,2.2.61.77,0,1.42-.14,1.96-.41.55-.28.96-.66,1.25-1.16.29-.5.44-1.09.44-1.77l.69.1h-4.12v-2.55h6.7v2.02c0,1.41-.3,2.61-.89,3.62-.59,1.01-1.41,1.78-2.45,2.33-1.04.54-2.23.81-3.58.81-1.5,0-2.82-.33-3.95-.99-1.14-.67-2.02-1.61-2.66-2.84-.63-1.23-.95-2.69-.95-4.38,0-1.3.19-2.45.56-3.47.38-1.02.91-1.89,1.59-2.59.68-.71,1.48-1.25,2.38-1.62.91-.37,1.89-.55,2.95-.55.91,0,1.75.13,2.53.4.78.26,1.47.63,2.08,1.11.61.48,1.11,1.05,1.49,1.71.39.66.63,1.38.74,2.17h-3.44ZM42.82,48v-12h3.33v12h-3.33ZM44.49,34.45c-.49,0-.92-.16-1.27-.49-.35-.33-.52-.73-.52-1.2s.17-.85.52-1.18c.35-.33.78-.5,1.27-.5s.92.17,1.27.5c.35.33.53.72.53,1.18s-.18.86-.53,1.2c-.35.33-.77.49-1.27.49ZM55.26,36v2.5h-7.41v-2.5h7.41ZM49.55,48v-12.87c0-.87.17-1.59.51-2.16.34-.57.81-1,1.41-1.29.59-.29,1.27-.43,2.02-.43.51,0,.98.04,1.4.12.43.08.74.15.95.21l-.59,2.5c-.13-.04-.29-.08-.48-.12-.19-.04-.38-.05-.58-.05-.49,0-.83.11-1.02.34-.19.22-.29.54-.29.95v12.8h-3.32ZM63.68,36v2.5h-7.23v-2.5h7.23ZM58.09,33.12h3.33v11.19c0,.31.05.55.14.72.09.17.22.28.39.35.17.07.37.1.59.1.16,0,.31-.01.47-.04.16-.03.28-.05.36-.07l.52,2.48c-.17.05-.4.11-.7.18-.3.07-.67.12-1.1.13-.8.03-1.51-.08-2.11-.32-.6-.24-1.07-.62-1.4-1.14-.33-.52-.5-1.17-.49-1.95v-11.62ZM76.36,48.23c-1.23,0-2.29-.26-3.17-.78-.88-.53-1.56-1.26-2.03-2.19-.47-.93-.7-2.01-.7-3.22s.24-2.31.71-3.23c.48-.93,1.16-1.66,2.04-2.18.88-.53,1.93-.79,3.14-.79,1.05,0,1.96.19,2.75.57.79.38,1.41.91,1.87,1.6.46.69.71,1.49.76,2.42h-3.14c-.09-.6-.32-1.08-.7-1.45-.38-.37-.87-.55-1.48-.55-.52,0-.97.14-1.35.42-.38.28-.68.68-.89,1.21-.21.53-.32,1.17-.32,1.93s.1,1.42.31,1.95c.21.54.51.95.9,1.23.39.28.84.42,1.35.42.38,0,.72-.08,1.02-.23.31-.16.56-.38.76-.68.2-.3.34-.66.4-1.09h3.14c-.05.92-.3,1.72-.75,2.42-.44.69-1.05,1.23-1.84,1.62-.78.39-1.71.59-2.77.59ZM87.27,48.23c-.77,0-1.45-.13-2.05-.4-.6-.27-1.07-.67-1.42-1.2-.34-.53-.52-1.19-.52-1.98,0-.67.12-1.23.37-1.68.24-.45.58-.82,1-1.09.42-.28.9-.48,1.44-.62.54-.14,1.11-.24,1.7-.3.7-.07,1.26-.14,1.69-.2.43-.07.74-.17.93-.3.19-.13.29-.32.29-.58v-.05c0-.49-.16-.88-.47-1.15-.31-.27-.74-.41-1.31-.41-.6,0-1.08.13-1.43.4-.35.26-.59.59-.7.98l-3.08-.25c.16-.73.46-1.36.92-1.89.46-.54,1.05-.95,1.77-1.23.73-.29,1.57-.44,2.53-.44.67,0,1.3.08,1.91.23.61.16,1.16.4,1.63.73.48.33.86.75,1.13,1.27.28.51.41,1.12.41,1.84v8.09h-3.16v-1.66h-.09c-.19.38-.45.71-.77.99-.32.28-.71.5-1.16.66-.45.16-.98.23-1.57.23ZM88.23,45.93c.49,0,.92-.1,1.3-.29.38-.2.67-.46.88-.8.21-.33.32-.71.32-1.13v-1.27c-.1.07-.25.13-.43.19-.18.05-.38.1-.6.15-.22.04-.45.08-.67.12-.22.03-.43.06-.61.09-.39.06-.73.15-1.02.27-.29.12-.52.29-.68.51-.16.21-.24.47-.24.78,0,.45.16.8.49,1.04.33.23.76.35,1.27.35ZM96.61,48v-12h3.23v2.09h.12c.22-.74.59-1.31,1.1-1.69.52-.39,1.11-.58,1.78-.58.17,0,.35.01.54.03.19.02.36.05.51.09v2.95c-.16-.05-.37-.09-.65-.12-.28-.04-.53-.05-.76-.05-.49,0-.93.11-1.31.32-.38.21-.68.5-.91.88-.22.38-.33.81-.33,1.3v6.79h-3.33ZM109.48,48.2c-.91,0-1.74-.23-2.48-.7-.73-.47-1.32-1.17-1.75-2.09-.43-.92-.64-2.05-.64-3.39s.22-2.52.66-3.43c.44-.92,1.03-1.6,1.77-2.05.74-.46,1.55-.69,2.43-.69.67,0,1.23.11,1.68.34.45.22.82.51,1.09.84.28.33.49.66.64.98h.1v-6.02h3.32v16h-3.28v-1.92h-.14c-.16.33-.38.66-.66.99-.28.32-.65.59-1.1.8-.45.21-1,.32-1.64.32ZM110.53,45.55c.54,0,.99-.15,1.36-.44.38-.3.66-.71.86-1.24.2-.53.31-1.15.31-1.87s-.1-1.33-.3-1.86c-.2-.53-.49-.93-.86-1.22-.38-.29-.83-.43-1.37-.43s-1.01.15-1.38.45-.66.71-.85,1.23c-.19.53-.29,1.14-.29,1.83s.1,1.32.29,1.85c.2.53.48.95.85,1.25.38.3.84.45,1.38.45ZM129.03,39.42l-3.05.19c-.05-.26-.16-.49-.34-.7-.17-.21-.4-.38-.68-.51-.28-.13-.61-.2-.99-.2-.52,0-.95.11-1.31.33-.35.21-.53.5-.53.86,0,.29.11.53.34.73.23.2.62.36,1.18.48l2.17.44c1.17.24,2.04.62,2.61,1.16.57.53.86,1.23.86,2.09,0,.79-.23,1.48-.7,2.07-.46.59-1.09,1.06-1.89,1.39-.8.33-1.72.49-2.76.49-1.59,0-2.85-.33-3.8-.99-.94-.67-1.49-1.57-1.65-2.72l3.27-.17c.1.48.34.85.72,1.11.38.25.87.38,1.46.38s1.05-.11,1.41-.34c.36-.23.54-.52.55-.88,0-.3-.13-.55-.38-.74-.25-.2-.64-.35-1.16-.45l-2.08-.41c-1.17-.23-2.04-.64-2.62-1.22-.57-.58-.85-1.32-.85-2.21,0-.77.21-1.43.62-1.99.42-.56,1.01-.99,1.77-1.29.77-.3,1.66-.45,2.69-.45,1.52,0,2.71.32,3.58.96.88.64,1.39,1.51,1.53,2.62Z" style="fill: #fff;"/>
          <path d="M117,68H24" style="fill: none; stroke: #fefefe;"/>
          <g>
            <path d="M76.36,112.93c-.19,0-.36-.07-.5-.21-.14-.14-.21-.31-.21-.5s.07-.36.21-.5c.14-.14.31-.21.5-.21s.36.07.5.21c.14.14.21.3.21.5,0,.13-.03.25-.1.36-.06.11-.15.19-.26.26-.11.06-.22.1-.36.1Z" style="fill: #fefefe;"/>
            <path d="M74.29,108.74l-.96.17c-.04-.12-.1-.24-.19-.35-.09-.11-.2-.2-.35-.27-.15-.07-.33-.11-.55-.11-.3,0-.55.07-.75.2-.2.13-.3.3-.3.51,0,.18.07.33.2.44.13.11.35.2.65.27l.87.2c.5.12.88.29,1.12.54.25.24.37.56.37.94,0,.33-.09.62-.28.87-.19.25-.45.45-.78.6-.33.14-.72.22-1.16.22-.61,0-1.11-.13-1.5-.39-.39-.26-.62-.64-.71-1.12l1.03-.16c.06.27.2.47.39.61.2.13.46.2.78.2.35,0,.63-.07.83-.22.21-.15.31-.33.31-.54,0-.17-.06-.31-.19-.43-.13-.12-.32-.2-.58-.26l-.92-.2c-.51-.12-.89-.3-1.13-.55-.24-.25-.36-.57-.36-.96,0-.32.09-.6.27-.85.18-.24.43-.43.75-.56.32-.14.68-.21,1.09-.21.59,0,1.05.13,1.39.38.34.25.56.59.67,1.02Z" style="fill: #fefefe;"/>
            <path d="M66.37,112.97c-.51,0-.96-.12-1.34-.35-.38-.23-.68-.56-.89-.98s-.32-.91-.32-1.48.11-1.06.32-1.48c.21-.42.51-.75.89-.99.38-.23.83-.35,1.34-.35s.96.12,1.34.35c.38.23.68.56.89.99.21.42.32.92.32,1.48s-.11,1.06-.32,1.48-.51.75-.89.98c-.38.23-.83.35-1.34.35ZM66.38,112.08c.33,0,.61-.09.82-.26.22-.18.38-.41.48-.7.11-.29.16-.61.16-.96s-.05-.67-.16-.96c-.1-.29-.27-.53-.48-.71-.22-.18-.49-.27-.82-.27s-.61.09-.83.27c-.22.18-.38.41-.49.71-.1.29-.16.61-.16.96s.05.67.16.96c.11.29.27.52.49.7.22.18.5.26.83.26Z" style="fill: #fefefe;"/>
            <path d="M63.16,107.41l-1.98,5.45h-1.14l-1.98-5.45h1.14l1.38,4.2h.06l1.38-4.2h1.14Z" style="fill: #fefefe;"/>
            <path d="M55.91,112.86v-5.45h1.06v5.45h-1.06ZM56.44,106.57c-.18,0-.34-.06-.48-.18-.13-.13-.2-.27-.2-.45s.07-.32.2-.45c.13-.13.29-.19.48-.19s.34.06.47.19c.13.12.2.27.2.45s-.07.32-.2.45c-.13.12-.29.18-.47.18Z" style="fill: #fefefe;"/>
            <path d="M54.53,108.74l-.96.17c-.04-.12-.1-.24-.19-.35-.09-.11-.2-.2-.35-.27-.15-.07-.33-.11-.55-.11-.3,0-.55.07-.75.2-.2.13-.3.3-.3.51,0,.18.07.33.2.44.13.11.35.2.65.27l.87.2c.5.12.88.29,1.12.54.25.24.37.56.37.94,0,.33-.09.62-.28.87-.19.25-.45.45-.78.6-.33.14-.72.22-1.16.22-.61,0-1.11-.13-1.49-.39-.39-.26-.62-.64-.71-1.12l1.03-.16c.06.27.2.47.39.61.2.13.46.2.78.2.35,0,.63-.07.83-.22.21-.15.31-.33.31-.54,0-.17-.06-.31-.19-.43-.13-.12-.32-.2-.58-.26l-.92-.2c-.51-.12-.89-.3-1.13-.55-.24-.25-.36-.57-.36-.96,0-.32.09-.6.27-.85.18-.24.43-.43.75-.56.32-.14.68-.21,1.09-.21.59,0,1.05.13,1.39.38.34.25.56.59.67,1.02Z" style="fill: #fefefe;"/>
            <path d="M47.85,110.6v-3.19h1.07v5.45h-1.04v-.94h-.06c-.13.29-.33.53-.6.73-.27.19-.62.29-1.03.29-.35,0-.66-.08-.93-.23-.27-.16-.48-.39-.63-.69-.15-.31-.23-.68-.23-1.13v-3.47h1.06v3.34c0,.37.1.67.31.89.21.22.47.33.8.33.2,0,.4-.05.59-.15.2-.1.36-.25.49-.45.13-.2.2-.46.2-.77Z" style="fill: #fefefe;"/>
            <path d="M42.87,105.59v7.27h-1.06v-7.27h1.06Z" style="fill: #fefefe;"/>
            <path d="M38.33,112.97c-.53,0-.98-.12-1.36-.36-.38-.24-.67-.57-.87-1-.2-.42-.31-.91-.31-1.46s.1-1.04.31-1.47c.21-.43.5-.76.88-1,.38-.24.83-.36,1.34-.36.41,0,.78.08,1.11.23.32.15.59.36.78.64.2.27.32.6.36.96h-1.03c-.06-.26-.19-.48-.39-.66-.2-.18-.47-.28-.81-.28-.3,0-.56.08-.78.23-.22.15-.39.37-.51.66-.12.28-.18.62-.18,1.01s.06.74.18,1.03c.12.29.29.51.51.67.22.16.48.24.78.24.2,0,.38-.04.55-.11.17-.08.3-.18.42-.32.11-.14.19-.31.24-.5h1.03c-.04.35-.15.67-.34.94-.19.28-.45.49-.77.65-.32.16-.69.24-1.13.24Z" style="fill: #fefefe;"/>
            <path d="M31.48,107.41l1.2,2.12,1.21-2.12h1.16l-1.7,2.73,1.72,2.73h-1.16l-1.23-2.04-1.23,2.04h-1.16l1.7-2.73-1.68-2.73h1.16Z" style="fill: #fefefe;"/>
            <path d="M27.15,112.97c-.54,0-1-.11-1.39-.34-.39-.23-.68-.56-.89-.98-.21-.42-.31-.92-.31-1.48s.1-1.05.31-1.48c.21-.43.5-.76.88-1,.38-.24.82-.36,1.33-.36.31,0,.61.05.89.15.29.1.55.26.78.48s.41.5.54.85c.13.35.2.77.2,1.26v.38h-4.34v-.8h3.3c0-.28-.06-.53-.17-.74-.11-.22-.27-.39-.48-.51-.2-.13-.44-.19-.72-.19-.3,0-.56.07-.78.22-.22.14-.39.33-.51.57-.12.23-.18.48-.18.76v.62c0,.36.06.67.19.93.13.26.31.45.54.59.23.13.5.2.81.2.2,0,.38-.03.55-.09.17-.06.31-.15.43-.26.12-.12.21-.26.28-.43l1,.18c-.08.3-.22.56-.43.78-.21.22-.47.39-.78.51-.31.12-.66.18-1.06.18Z" style="fill: #fefefe;"/>
            <path d="M110.49,92.74l-.96.17c-.04-.12-.1-.24-.19-.35-.09-.11-.2-.2-.35-.27-.15-.07-.33-.11-.55-.11-.3,0-.55.07-.75.2-.2.13-.3.3-.3.51,0,.18.07.33.2.44.14.11.35.2.65.27l.87.2c.5.12.88.29,1.12.54.25.24.37.56.37.94,0,.33-.09.62-.28.87-.19.25-.45.45-.78.6-.33.14-.72.22-1.16.22-.61,0-1.11-.13-1.5-.39-.39-.26-.62-.64-.71-1.12l1.03-.16c.06.27.2.47.39.61.2.13.46.2.78.2.35,0,.63-.07.83-.22.21-.15.31-.33.31-.54,0-.17-.06-.31-.19-.43-.13-.12-.32-.2-.58-.26l-.92-.2c-.51-.12-.89-.3-1.13-.55-.24-.25-.36-.57-.36-.96,0-.32.09-.6.27-.85.18-.24.43-.43.75-.56.32-.14.68-.21,1.09-.21.59,0,1.05.13,1.39.38.34.25.56.59.67,1.02Z" style="fill: #fefefe;"/>
            <path d="M102.08,96.97c-.44,0-.83-.11-1.18-.34-.34-.23-.61-.55-.81-.97-.19-.42-.29-.93-.29-1.52s.1-1.09.29-1.51c.2-.42.47-.74.82-.96.35-.22.74-.33,1.18-.33.34,0,.61.06.82.17.21.11.37.24.48.39.12.15.21.28.27.39h.06v-2.7h1.06v7.27h-1.04v-.85h-.09c-.06.12-.16.25-.28.4-.12.15-.28.28-.49.39-.21.11-.48.17-.81.17ZM102.32,96.06c.31,0,.56-.08.77-.24.21-.16.37-.39.48-.68.11-.29.17-.62.17-1.01s-.05-.71-.16-.99-.27-.51-.48-.66c-.21-.16-.47-.24-.78-.24s-.59.08-.8.25c-.21.17-.37.39-.48.68-.11.29-.16.61-.16.97s.05.69.16.98c.11.29.27.52.48.69.22.17.48.25.8.25Z" style="fill: #fefefe;"/>
            <path d="M96.3,96.86v-5.45h1.03v.87h.06c.1-.29.27-.52.53-.69.25-.17.54-.26.86-.26.07,0,.14,0,.23,0,.09,0,.16.01.22.02v1.02s-.12-.02-.23-.04c-.11-.02-.22-.02-.33-.02-.25,0-.47.05-.67.16-.19.1-.35.25-.46.44-.11.18-.17.4-.17.63v3.33h-1.06Z" style="fill: #fefefe;"/>
            <path d="M92.09,96.98c-.35,0-.66-.06-.94-.19-.28-.13-.5-.32-.66-.56-.16-.25-.24-.55-.24-.91,0-.31.06-.56.18-.76.12-.2.28-.36.48-.47.2-.12.43-.2.67-.26.25-.06.5-.1.76-.13.33-.04.59-.07.8-.09.2-.03.35-.07.44-.12.09-.06.14-.15.14-.28v-.02c0-.31-.09-.55-.26-.72-.17-.17-.43-.26-.77-.26s-.64.08-.85.24c-.2.16-.34.33-.42.52l-1-.23c.12-.33.29-.6.52-.8.23-.21.49-.36.79-.45.3-.09.61-.14.94-.14.22,0,.45.03.69.08.25.05.48.14.69.28.22.13.39.33.53.58.14.25.21.57.21.97v3.62h-1.04v-.75h-.04c-.07.14-.17.27-.31.4-.14.13-.31.24-.53.33-.22.09-.47.13-.77.13ZM92.32,96.13c.29,0,.54-.06.75-.17.21-.12.37-.27.48-.45.11-.19.17-.39.17-.61v-.7s-.11.07-.22.11c-.11.03-.23.06-.37.08-.14.02-.27.04-.4.06-.13.02-.24.03-.33.04-.21.03-.39.07-.56.13-.17.06-.3.15-.4.27-.1.11-.15.27-.15.45,0,.26.1.46.29.6.19.13.44.2.74.2Z" style="fill: #fefefe;"/>
            <path d="M87.03,96.97c-.53,0-.98-.12-1.36-.36-.38-.24-.67-.57-.87-1-.2-.42-.31-.91-.31-1.46s.1-1.04.31-1.47c.21-.43.5-.76.88-1s.83-.36,1.34-.36c.41,0,.78.08,1.11.23.32.15.59.36.78.64.2.27.32.6.36.96h-1.03c-.06-.26-.19-.48-.39-.66-.2-.18-.47-.28-.81-.28-.3,0-.56.08-.78.23-.22.15-.39.37-.51.66-.12.28-.18.62-.18,1.01s.06.74.18,1.03c.12.29.29.51.51.67.22.16.48.24.78.24.2,0,.38-.04.55-.11.17-.08.3-.18.42-.32.11-.14.19-.31.24-.5h1.03c-.04.35-.15.67-.34.94-.19.28-.45.49-.77.65-.32.16-.69.24-1.13.24Z" style="fill: #fefefe;"/>
            <path d="M80.71,91.41v.85h-2.98v-.85h2.98ZM78.53,90.1h1.06v5.16c0,.21.03.36.09.47.06.1.14.17.24.21.1.04.21.05.32.05.09,0,.16,0,.22-.02.06-.01.11-.02.15-.03l.19.88c-.06.02-.15.05-.26.07-.11.03-.26.04-.43.04-.28,0-.54-.04-.78-.15-.24-.1-.44-.27-.59-.48-.15-.22-.22-.49-.22-.82v-5.38Z" style="fill: #fefefe;"/>
            <path d="M76.97,91.41v.85h-3.08v-.85h3.08ZM74.73,96.86v-6.09c0-.34.07-.62.22-.85.15-.23.35-.4.59-.51.25-.11.51-.17.8-.17.21,0,.4.02.55.05.15.03.26.06.34.09l-.25.86s-.11-.03-.19-.05c-.08-.02-.17-.03-.28-.03-.26,0-.44.06-.55.19-.11.13-.16.31-.16.55v5.94h-1.06Z" style="fill: #fefefe;"/>
            <path d="M71.78,96.86v-5.45h1.06v5.45h-1.06ZM72.32,90.57c-.18,0-.34-.06-.48-.18-.13-.13-.2-.27-.2-.45s.07-.32.2-.45c.13-.13.29-.19.48-.19s.34.06.47.19c.13.12.2.27.2.45s-.07.32-.2.45c-.13.12-.29.18-.47.18Z" style="fill: #fefefe;"/>
            <path d="M67.8,99.02c-.43,0-.81-.06-1.12-.17-.31-.11-.56-.26-.76-.45-.2-.19-.34-.39-.44-.61l.91-.38c.06.1.15.21.26.33.11.12.26.22.44.3.19.08.43.12.72.12.4,0,.74-.1,1-.29.26-.19.39-.5.39-.93v-1.07h-.07c-.06.12-.16.25-.28.39-.12.14-.28.27-.49.37-.21.1-.48.16-.81.16-.43,0-.82-.1-1.16-.3-.34-.2-.62-.5-.82-.9-.2-.4-.3-.89-.3-1.47s.1-1.08.29-1.49c.2-.41.47-.73.82-.95.35-.22.74-.33,1.18-.33.34,0,.61.06.82.17.21.11.37.24.49.39.12.15.21.28.27.39h.08v-.88h1.04v5.58c0,.47-.11.85-.33,1.15s-.51.52-.88.67c-.37.14-.79.22-1.25.22ZM67.79,95.9c.31,0,.56-.07.77-.21.21-.14.37-.35.48-.62.11-.27.17-.59.17-.97s-.05-.69-.16-.97c-.11-.28-.27-.5-.48-.65-.21-.16-.47-.24-.78-.24s-.59.08-.8.25c-.21.16-.37.39-.48.67-.11.28-.16.6-.16.94s.05.67.16.94c.11.27.27.48.48.63.22.15.48.23.8.23Z" style="fill: #fefefe;"/>
            <path d="M58.56,96.98c-.35,0-.66-.06-.94-.19-.28-.13-.5-.32-.66-.56-.16-.25-.24-.55-.24-.91,0-.31.06-.56.18-.76.12-.2.28-.36.48-.47.2-.12.43-.2.67-.26.25-.06.5-.1.76-.13.33-.04.59-.07.8-.09.2-.03.35-.07.44-.12.09-.06.14-.15.14-.28v-.02c0-.31-.09-.55-.26-.72-.17-.17-.43-.26-.77-.26s-.64.08-.85.24c-.2.16-.34.33-.42.52l-1-.23c.12-.33.29-.6.52-.8.23-.21.49-.36.79-.45.3-.09.61-.14.94-.14.22,0,.45.03.69.08.25.05.48.14.69.28.22.13.39.33.53.58.14.25.21.57.21.97v3.62h-1.04v-.75h-.04c-.07.14-.17.27-.31.4-.14.13-.31.24-.53.33-.22.09-.47.13-.77.13ZM58.79,96.13c.29,0,.54-.06.75-.17.21-.12.37-.27.48-.45.11-.19.17-.39.17-.61v-.7s-.11.07-.22.11c-.11.03-.23.06-.37.08-.14.02-.27.04-.4.06-.13.02-.24.03-.33.04-.21.03-.39.07-.56.13-.17.06-.3.15-.4.27-.1.11-.15.27-.15.45,0,.26.1.46.29.6.19.13.44.2.74.2Z" style="fill: #fefefe;"/>
            <path d="M53.08,96.86v-5.45h1.03v.87h.06c.1-.29.27-.52.53-.69.25-.17.54-.26.86-.26.07,0,.14,0,.23,0,.09,0,.16.01.22.02v1.02s-.12-.02-.23-.04c-.11-.02-.22-.02-.33-.02-.25,0-.47.05-.67.16-.19.1-.35.25-.46.44-.11.18-.17.4-.17.63v3.33h-1.06Z" style="fill: #fefefe;"/>
            <path d="M50.49,96.86v-5.45h1.06v5.45h-1.06ZM51.03,90.57c-.18,0-.34-.06-.48-.18-.13-.13-.2-.27-.2-.45s.07-.32.2-.45c.13-.13.29-.19.48-.19s.34.06.47.19c.13.12.2.27.2.45s-.07.32-.2.45c-.13.12-.29.18-.47.18Z" style="fill: #fefefe;"/>
            <path d="M47.9,94.6v-3.19h1.07v5.45h-1.04v-.94h-.06c-.13.29-.33.53-.6.73-.27.19-.62.29-1.03.29-.35,0-.66-.08-.93-.23-.27-.16-.48-.39-.63-.69-.15-.31-.23-.68-.23-1.13v-3.47h1.06v3.34c0,.37.1.67.31.89.21.22.47.33.8.33.2,0,.4-.05.59-.15.2-.1.36-.25.49-.45.13-.2.2-.46.2-.77Z" style="fill: #fefefe;"/>
            <path d="M41.86,98.91v-2.89h-.06c-.06.12-.16.25-.28.4-.12.15-.28.28-.49.39-.21.11-.48.17-.81.17-.44,0-.83-.11-1.18-.34-.34-.23-.61-.55-.81-.97-.19-.42-.29-.93-.29-1.52s.1-1.09.29-1.51c.2-.42.47-.74.82-.96.35-.22.74-.33,1.18-.33.34,0,.61.06.82.17.21.11.37.24.48.39.12.15.21.28.27.39h.09v-.88h1.04v7.5h-1.06ZM40.45,96.06c.31,0,.56-.08.77-.24.21-.16.37-.39.48-.68.11-.29.17-.62.17-1.01s-.05-.71-.16-.99-.27-.51-.48-.66c-.21-.16-.47-.24-.78-.24s-.59.08-.8.25c-.21.17-.37.39-.48.68-.11.29-.16.61-.16.97s.05.69.16.98c.11.29.27.52.48.69.22.17.48.25.8.25Z" style="fill: #fefefe;"/>
            <path d="M33.85,96.97c-.44,0-.83-.11-1.18-.34-.34-.23-.61-.55-.81-.97-.19-.42-.29-.93-.29-1.52s.1-1.09.29-1.51c.2-.42.47-.74.82-.96.35-.22.74-.33,1.18-.33.34,0,.61.06.82.17.21.11.37.24.48.39.12.15.21.28.27.39h.06v-2.7h1.06v7.27h-1.04v-.85h-.09c-.06.12-.16.25-.28.4-.12.15-.28.28-.49.39-.21.11-.48.17-.81.17ZM34.09,96.06c.31,0,.56-.08.77-.24.21-.16.37-.39.48-.68.11-.29.17-.62.17-1.01s-.05-.71-.16-.99-.27-.51-.48-.66c-.21-.16-.47-.24-.78-.24s-.59.08-.8.25-.37.39-.48.68c-.11.29-.16.61-.16.97s.05.69.16.98c.11.29.27.52.48.69.22.17.48.25.8.25Z" style="fill: #fefefe;"/>
            <path d="M25.5,96.86h-1.16l2.62-7.27h1.27l2.62,7.27h-1.16l-2.06-5.95h-.06l-2.06,5.95ZM25.7,94.01h3.78v.92h-3.78v-.92Z" style="fill: #fefefe;"/>
          </g>
        </g>
      </g>
      <rect x=".5" y=".5" width="327" height="159" rx="15.5" ry="15.5" style="fill: none; stroke: #e4e7ec;"/>
    </g>
  </g>
</svg>