# frozen_string_literal: true

class Webhook::Asaas::Events::PaymentApprove < Webhook::Asaas::Events::Base
  EVENTS = %w[
    PAYMENT_AUTHORIZED
    PAYMENT_APPROVED_BY_RISK_ANALYSIS
    PAYMENT_RECEIVED
    PAYMENT_ANTICIPATED
    PAYMENT_CONFIRMED
    SUBSCRIPTION_CREATED
  ]

  def action
    customer_id = params.dig("payment", "customer") || params.dig("subscription", "customer")
    customer = customer_data(customer_id)
    Webhook::Actions::CreateAuthorizedUser.new(
      business:,
      cpf: customer[:cpf],
      name: customer[:name],
      phone: customer[:phone],
      email: customer[:email]
    )
  end
end
