# frozen_string_literal: true

class Webhook::Asaas::Events::Base
  ALL_EVENTS = %w[
    PAYMENT_CREATED
    PAYMENT_AWAITING_RISK_ANALYSIS
    PAYMENT_APPROVED_BY_RISK_ANALYSIS
    PAYMENT_REPROVED_BY_RISK_ANALYSIS
    PAYMENT_AUTHORIZED
    PAYMENT_UPDATED
    PAYMENT_CONFIRMED
    PAYMENT_RECEIVED
    PAYMENT_CREDIT_CARD_CAPTURE_REFUSED
    PAYMENT_ANTICIPATED
    PAYMENT_OVERDUE
    PAYMENT_DELETED
    PAYMENT_RESTORED
    PAYMENT_REFUNDED
    PAYMENT_PARTIALLY_REFUNDED
    PAYMENT_REFUND_IN_PROGRESS
    PAYMENT_RECEIVED_IN_CASH_UNDONE
    PAYMENT_CHARGEBACK_REQUESTED
    PAYMENT_CHARGEBACK_DISPUTE
    PAYMENT_AWAITING_CHARGEBACK_REVERSAL
    PAYMENT_DUNNING_RECEIVED
    PAYMENT_DUNNING_REQUESTED
    PAYMENT_BANK_SLIP_VIEWED
    PAYMENT_CHECKOUT_VIEWED
    PAYMENT_SPLIT_DIVERGENCE_BLOCK
    PAYMENT_SPLIT_DIVERGENCE_BLOCK_FINISHED
    INVOICE_CREATED
  ]

  def initialize(business:, params:)
    @business = business
    @params = params
  end

  def action
    raise NotImplementedError
  end

  protected

  attr_reader :business, :params

  def customer_data(customer_id)
    customer_response = request_customer_data(customer_id)
    return {} if customer_response.nil?
    {
      cpf: customer_response["cpfCnpj"],
      email: customer_response["email"],
      phone: customer_response["mobilePhone"],
      name: customer_response["name"]
    }.with_indifferent_access
  end

  def request_customer_data(customer_id)
    api_key = business.webhook_client.api_key
    response = Faraday.get("#{Rails.application.credentials.asaas_url}/v3/customers/#{customer_id}") do |req|
      req.headers["Content-Type"] = "application/json; charset=utf-8"
      req.headers["access-token"] = api_key
    end

    (response.status == 200) ? JSON.parse(response.body) : nil
  rescue Faraday::ConnectionFailed => e
    raise HttpError, e.message
  rescue Faraday::TimeoutError => e
    raise HttpError, e.message
  end
end
