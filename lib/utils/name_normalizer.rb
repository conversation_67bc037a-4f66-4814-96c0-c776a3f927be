module Utils
  class NameNormalizer
    def self.call(name)
      return if name.blank?

      name_particles = %w[
        de do da dos das d' no na nos nas
        e
        van vander vanden der
        von
        di del della dello
        du des
        le la
        y
      ]

      name.to_s
        .gsub(/\p{Emoji_Presentation}/, "")
        .strip
        .gsub(/\s+/, " ")
        .downcase
        .split
        .each_with_index
        .map do |word, index|
          if word.start_with?("d'")
            "d'#{word[2..].capitalize}"
          elsif index.positive? && name_particles.include?(word)
            word
          else
            word.capitalize
          end
        end
        .join(" ")
    end
  end
end
