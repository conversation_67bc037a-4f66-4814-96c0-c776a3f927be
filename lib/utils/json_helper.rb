module Utils
  module <PERSON><PERSON><PERSON><PERSON><PERSON>
    def self.stringify_and_remove_nils(obj)
      case obj
      when Hash
        obj.each_with_object({}) do |(k, v), result|
          processed_v = stringify_and_remove_nils(v)
          result[k] = processed_v unless processed_v.nil?
        end
      when Array
        obj.filter_map { |item| stringify_and_remove_nils(item) }
      else
        obj&.to_s
      end
    end
  end
end
