# frozen_string_literal: true

module CustomSso
  module Multimarcas
    class Auth
      def initialize(params)
        @params = ActiveSupport::HashWithIndifferentAccess.new(params.slice(:access_token, :cpf, :name, :email, :password, :cellphone, :business))
        @new_record = false
      end

      def call
        return CustomSso::Response.new(success: false, message: "Autenticação mal-sucedida", user: nil) if user_info.blank? || !user_info[:status]

        user = find_user || create_user

        CustomSso::Response.new(success: true, message: nil, user:, new_record:)
      end

      private

      attr_reader :params, :new_record

      def find_user
        return if user_info[:cpf].blank?

        User.where(main_business_id: business.project_ids)
          .where(cpf: user_info[:cpf])
          .first
      end

      def create_user
        user = User.find_or_initialize_by(main_business_id: business.all_project_ids, cpf: serialized_params[:cpf])
        user.assign_attributes(serialized_params)
        user.validate

        if user.errors.reject { _1.type == :blank && _1.attribute == :authorized_user }.blank?
          create_authorization(user) if user.authorized_user_id.nil?
          @new_record = true if user.new_record?
          user.save!
        end

        user
      end

      def user_info
        @user_info ||= CustomSso::Multimarcas::Decode.decode(access_token: params[:access_token])&.with_indifferent_access
      end

      def serialized_params
        {
          cpf: params[:cpf] || user_info[:cpf],
          name: params[:name] || user_info[:name],
          email: params[:email] || user_info[:email],
          cellphone: params[:cellphone],
          password: dummy_password,
          business_id: business.id
        }.compact
      end

      def dummy_password
        SecureRandom.hex(16)
      end

      def create_authorization(user)
        authorized_user = AuthorizedUser.find_or_initialize_by(cpf: user.cpf, business_id: user.business.all_project_ids)
        authorized_user.update!(name: user.name, email: user.email, phone: user.cellphone, business_id: user.business_id)
        user.authorized_user = authorized_user
      end

      def business
        @business ||= Business.find_by(cnpj: "04124922000162")
      end
    end
  end
end
