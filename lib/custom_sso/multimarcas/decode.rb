class CustomSso::Multimarcas::Decode
  def self.decode(access_token:)
    payload = JWT.decode(access_token, Rails.application.credentials.multimarcas_client_secret, true)&.dig(0)

    return {} if payload.blank?

    name = payload["name"].presence
    email = payload["email"].presence
    status = payload["status"].presence
    cpf = CPF.new(payload["cpf"]).valid? ? CPF.new(payload["cpf"]).stripped : nil

    {name:, email:, cpf:, status:}.compact
  rescue JWT::DecodeError
    {}
  end
end
