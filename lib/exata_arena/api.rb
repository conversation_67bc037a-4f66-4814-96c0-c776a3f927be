# frozen_string_literal: true

class ExataArena::Api
  BASE_URL = "https://iarena.exata.it"
  AUTH_ENDPOINT = "/api/Token/Auth"
  SELLOUT_ENDPOINT = "/api/Arena/Sellout"

  EMAIL_SECRET = Rails.application.credentials.exata_arena_email
  PASSWORD_SECRET = Rails.application.credentials.exata_arena_password

  def initialize
    @email = EMAIL_SECRET
    @password = PASSWORD_SECRET
    @token = nil
  end

  def authenticate
    uri = URI("#{BASE_URL}#{AUTH_ENDPOINT}")
    response = Faraday.new(url: uri).post do |req|
      req.headers["Content-Type"] = "application/json; charset=utf-8"
      req.body = {Email: @email, Password: @password}.to_json
    end

    if response.status.to_i == 200
      body = JSON.parse(response.body)
      @token = body["token"]
    else
      raise "Exata Arena - Falha na autenticação: #{response.body}"
    end
  end

  def fetch_sellouts(start_date, end_date, page_number = 1, page_records = 100)
    authenticate if @token.nil?

    uri = URI("#{BASE_URL}#{SELLOUT_ENDPOINT}/#{start_date}&#{end_date}?pageNumber=#{page_number}&pageRecords=#{page_records}")
    response = Faraday.new(url: uri) do |req|
      req.headers["Content-Type"] = "application/json; charset=utf-8"
      req.headers["Authorization"] = "Bearer #{@token}"
    end.get

    if response.status.to_i == 200
      Rails.logger.debug response.body
      JSON.parse(response.body)
    else
      raise "Exata Arena - Erro ao buscar sellouts: #{response.body}"
    end
  end
end
