# frozen_string_literal: true

class ExataArena::SupabaseClient
  BASE_URL = "https://qtavmyhwbzbryzzavzrp.supabase.co"
  TABLE_NAME = "exata_sellouts"
  API_KEY = Rails.application.credentials.supabase_api_key

  def initialize
    @conn = Faraday.new(url: BASE_URL) do |faraday|
      faraday.headers["Content-Type"] = "application/json"
      faraday.headers["apikey"] = API_KEY
      faraday.headers["Authorization"] = "Bearer #{API_KEY}"
      faraday.headers["Prefer"] = "resolution=merge-duplicates"
      faraday.response :json, parser_options: {symbolize_names: true}
      faraday.adapter Faraday.default_adapter
    end
  end

  def insert_sellout(data)
    response = @conn.post("/rest/v1/#{TABLE_NAME}") do |req|
      req.body = data.to_json
    end

    if response.status == 201
      Rails.logger.debug "Sellout salvo com sucesso!"
    else
      Rails.logger.debug { "Erro ao salvar sellout: #{response.body}" }
    end
  end
end
