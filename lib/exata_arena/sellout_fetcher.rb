# frozen_string_literal: true

class ExataArena::SelloutFetcher
  def initialize
    @api_client = ExataArena::Api.new
    @supabase_client = ExataArena::SupabaseClient.new
  end

  def fetch_and_save(start_date, end_date)
    page = 1
    loop do
      response = @api_client.fetch_sellouts(start_date, end_date, page)
      sellouts = response["records"]

      sellouts.each do |sellout_data|
        @supabase_client.insert_sellout(sellout_sanitized_params(sellout_data))
      end

      break if sellouts.empty?
      page += 1
    end
  end

  def sellout_sanitized_params(sellout_data)
    {
      cnpj: sellout_data["Cnpj"],
      uf: sellout_data["Uf"],
      cnpj_cpf_cliente: sellout_data["Cnpj_cpf_cliente"],
      razao_social_cliente: sellout_data["Razao_social_cliente"],
      sellout: sellout_data["Sellout"],
      municipio: sellout_data["Municipio"],
      segmento: sellout_data["Segmento"],
      canal: sellout_data["Canal"],
      nome_marca: sellout_data["Nome_marca"],
      id_premium: sellout_data["Id_premium"],
      ano: sellout_data["Ano"],
      mes: sellout_data["Mes"],
      grupo: sellout_data["Grupo"],
      nome_classe_produto: sellout_data["Nome_classe_produto"],
      nome_produto: sellout_data["Nome_produto"],
      sku_iconic: sellout_data["Sku_iconic"],
      cod_municipio_ibge_matriz: sellout_data["Cod_municipio_ibge_matriz"]

    }
  end
end
