# frozen_string_literal: true

class ExataArena::Workers::SelloutWorker
  include Sidekiq::Worker
  sidekiq_options queue: "low"

  def perform
    return unless Rails.env.production?

    start_date = Time.current.last_month.at_beginning_of_month.strftime("%Y-%m")
    end_date = Time.current.next_month.at_beginning_of_month.strftime("%Y-%m")
    sellout_fetcher = ExataArena::SelloutFetcher.new
    sellout_fetcher.fetch_and_save(start_date, end_date)
  end
end
