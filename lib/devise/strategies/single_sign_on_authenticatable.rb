require "devise/strategies/authenticatable"

module Devise
  module Strategies
    class SingleSignOnAuthenticatable < Authenticatable
      def valid?
        access_token = params&.dig(:user, :access_token) || params&.dig(:access_token)
        access_token.present?
      end

      def authenticate!
        @business = Business.current(request.headers)
        unless @business
          return fail!(I18n.t("devise.failure.invalid_business_headers"))
        end

        if custom_sso.success?
          if custom_sso.user.persisted?
            call_user_hook(custom_sso.user) if custom_sso.new_record
            custom_sso.user.create_wallet! if custom_sso.new_record && @business.cashback?

            success!(custom_sso.user)
          else
            custom!([422, {"Content-Type" => "application/json"}, StringIO.new(sso_user_attributes(custom_sso.user).to_json)])
          end
        else
          fail!(I18n.t("devise.failure.sso_error"))
        end
      end

      private

      def custom_sso
        @custom_sso ||=
          CustomSso::Auth.new(business: @business, params: sign_up_params).sign_up
      end

      def sign_up_params
        {
          cpf: params.dig(:user, :cpf) || params.dig(:cpf),
          name: params.dig(:user, :name) || params.dig(:name),
          access_token: params.dig(:user, :access_token) || params.dig(:access_token),
          email: params.dig(:user, :email) || params.dig(:email),
          cellphone: params.dig(:user, :cellphone) || params.dig(:cellphone),
          metadata: params.dig(:user, :metadata) || params.dig(:metadata)
        }
      end

      def sso_user_attributes(sso_user)
        sso_user.attributes
          .merge("taxpayer_number" => sso_user["cpf"])
          .slice("cpf", "taxpayer_number", "name", "email", "cellphone")
          .transform_values!(&:presence)
      end

      def call_user_hook(user)
        WebhookSender::EventWorker.perform_async(WebhookSender::EventTypes::USER_CREATED, user.id)
      end
    end
  end
end
