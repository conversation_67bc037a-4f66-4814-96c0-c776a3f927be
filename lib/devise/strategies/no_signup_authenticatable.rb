require "devise/strategies/authenticatable"

module Devise
  module Strategies
    class NoSignupAuthenticatable < Authenticatable
      attr_reader :new_record, :user, :authorized_user

      def valid?
        params["controller"] == "api/v2/users/sessions" && params["action"] == "create" &&
          current_business&.project_config&.user_manager == Enums::UserManager::NO_SIGNUP &&
          current_business.project_config.auth_integration_type.present? &&
          !User::SignIn.default_auth_flow?(current_business, params["user"]["cpf"])
      end

      def authenticate!
        return fail_with_invalid_business if current_business.blank?
        return fail_with_unsuccessfull_custom_auth unless custom_auth.success?

        if create_or_update_user(custom_auth.user_params)
          call_user_hook(user) if new_record
          user.create_wallet! if new_record && current_business.cashback?
          success!(user)
        else
          fail_with_invalid_user
        end
      end

      private

      def fail_with_invalid_business
        fail!(I18n.t("devise.failure.invalid_business_headers"))
      end

      def fail_with_unsuccessfull_custom_auth
        fail!(custom_auth.message)
      end

      def fail_with_invalid_user
        fail!(I18n.t("devise.failure.unexpected_invalid_integration_data"))
      end

      def current_business
        @current_business ||= Business.current(request.headers)
      end

      def custom_auth
        @custom_auth ||=
          CustomAuth::AuthV2.new(main_business: current_business.main_project, params: params["user"]).call
      end

      def call_user_hook(user)
        WebhookSender::EventWorker.perform_async(WebhookSender::EventTypes::USER_CREATED, user.id)
      end

      def create_or_update_user(user_params)
        @authorized_user = AuthorizedUser.find_or_initialize_by(main_business_id: current_business.main_project_id, cpf: user_params[:cpf])
        @user = User.find_or_initialize_by(main_business_id: current_business.main_project_id, cpf: user_params[:cpf])
        @new_record = user.new_record?

        authorized_user.update(user_params.slice(:cpf, :email, :name, :default_auth_flow, :business_id)) &&
          user.update(user_params.merge(authorized_user_id: authorized_user.id))
      end
    end
  end
end
