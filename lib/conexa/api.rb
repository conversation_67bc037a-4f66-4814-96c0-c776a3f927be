class Conexa::A<PERSON>
  def create_or_update_user(user_params, plan)
    response = http_client(plan).post("/integration/enterprise/patients") { |req| req.body = user_params }
    if response.success? && response.body["status"] == 200
      response.body["object"]
    else
      handle_failure(Conexa::IntegrationError.new(I18n.t("conexa.errors.create_error"), user_params, response.body), "create")
    end
  rescue Faraday::ConnectionFailed, Faraday::TimeoutError => e
    handle_failure(Conexa::IntegrationError.new(I18n.t("conexa.errors.create_error"), {}, e.message), "create")
  end

  def block_user(id, plan)
    response = http_client(plan).post("/integration/enterprise/v2/patients/#{id}/block")
    if response.success? && response.body["status"] == 200
      response.body["object"]
    else
      handle_failure(Conexa::IntegrationError.new(I18n.t("conexa.errors.deactivate_error"), id, response.body), "deactivate")
    end
  rescue Faraday::ConnectionFailed, Faraday::TimeoutError => e
    handle_failure(Conexa::IntegrationError.new(I18n.t("conexa.errors.deactivate_error"), {}, e.message), "deactivate")
  end

  def unblock_user(id, plan)
    response = http_client(plan).post("/integration/enterprise/v2/patients/#{id}/unblock")

    if response.success? && response.body["status"] == 200
      response.body["object"]
    else
      handle_failure(Conexa::IntegrationError.new(I18n.t("conexa.errors.activate_error"), id, response.body), "activate")
    end
  rescue Faraday::ConnectionFailed, Faraday::TimeoutError => e
    handle_failure(Conexa::IntegrationError.new(I18n.t("conexa.errors.activate_error"), {}, e.message), "activate")
  end

  def generate_smart_link(id, plan)
    response = http_client(plan).get("/integration/enterprise/patients/generate-magiclink-access-app/#{id}")

    if response.success? && response.body["status"] == 200
      response.body["object"]["linkMagicoWeb"]
    else
      handle_failure(Conexa::IntegrationError.new(I18n.t("conexa.errors.smart_link_error"), id, response.body), "smart_link_error")
    end
  rescue Faraday::ConnectionFailed, Faraday::TimeoutError => e
    handle_failure(Conexa::IntegrationError.new(I18n.t("conexa.errors.smart_link_error"), {}, e.message), "smart_link_error")
  end

  private

  def http_client(plan)
    Faraday.new(url:) do |req|
      req.options.timeout = 10
      req.headers["Content-Type"] = "application/json"
      req.headers["token"] = auth_token(plan)
      req.request :json
      req.response :json
    end
  end

  def url = Rails.application.credentials.conexa[:url]

  def auth_token(plan)
    case plan
    when Telemedicine::Plans::COPART
      Rails.application.credentials.conexa[:auth_token][:copart]
    when Telemedicine::Plans::INTEGRAL
      Rails.application.credentials.conexa[:auth_token][:integral]
    when Telemedicine::Plans::INTEGRAL_PLUS
      Rails.application.credentials.conexa[:auth_token][:integral_plus]
    when Telemedicine::Plans::ULTRA
      Rails.application.credentials.conexa[:auth_token][:ultra]
    end
  end

  def handle_failure(exception, context)
    log_error("#{context} - Request params: #{exception.request_params} - Response Body: #{exception.response_body}")
    raise(exception)
  end

  def log_error(text)
    return if Rails.env.test?
    log_file_path = Rails.root.join("log", "conexa_#{Rails.env}.log")
    Logger.new(log_file_path).error(text)
  end
end
