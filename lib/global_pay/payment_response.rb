module GlobalPay
  class PaymentResponse
    attr_reader :success, :error_message, :order_code, :payment_id, :authorization_code, :status_text, :amount, :json

    def initialize(success:, json:, error_message: nil, order_code: nil, payment_id: nil, authorization_code: nil, status_text: nil, amount: nil)
      @success = success
      @error_message = error_message
      @order_code = order_code
      @payment_id = payment_id
      @authorization_code = authorization_code
      @status_text = status_text
      @amount = amount
      @json = json
    end
  end
end
