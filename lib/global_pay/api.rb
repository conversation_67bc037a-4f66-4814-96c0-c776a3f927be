module GlobalPay
  class Api
    attr_accessor :http_client

    def initialize(http_client: build_http_client)
      @http_client = http_client
      @authorization_token = Token.new
    end

    def payment(payment_request)
      response = http_client.post("/api/transactions/v1/payments/card", payment_request.to_hash)

      ApiResponse.new(status: response.status, json: response.body)
    rescue HttpClient::ResponseError => e
      ApiResponse.new(status: e.code, json: e.body, error_message: payment_error_message(e))
    end

    def store_card(card)
      response = http_client.post("/api/public/v1/vault/cards", card.to_hash)

      ApiResponse.new(status: response.status, json: response.body)
    rescue HttpClient::ResponseError => e
      ApiResponse.new(status: e.code, json: e.body, error_message: bad_stored_card_message(e))
    end

    def sellers(page = 1, limit = 100)
      response = http_client.get("/api/transactions/v1/seller/pagination?page=#{page}&limit=#{limit}")

      response.body
    end

    def merchants
      response = http_client.get("/api/public/v1/merchant")

      response.body
    end

    def merchant(id)
      response = http_client.get("/api/public/v1/merchant/#{id}")

      response.body
    end

    def split_rules(page = 1, limit = 100)
      response = http_client.get("/api/transactions/v1/payments/split-rules/pagination?limit=#{limit}&page=#{page}")

      response.body
    end

    def split_rule(id)
      response = http_client.get("/api/transactions/v1/payments/split-rules/#{id}/view-detail")

      response.body
    end

    def create_split_rule(fee, receiver_id)
      rule = {"name" => "split_rule", "fee" => fee, "splits" => [{"receiverId" => receiver_id, "percentage" => 100}]}

      http_client.post("/api/transactions/v1/payments/split-rules", rule.to_hash)
    end

    private

    def bad_stored_card_message(error)
      case error.code
      when 400
        if error.body.dig("data", 0, "description") == "O cartão está vencido."
          "O cartão está vencido."
        elsif error.body.dig("data", 0, "description")
          error.body.dig("data", 0, "description")
        elsif error.body.dig("data", "errors", "number")
          "Número do cartão inválido."
        elsif error.body.dig("data", "errors", "securityCode")
          "CVV inválido."
        elsif error.body.dig("data", "errors", "holderName")
          "Nome impresso no cartão inválido."
        elsif error.body.dig("data", "errors", "expirationMonth")
          "Mês de vencimento inválido."
        elsif error.body.dig("data", "errors", "expirationYear")
          "Ano de vencimento inválido."
        elsif error.body.dig("data", "errors", "brand")
          "Bandeira inválida."
        else
          "Ocorreu um erro ao salvar o cartão"
        end
      else
        "Ocorreu um erro ao salvar o cartão"
      end
    end

    def payment_error_message(error)
      case error.code
      when 400
        if error.body["data"]
          error = error_data(error.body)

          return "CPF inválido." if error["customer.documentNumber"]
          return "Cartão inválido" if error["card.number"] || error["number"]
          return error_message_mapping[error["tag"]] || error["description"]
        end
      when 404
        if error.body["message"] == "Não foi encontrado o cartão armazenado em cofre"
          return error.body["message"]
        end
      end

      "Não foi possível completar o pagamento"
    end

    def error_data(error_body)
      case error_body["data"]
      when Array
        error_body.dig("data", 0)
      when Hash
        error_body.dig("data", "errors")
      end
    end

    def error_message_mapping
      {
        "01" => "Transação não autorizada",
        "03" => "Estabelecimento inválido",
        "04" => "Transação não autorizada: cartão inválido",
        "05" => "Transação não autorizada",
        "06" => "Erro desconhecido",
        "07" => "Cartão inválido",
        "12" => "Transação inválida",
        "13" => "Valor inválido",
        "14" => "Cartão inválido",
        "15" => "Transação inválida",
        "19" => "Tente novamente",
        "30" => "Transação inválida",
        "38" => "Senha inválida",
        "39" => "Transação inválida",
        "40" => "Função inválida",
        "41" => "Cartão bloqueado",
        "43" => "Cartão bloqueado",
        "46" => "Transação não autorizada: conta encerrada",
        "51" => "Transação não autorizada",
        "52" => "Transação inválida",
        "53" => "Transação inválida",
        "54" => "Cartão vencido",
        "55" => "Senha inválida",
        "57" => "Transação não autorizada",
        "58" => "Transação não autorizada: não permitida para o estabelecimento",
        "59" => "Cartão suspenso",
        "61" => "Limite excedido",
        "62" => "Cartão restrito",
        "63" => "Senha inválida",
        "65" => "Transação não autorizada",
        "70" => "Transação não autorizada: erro desconhecido",
        "75" => "Senha inválida",
        "78" => "Cartão bloqueado",
        "79" => "Transação não autorizada",
        "82" => "Transação não autorizada",
        "83" => "Transação não autorizada",
        "91" => "Transação não autorizada: bandeira/emissor indisponível",
        "93" => "Cartão inválido",
        "94" => "Transação não autorizada: transação duplicada",
        "96" => "Transação não autorizada: erro de processamento",
        "99" => "Transação não autorizada: erro interno"
      }
    end

    def build_http_client
      @build_http_client ||= HttpClient::HttpClient.new(url: Config[:base_url]) do |builder|
        builder.options.timeout = 30
        builder.use Faraday::Request::Authorization, "Bearer", -> { GlobalPay::Token.new.generate }
        builder.request :json
        builder.response :json
      end
    end
  end
end
