module GlobalPay
  class Client
    attr_reader :api

    def initialize
      @api = Api.new
    end

    def payment(payment_request)
      response = api.payment(payment_request)
      json = response.json

      if response.success?
        PaymentResponse.new(
          success: true,
          error_message: response.error_message,
          order_code: json.dig("data", "orderCode"),
          payment_id: json.dig("data", "paymentId"),
          authorization_code: json.dig("data", "authorizationCode"),
          status_text: json.dig("data", "statusText"),
          amount: json.dig("data", "amount"),
          json:
        )
      else
        PaymentResponse.new(success: false, error_message: response.error_message, json:)
      end
    end

    def store_card(card)
      response = api.store_card(card)
      json = response.json

      if response.success?
        StoreCardResponse.new(
          success: true,
          error_message: response.error_message,
          store_card_id: json.dig("data", "storeCardId"),
          json:
        )
      else
        StoreCardResponse.new(success: false, error_message: response.error_message, json:)
      end
    end
  end
end
