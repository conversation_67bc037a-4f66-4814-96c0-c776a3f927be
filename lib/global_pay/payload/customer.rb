module GlobalPay
  module Payload
    class Customer
      attr_accessor :name, :document_type, :document_number, :email, :phone_number, :cell_phone_number, :address

      def initialize(name:, document_type:, document_number:, email:, phone_number:, cell_phone_number:, address:)
        @name = name
        @document_type = document_type
        @document_number = document_number
        @email = email
        @phone_number = phone_number
        @cell_phone_number = cell_phone_number
        @address = address
      end

      def to_hash
        {
          "name" => @name,
          "documentType" => @document_type,
          "documentNumber" => @document_number,
          "email" => @email,
          "phoneNumber" => @phone_number,
          "cellPhoneNumber" => @cell_phone_number,
          "address" => @address.to_hash
        }
      end
    end
  end
end
