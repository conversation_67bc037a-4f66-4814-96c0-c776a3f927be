module GlobalPay
  module Payload
    class DeviceInfo
      attr_accessor :ip_address,
        :http_accept_browser_value,
        :http_accept_content,
        :http_browser_language,
        :http_browser_java_enabled,
        :http_browser_javascript_enabled,
        :http_browser_color_depth,
        :http_browser_screen_height,
        :http_browser_screen_width,
        :http_browser_time_difference,
        :user_agent_browser_value

      def initialize(
        ip_address:,
        http_accept_browser_value: nil,
        http_accept_content: nil,
        http_browser_language: nil,
        http_browser_java_enabled: nil,
        http_browser_javascript_enabled: nil,
        http_browser_color_depth: nil,
        http_browser_screen_height: nil,
        http_browser_screen_width: nil,
        http_browser_time_difference: nil,
        user_agent_browser_value: nil
      )
        @ip_address = ip_address
        @http_accept_browser_value = http_accept_browser_value
        @http_accept_content = http_accept_content
        @http_browser_language = http_browser_language
        @http_browser_java_enabled = http_browser_java_enabled
        @http_browser_javascript_enabled = http_browser_javascript_enabled
        @http_browser_color_depth = http_browser_color_depth
        @http_browser_screen_height = http_browser_screen_height
        @http_browser_screen_width = http_browser_screen_width
        @http_browser_time_difference = http_browser_time_difference
        @user_agent_browser_value = user_agent_browser_value
      end

      def to_hash
        {
          "ipAddress" => @ip_address,
          "httpAcceptBrowserValue" => @http_accept_browser_value,
          "httpAcceptContent" => @http_accept_content,
          "httpBrowserLanguage" => @http_browser_language,
          "httpBrowserJavaEnabled" => @http_browser_java_enabled,
          "httpBrowserJavaScriptEnabled" => @http_browser_javascript_enabled,
          "httpBrowserColorDepth" => @http_browser_color_depth,
          "httpBrowserScreenHeight" => @http_browser_screen_height,
          "httpBrowserScreenWidth" => @http_browser_screen_width,
          "httpBrowserTimeDifference" => @http_browser_time_difference,
          "userAgentBrowserValue" => @user_agent_browser_value
        }.compact_blank
      end
    end
  end
end
