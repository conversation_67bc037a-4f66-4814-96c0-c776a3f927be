module GlobalPay
  module Payload
    class Card
      attr_accessor :number, :holder_name, :cvv, :expiration_month, :expiration_year, :brand

      def initialize(seller_id:, number:, holder_name:, cvv:, expiration_month:, expiration_year:, brand: nil)
        @seller_id = seller_id
        @number = number
        @holder_name = holder_name
        @cvv = cvv
        @expiration_month = expiration_month
        @expiration_year = expiration_year
        @brand = brand
      end

      def to_hash
        {
          "sellerId" => @seller_id,
          "number" => @number,
          "holderName" => @holder_name,
          "securityCode" => @cvv,
          "expirationMonth" => @expiration_month,
          "expirationYear" => @expiration_year,
          "brand" => @brand
        }.compact_blank
      end
    end
  end
end
