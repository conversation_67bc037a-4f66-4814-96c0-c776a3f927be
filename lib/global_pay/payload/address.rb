module GlobalPay
  module Payload
    class Address
      attr_accessor :street, :number, :complement, :neighborhood, :city, :state, :zip_code, :country

      def initialize(street:, number:, complement:, neighborhood:, city:, state:, zip_code:, country:)
        @street = street
        @number = number
        @complement = complement
        @neighborhood = neighborhood
        @city = city
        @state = state
        @zip_code = zip_code
        @country = country
      end

      def to_hash
        {
          "street" => @street,
          "number" => @number,
          "complement" => @complement,
          "neighborhood" => @neighborhood,
          "city" => @city,
          "state" => @state,
          "zipCode" => @zip_code,
          "country" => @country
        }
      end
    end
  end
end
