module GlobalPay
  module Payload
    class SplitItem
      attr_accessor :code, :description, :amount

      def initialize(code:, description:, amount:)
        @code = code
        @description = description
        @amount = amount
      end

      def to_hash
        {
          "code" => @code,
          "description" => @description,
          "amount" => @amount
        }
      end
    end
  end
end
