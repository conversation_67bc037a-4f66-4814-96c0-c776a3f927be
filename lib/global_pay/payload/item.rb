module GlobalPay
  module Payload
    class Item
      attr_accessor :unit_price, :product_name, :quantity

      def initialize(unit_price:, product_name:, quantity:)
        @unit_price = unit_price
        @product_name = product_name
        @quantity = quantity
      end

      def to_hash
        {
          "unitPrice" => @unit_price,
          "productName" => @product_name,
          "quantity" => @quantity
        }
      end
    end
  end
end
