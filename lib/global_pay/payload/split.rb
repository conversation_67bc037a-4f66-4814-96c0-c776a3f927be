module GlobalPay
  module Payload
    class Split
      attr_accessor :seller_id, :amount, :items

      def initialize(seller_id:, amount:, items:)
        @seller_id = seller_id
        @amount = amount
        @items = items
      end

      def to_hash
        {
          "sellerId" => @seller_id,
          "amount" => @amount.to_s,
          "items" => [*@items.map(&:to_hash)]
        }
      end
    end
  end
end
