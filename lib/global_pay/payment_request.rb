module GlobalPay
  class PaymentRequest
    attr_reader :amount, :antifraud_code, :customer, :items, :device_info
    attr_accessor :merchant, :card, :capture_type

    private_class_method :new

    PAYMENT_CARD_TYPE = "credit"
    INSTALLMENTS = 1

    class << self
      def with_merchant(merchant:, capture_type: "AC")
        new.with_merchant(merchant:, capture_type:)
      end

      def with_card(card:)
        raise ArgumentError, "Invalid Payload::StoredCard object" unless card.is_a?(Payload::StoredCard) || card.is_a?(Payload::Card)

        new.with_card(card:)
      end
    end

    def with_merchant(merchant:, capture_type: "AC")
      @merchant = merchant
      @capture_type = capture_type

      self
    end

    def with_card(card:)
      @card = card
      self
    end

    def request(amount:, antifraud_code:, customer:, device_info:, items: [], splits: [], soft_description: nil)
      @amount = amount
      @antifraud_code = antifraud_code
      @customer = customer
      @device_info = device_info
      @items = items
      @splits = splits
      @soft_description = soft_description

      self
    end

    def to_hash
      data = {
        "amount" => @amount,
        "paymentCardType" => PAYMENT_CARD_TYPE,
        "softDescription" => @soft_description,
        "installments" => INSTALLMENTS,
        "antifraudCode" => @antifraud_code,
        "captureType" => @capture_type,
        "card" => @card.to_hash,
        "customer" => @customer.to_hash,
        "itens" => [*@items.map(&:to_hash)],
        "splits" => [*@splits.map(&:to_hash)],
        "deviceInfo" => @device_info.to_hash
      }.compact_blank

      Utils::JsonHelper.stringify_and_remove_nils(data.to_hash)
    end
  end
end
