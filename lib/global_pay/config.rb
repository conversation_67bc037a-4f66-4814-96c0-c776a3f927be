module GlobalPay
  # rubocop:disable Naming/ConstantName
  Config = {
    base_url: Rails.application.credentials.global_pay.base_url,
    jwt_secret: Rails.application.credentials.global_pay.jwt_secret,
    issuer: Rails.application.credentials.global_pay.issuer,
    ec_client: {
      id: Rails.application.credentials.global_pay.ec_client.id,
      companyName: Rails.application.credentials.global_pay.ec_client.company_name,
      documentNumber: Rails.application.credentials.global_pay.ec_client.document_number
    }
  }
  # rubocop:enable Naming/ConstantName
end
