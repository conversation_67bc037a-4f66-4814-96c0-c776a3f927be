module GlobalPay
  module Authorization
    class Token<PERSON><PERSON>er
      def initialize(secret_key: fetch_secret_key)
        @secret_key = secret_key
      end

      def sign(header, payload)
        create_signed_token(header, payload)
      end

      private

      def create_signed_token(header, payload)
        token = encode_header_and_payload(header, payload)
        signature = sign_token(token)
        "#{token}.#{signature}"
      end

      def encode_header_and_payload(header, payload)
        "#{safe_encode(header.to_json)}.#{safe_encode(payload.to_json)}"
      end

      def sign_token(token)
        safe_encode(OpenSSL::HMAC.digest(OpenSSL::Digest.new("sha256"), @secret_key, token))
      end

      def fetch_secret_key = Config[:jwt_secret]

      def safe_encode(value)
        Base64.urlsafe_encode64(value, padding: false)
      end
    end
  end
end
