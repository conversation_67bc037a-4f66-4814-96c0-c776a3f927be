module GlobalPay
  module Authorization
    class TokenPayload
      attr_reader :issuer, :iat, :exp, :ec_client

      def initialize(issuer:, ec_client:, expiration_in_seconds: 300)
        @issuer = issuer
        @ec_client = ec_client
        @iat = Time.now.to_i
        @exp = @iat + expiration_in_seconds
      end

      def to_h
        {
          issuer: @issuer,
          iat: @iat,
          exp: @exp,
          ec_client: @ec_client
        }
      end
    end
  end
end
