# frozen_string_literal: true

module Tenex
  class Api
    PER_PAGE = 500

    def initialize(tenex_client:)
      @tenex_client = tenex_client
    end

    def fetch_users(page: 1)
      response = Faraday.new(url: tenex_client.base_url) do |req|
        req.headers["Authorization"] = "Basic #{Base64.strict_encode64("#{tenex_client.token}:")}"
        req.headers["Content-type"] = "application/json"
        req.params["_offset"] = PER_PAGE * (page - 1)
        req.params["_limit"] = PER_PAGE
        req.request :json
        req.response :json
      end.get

      if response.status == 200
        response.body
      else
        {}
      end
    end

    private

    attr_reader :tenex_client
  end
end
