# frozen_string_literal: true

module Tenex
  class Users
    ACTIVE_STATUS_CODE = 1

    def initialize(tenex_client:)
      @tenex_client = tenex_client
      @next_page = 1
    end

    def list_all
      redefine_page_count

      [].tap do |all_users|
        all_users << next_users while needs_next_page?
      end.flatten
    end

    def next_users
      return [] unless needs_next_page?
      response_hash = request_users
      calculate_total_pages(response_hash.dig("header", "count")) if total_pages.blank?

      [].tap do |users|
        if response_hash["data"].present?
          response_hash["data"].each do |tenex_user|
            next unless valid_user?(tenex_user)
            users << {
              name: tenex_user["nome"],
              cpf: CPF.new(tenex_user["cpf"]).stripped
            }
          end
        end

        @next_page += 1
      end
    end

    def needs_next_page?
      total_pages.blank? || next_page <= total_pages
    end

    private

    attr_reader :tenex_client, :next_page, :total_pages

    def valid_user?(tenex_user)
      CPF.new(tenex_user["cpf"]).valid? && tenex_user["status"] == ACTIVE_STATUS_CODE
    end

    def request_users
      tenex.fetch_users(page: next_page)
    end

    def tenex
      @tenex ||= Tenex::Api.new(tenex_client:)
    end

    def calculate_total_pages(total)
      @total_pages = (total.to_i / Tenex::Api::PER_PAGE.to_f).ceil
    end

    def redefine_page_count
      @next_page = 1
    end
  end
end
