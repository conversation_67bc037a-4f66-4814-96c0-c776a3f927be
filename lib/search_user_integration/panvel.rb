# frozen_string_literal: true

class SearchUserIntegration::<PERSON><PERSON>
  def initialize(user_cpf:, organization_name:)
    @user_cpf = user_cpf
    @organization_name = organization_name
    # 414 -> RBS
    @ignored_business_ids = [414]
  end

  def call
    (organization&.active? && authorized_user) ? success_json : fail_json
  end

  private

  attr_reader :user_cpf, :organization_name

  def organization
    @organization ||=
      Organization.find_by(name: organization_name)
  end

  def authorized_user
    @authorized_user ||=
      AuthorizedUser
        .where(cpf: user_cpf&.gsub(/\D/, ""))
        .where.not(main_business_id: @ignored_business_ids)
        .order(created_at: :desc)
        .first
  end

  def success_json
    {
      status: I18n.t("search_user_integration.status.eligible_message"),
      cpf: authorized_user.cpf,
      name: authorized_user.name
    }
  end

  def fail_json
    {status: I18n.t("search_user_integration.status.not_eligible_message")}
  end
end
