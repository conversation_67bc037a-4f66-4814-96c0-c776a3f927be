#!/bin/bash

set -o allexport
source /home/<USER>/.rails_env
set +o allexport

cd /var/www/lecupon-rails

echo "Ruby binary path: $(which ruby)"

echo "Deploying on $DEPLOYMENT_GROUP_NAME..."

echo "Installing gems..."
bin/bundle config set path 'vendor/cache'
bin/bundle install

echo "Migrating database..."
RAILS_ENV=$DEPLOYMENT_GROUP_NAME bin/rake db:create db:migrate

echo "Creating shared directories..."
mkdir -p shared/pids shared/sockets shared/log

if [ "$DEPLOYMENT_GROUP_NAME" != "production" ]; then
  echo "Starting Redis server..."
  redis-server --daemonize yes
fi

PRIVATE_IP="$(hostname -I | awk '{print $1}')"
BR_PRIVATE_IP="************"
US_PRIVATE_IP="************"

if [[ "$PRIVATE_IP" == "$BR_PRIVATE_IP" || "$DEPLOYMENT_GROUP_NAME" != "production" ]]; then
  echo "Restarting Puma..."
  sudo systemctl restart puma.service

  echo "Restarting Sidekiq..."
  sudo systemctl restart sidekiq.service

  if [ "$DEPLOYMENT_GROUP_NAME" == "staging" ]; then
    echo "Updating crontab for staging..."
    bin/whenever --set environment=$DEPLOYMENT_GROUP_NAME --update-crontab
  else
    echo "Updating crontab..."
    bin/whenever --update-crontab
  fi
fi
