@startuml SubscriptionFlow
!theme plain

title Subscription Creation and Payment Flow

actor User
participant "Mobile App" as App
participant "SubscriptionsController" as Controller
participant "Subscription" as Sub
participant "Payment::Processor" as Processor
participant "Payment Gateway" as Gateway
participant "CreditCard" as CC
participant "User" as UserModel
participant "Email Service" as Email

User -> App: Select plan and payment method
App -> Controller: POST /api/v2/subscriptions
note right: Headers: Idempotency-Key\nBody: plan_id, payment_details

Controller -> Controller: set_plan()
note right: Validates plan belongs to business

Controller -> Controller: set_request_params()
note right: Creates Api::V2::PaymentParams

Controller -> Controller: validate_request_params()
note right: Validates payment details

Controller -> Sub: create!(plan:, subscription_config:, active: false)
note right: Creates inactive subscription

Controller -> Sub: pay(idempotency_key:, payment_method:, params:)

Sub -> Sub: increment!(:retries)
note right: Track payment attempts

Sub -> Processor: new(order: self, idempotency_key:, payment_method:, params:)

Sub -> Processor: call()

Processor -> Processor: create_payment()
note right: Creates Payment record

Processor -> Gateway: process()
note right: Calls external payment gateway

Gateway --> Processor: payment_response

Processor -> Processor: update_payment(response)

alt Payment Captured
    Processor -> Sub: complete()
    
    Sub -> Sub: update!(active: true)
    note right: Activate subscription
    
    Sub -> UserModel: funds.points.approved.create!()
    note right: Credit points to user
    
    Sub -> Sub: user.subscriptions.active.without(self).each { |sub| sub.cancel! }
    note right: Cancel other active subscriptions
    
    Sub -> CC: update_credit_card!(processor.payment_data)
    note right: Update user's main credit card
    
    Sub -> Email: send_approved_email()
    
    Controller --> App: 201 Created
    note right: Success message with points credit info
    
else Payment Failed
    Controller --> App: 422 Unprocessable Entity
    note right: Error message with failure reason
end

== Recurring Payment (Background Job) ==

note over Sub: Daily job: Subscription.charge_due_installments

Sub -> Sub: active.where(retries: ...RETRY_LIMIT).select(&:payment_due?)

loop For each due subscription
    Sub -> Sub: pay(idempotency_key:, payment_method: "credit_card", params:)
    
    alt Payment Successful
        Sub -> Sub: update_columns(retries: 0)
        Sub -> Email: send_approved_email()
    else Payment Failed && retries >= RETRY_LIMIT
        Sub -> Sub: cancel!(reason:, email_template: :payment_failed)
        Sub -> Email: send_canceled_email(:payment_failed)
    end
end

@enduml
