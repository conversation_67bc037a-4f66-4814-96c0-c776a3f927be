@startuml SubscriptionDataModel
!theme plain

title Subscription Feature Data Model

entity "Business" {
  * id : integer
  --
  name : string
  api_secret : string
}

entity "User" {
  * id : integer
  --
  name : string
  email : string
  business_id : integer
}

entity "SubscriptionConfig" {
  * id : integer
  --
  business_id : integer
  title : string
  description : text
  cancellation_description : text
  days_to_credit_points : integer
  recommended_plan_id : integer
  cancel_destination_business_id : integer
}

entity "SubscriptionGroup" {
  * id : integer
  --
  subscription_config_id : integer
  name : string
}

entity "Plan" {
  * id : integer
  --
  business_id : integer
  subscription_config_id : integer
  title : string
  price : decimal(10,2)
  background_color : string
  font_color : string
  points : integer
  recurrence : enum(monthly, yearly)
  active : boolean
  destination_business_id : integer
  description : text
  benefit_description : text
}

entity "SubscriptionGroupPlan" {
  * id : integer
  --
  subscription_group_id : integer
  plan_id : integer
}

entity "Subscription" {
  * id : integer
  --
  user_id : integer
  subscription_config_id : integer
  plan_id : integer
  credit_card_id : integer
  title : string
  price : decimal(10,2)
  points : integer
  active : boolean
  retries : integer
  reason : string
}

entity "SubscriptionInstallment" {
  * id : integer
  --
  subscription_id : integer
  number : integer
  price : decimal(10,2)
  status : enum(pending, paid, failed)
  paid_at : datetime
  due_at : datetime
  retry_count : integer
}

entity "CreditCard" {
  * id : integer
  --
  user_id : integer
  token : string (encrypted)
  last4 : string
  brand : string
  main : boolean
  billing_name : string (encrypted)
  billing_document : string (encrypted)
  billing_email : string (encrypted)
  billing_phone : string (encrypted)
  billing_postal_code : string (encrypted)
  billing_street : string (encrypted)
  billing_number : string (encrypted)
  billing_complement : string (encrypted)
  billing_neighborhood : string (encrypted)
  billing_city : string (encrypted)
  billing_state : string (encrypted)
}

entity "Payment" {
  * id : integer
  --
  subscription_id : integer
  amount : decimal(10,2)
  status : string
  gateway_transaction_id : string
  idempotency_key : string
  payment_method : string
  reason_denied : string
}

entity "Fund" {
  * id : integer
  --
  user_id : integer
  subscription_id : integer
  amount : decimal(10,2)
  fund_type : string
  status : string
  credits_at : datetime
}

' Relationships
Business ||--o{ User : "has many"
Business ||--|| SubscriptionConfig : "has one"
Business ||--o{ Plan : "has many"

User ||--o{ Subscription : "has many"
User ||--o{ CreditCard : "has many"
User ||--o{ Fund : "has many"

SubscriptionConfig ||--o{ SubscriptionGroup : "has many"
SubscriptionConfig ||--o{ Plan : "has many"
SubscriptionConfig ||--o{ Subscription : "has many"
SubscriptionConfig }o--|| Plan : "recommended_plan"

SubscriptionGroup ||--o{ SubscriptionGroupPlan : "has many"
Plan ||--o{ SubscriptionGroupPlan : "has many"
Plan ||--o{ Subscription : "has many"

Subscription ||--o{ SubscriptionInstallment : "has many"
Subscription ||--o{ Payment : "has many"
Subscription ||--o{ Fund : "has many"
Subscription }o--|| CreditCard : "belongs to (optional)"

@enduml
