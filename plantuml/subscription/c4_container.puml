@startuml C4_Container_Subscription
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

LAYOUT_WITH_LEGEND()

title Container diagram for LeCupon Subscription System

Person(user, "User", "Customer subscribing to plans")
Person(business_admin, "Business Admin", "Manages subscription settings")

System_Boundary(lecupon, "LeCupon Platform") {
    Container(mobile_app, "Mobile Application", "React Native", "Provides subscription interface for users")
    Container(web_admin, "Admin Web Application", "React", "Business administration interface")
    
    Container(api_v2, "API v2", "Ruby on Rails", "Provides subscription functionality via JSON/HTTPS API")
    Container(client_v2, "Client API v2", "Ruby on Rails", "Provides admin functionality for business management")
    
    Container(background_jobs, "Background Jobs", "Sidekiq", "Processes recurring payments and notifications")
    
    ContainerDb(database, "Database", "PostgreSQL", "Stores subscriptions, plans, payments, and user data")
    ContainerDb(redis, "Redis", "Redis", "Job queue and caching")
}

System_Ext(payment_gateway, "Payment Gateway", "External payment processor")
System_Ext(email_service, "Email Service", "Notification delivery")

Rel(user, mobile_app, "Uses", "HTTPS")
Rel(business_admin, web_admin, "Manages", "HTTPS")

Rel(mobile_app, api_v2, "API calls", "JSON/HTTPS")
Rel(web_admin, client_v2, "API calls", "JSON/HTTPS")

Rel(api_v2, database, "Reads from and writes to", "SQL")
Rel(client_v2, database, "Reads from and writes to", "SQL")

Rel(api_v2, background_jobs, "Enqueues jobs", "Redis")
Rel(background_jobs, redis, "Uses", "Redis Protocol")
Rel(background_jobs, database, "Updates", "SQL")

Rel(api_v2, payment_gateway, "Processes payments", "HTTPS")
Rel(background_jobs, payment_gateway, "Recurring charges", "HTTPS")
Rel(background_jobs, email_service, "Sends emails", "SMTP")

@enduml
