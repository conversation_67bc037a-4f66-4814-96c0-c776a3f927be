# Subscription Feature C4 Model

This directory contains the C4 model diagrams for the LeCupon subscription feature, which allows users to subscribe to different plans and receive points periodically.

## Architecture Overview

The subscription feature is built around several core components:

### Core Models
- **Subscription**: Main subscription record linking users to plans
- **Plan**: Defines subscription tiers with pricing and benefits
- **SubscriptionConfig**: Business-specific subscription configuration
- **SubscriptionGroup**: Groups plans for better organization
- **SubscriptionInstallment**: Tracks individual payments for subscriptions

### Key Controllers
- **Api::V2::SubscriptionsController**: Handles subscription listing and creation
- **Api::V2::SubscriptionController**: Manages individual subscription operations
- **Api::V2::Subscriptions::CreditCardController**: Updates payment methods
- **Api::V2::Checkout::PlansController**: Provides plan details for checkout

### Payment Integration
- **Payment::Processor**: Orchestrates payment processing
- **CreditCard**: Manages user payment methods
- **Payment**: Records payment transactions

## Diagrams

1. **c4_context.puml** - System context showing external actors
2. **c4_container.puml** - Container-level view of the subscription system
3. **c4_component.puml** - Component-level details of subscription management
4. **c4_code.puml** - Code-level view of key classes
5. **subscription_flow.puml** - Sequence diagram showing subscription creation flow

## Key Features

- Multiple subscription plans with different pricing tiers
- Automatic recurring payments via credit card
- Points allocation upon successful payment
- Plan switching and cancellation
- Business-specific subscription configurations
- Integration with payment gateways
- Email notifications for subscription events
