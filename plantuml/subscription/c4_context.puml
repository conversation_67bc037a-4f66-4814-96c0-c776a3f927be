@startuml C4_Context_Subscription
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml

LAYOUT_WITH_LEGEND()

title System Context diagram for LeCupon Subscription Feature

Person(user, "User", "A customer who wants to subscribe to plans to earn points")
Person(business_admin, "Business Admin", "Manages subscription configurations and plans")

System_Boundary(lecupon, "LeCupon Platform") {
    System(subscription_system, "Subscription System", "Manages user subscriptions, plans, and recurring payments")
}

System_Ext(payment_gateway, "Payment Gateway", "External payment processor (GlobalPay)")
System_Ext(email_service, "Email Service", "Sends subscription-related notifications")
System_Ext(mobile_app, "Mobile App", "User interface for subscription management")
System_Ext(web_admin, "Web Admin Panel", "Business administration interface")

Rel(user, mobile_app, "Uses", "HTTPS")
Rel(mobile_app, subscription_system, "Manages subscriptions", "JSON/HTTPS")
Rel(business_admin, web_admin, "Configures", "HTTPS")
Rel(web_admin, subscription_system, "Manages plans and configs", "JSON/HTTPS")
Rel(subscription_system, payment_gateway, "Processes payments", "HTTPS")
Rel(subscription_system, email_service, "Sends notifications", "SMTP")

@enduml
