@startuml C4_Code_Subscription
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Code.puml

LAYOUT_WITH_LEGEND()

title Code diagram for LeCupon Subscription System

class "Api::V2::SubscriptionsController" {
  +index()
  +create()
  -set_plan()
  -set_request_params()
  -validate_request_params()
}

class "Api::V2::SubscriptionController" {
  +show()
  +destroy()
  -credit_card_info(plan)
}

class "Api::V2::BffSubscriptionPresenter" {
  +initialize(subscription_config:, supports:, faqs:, group_sections:, business:, user:)
  +subscription()
  +supports()
  +group_sections()
  +faqs()
  +current_subscription()
}

class "Subscription" {
  +complete()
  +pay(idempotency_key:, payment_method:, params:)
  +cancel!(notify:, email_template:, reason:)
  +update_credit_card!(credit_card)
  +payment_due?()
  +payment_next_date()
  +self.charge_due_installments()
}

class "Plan" {
  +free?()
  +self.build_free(business:)
  +title: string
  +price: decimal
  +points: integer
  +recurrence: enum
  +background_color: string
  +font_color: string
  +benefit_description: text
  -validates presence and numericality
}

class "SubscriptionConfig" {
  +title: string
  +description: text
  +cancellation_description: text
  +title_support: string
  +title_faq: string
  +days_to_credit_points: integer
  +recommended_plan_id: integer
  +image_desktop: uploader
  +image_mobile: uploader
}

class "SubscriptionGroup" {
  +name: string
  +plans()
  +subscription_config_id: integer
}

class "Card" {
  +title: string
  +description: text
  +position: integer
  +view_type: enum(support, faq)
  +feature: enum(subscription)
  +active: boolean
  +scope.support()
  +scope.faq()
}

class "Business" {
  +name: string
  +api_secret: string
  +subscription_config()
  +cards()
  +support()
  +faq()
}

class "User" {
  +name: string
  +email: string
  +business_id: integer
  +subscriptions()
  +current_subscription()
  +credit_cards()
}

class "User::Subscribable" {
  +current_subscription()
  +subscriptions()
}

class "Payment::Processor" {
  +initialize(order:, idempotency_key:, payment_method:, params:)
  +call()
  -create_payment()
  -process_payment()
  -update_payment(response)
  -update_order()
}

class "CreditCard" {
  +token: string (encrypted)
  +last4: string
  +brand: string
  +main: boolean
  +billing_*: string (encrypted)
  +tokenize!()
  +set_last4()
  +set_brand()
  +set_main()
  -validates uniqueness and presence
}

class "SubscriptionInstallment" {
  +number: integer
  +price: decimal
  +status: enum(pending, paid, failed)
  +paid_at: datetime
  +due_at: datetime
  +retry_count: integer
}

' Controller relationships
"Api::V2::SubscriptionsController" --> "Api::V2::BffSubscriptionPresenter" : uses
"Api::V2::SubscriptionsController" --> "Subscription" : creates
"Api::V2::SubscriptionsController" --> "SubscriptionGroup" : queries
"Api::V2::SubscriptionsController" --> "Card" : queries
"Api::V2::SubscriptionController" --> "Subscription" : queries

' BffSubscriptionPresenter relationships
"Api::V2::BffSubscriptionPresenter" --> "SubscriptionConfig" : queries
"Api::V2::BffSubscriptionPresenter" --> "SubscriptionGroup" : queries
"Api::V2::BffSubscriptionPresenter" --> "Plan" : queries
"Api::V2::BffSubscriptionPresenter" --> "Card" : queries
"Api::V2::BffSubscriptionPresenter" --> "Business" : queries
"Api::V2::BffSubscriptionPresenter" --> "User" : queries
"Api::V2::BffSubscriptionPresenter" --> "Subscription" : queries

' Domain model relationships
"Subscription" --> "Plan" : belongs_to
"Subscription" --> "SubscriptionConfig" : belongs_to
"Subscription" --> "User" : belongs_to
"Subscription" --> "CreditCard" : belongs_to (optional)
"Subscription" --> "SubscriptionInstallment" : has_many
"Subscription" --> "Payment::Processor" : uses

"Plan" --> "Business" : belongs_to
"Plan" --> "SubscriptionConfig" : belongs_to

"SubscriptionConfig" --> "Business" : belongs_to
"SubscriptionConfig" --> "Plan" : recommended_plan

"SubscriptionGroup" --> "SubscriptionConfig" : belongs_to
"SubscriptionGroup" --> "Plan" : has_many (through SubscriptionGroupPlan)

"Card" --> "Business" : belongs_to

"Business" --> "SubscriptionConfig" : has_one
"Business" --> "Card" : has_many
"Business" --> "User" : has_many
"Business" --> "Plan" : has_many

"User" --> "Subscription" : has_many
"User" --> "CreditCard" : has_many
"User::Subscribable" --> "Subscription" : has_many

"Payment::Processor" --> "CreditCard" : uses

@enduml
