@startuml C4_Component_Subscription
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

LAYOUT_WITH_LEGEND()

title Component diagram for LeCupon Subscription System

Container(mobile_app, "Mobile Application", "React Native", "User interface")
Container(web_admin, "Admin Web Application", "React", "Business admin interface")

Container_Boundary(api_v2, "API v2 Application") {
    Component(subscriptions_controller, "SubscriptionsController", "Rails Controller", "Lists plans and creates subscriptions")
    Component(subscription_controller, "SubscriptionController", "Rails Controller", "Shows and cancels user subscriptions")
    Component(credit_card_controller, "CreditCardController", "Rails Controller", "Updates subscription payment methods")
    Component(checkout_controller, "Checkout::PlansController", "Rails Controller", "Provides plan checkout details")
    
    Component(bff_presenter, "BffSubscriptionPresenter", "Presenter", "Aggregates subscription data for frontend")
    Component(subscription_serializer, "SubscriptionSerializer", "Serializer", "Formats subscription data for API responses")
}

Container_Boundary(models, "Domain Models") {
    Component(subscription_model, "Subscription", "ActiveRecord Model", "Core subscription entity")
    Component(plan_model, "Plan", "ActiveRecord Model", "Subscription plan definitions")
    Component(subscription_config, "SubscriptionConfig", "ActiveRecord Model", "Business subscription settings")
    Component(subscription_group, "SubscriptionGroup", "ActiveRecord Model", "Groups plans for organization")
    Component(user_subscribable, "User::Subscribable", "Concern", "User subscription methods")
}

Container_Boundary(payment_system, "Payment System") {
    Component(payment_processor, "Payment::Processor", "Service", "Orchestrates payment processing")
    Component(credit_card_model, "CreditCard", "ActiveRecord Model", "User payment methods")
    Component(payment_model, "Payment", "ActiveRecord Model", "Payment records")
}

ContainerDb(database, "Database", "PostgreSQL", "Data persistence")
System_Ext(payment_gateway, "Payment Gateway", "External payment processor")

Rel(mobile_app, subscriptions_controller, "API calls", "JSON/HTTPS")
Rel(mobile_app, subscription_controller, "API calls", "JSON/HTTPS")
Rel(mobile_app, credit_card_controller, "API calls", "JSON/HTTPS")
Rel(mobile_app, checkout_controller, "API calls", "JSON/HTTPS")

Rel(subscriptions_controller, bff_presenter, "Uses")
Rel(subscriptions_controller, subscription_model, "Creates")
Rel(subscription_controller, subscription_model, "Queries")
Rel(credit_card_controller, subscription_model, "Updates")
Rel(checkout_controller, plan_model, "Queries")

Rel(bff_presenter, subscription_serializer, "Uses")
Rel(bff_presenter, subscription_config, "Queries")
Rel(bff_presenter, subscription_group, "Queries")

Rel(subscription_model, payment_processor, "Uses")
Rel(subscription_model, plan_model, "Belongs to")
Rel(subscription_model, subscription_config, "Belongs to")
Rel(plan_model, subscription_config, "Belongs to")

Rel(user_subscribable, subscription_model, "Has many")
Rel(payment_processor, credit_card_model, "Uses")
Rel(payment_processor, payment_model, "Creates")
Rel(payment_processor, payment_gateway, "Calls", "HTTPS")

Rel(subscription_model, database, "Persists to", "SQL")
Rel(plan_model, database, "Persists to", "SQL")
Rel(subscription_config, database, "Persists to", "SQL")
Rel(credit_card_model, database, "Persists to", "SQL")
Rel(payment_model, database, "Persists to", "SQL")

@enduml
