# Telemedicine System - C4 Model Documentation

This directory contains C4 model diagrams for the Telemedicine feature in the LeCupon platform. The telemedicine system enables businesses to provide telemedicine services to their authorized users through integration with the Conexa platform.

## Overview

The telemedicine feature allows:
- Businesses to configure telemedicine plans and beneficiary limits
- Users to subscribe to telemedicine services
- Integration with Conexa (third-party telemedicine provider)
- Activity tracking and smart link generation for seamless access
- Background job processing for user synchronization

## Diagrams

### 1. C4 Context Diagram (`c4_context.puml`)
Shows the high-level view of the telemedicine system and its external actors:
- **End Users**: Access telemedicine services
- **Business Admins**: Configure telemedicine settings for their business
- **Client Employees**: Manage telemedicine plans and configurations
- **External Systems**: Conexa Platform, Mobile App, Web Application

### 2. C4 Container Diagram (`c4_container.puml`)
Illustrates the major containers within the LeCupon platform:
- **API v2**: REST API for user telemedicine operations
- **Client API v2**: REST API for business administration
- **Background Jobs**: Sidekiq workers for user synchronization
- **Database**: PostgreSQL for data persistence
- **Redis**: Job queue and caching

### 3. C4 Component Diagram (`c4_component.puml`)
Details the internal components of the telemedicine system:
- **API Layer**: Controllers and authorization policies
- **Business Logic**: Models, concerns, and service classes
- **Integration Layer**: Conexa client and API integration
- **Background Processing**: Sidekiq workers for async operations
- **Data Layer**: ActiveRecord models and database entities

### 4. C4 Code Diagram (`c4_code.puml`)
Shows the detailed class relationships and method signatures:
- Controllers with their actions
- Models with their key methods
- Workers with their processing logic
- Policies with authorization rules

### 5. Data Model Diagram (`data_model.puml`)
Represents the database schema and relationships:
- **Business**: Has telemedicine configuration
- **TelemedicineConfig**: Stores plan and beneficiary limits
- **AuthorizedUser**: Users with telemedicine access
- **TelemedicineActivity**: Activity tracking
- **TelemedicineBeneficiary**: Legacy model (being phased out)

### 6. Sequence Diagram (`telemedicine_flow.puml`)
Illustrates the key flows:
- Telemedicine subscription process
- Telemedicine access and smart link generation
- Business configuration changes and synchronization

## Key Components

### Models and Concerns

#### AuthorizedUser::Telemedicine
- Manages user telemedicine functionality
- Handles subscription, activation, and deactivation
- Generates smart links for platform access
- Tracks user activities

#### Business::Telemedicine
- Manages business-level telemedicine configuration
- Checks slot availability
- Synchronizes user states when business settings change

#### TelemedicineConfig
- Stores telemedicine plan and contracted beneficiaries
- Validates beneficiary limits against plan constraints
- Manages user synchronization through background jobs

### Integration Layer

#### Conexa::Client
- Provides high-level interface to Conexa platform
- Handles user creation, updates, blocking/unblocking
- Generates smart links for seamless access

#### Conexa::Api
- Low-level HTTP client for Conexa API
- Handles authentication tokens per plan
- Manages error handling and retries

### Background Processing

#### TelemedicineBeneficiary Workers
- **ThirdPartyUpsertWorker**: Creates/updates users in Conexa
- **ThirdPartyDeactivateWorker**: Deactivates users in Conexa
- **ThirdPartyReactivateWorker**: Reactivates users in Conexa

All workers include throttling and retry mechanisms for reliability.

### Telemedicine Plans

The system supports four telemedicine plans with different beneficiary limits:
- **COPART**: Basic plan (500+ beneficiaries)
- **INTEGRAL**: Standard plan (1000+ beneficiaries)
- **ULTRA**: Premium plan (500+ beneficiaries)
- **INTEGRAL_PLUS**: Enterprise plan (1500+ beneficiaries)

## API Endpoints

### User Endpoints (API v2)
- `POST /api/v2/users/telemedicine/access` - Generate telemedicine access link
- `POST /api/v2/users/telemedicine/subscription` - Subscribe to telemedicine

### Admin Endpoints (Client v2)
- `GET /client/v2/telemedicine_plans` - List available telemedicine plans

## Security and Authorization

The system uses Rails policies for authorization:
- **Business::TelemedicinePolicy**: Ensures business has telemedicine enabled
- **Users::Telemedicine::AccessPolicy**: Verifies user has active telemedicine
- **Users::Telemedicine::SubscriptionPolicy**: Checks eligibility and slot availability

## Activity Tracking

All telemedicine activities are tracked with the following entry types:
- `accessed`: User accessed telemedicine platform
- `subscribed`: User subscribed to telemedicine
- `reactivated`: User was reactivated
- `deactivated`: User was deactivated
- `changed_plan`: Plan was changed
- `subscribe_error`: Error during subscription
- `unsubscribe_error`: Error during unsubscription

## Error Handling

The system includes comprehensive error handling:
- Conexa API integration errors with rollback mechanisms
- Background job retry logic with exponential backoff
- Validation errors for plan limits and configurations
- Graceful degradation when external services are unavailable

## Future Considerations

- Migration away from legacy TelemedicineBeneficiary model
- Enhanced error logging and monitoring
- Support for additional telemedicine providers
- Real-time synchronization improvements
