@startuml c4_context_telemedicine
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml

title Telemedicine System - C4 Context Diagram

Person(user, "End User", "Authorized user who needs telemedicine services")
Person(business_admin, "Business Admin", "Manages telemedicine configuration for their business")
Person(client_employee, "Client Employee", "Manages telemedicine plans and configurations")

System(lecupon_api, "LeCupon API", "Main platform providing telemedicine services integration")

System_Ext(conexa, "Conexa Platform", "Third-party telemedicine service provider")
System_Ext(mobile_app, "Mobile App", "User interface for accessing telemedicine services")
System_Ext(web_app, "Web Application", "Administrative interface for managing telemedicine")

Rel(user, lecupon_api, "Subscribes to telemedicine, generates access links", "HTTPS/JSON")
Rel(business_admin, lecupon_api, "Configures telemedicine plans and beneficiaries", "HTTPS/JSON")
Rel(client_employee, lecupon_api, "Manages telemedicine plans and settings", "HTTPS/JSON")

Rel(lecupon_api, conexa, "Creates/updates users, generates smart links", "HTTPS/JSON")
Rel(user, mobile_app, "Accesses telemedicine services", "HTTPS")
Rel(business_admin, web_app, "Manages business telemedicine settings", "HTTPS")
Rel(client_employee, web_app, "Configures telemedicine plans", "HTTPS")

Rel(mobile_app, lecupon_api, "Requests telemedicine access", "HTTPS/JSON")
Rel(web_app, lecupon_api, "Manages configurations", "HTTPS/JSON")
Rel(user, conexa, "Uses telemedicine services via smart link", "HTTPS")

@enduml
