@startuml telemedicine_data_model
!define ENTITY class
!define ENUM enum

title Telemedicine Data Model

ENTITY Business {
  +id: bigint
  +name: string
  +cnpj: string
  +telemedicine: boolean
  --
  +telemedicine?()
  +telemedicine_slot_available?()
  +telemedicine_beneficiaries()
}

ENTITY TelemedicineConfig {
  +id: bigint
  +business_id: bigint
  +plan: string
  +contracted_beneficiaries: integer
  +created_at: datetime
  +updated_at: datetime
  --
  +sync_telemedicine()
  +apply_contracted_beneficiaries_limit()
  +upsert_beneficiaries(ids)
  +disable_beneficiaries(ids)
  +enable_beneficiaries(ids)
}

ENTITY AuthorizedUser {
  +id: bigint
  +business_id: bigint
  +name: string
  +email: string
  +cpf: string
  +telemedicine: boolean
  +telemedicine_enabled: boolean
  +telemedicine_external_id: string
  +active: boolean
  --
  +subscribed_to_telemedicine?()
  +active_telemedicine?()
  +inactive_telemedicine?()
  +generate_telemedicine_smart_link()
  +track_telemedicine_activity!()
}

ENTITY TelemedicineActivity {
  +id: bigint
  +authorized_user_id: bigint
  +entry_type: telemedicine_activity_entry_type
  +activity_log: jsonb
  +created_at: datetime
  +updated_at: datetime
}

ENTITY TelemedicineBeneficiary {
  +id: bigint
  +authorized_user_id: bigint
  +name: string
  +email: string
  +cpf: string
  +enabled: boolean
  +external_id: string
  +created_at: datetime
  +updated_at: datetime
  --
  Note: Legacy model, being phased out
}

ENUM telemedicine_activity_entry_type {
  accessed
  deactivated
  subscribed
  subscribe_error
  deactivated_due_to_project_configuration
  unsubscribe_error
  reactivated
  changed_plan
}

' Service Classes (not stored in DB)
class "Telemedicine::Plans" as TelemedicinePlans {
  +COPART: string
  +INTEGRAL: string
  +INTEGRAL_PLUS: string
  +ULTRA: string
  --
  +all()
  +higher_plans(plan)
  +lower_plans(plan)
  +upgrade?(from, to)
  +contracted_beneficiaries_limits_for_plan(plan)
}

' External Integration
class "Conexa::Client" as ConexaClient {
  --
  +create_or_update_user(params, plan)
  +block_user(external_id, plan)
  +unblock_user(external_id, plan)
  +generate_smart_link(external_id, plan)
}

' Relationships
Business ||--|| TelemedicineConfig : has_one
Business ||--o{ AuthorizedUser : has_many

AuthorizedUser ||--o{ TelemedicineActivity : has_many
AuthorizedUser ||--o| TelemedicineBeneficiary : has_one

TelemedicineActivity }o--|| telemedicine_activity_entry_type : uses

' Scopes and Queries
note right of AuthorizedUser
  Scopes:
  - subscribed_to_telemedicine
  - elegible_to_telemedicine  
  - with_telemedicine_enabled
  - with_telemedicine_disabled
end note

note right of TelemedicineConfig
  Validations:
  - contracted_beneficiaries_must_be_between_plan_limits
  
  Callbacks:
  - sync_telemedicine (after_commit)
end note

note right of Business
  Callbacks:
  - sync_telemedicine (after_commit)
  
  Telemedicine Plans:
  - COPART: Basic plan
  - INTEGRAL: Standard plan  
  - ULTRA: Premium plan
  - INTEGRAL_PLUS: Enterprise plan
end note

@enduml
