@startuml c4_code_telemedicine
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

title Telemedicine System - C4 Code Diagram

System_Boundary(telemedicine_code, "Telemedicine Code Structure") {
    Container_Boundary(controllers, "Controllers") {
        Component(accesses_controller, "AccessesController", "Rails Controller", "create()")
        Component(telemedicine_plans_controller, "TelemedicinePlansController", "Rails Controller", "index()")
    }
    
    Container_Boundary(models, "Models & Concerns") {
        Component(authorized_user_telemedicine, "AuthorizedUser::Telemedicine", "Rails Concern", "sync_telemedicine()\ngenerate_telemedicine_smart_link()\ntrack_telemedicine_activity!()")
        Component(business_telemedicine, "Business::Telemedicine", "Rails Concern", "telemedicine_slot_available?()\nsync_telemedicine()")
        Component(telemedicine_config_model, "TelemedicineConfig", "Rails Model", "sync_telemedicine()\napply_contracted_beneficiaries_limit()")
        Component(telemedicine_plans_service, "Telemedicine::Plans", "Service Class", "all()\nhigher_plans()\ncontracted_beneficiaries_limits_for_plan()")
        Component(telemedicine_activity_model, "TelemedicineActivity", "Rails Model", "entry_type enum")
        Component(telemedicine_beneficiary_model, "TelemedicineBeneficiary", "Rails Model", "Legacy model")
    }
    
    Container_Boundary(integrations, "Integration Layer") {
        Component(third_party_integration, "TelemedicineBeneficiary::ThirdPartyIntegration", "Rails Concern", "create_or_update_third_party_beneficiary()\nreactivate_third_party_beneficiary()\ndeactivate_third_party_beneficiary()")
        Component(conexa_client, "Conexa::Client", "Service Class", "create_or_update_user()\nblock_user()\nunblock_user()\ngenerate_smart_link()")
        Component(conexa_api, "Conexa::Api", "HTTP Client", "HTTP methods for Conexa integration")
    }
    
    Container_Boundary(workers, "Background Workers") {
        Component(upsert_worker, "TelemedicineBeneficiary::ThirdPartyUpsertWorker", "Sidekiq Worker", "perform(id)")
        Component(deactivate_worker, "TelemedicineBeneficiary::ThirdPartyDeactivateWorker", "Sidekiq Worker", "perform(id)")
        Component(reactivate_worker, "TelemedicineBeneficiary::ThirdPartyReactivateWorker", "Sidekiq Worker", "perform(id)")
        Component(workers_helper, "TelemedicineConfig::WorkersHelper", "Rails Concern", "upsert_beneficiaries()\ndisable_beneficiaries()\nenable_beneficiaries()")
    }
    
    Container_Boundary(policies, "Authorization") {
        Component(business_telemedicine_policy, "Api::V2::Business::TelemedicinePolicy", "Rails Policy", "manage?()")
        Component(access_policy, "Api::V2::Users::Telemedicine::AccessPolicy", "Rails Policy", "create?()")
        Component(subscription_policy, "Api::V2::Users::Telemedicine::SubscriptionPolicy", "Rails Policy", "create?()")
    }
}

' Controller relationships
Rel(accesses_controller, business_telemedicine_policy, "authorize!")
Rel(accesses_controller, access_policy, "authorize!")
Rel(accesses_controller, authorized_user_telemedicine, "generate_telemedicine_smart_link()")

Rel(telemedicine_plans_controller, telemedicine_plans_service, "all()")

' Model relationships
Rel(authorized_user_telemedicine, telemedicine_activity_model, "has_many")
Rel(authorized_user_telemedicine, telemedicine_beneficiary_model, "has_one")
Rel(authorized_user_telemedicine, third_party_integration, "delegates to")

Rel(business_telemedicine, telemedicine_config_model, "has_one")

Rel(telemedicine_config_model, workers_helper, "includes")
Rel(workers_helper, upsert_worker, "enqueues")
Rel(workers_helper, deactivate_worker, "enqueues")
Rel(workers_helper, reactivate_worker, "enqueues")

' Integration relationships
Rel(third_party_integration, conexa_client, "delegates to")
Rel(conexa_client, conexa_api, "uses")

' Worker relationships
Rel(upsert_worker, authorized_user_telemedicine, "finds and calls")
Rel(deactivate_worker, authorized_user_telemedicine, "finds and calls")
Rel(reactivate_worker, authorized_user_telemedicine, "finds and calls")

Rel(upsert_worker, third_party_integration, "create_or_update_third_party_beneficiary()")
Rel(deactivate_worker, third_party_integration, "deactivate_third_party_beneficiary()")
Rel(reactivate_worker, third_party_integration, "reactivate_third_party_beneficiary()")

' Policy relationships
Rel(business_telemedicine_policy, business_telemedicine, "checks business.telemedicine?")
Rel(access_policy, authorized_user_telemedicine, "checks active_telemedicine?")
Rel(subscription_policy, authorized_user_telemedicine, "checks telemedicine?")
Rel(subscription_policy, business_telemedicine, "checks telemedicine_slot_available?")

@enduml
