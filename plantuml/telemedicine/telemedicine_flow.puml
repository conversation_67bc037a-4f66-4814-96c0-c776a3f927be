@startuml telemedicine_flow
title Telemedicine Access and Subscription Flow

actor User
participant "Mobile App" as App
participant "API v2" as API
participant "AuthorizedUser" as AuthUser
participant "TelemedicineConfig" as Config
participant "Background Job" as Job
participant "Conexa Client" as Conexa
participant "Conexa Platform" as ConexaPlatform

== Telemedicine Subscription Flow ==

User -> App: Enable telemedicine
App -> API: PATCH /api/v2/users/me {telemedicine: true}
API -> AuthUser: update(telemedicine: true)

alt Business has available slots
    AuthUser -> AuthUser: sync_telemedicine()
    AuthUser -> Config: upsert_beneficiaries([user_id])
    Config -> Job: TelemedicineBeneficiary::ThirdPartyUpsertWorker.perform_async(user_id)
    
    Job -> AuthUser: find(user_id)
    Job -> AuthUser: create_or_update_third_party_beneficiary()
    AuthUser -> Conexa: create_or_update_user(user_params, plan)
    Conexa -> ConexaPlatform: POST /integration/enterprise/patients
    ConexaPlatform --> Conexa: {id: external_id, status: 200}
    Conexa --> AuthUser: external_id
    
    AuthUser -> AuthUser: update!(telemedicine_external_id: external_id, telemedicine_enabled: true)
    AuthUser -> AuthUser: telemedicine_activities.create!(entry_type: :subscribed)
    
    API --> App: 200 OK
    App --> User: Telemedicine activated
else No available slots
    API --> App: 422 Unprocessable Entity
    App --> User: No slots available
end

== Telemedicine Access Flow ==

User -> App: Access telemedicine
App -> API: POST /api/v2/users/telemedicine/access

API -> API: authorize! with TelemedicinePolicy
API -> API: authorize! with AccessPolicy

alt User has active telemedicine
    API -> AuthUser: generate_telemedicine_smart_link()
    AuthUser -> Conexa: generate_smart_link(telemedicine_external_id, plan)
    Conexa -> ConexaPlatform: GET /integration/enterprise/patients/generate-magiclink-access-app/{id}
    ConexaPlatform --> Conexa: {linkMagicoWeb: "https://..."}
    Conexa --> AuthUser: smart_link_url
    
    AuthUser -> AuthUser: track_telemedicine_activity!()
    AuthUser -> AuthUser: telemedicine_activities.create!(entry_type: :accessed)
    
    API -> API: response.set_header("Location", smart_link_url)
    API --> App: 200 OK {url: smart_link_url}
    App -> User: Open telemedicine platform
    User -> ConexaPlatform: Access via smart link
else User doesn't have active telemedicine
    API --> App: 403 Forbidden
    App --> User: Access denied
end

== Business Configuration Change ==

participant "Business Admin" as Admin
participant "Client API" as ClientAPI

Admin -> ClientAPI: Update telemedicine config
ClientAPI -> Config: update(plan: new_plan, contracted_beneficiaries: new_limit)
Config -> Config: sync_telemedicine()

alt Plan changed
    Config -> Config: nulify_beneficiaries_external_ids()
    Config -> AuthUser: update_all(telemedicine_external_id: nil)
end

Config -> Config: apply_contracted_beneficiaries_limit()

alt More beneficiaries needed
    Config -> Config: enable_users_to_telemedicine(elegible_ids)
    Config -> Job: TelemedicineBeneficiary::ThirdPartyUpsertWorker.perform_async(user_id)
else Too many beneficiaries
    Config -> Config: disable_users_to_telemedicine(exceeded_ids)
    Config -> Job: TelemedicineBeneficiary::ThirdPartyDeactivateWorker.perform_async(user_id)
end

Job -> Conexa: block_user() or create_or_update_user()
Conexa -> ConexaPlatform: API calls

@enduml
