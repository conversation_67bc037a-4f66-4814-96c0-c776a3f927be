@startuml c4_container_telemedicine
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

title Telemedicine System - C4 Container Diagram

Person(user, "End User", "Authorized user accessing telemedicine services")
Person(business_admin, "Business Admin", "Manages business telemedicine configuration")
Person(client_employee, "Client Employee", "Manages telemedicine plans")

System_Boundary(lecupon_system, "LeCupon Platform") {
    Container(api_v2, "API v2", "Ruby on Rails", "REST API for user telemedicine operations")
    Container(client_v2, "Client API v2", "Ruby on Rails", "REST API for business administration")
    Container(background_jobs, "Background Jobs", "Sidekiq", "Processes telemedicine user synchronization")
    Container(database, "Database", "PostgreSQL", "Stores telemedicine configurations and user data")
    Container(redis, "Redis", "Redis", "Job queue and caching")
}

System_Ext(conexa_api, "Conexa API", "Third-party telemedicine service provider")

Rel(user, api_v2, "Subscribes to telemedicine, requests access", "HTTPS/JSON")
Rel(business_admin, client_v2, "Configures telemedicine settings", "HTTPS/JSON")
Rel(client_employee, client_v2, "Manages telemedicine plans", "HTTPS/JSON")

Rel(api_v2, database, "Reads/writes user and configuration data", "SQL")
Rel(client_v2, database, "Reads/writes configuration data", "SQL")

Rel(api_v2, background_jobs, "Enqueues user sync jobs", "Redis")
Rel(client_v2, background_jobs, "Enqueues configuration sync jobs", "Redis")
Rel(background_jobs, redis, "Processes job queue", "Redis Protocol")

Rel(background_jobs, conexa_api, "Creates/updates/blocks users", "HTTPS/JSON")
Rel(api_v2, conexa_api, "Generates smart links for access", "HTTPS/JSON")

Rel(background_jobs, database, "Updates user sync status", "SQL")

@enduml
