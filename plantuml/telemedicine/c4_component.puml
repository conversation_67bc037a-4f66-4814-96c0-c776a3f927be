@startuml c4_component_telemedicine
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

title Telemedicine System - C4 Component Diagram

Person(user, "End User")
Person(business_admin, "Business Admin")

System_Boundary(telemedicine_system, "Telemedicine System") {
    Container_Boundary(api_layer, "API Layer") {
        Component(access_controller, "Access Controller", "Rails Controller", "Handles telemedicine access requests")
        Component(subscription_controller, "Subscription Controller", "Rails Controller", "Manages user subscriptions")
        Component(plans_controller, "Plans Controller", "Rails Controller", "Manages telemedicine plans")
        Component(policies, "Authorization Policies", "Rails Policies", "Controls access to telemedicine features")
    }
    
    Container_Boundary(business_logic, "Business Logic Layer") {
        Component(user_telemedicine, "User Telemedicine", "Rails Concern", "User telemedicine functionality")
        Component(business_telemedicine, "Business Telemedicine", "Rails Concern", "Business telemedicine management")
        Component(telemedicine_config, "Telemedicine Config", "Rails Model", "Configuration management")
        Component(telemedicine_plans, "Telemedicine Plans", "Service Class", "Plan management and validation")
    }
    
    Container_Boundary(integration_layer, "Integration Layer") {
        Component(conexa_client, "Conexa Client", "Service Class", "Conexa API integration")
        Component(conexa_api, "Conexa API", "HTTP Client", "Low-level API communication")
        Component(third_party_integration, "Third Party Integration", "Rails Concern", "Generic third-party integration")
    }
    
    Container_Boundary(background_processing, "Background Processing") {
        Component(upsert_worker, "Upsert Worker", "Sidekiq Worker", "Creates/updates users in Conexa")
        Component(deactivate_worker, "Deactivate Worker", "Sidekiq Worker", "Deactivates users in Conexa")
        Component(reactivate_worker, "Reactivate Worker", "Sidekiq Worker", "Reactivates users in Conexa")
        Component(workers_helper, "Workers Helper", "Rails Concern", "Worker management utilities")
    }
    
    Container_Boundary(data_layer, "Data Layer") {
        Component(authorized_user, "Authorized User", "Rails Model", "User with telemedicine access")
        Component(business, "Business", "Rails Model", "Business entity")
        Component(telemedicine_activity, "Telemedicine Activity", "Rails Model", "Activity tracking")
        Component(telemedicine_beneficiary, "Telemedicine Beneficiary", "Rails Model", "Legacy beneficiary model")
    }
}

System_Ext(conexa_platform, "Conexa Platform")
ContainerDb(database, "Database", "PostgreSQL")

' API Layer relationships
Rel(user, access_controller, "Requests telemedicine access")
Rel(user, subscription_controller, "Subscribes to telemedicine")
Rel(business_admin, plans_controller, "Manages plans")

Rel(access_controller, policies, "Checks authorization")
Rel(subscription_controller, policies, "Checks authorization")

' Business Logic relationships
Rel(access_controller, user_telemedicine, "Generates smart links")
Rel(subscription_controller, user_telemedicine, "Manages subscriptions")
Rel(plans_controller, telemedicine_plans, "Gets plan information")

Rel(user_telemedicine, telemedicine_config, "Manages configuration")
Rel(business_telemedicine, telemedicine_config, "Syncs business settings")
Rel(telemedicine_config, workers_helper, "Enqueues background jobs")

' Integration Layer relationships
Rel(user_telemedicine, third_party_integration, "Generates smart links")
Rel(third_party_integration, conexa_client, "Calls Conexa services")
Rel(conexa_client, conexa_api, "Makes HTTP requests")
Rel(conexa_api, conexa_platform, "API calls")

' Background Processing relationships
Rel(workers_helper, upsert_worker, "Enqueues user creation")
Rel(workers_helper, deactivate_worker, "Enqueues user deactivation")
Rel(workers_helper, reactivate_worker, "Enqueues user reactivation")

Rel(upsert_worker, third_party_integration, "Creates/updates users")
Rel(deactivate_worker, third_party_integration, "Deactivates users")
Rel(reactivate_worker, third_party_integration, "Reactivates users")

' Data Layer relationships
Rel(user_telemedicine, authorized_user, "Extends functionality")
Rel(business_telemedicine, business, "Extends functionality")
Rel(telemedicine_config, business, "Belongs to")
Rel(authorized_user, telemedicine_activity, "Has many")
Rel(authorized_user, telemedicine_beneficiary, "Has one")

Rel_Back(database, authorized_user, "Stores")
Rel_Back(database, business, "Stores")
Rel_Back(database, telemedicine_activity, "Stores")
Rel_Back(database, telemedicine_beneficiary, "Stores")

@enduml
