require "rails_helper"

describe "Api::V1::Concerns::PaginationValidator", type: :controller do
  let(:json_parse_response_body) { JSON.parse(response.body) }

  controller do
    include Api::V1::Concerns::PaginationValidator

    def action
      render json: {page: params[:page], per_page: params[:per_page]}
    end
  end

  before do
    routes.draw { get :action, to: "anonymous#action" }
  end

  context "when page and per_page parameters are not sent" do
    it "return default values" do
      get :action

      expect(json_parse_response_body).to eq({"page" => nil, "per_page" => nil})
    end
  end

  context "when page parameter is less than minimum" do
    it "returns validation error" do
      get :action, params: {page: 0}

      expect(response).to have_http_status(:bad_request)
      expect(json_parse_response_body["error"]).to eq(I18n.t("paginate.range_error.page", min_page: Api::V1::Concerns::PaginationValidator::MIN_PAGE))
    end
  end

  context "when per_page parameter is less than minimum" do
    it "returns validation error" do
      get :action, params: {per_page: 0}

      expect(response).to have_http_status(:bad_request)
      expect(json_parse_response_body["error"]).to eq(I18n.t("paginate.range_error.per_page",
        min_per_page: Api::V1::Concerns::PaginationValidator::MIN_PER_PAGE,
        max_per_page: Api::V1::Concerns::PaginationValidator::MAX_PER_PAGE))
    end
  end

  context "when per_page parameter is greater than maximum" do
    it "returns validation error" do
      get :action, params: {per_page: 101}

      expect(response).to have_http_status(:bad_request)
      expect(json_parse_response_body["error"]).to eq(I18n.t("paginate.range_error.per_page",
        min_per_page: Api::V1::Concerns::PaginationValidator::MIN_PER_PAGE,
        max_per_page: Api::V1::Concerns::PaginationValidator::MAX_PER_PAGE))
    end
  end

  context "when both page and per_page parameters are valid" do
    it "passes validation" do
      get :action, params: {page: 1, per_page: 15}

      expect(response).to have_http_status(:success)
      expect(json_parse_response_body).to eq({"page" => "1", "per_page" => "15"})
    end

    it "accepts per_page at the maximum value" do
      get :action, params: {page: 1, per_page: 100}

      expect(response).to have_http_status(:success)
      expect(json_parse_response_body).to eq({"page" => "1", "per_page" => "100"})
    end
  end

  context "when testing boundary conditions" do
    it "works with both parameters at minimum allowed values" do
      get :action, params: {page: Api::V1::Concerns::PaginationValidator::MIN_PAGE,
                            per_page: Api::V1::Concerns::PaginationValidator::MIN_PER_PAGE}

      expect(response).to have_http_status(:success)
      expect(json_parse_response_body).to eq({"page" => Api::V1::Concerns::PaginationValidator::MIN_PAGE.to_s,
                                             "per_page" => Api::V1::Concerns::PaginationValidator::MIN_PER_PAGE.to_s})
    end

    it "works with per_page at maximum allowed value and a valid page number" do
      get :action, params: {page: 100,
                            per_page: Api::V1::Concerns::PaginationValidator::MAX_PER_PAGE}

      expect(response).to have_http_status(:success)
      expect(json_parse_response_body).to eq({"page" => "100",
                                             "per_page" => Api::V1::Concerns::PaginationValidator::MAX_PER_PAGE.to_s})
    end

    it "fails when page is at minimum value but per_page is below minimum" do
      get :action, params: {page: Api::V1::Concerns::PaginationValidator::MIN_PAGE,
                            per_page: Api::V1::Concerns::PaginationValidator::MIN_PER_PAGE - 1}

      expect(response).to have_http_status(:bad_request)
      expect(json_parse_response_body["error"]).to eq(I18n.t("paginate.range_error.per_page",
        min_per_page: Api::V1::Concerns::PaginationValidator::MIN_PER_PAGE,
        max_per_page: Api::V1::Concerns::PaginationValidator::MAX_PER_PAGE))
    end

    it "fails when per_page is at maximum value but page is below minimum" do
      get :action, params: {page: Api::V1::Concerns::PaginationValidator::MIN_PAGE - 1,
                            per_page: Api::V1::Concerns::PaginationValidator::MAX_PER_PAGE}

      expect(response).to have_http_status(:bad_request)
      expect(json_parse_response_body["error"]).to eq(I18n.t("paginate.range_error.page",
        min_page: Api::V1::Concerns::PaginationValidator::MIN_PAGE))
    end

    it "fails when page is at zero and per_page is below minimum" do
      get :action, params: {page: 0,
                            per_page: 0}

      expect(response).to have_http_status(:bad_request)
      expect(json_parse_response_body["error"]).to eq(I18n.t("paginate.range_error.per_page",
        min_per_page: Api::V1::Concerns::PaginationValidator::MIN_PER_PAGE,
        max_per_page: Api::V1::Concerns::PaginationValidator::MAX_PER_PAGE))
    end
  end
end
