require "rails_helper"

describe "Client::V2::Concerns::CurrentBusiness", type: :controller do
  let(:json_parse_response_body) { JSON.parse(response.body) }

  let!(:secret_key) { Rails.application.credentials.secret_key_base }
  let!(:business) { create(:business) }

  controller do
    include Client::V2::Concerns::CurrentBusiness

    before_action :checky_if_choosing_business_is_mandatory
    before_action :guard_not_found_current_business
    before_action :guard_admin_client_current_business

    def action
      render json: {id: current_business&.id}.to_json
    end
  end

  before do
    routes.draw { get :action, to: "anonymous#action" }
  end

  context "when user is admin_lecupon" do
    before do
      admin_lecupon = create(:client_employee, :admin_lecupon)
      allow(controller).to receive(:current_user).and_return(admin_lecupon)
    end

    it "business_identifier is required" do
      get :action

      expect(json_parse_response_body["error"]).to eq("<PERSON><PERSON><PERSON><PERSON> possui dois ou mais businesses. Parâmetro business_identifier é obrigatório")
    end

    it "returns the business according to identifier" do
      business_identifier = [business.id, business.cnpj].sample
      request.headers[:"Tenant-id"] = business_identifier

      get :action

      expect(json_parse_response_body["id"]).to eq(business.id)
    end

    it "using params returns business according to identifier" do
      business_identifier = [business.id, business.cnpj].sample
      payload = {business_identifier:}

      get :action, params: payload

      expect(json_parse_response_body["id"]).to eq(business.id)
    end
  end

  context "when user is admin_client" do
    context "with one business" do
      before do
        admin_client = create(:client_employee, :admin_client, businesses: [business])
        allow(controller).to receive(:current_user).and_return(admin_client)
      end

      it "returns the only business the current user has" do
        get :action

        expect(json_parse_response_body["id"]).to eq(business.id)
      end
    end

    context "with two or more businesses" do
      before do
        business_two = create(:business)
        admin_client = create(:client_employee, :admin_client, businesses: [business, business_two])
        allow(controller).to receive(:current_user).and_return(admin_client)
      end

      it "business_identifier is required" do
        get :action

        expect(json_parse_response_body["error"]).to eq("Usuário Logado possui dois ou mais businesses. Parâmetro business_identifier é obrigatório")
      end

      it "returns the business according to identifier" do
        business_identifier = [business.id, business.cnpj].sample
        request.headers[:"Tenant-id"] = business_identifier

        get :action

        expect(json_parse_response_body["id"]).to eq(business.id)
      end
    end

    context "with inactive business" do
      before do
        business.update_columns(active: false)
        admin_client = create(:client_employee, :admin_client, businesses: [business])
        allow(controller).to receive(:current_user).and_return(admin_client)
      end

      it "returns the only business the current user has" do
        get :action

        expect(response).to have_http_status(:forbidden)
        expect(json_parse_response_body["error"]).to eq("Acesso negado")
      end
    end
  end

  context "when user is shopkeeper" do
    context "with one business" do
      before do
        shopkeeper = create(:client_employee, :shopkeeper, businesses: [business])
        allow(controller).to receive(:current_user).and_return(shopkeeper)
      end

      it "returns the only business the current user has" do
        get :action

        expect(json_parse_response_body["id"]).to eq(business.id)
      end
    end

    context "with two or more businesses" do
      before do
        business_two = create(:business)
        shopkeeper = create(:client_employee, :shopkeeper, businesses: [business, business_two])
        allow(controller).to receive(:current_user).and_return(shopkeeper)
      end

      it "business_identifier is required" do
        get :action

        expect(json_parse_response_body["error"]).to eq("Usuário Logado possui dois ou mais businesses. Parâmetro business_identifier é obrigatório")
      end

      it "returns the business according to identifier" do
        business_identifier = [business.id, business.cnpj].sample
        request.headers[:"Tenant-id"] = business_identifier

        get :action

        expect(json_parse_response_body["id"]).to eq(business.id)
      end
    end
  end

  context "when business is not found" do
    before do
      admin_client = create(:client_employee, :admin_client, businesses: [business])
      allow(controller).to receive(:current_user).and_return(admin_client)
    end

    it "business is not found" do
      request.headers[:"Tenant-id"] = 10

      get :action

      expect(response).to have_http_status(:not_found)
      expect(json_parse_response_body["error"]).to eq("Business não encontrado")
    end
  end
end
