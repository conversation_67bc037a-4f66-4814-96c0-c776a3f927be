FactoryBot.define do
  factory :order do
    number { "LC#{[*("A".."Z"), *("0".."9")].sample(16).join}" }
    redeemed_at { DateTime.current }
    promotion_provider { Promotion::Provider.all.sample }
    business {
      association :business, :with_cashback
    }
    user {
      association :user, :with_cashback, business: instance.business
    }
    association :cupon, :with_organization

    trait :percent_discount do
      discount_value { 30.0 }
      discount_type { Promotion::DiscountType::PERCENT }
    end

    trait :fixed_discount do
      discount_value { 10.0 }
      discount_type { Promotion::DiscountType::FIXED }
    end

    trait :completed do
      status { Order::Status::COMPLETED }
      paid_at { DateTime.current }
    end

    trait :pending do
      status { Order::Status::PENDING }
    end

    trait :canceled do
      status { Order::Status::CANCELED }
    end

    trait :online do
      redeem_type { Enums::OrderRedeemType::ONLINE }
      association :cupon, :online, :with_organization
    end

    trait :without_cashback do
      association :user, :without_cashback
    end

    trait :physical do
      redeem_type { Enums::OrderRedeemType::PHYSICAL }
      association :cupon, :classic, :with_branch
      confirmation_key { instance.cupon.branch.cnpj }
    end

    trait :with_usage_expired do
      usage_ended_at { 1.day.ago }
      usage_expired { true }
    end

    trait :with_giftcard do
      cupon { nil }
      association :giftcard, :with_vouchers, :with_branches
      cashback_type { Cashback::Type::PERCENT }
      cashback_value { 10 }
    end

    trait :without_user do
      association :user, strategy: :null
    end
  end

  factory :order_without_coupon, class: "Order" do
    net_value { 100.00 }
    description { FFaker::Name.name }
    discount_type { "fixed" }
    discount_value { 10.0 }
    number { "LC#{[*("A".."Z"), *("0".."9")].sample(16).join}" }
    redeemed_at { DateTime.current }
  end
end
