FactoryBot.define do
  factory :authorized_user do
    name { FFaker::Name.name }
    email { FFaker::Internet.email }
    cpf { CPF.new(FFaker::IdentificationBR.cpf).stripped }
    business

    trait :with_authorized_user_group do
      authorized_user_group do
        association :authorized_user_group, business_id: instance.business_id
      end
    end
    trait :with_user do
      user do
        create(:user, authorized_user: instance, business: instance.business, cpf: instance.cpf, active: instance.active)
      end
    end

    trait :subscribed_to_telemedicine do
      telemedicine { true }
      telemedicine_external_id { 401 }
    end

    trait :active_to_telemedicine do
      telemedicine { true }
      telemedicine_external_id { 401 }
      telemedicine_enabled { true }
    end
  end
end
