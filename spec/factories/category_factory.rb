FactoryBot.define do
  factory :category do
    title { FFaker::Food.vegetable }
    image { Tempfile.new(["premio", ".png"], "tmp") }

    trait :active do
      visible { true }
    end

    trait :inactive do
      visible { false }
    end

    trait :promotion_category do
      category_type { Enums::Category::Type::PROMOTION }
    end

    trait :organization_category do
      category_type { Enums::Category::Type::ORGANIZATION }
    end

    trait :home_pinned do
      home_pinned { true }
      visible { true }
      deprecated_icon { Rails.root.join("spec/fixtures/samplefile.png").open }
    end

    trait :with_organizations do
      transient do
        organizations_count { 40 }
      end

      after(:create) do |category, evaluator|
        organizations = create_list(:organization, evaluator.organizations_count, :active_with_coupon, :with_cashback, :percent_highest_discount, categories: [category])

        organizations.each do |organization|
          branch = create(:branch, organization:, lat: 1.0, lng: 1.0, promotion_redeemable: true)
          promotion = create(:promotion, :qrcode, :available, business: category.business, organization:)
          create(:cupon, :classic, promotion:, branch:)
        end
      end
    end
  end
end
