FactoryBot.define do
  factory :credit_card do
    user

    token { SecureRandom.uuid }
    last4 { FFaker::Bank.card_number.last(4) }
    brand { FFaker::Bank.card_type }
    security_code { Array.new(3) { rand(0..9) }.join }

    main { false }
    trait :main do
      main { true }
    end

    billing_name { FFaker::NameBR.name }
    billing_document { FFaker::IdentificationBR.cpf }
    billing_email { FFaker::Internet.email }
    billing_phone { FFaker::PhoneNumberBR.mobile_phone_number }
    billing_postal_code { FFaker::AddressBR.zip_code }
    billing_street { FFaker::AddressBR.street }
    billing_number { FFaker::AddressBR.building_number }
    billing_neighborhood { FFaker::AddressBR.neighborhood }
    billing_city { FFaker::AddressBR.city }
    billing_state { FFaker::AddressBR.state }
  end
end
