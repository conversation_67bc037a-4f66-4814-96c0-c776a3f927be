FactoryBot.define do
  factory :subscription do
    user { association :user }
    subscription_config { association :subscription_config, business: user.business }
    plan { association :plan, subscription_config: subscription_config, business: user.business }
    credit_card { association :credit_card, user: user }
    price { FFaker::Number.decimal.to_f + 5 }
    points { 1000 }
    recurrence { "monthly" }
    title { FFaker::Name.name }
  end
end
