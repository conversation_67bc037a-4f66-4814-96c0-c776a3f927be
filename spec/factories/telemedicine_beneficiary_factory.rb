FactoryBot.define do
  factory :telemedicine_beneficiary do
    name { FFaker::Name.name }
    email { FFaker::Internet.email }
    cellphone { FFaker::PhoneNumberBR.mobile_phone_number }
    cpf { FFaker::IdentificationBR.cpf }
    sex { "MALE" }
    birthdate { 21.years.ago }
    last_access_at { Time.current }
    authorized_user do
      create(:authorized_user, cpf: instance.cpf)
    end

    trait :active do
      enabled { true }
    end

    trait :inactive do
      enabled { false }
    end
  end
end
