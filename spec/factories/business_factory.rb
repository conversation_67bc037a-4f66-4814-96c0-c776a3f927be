FactoryBot.define do
  factory :business do
    contact_email { "<EMAIL>" }
    transient do
      create_project_config { true }
      create_giftcard_config { true }
      create_mailer_config { true }
      create_webhook_config { true }
      create_web_application { true }
      create_member_referral_config { true }
      create_telemedicine_config { true }
    end

    name { FFaker::Company.unique.name }
    cnpj { FFaker::IdentificationBR.cnpj }
    api_secret { SecureRandom.hex(100) }
    signature_secret { SecureRandom.hex(20) }
    spread_percent { 10 }
    integration_type { Enums::Business::IntegrationType::APP }
    send_email_onboarding { false }
    organization_manage { true }
    project_config do
      if create_project_config
        association :project_config, business: instance, web_domain: "#{instance.name.parameterize}.com", email_sender: "<EMAIL>"
      end
    end

    trait :active do
      active { true }
    end

    trait :inactive do
      active { false }
    end

    giftcard_config do
      if create_giftcard_config
        association :giftcard_config, business: instance
      end
    end

    mailer_config do
      if create_mailer_config
        association :mailer_config, business: instance
      end
    end

    webhook_config do
      if create_webhook_config
        association :webhook_config, business: instance
      end
    end

    web_application do
      if create_web_application
        association :web_application, business: instance
      end
    end

    member_referral_config do
      if create_member_referral_config
        association :member_referral_config, business: instance
      end
    end

    telemedicine_config do
      if create_telemedicine_config
        association :telemedicine_config, business: instance, plan: Telemedicine::Plans::INTEGRAL, contracted_beneficiaries: 1000
      end
    end

    cashback_requestable_amount do
      20 if cashback
    end

    trait :lecupon_equipe do
      name { "Lecupon Equipe" }
      cnpj { "04600980000114" } # auto-truck cnpj
    end
    trait :lecupon do
      name { "Lecupon" }
      cnpj { "26989697000169" }
    end

    trait :publicar do
      name { "Publicar" }
      cnpj { "53026472000937" }
    end

    trait :ixc_business do
      ixc_token
    end

    trait :unifacisa_business do
      unifacisa_client
    end

    trait :tenex_business do
      tenex_client
    end

    trait :voalle_business do
      voalle_token
    end

    trait :mk_solution_business do
      mk_solution_token
    end

    trait :hubsoft_business do
      hubsoft_token
    end

    trait :hinova_business do
      after(:create) do |hinova_business|
        create(:hinova_token, business: hinova_business)

        hinova_business.reload
      end
    end

    trait :oab do
      name { "OABMG" }
      cnpj { "59741569000150" }

      after(:create) do |business|
        if business.main_business.nil?
          create(:custom_field,
            business:,
            name: "Número de Inscrição",
            param_name: "number",
            label: "Número de Inscrição",
            description: "Somente para associados")
          business.reload
        end
      end
    end

    trait :oab_sp do
      name { "OAB SP" }
      cnpj { "59741569000151" }
      after(:create) do |business|
        if business.main_business.nil?
          create(:custom_field,
            business:,
            name: "Número de Inscrição",
            param_name: "number",
            label: "Seu número de inscrição",
            description: "Somente para associados")
          business.reload
        end
      end
    end

    trait :oab_mt do
      name { "OAB-MT" }
      cnpj { "59741569000152" }
      create_project_config { false }
      after(:create) do |business|
        if business.main_business.nil?
          create(:custom_field,
            business:,
            name: "Número de Inscrição",
            param_name: "number",
            label: "Seu número de inscrição",
            description: "Somente para associados")

          auth_integration = create(:auth_integration,
            business:,
            base_url: "https://servicos.oabmt.org.br/servicosonline/api/v1/verifica-advogado",
            params: {inscricao: "{number}", cpf: "{cpf}"},
            headers: {Token: "c08d3dfd6e2f467c8df46a1cefa4e434"},
            http_method: "post")

          create(:response_condition,
            http_status_code: 200,
            text_matcher: '"cadastro":"existente"',
            response_type: Enums::ResponseCondition::ResponseTypes::SUCCESS,
            auth_integration:)

          create(:response_condition,
            http_status_code: 200,
            text_matcher: '"cadastro":"inexistente"',
            response_type: Enums::ResponseCondition::ResponseTypes::INACTIVE_CPF,
            auth_integration:)

          create(:response_condition,
            text_matcher: '"erro"',
            response_type: Enums::ResponseCondition::ResponseTypes::CPF_NOT_FOUND,
            auth_integration:)

          business.create_project_config!(
            auth_integration_type: Enums::ProjectConfig::AuthIntegrationType::CUSTOM,
            user_manager: Enums::UserManager::DEFAULT_WITH_EXTERNAL_VALIDATION,
            custom_sign_in: true,
            custom_sign_up: true
          )
          business.reload
        end
      end
    end

    trait :with_smart_link_integration do
      after(:create) do |business|
        auth_integration = create(:auth_integration,
          business: business,
          base_url: "https://jzs8363ys2.execute-api.us-east-1.amazonaws.com/default/LogaInternetNoSignupAuth",
          params: {token: "6&gul6zZW2QaTKEeL%z39IQCTNWuqgzomfl3nAZ63C5ZkKgb7c", cpf: "{cpf}"},
          http_method: "post")

        create(:response_condition,
          http_status_code: 200,
          text_matcher: '"active":1',
          response_type: Enums::ResponseCondition::ResponseTypes::SUCCESS,
          auth_integration:)

        create(:response_condition,
          http_status_code: 200,
          text_matcher: '"active":0',
          response_type: Enums::ResponseCondition::ResponseTypes::INACTIVE_CPF,
          auth_integration:)

        create(:response_condition,
          text_matcher: '"error":"CPF não encontrado"',
          response_type: Enums::ResponseCondition::ResponseTypes::CPF_NOT_FOUND,
          auth_integration:)

        business.project_config.update!(
          auth_integration_type: Enums::ProjectConfig::AuthIntegrationType::SMART_LINK_NO_SIGNUP,
          user_manager: Enums::UserManager::EXTERNAL,
          sign_up_url: "https://foo.com/sign-up-url",
          google_identifier: "android_id",
          apple_identifier: "ios_id",
          firebase_dynamic_link_domain: "app.page.link",
          web_domain: "web.example.com",
          create_user_on_smart_link: false,
          custom_sign_in: true,
          custom_sign_up: true
        )
      end
    end

    trait :oab_ba do
      name { "OAB-BA" }
      cnpj { "59741569000153" }
    end

    trait :plano_mais_assistencial do
      name { "Plano Mais Assistencial" }
      cnpj { "59741569000153" }
    end

    trait :sicoob do
      name { "SICOOB DiviCred" }
      cnpj { "96350989000132" }
    end

    trait :avanza_ti do
      name { "Avanza Ti" }
      cnpj { "22733937000109" }
      project_config do
        if create_project_config
          association :project_config,
            business: instance,
            web_domain: "#{instance.name.parameterize}.com",
            email_sender: "<EMAIL>",
            user_manager: Enums::UserManager::NO_SIGNUP,
            auth_integration_type: Enums::ProjectConfig::AuthIntegrationType::VOALLE,
            custom_sign_in: true,
            custom_sign_up: true
        end
      end

      voalle_token { create(:voalle_token, business: instance) }
    end

    trait :with_cashback do
      cashback { true }
      cashback_wallet_destination { Wallet::Kind::CASHBACK }
    end

    trait :subscription do
      subscription_config
    end
  end
end
