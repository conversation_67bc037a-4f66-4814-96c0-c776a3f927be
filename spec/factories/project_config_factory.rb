FactoryBot.define do
  factory :project_config do
    business
    name { "Nome do APP" }
    primary_color { "#999ccc" }
    secondary_color { "grey" }
    font_color { "#000" }
    background_color { "white" }
    google_identifier { "xyz.anybusiness" }
    apple_identifier { FFaker::Name.name }
    policy_url { "anyurl.com" }
    term_of_use_url { "anyurl.com/term_of_use" }
    firebase_dynamic_link_domain { "anybusiness.page.link" }
    favicon { "anyurl.com/anywebicon.png" }
    apple_icon { "anyurl.com/anyappleicon.png" }
    vertical_logo { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png")) }
    horizontal_logo { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.svg")) }
    horizontal_white_logo { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.svg")) }
    google_icon { "anyurl.com/anygoogleicon.png" }
    web_domain { "businessdomain.com" }

    trait :with_google_key_file do
      google_key_file { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/project_config/google_key_file.json")) }
    end

    trait :with_external_user_manager do
      user_manager { Enums::UserManager::EXTERNAL }
      password_recovery_url { "anyurl.com/password_recovery" }
      sign_up_url { "anyurl.com/sign_up" }
      user_update_url { "anyurl.com/user_update" }
    end

    trait :with_facebook_sdk do
      facebook_sdk { true }
      facebook_sdk_app_id { SecureRandom.hex }
      facebook_sdk_app_name { FFaker::Name.name }
      facebook_sdk_client_token { SecureRandom.hex }
    end

    trait :with_clever_tap do
      clever_tap { true }
      clever_tap_account_id { SecureRandom.hex }
      clever_tap_project_token { SecureRandom.hex }
      clever_tap_region { "us1" }
      clever_tap_passcode { SecureRandom.hex }
    end
  end
end
