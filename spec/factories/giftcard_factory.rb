FactoryBot.define do
  factory :giftcard do
    title { FFaker::Name.name }
    price { FFaker::Number.decimal.to_f + 5 }
    available { true }
    sale_started_at { 1.month.ago }
    sale_ended_at { 1.month.from_now }
    usage_started_at { 1.month.ago }
    usage_ended_at { 3.months.from_now }
    cashback_type { "percent" }
    cashback_value { 10 }
    usage_instruction { FFaker::LoremBR.phrase }

    trait :with_usage_expired do
      sale_ended_at { 2.days.ago }
      usage_ended_at { 1.day.ago }
      usage_expired { true }
    end

    trait :with_vouchers do
      transient do
        quantity { 4 }
      end

      voucher_bucket { association(:voucher_bucket) }

      after(:create) do |giftcard, evaluator|
        create_list(:voucher, evaluator.quantity, bucket: evaluator.voucher_bucket)
      end
    end

    trait :with_branches do
      after(:create) do |giftcard|
        organization = create(:organization)
        branch = create(:branch, organization:)
        create(:giftcard_branch, giftcard:, branch:)
      end
    end
  end
end
