# Preview all emails at http://localhost:3000/rails/mailers

class FundMailerPreview < ActionMailer::Preview
  def pending
    project_config = FactoryBot.create(
      :project_config,
      name: "Sample Business",
      primary_color: "#abcd49",
      email_sender: "<EMAIL>"
    )
    business = FactoryBot.create(:business, project_config:)
    user = FactoryBot.create(:user, business:)

    FundMailer.with(business:, user:).pending
  end

  def approved
    project_config = FactoryBot.create(
      :project_config,
      name: "Sample Business",
      primary_color: "#abcd49",
      email_sender: "<EMAIL>"
    )
    business = FactoryBot.create(:business, project_config:)
    user = FactoryBot.create(:user, business:)

    FundMailer.with(business:, user:).approved
  end

  def available
    project_config = FactoryBot.create(
      :project_config,
      name: "Sample Business",
      primary_color: "#abcd49",
      email_sender: "<EMAIL>"
    )
    business = FactoryBot.create(:business, project_config:)
    user = FactoryBot.create(:user, business:)

    FundMailer.with(business:, user:).available
  end
end
