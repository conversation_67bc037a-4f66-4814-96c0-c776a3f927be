# Preview all emails at http://localhost:3000/rails/mailers

class WalletRedemptionMailerPreview < ActionMailer::Preview
  def requested
    project_config = FactoryBot.create(
      :project_config,
      name: "Sample Business",
      primary_color: "#abcd49",
      email_sender: "<EMAIL>"
    )
    business = FactoryBot.create(:business, project_config:)
    user = FactoryBot.create(:user, business:)

    WalletRedemptionMailer.with(business:, user:).requested
  end
end
