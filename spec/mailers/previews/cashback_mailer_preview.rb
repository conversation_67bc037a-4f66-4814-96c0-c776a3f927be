# Preview all emails at http://localhost:3000/rails/mailers

class CashbackMailerPreview < ActionMailer::Preview
  def pending
    @user = FactoryBot.create(:user)

    CashbackMailer.with(business: @user.business, user: @user).pending
  end

  def approved
    @user = FactoryBot.create(:user)

    CashbackMailer.with(business: @user.business, user: @user).approved
  end

  def available
    @user = FactoryBot.create(:user)

    CashbackMailer.with(business: @user.business, user: @user).available
  end

  def in_transfer
    @user = FactoryBot.create(:user)

    CashbackMailer.with(business: @user.business, user: @user).in_transfer
  end

  def transferred
    @user = FactoryBot.create(:user)

    CashbackMailer.with(business: @user.business, user: @user).transferred
  end
end
