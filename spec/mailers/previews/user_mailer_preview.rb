# Preview all emails at http://localhost:3000/rails/mailers

class UserMailerPreview < ActionMailer::Preview
  def reset_password
    business = FactoryBot.create(:business)
    user = FactoryBot.create(:user, business:)
    new_password = SecureRandom.hex(10)

    UserMailer.with(business:, user:).reset_password(new_password:)
  end

  def onboarding_email
    business = FactoryBot.create(:business)
    user = FactoryBot.create(:user, business:)
    UserMailer.with(business:, user:).onboarding_email
  end
end
