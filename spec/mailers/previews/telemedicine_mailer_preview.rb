# Preview all emails at http://localhost:3000/rails/mailers

class TelemedicineMailerPreview < ActionMailer::Preview
  def sign_up
    @user = FactoryBot.create(:user)
    @user.business.project_config.update(web_domain: "anydomain.com")

    TelemedicineMailer.with(business: @user.business, user: @user).sign_up
  end

  def idle_deactivation
    @user = FactoryBot.create(:user)
    @user.business.project_config.update(web_domain: "anydomain.com")

    TelemedicineMailer.with(business: @user.business, user: @user).idle_deactivation
  end
end
