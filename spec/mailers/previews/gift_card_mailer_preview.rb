# Preview all emails at http://localhost:3000/rails/mailers

class GiftCardMailerPreview < ActionMailer::Preview
  def error_feedback
    project_config = FactoryBot.create(
      :project_config,
      name: "Sample Business",
      primary_color: "#abcd49",
      email_sender: "<EMAIL>"
    )
    business = FactoryBot.create(:business, project_config:)
    user = FactoryBot.create(:user, business:)

    GiftCardMailer.with(business:, user:).error_feedback
  end

  def success_feedback
    project_config = FactoryBot.create(
      :project_config,
      name: "Sample Business",
      primary_color: "#378bdb"
    )
    business = FactoryBot.create(:business, project_config:)
    user = FactoryBot.create(:user, business:)

    GiftCardMailer.with(business:, user:).success_feedback
  end
end
