# Preview all emails at http://localhost:3000/rails/mailers

class SubscriptionMailerPreview < ActionMailer::Preview
  def approved
    business = FactoryBot.create(:business, :subscription)
    user = FactoryBot.create(:user, business:)

    DataGenerator::Subscription::Plan.process(business)

    plan = Plan.where(business_id: business.id).sample

    SubscriptionMailer.with(business:, user:).approved(plan:)
  end

  def canceled
    business = FactoryBot.create(:business, :subscription)
    user = FactoryBot.create(:user, business:)

    DataGenerator::Subscription::Plan.process(business)
    plan = Plan.where(business_id: business.id).sample

    SubscriptionMailer.with(business:, user:).canceled(plan:)
  end

  def payment_failed
    business = FactoryBot.create(:business, :subscription)
    user = FactoryBot.create(:user, business:)

    DataGenerator::Subscription::Plan.process(business)
    plan = Plan.where(business_id: business.id).sample

    SubscriptionMailer.with(business:, user:).payment_failed(plan:)
  end
end
