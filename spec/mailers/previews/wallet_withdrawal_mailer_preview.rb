# Preview all emails at http://localhost:3000/rails/mailers

class WalletWithdrawalMailerPreview < ActionMailer::Preview
  def point_transferred_success
    project_config = FactoryBot.create(
      :project_config,
      name: "Sample Business",
      primary_color: "#abcd49",
      email_sender: "<EMAIL>"
    )
    business = FactoryBot.create(:business, project_config:)
    user = FactoryBot.create(:user, business:)

    WalletWithdrawalMailer.with(business:, user:).point_transferred_success
  end

  def point_in_transfer
    project_config = FactoryBot.create(
      :project_config,
      name: "Sample Business",
      primary_color: "#abcd49",
      email_sender: "<EMAIL>"
    )
    business = FactoryBot.create(:business, project_config:)
    user = FactoryBot.create(:user, business:)

    WalletWithdrawalMailer.with(business:, user:).point_in_transfer
  end
end
