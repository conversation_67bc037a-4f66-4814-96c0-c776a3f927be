# Preview all emails at http://localhost:3000/rails/mailers

class WithdrawalTudoAzulMailerPreview < ActionMailer::Preview
  def success
    project_config = FactoryBot.create(
      :project_config,
      name: "Sample Business",
      primary_color: "#6A18E5",
      email_sender: "<EMAIL>"
    )
    business = FactoryBot.create(:business, project_config:)
    user = FactoryBot.create(:user, business:)

    WithdrawalTudoAzulMailer.with(business:, user:).success
  end

  def failed
    project_config = FactoryBot.create(
      :project_config,
      name: "Sample Business",
      primary_color: "#6A18E5",
      email_sender: "<EMAIL>"
    )
    business = FactoryBot.create(:business, project_config:)
    user = FactoryBot.create(:user, business:)

    WithdrawalTudoAzulMailer.with(business:, user:).failed
  end
end
