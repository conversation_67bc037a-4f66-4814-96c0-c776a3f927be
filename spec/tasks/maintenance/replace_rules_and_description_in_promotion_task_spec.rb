require "rails_helper"

RSpec.describe Maintenance::ReplaceRulesAndDescriptionInPromotionTask, type: :task do
  context "when there is no record" do
    it "replace values in rules" do
      task = Maintenance::ReplaceRulesAndDescriptionInPromotionTask.new
      promotion_one = create(:promotion, rules: "Xpto Alloyal Rules. ALLOYAL", description: "Alloyal, ALLOYAL")
      cupons_one = create_list(:cupon, 2, promotion: promotion_one, rules: "ALLOYAL @ Alloyal new Rules")
      task.to_replace = "Alloyal"
      task.concat_with = "{{client_name}}"

      task.process(promotion_one)

      expect(promotion_one.reload.rules).to eq("Xpto Alloyal/{{client_name}} Rules. ALLOYAL/{{client_name}}")
      expect(promotion_one.reload.description).to eq("Alloyal/{{client_name}}, ALLOYAL/{{client_name}}")
      expect(cupons_one.first.reload.rules).to eq("ALLOYAL/{{client_name}} @ Alloyal/{{client_name}} new Rules")
      expect(cupons_one.last.reload.rules).to eq("ALLOYAL/{{client_name}} @ Alloyal/{{client_name}} new Rules")
    end
  end
end
