require "rails_helper"

RSpec.describe Maintenance::NormalizeUserNamesTask, type: :task do
  subject(:task) { described_class.new }

  describe "#process" do
    context "when there is an user associated" do
      let!(:user) { create(:user,  name: "PEDRO DE LARA") }
      let!(:authorized_user) { create(:authorized_user, user: user, name: "PEDRO DE LARA") }
      let!(:user_two) { create(:user,  name: "JOAO DE LARA") }
      let!(:authorized_user_two) { create(:authorized_user, user: user, name: "JOAO DE LARA") }

      it "must normalize both user and auth user names" do
        task.process

        expect(authorized_user.reload.name).to eq("<PERSON>")
        expect(user.reload.name).to eq("<PERSON>")
        expect(authorized_user_two.reload.name).to eq("<PERSON><PERSON>")
        expect(user_two.reload.name).to eq("<PERSON><PERSON>")
      end
    end

    context "when there is not an user associated" do
      let!(:authorized_user) { create(:authorized_user, user: nil, name: "PEDRO DE LARA") }
      let!(:authorized_user_two) { create(:authorized_user, user: nil, name: "JOAO DE LARA") }

      it "must normalize auth user name" do
        task.process

        expect(authorized_user.reload.name).to eq("<PERSON> de <PERSON>")
        expect(authorized_user_two.reload.name).to eq("Joao de Lara")
      end
    end
  end
end
