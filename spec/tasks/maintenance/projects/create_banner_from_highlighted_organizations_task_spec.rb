# frozen_string_literal: true

require "rails_helper"

module Maintenance
  RSpec.describe Projects::CreateBannerFromHighlightedOrganizationsTask do
    let!(:business) { create(:business, home_banner: false) }
    let(:task) do
      task = described_class.new
      task.business_cnpj = business.cnpj
      task
    end

    describe "#process", :vcr do
      context "when business has hightlighted organizations" do
        let!(:organization) { create(:organization, banner_image: Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png"))) }
        let!(:highlight) { create(:highlight_organization, organization:, business:) }
        let!(:organization_two) { create(:organization, banner_image: Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png"))) }
        let!(:highlight_two) { create(:highlight_organization, organization: organization_two, business:) }
        let!(:existing_banner) { create(:banner, :external_link, business:, position: 1) }

        it "must create a banner for highlighted organizations, expire old banners and set home_banner flag as true" do
          task.process

          expect(business.banners.count).to eq(3)
          first_created_banner = business.banners.last(2).first
          last_created_banner = business.banners.last(2).second
          expect(first_created_banner.destination).to include("/organizations/").and include("/coupons")
          expect(last_created_banner.destination).to include("/organizations/").and include("/coupons")
          expect(first_created_banner.image).to be_present
          expect(last_created_banner.image).to be_present
          expect(last_created_banner.position).to eq(1)
          expect(first_created_banner.position).to eq(2)
          expect(existing_banner.reload.position).to eq(3)
          expect(existing_banner.starts_at).to be < 1.minute.after
          expect(existing_banner.ends_at).to be < 10.minutes.after
          expect(business.reload.home_banner).to eq(true)
        end
      end

      context "when business does not have hightlighted organizations" do
        let!(:organization_two) { create(:organization, banner_image: Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png"))) }
        let!(:existing_banner) { create(:banner, :external_link, business:, position: 1) }

        it "must expire old banners and set home_banner flag as false" do
          expect do
            task.process

            expect(business.banners.count).to eq(1)
            expect(existing_banner.reload.position).to eq(1)
            expect(existing_banner.ends_at).to be < 1.minute.after
            expect(business.reload.home_banner).to eq(false)
          end.to not_change(Navigation, :count)
        end
      end
    end
  end
end
