require "rails_helper"

RSpec.describe Maintenance::ResizeNavigationBannerImagesTask do
  let(:business) { create(:business, prize_draw: true) }
  let!(:fist_banner) { create(:banner, :prize_draw, business:) }
  let!(:second_banner) { create(:banner, :prize_draw, business:) }

  describe ".collection" do
    it "returns the collection of navigations with images" do
      expect(described_class.collection).to match_array([fist_banner, second_banner])
    end
  end

  describe "#process" do
    let(:navigation) { instance_double(Navigation) }
    let(:image_double) { double("image", recreate_versions!: true) }

    before do
      allow(navigation).to receive(:image).and_return(image_double)
    end

    it "recreates the versions of the navigation banner images" do
      described_class.new.process(navigation)

      expect(image_double).to have_received(:recreate_versions!)
    end
  end
end
