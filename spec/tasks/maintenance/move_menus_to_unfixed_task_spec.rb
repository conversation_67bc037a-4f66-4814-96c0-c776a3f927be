# frozen_string_literal: true

require "rails_helper"

module Maintenance
  RSpec.describe MoveMenusToUnfixedTask do
    describe "#process" do
      let!(:business) { create(:business) }

      before do
        create(:menu, business:, active: true, position: 1, fixed: true)
        create(:menu, business:, active: true, position: 2, fixed: true)
        create(:menu, business:, active: true, position: 3, fixed: true)
      end

      it "update menus to fixed false" do
        task = described_class.new
        expect { task.process }
          .to change(business.menus.where(fixed: false), :count).from(0).to(3)
      end
    end
  end
end
