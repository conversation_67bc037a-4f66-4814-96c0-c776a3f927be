# frozen_string_literal: true

require "rails_helper"

module Maintenance
  RSpec.describe CashInWalletTask do
    describe "#process" do
      let(:user) { create(:user, business:) }
      let(:user_cpf) { user.cpf }
      let(:business_cnpj) { business.cnpj }

      let(:inflow_wallet) { create(:wallet, :cashback, :inflow, currency:) }
      let(:business_wallet) { create(:wallet, :cashback, business:, currency:) }
      let(:user_wallet) { create(:wallet, :cashback, user:, currency:) }

      let!(:promotion) { create(:promotion, :online, cashback_type: :percent, cashback_value: 10, status: :available) }
      let!(:cupon) { create(:cupon, :online, promotion:) }

      let(:task) do
        task = described_class.new
        task.user_cpf = user_cpf
        task.business_cnpj = business_cnpj
        task
      end

      describe "cash in brl wallet" do
        let(:currency) { "BRL" }
        let(:business) { create(:business, :with_cashback, currency:) }

        it "cashes in brl wallet" do
          expect { task.process }
            .to change { user.cashback_records.available.count }.by(10)
            .and change { user_wallet.reload.balance }.by_at_least(10)
            .and not_change { business_wallet.reload.balance }
            .and not_change { inflow_wallet.reload.balance }
        end
      end

      describe "cash in point wallet" do
        let(:currency) { "points" }
        let(:business) { create(:business, :with_cashback, currency:, fair_value: 0.03) }

        it "cashes in point wallet" do
          expect { task.process }
            .to change { user.funds.points.count }.by(10)
            .and change { user_wallet.reload.balance }.by_at_least(10)
            .and not_change { business_wallet.reload.balance }
            .and not_change { inflow_wallet.reload.balance }
        end
      end
    end
  end
end
