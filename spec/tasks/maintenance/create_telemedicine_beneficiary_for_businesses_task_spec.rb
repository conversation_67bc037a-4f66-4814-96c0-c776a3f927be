# frozen_string_literal: true

require "rails_helper"

module Maintenance
  RSpec.describe CreateTelemedicineBeneficiaryForBusinessesTask do
    describe "#collection" do
      it "returns all enabled telemedicine beneficiaries without authorized user" do
        create(:telemedicine_beneficiary, enabled: false)
        create(:telemedicine_beneficiary, enabled: true)
        enabled_telemedicine_beneficiary_without_authorized_user = create(:telemedicine_beneficiary, enabled: true)
        disabled_telemedicine_beneficiary_without_authorized_user = create(:telemedicine_beneficiary, enabled: false)
        enabled_telemedicine_beneficiary_without_authorized_user.update_columns(authorized_user_id: nil)
        disabled_telemedicine_beneficiary_without_authorized_user.update_columns(authorized_user_id: nil)

        expect(described_class.collection).to match_array([enabled_telemedicine_beneficiary_without_authorized_user, disabled_telemedicine_beneficiary_without_authorized_user])
      end
    end

    describe "#process", :vcr do
      context "with enabled telemedicine beneficiary" do
        let!(:telemedicine_beneficiary) { create(:telemedicine_beneficiary) }
        let!(:authorized_user_with_same_cpf_on_another_business) { create(:authorized_user, cpf: telemedicine_beneficiary.cpf) }
        let!(:second_authorized_user_with_same_cpf_on_another_business) { create(:authorized_user, cpf: telemedicine_beneficiary.cpf) }
        let!(:inactive_authorized_user) { create(:authorized_user, cpf: telemedicine_beneficiary.cpf, active: false) }

        before do
          telemedicine_beneficiary.update_columns(authorized_user_id: nil)
        end

        it "must create telemedicine config and sync in third party" do
          expect(AuthorizedUser.active.count).to eq(3)
          expect(Telemedicine::ThirdPartyUpsertWorker).to receive(:perform_async).thrice
          expect(Telemedicine::ThirdPartyUpsertWorker).to receive(:perform_in).once
          expect(Telemedicine::ThirdPartyDeactivateWorker).to receive(:perform_in).once
          expect { described_class.process(telemedicine_beneficiary) }.to change(TelemedicineBeneficiary, :count).from(1).to(4)
          expect(TelemedicineBeneficiary.where(enabled: true).count).to eq(3)
          expect(TelemedicineBeneficiary.where(enabled: false).count).to eq(1)
        end
      end

      context "with disabled telemedicine beneficiary" do
        let!(:telemedicine_beneficiary) { create(:telemedicine_beneficiary, enabled: false) }
        let!(:authorized_user_with_same_cpf_on_another_business) { create(:authorized_user, cpf: telemedicine_beneficiary.cpf) }
        let!(:second_authorized_user_with_same_cpf_on_another_business) { create(:authorized_user, cpf: telemedicine_beneficiary.cpf) }

        before do
          telemedicine_beneficiary.update_columns(authorized_user_id: nil)
        end

        it "must create telemedicine config and sync in third party" do
          expect(AuthorizedUser.count).to eq(3)
          expect(Telemedicine::ThirdPartyUpsertWorker).not_to receive(:perform_async)
          expect(Telemedicine::ThirdPartyUpsertWorker).to receive(:perform_in).thrice
          expect(Telemedicine::ThirdPartyDeactivateWorker).to receive(:perform_in).thrice
          expect { described_class.process(telemedicine_beneficiary) }.to change(TelemedicineBeneficiary, :count).from(1).to(3)
          expect(TelemedicineBeneficiary.where(enabled: true).count).to eq(0)
          expect(TelemedicineBeneficiary.where(enabled: false).count).to eq(3)
        end
      end
    end
  end
end
