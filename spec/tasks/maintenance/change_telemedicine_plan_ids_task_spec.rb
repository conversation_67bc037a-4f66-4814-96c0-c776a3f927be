# frozen_string_literal: true

require "rails_helper"

module Maintenance
  RSpec.describe ChangeTelemedicinePlanIdsTask do
    describe "#process" do
      let(:copart_telemedicine_config) { create(:telemedicine_config, business: create(:business, create_telemedicine_config: false)) }
      let(:integral_telemedicine_config) { create(:telemedicine_config, business: create(:business, create_telemedicine_config: false)) }
      let(:integral_plus_telemedicine_config) { create(:telemedicine_config, business: create(:business, create_telemedicine_config: false)) }

      before do
        copart_telemedicine_config.update_columns(plan: 13)
        integral_telemedicine_config.update_columns(plan: 7)
        integral_plus_telemedicine_config.update_columns(plan: 25)
      end

      it "update menus to fixed false" do
        described_class.process
        expect(copart_telemedicine_config.reload.plan).to eq(Telemedicine::Plans::COPART)
        expect(integral_telemedicine_config.reload.plan).to eq(Telemedicine::Plans::INTEGRAL)
        expect(integral_plus_telemedicine_config.reload.plan).to eq(Telemedicine::Plans::ULTRA)
      end
    end
  end
end
