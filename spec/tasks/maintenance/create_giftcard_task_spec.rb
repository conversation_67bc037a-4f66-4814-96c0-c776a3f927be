# frozen_string_literal: true

require "rails_helper"

module Maintenance
  RSpec.describe CreateGiftcardTask do
    describe "#process" do
      let(:organization) { create(:organization) }
      let(:price) { 100 }
      let(:voucher_quantity) { 10 }

      let(:task) do
        task = described_class.new
        task.organization_id = organization.id
        task.price = price
        task.voucher_quantity = voucher_quantity
        task
      end

      it "creates giftcard and vouchers" do
        expect { task.process }
          .to change { organization.giftcards.available.where(price:).count }.by(1)
          .and change { organization.reload.giftcard_redeemable }.to(true)
          .and change { organization.giftcards.reload.sum { |g| g.voucher_bucket.vouchers.count } }.by(voucher_quantity)
      end
    end
  end
end
