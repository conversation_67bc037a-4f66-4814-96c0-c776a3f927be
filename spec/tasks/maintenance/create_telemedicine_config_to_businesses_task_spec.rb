# frozen_string_literal: true

require "rails_helper"

module Maintenance
  RSpec.describe CreateTelemedicineConfigToBusinessesTask do
    describe "#collection" do
      it "returns businesses that not have telemedicine_config" do
        _business_with_telemedicine_config = create_list(:business, 2)
        business_without_telemedicine_config = create_list(:business, 2, create_telemedicine_config: false)

        expect(described_class.collection).to match_array(business_without_telemedicine_config)
      end
    end

    describe "#process", :vcr do
      let(:business) { create(:business, create_telemedicine_config: false) }

      it "must create telemedicine config" do
        allow(Telemedicine::Plans).to receive(:contracted_beneficiaries_limits_for_plan).and_return([500])
        expect { described_class.process(business) }.to change(business, :telemedicine_config).from(nil)
        expect(business.telemedicine_config.plan).to eq(10091)
      end
    end
  end
end
