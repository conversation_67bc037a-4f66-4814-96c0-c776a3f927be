# frozen_string_literal: true

require "rails_helper"
require "sidekiq/testing"

RSpec.describe Shopkeeper::V2::PromotionsController, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  let!(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
  let!(:organization) { create(:organization) }
  let!(:organization_two) { create(:organization) }
  let!(:org_coupon_code) do
    create(:organization_promotion_provider,
      organization:,
      promotion_provider: Promotion::Provider::COUPON_CODE)
  end
  let!(:org_two_whatsapp) do
    create(:organization_promotion_provider,
      organization: organization_two,
      promotion_provider: Promotion::Provider::WHATSAPP)
  end
  let!(:branch_one) { create(:branch, organization:) }
  let!(:branch_two) { create(:branch, organization:) }
  let!(:branch_three) { create(:branch, organization:) }
  let!(:branch_four) { create(:branch, organization:) }
  let(:category_one) { create(:category, :promotion_category) }
  let(:category_two) { create(:category, :promotion_category) }
  let(:headers) { {"X-Shopkeeper-Email": shopkeeper.email, "X-Shopkeeper-Token": shopkeeper.authentication_token} }
  let(:physical_promotion_params) do
    {
      provider: Promotion::Provider::COUPON_CODE,
      redeem_type: Enums::PromotionRedeemType::DISCOUNT,
      description: "description",
      title: "title",
      discount: 10,
      start_date: DateTime.current,
      end_date: 1.year.from_now,
      start_hour: "09:00",
      end_hour: "18:00",
      days_of_week: %w[0 1 2 3 4 5 6],
      picture: "data:image/png;base64,#{Base64.encode64(Rails.root.join("spec/fixtures/samplefile.png").read)}",
      quantity: 10,
      infinity: false,
      rules: "Não tem regras",
      cumulative: false,
      redeems_per_cpf: 999,
      contract_custom_text: "Rede de Parcerias Qualquer",
      frequency_in_days: 1,
      category_ids: [category_one.id, category_two.id],
      sync_cashback_with_organization: false
    }
  end
  let(:online_promotion_params) do
    {
      provider: Promotion::Provider::WHATSAPP,
      redeem_type: Enums::PromotionRedeemType::DISCOUNT,
      url: FFaker::Internet.http_url,
      description: "description",
      title: "title",
      discount: 10,
      start_date: DateTime.current,
      end_date: 1.year.from_now,
      start_hour: "00:00:00",
      end_hour: "23:59:59",
      days_of_week: %w[0 1 2 3 4 5 6],
      picture: "data:image/png;base64,#{Base64.encode64(Rails.root.join("spec/fixtures/samplefile.png").read)}",
      quantity: 10,
      rules: "Não tem regras",
      cumulative: false,
      redeems_per_cpf: 999,
      frequency_in_days: 1,
      category_ids: [category_one.id, category_two.id]
    }
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "GET /shopkeeper/v2/promotions" do
    let!(:promotion_one) do
      create(:promotion,
        :percent_discount,
        provider: Promotion::Provider::COUPON_CODE,
        organization:,
        description: "promo top",
        title: "promo")
    end
    let!(:promotion_two) do
      create(:promotion,
        :percent_discount,
        organization: organization_two,
        provider: Promotion::Provider::CPF,
        description: "description 2",
        title: "titulo")
    end
    let!(:branch_five) { create(:branch, organization: organization_two) }
    let!(:branch_six) { create(:branch, organization: organization_two) }
    let!(:coupon) { create(:cupon, :cpf, :percent_discount, branch: branch_five, promotion: promotion_two) }
    let!(:coupon_two) { create(:cupon, :cpf, :percent_discount, branch: branch_six, promotion: promotion_two) }
    let(:serializer_keys) do
      %w[id description contract_custom_text provider rules cashback_rules code
        url discount discount_type quantity start_date end_date start_hour end_hour days_of_week
        cumulative only_debit only_physical redeems_per_cpf one_for_cpf one_per_day infinity
        price average_ticket frequency_in_days integration_code integration_partner
        status redeem_type business organization picture_small_url picture_large_url title dynamic_voucher
        closed_interval cashback_value cashback_type sync_cashback_with_organization redeemed_count usage_instruction]
    end

    it "returns the promotions" do
      get(shopkeeper_v2_promotions_url, headers:)

      expect(response).to be_successful
      expect(response_hash.count).to eq(2)
      expect(response_hash.first.keys).to match_array(serializer_keys)
      expect(response_hash.pluck("discount")).to match_array([30, 30])
    end

    it "returns the promotions filtered by branch id" do
      get(shopkeeper_v2_promotions_url(branch_id: branch_five.id), headers:)

      expect(response).to be_successful
      expect(response_hash.count).to eq(1)
      expect(response_hash.first.keys).to match_array(serializer_keys)
      expect(response_hash.first["id"]).to eq(promotion_two.id)
      expect(response_hash.pluck("discount")).to match_array([30])
    end

    it "returns the promotions filtered by organization id" do
      get(shopkeeper_v2_promotions_url(organization_id: organization_two.id), headers:)

      expect(response).to be_successful
      expect(response_hash.count).to eq(1)
      expect(response_hash.first.keys).to match_array(serializer_keys)
      expect(response_hash.first["id"]).to eq(promotion_two.id)
      expect(response_hash.pluck("discount")).to match_array([30])
    end

    it "returns the promotions filtered by provider" do
      get(shopkeeper_v2_promotions_url(provider: Promotion::Provider::CPF), headers:)

      expect(response).to be_successful
      expect(response_hash.count).to eq(1)
      expect(response_hash.first.keys).to match_array(serializer_keys)
      expect(response_hash.first["id"]).to eq(promotion_two.id)
      expect(response_hash.pluck("discount")).to match_array([30])
    end

    it "returns the promotions filtered by description" do
      get(shopkeeper_v2_promotions_url(description: "desc"), headers:)

      expect(response).to be_successful
      expect(response_hash.count).to eq(1)
      expect(response_hash.first.keys).to match_array(serializer_keys)
      expect(response_hash.first["id"]).to eq(promotion_two.id)
      expect(response_hash.pluck("discount")).to match_array([30])
    end

    it "returns the promotions filtered by title" do
      get(shopkeeper_v2_promotions_url(title: "titu"), headers:)

      expect(response).to be_successful
      expect(response_hash.pluck("id")).to eq([promotion_two.id])
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("discount")).to match_array([30])
    end

    it "returns the promotions ordered by order_type" do
      get("/shopkeeper/v2/promotions",
        params: {order_type: Enums::Promotion::OrderType::BIGGEST_DISCOUNT},
        headers:)

      expect(response).to be_successful
      expect(response_hash.pluck("id")).to match_array([promotion_two.id, promotion_one.id])
    end

    context "when the current shokpeeper is not admin lecupon" do
      let!(:shopkeeper) { create(:shopkeeper, :admin_shopkeeper) }

      before do
        shopkeeper.branches = [branch_five]
      end

      it "returns only the promotions the current shopkeeper has access to" do
        get(shopkeeper_v2_promotions_url, headers:)

        expect(response).to be_successful
        expect(response_hash.count).to eq(1)
        expect(response_hash.first.keys).to match_array(serializer_keys)
        expect(response_hash.first["id"]).to eq(promotion_two.id)
        expect(response_hash.pluck("discount")).to match_array([30])
      end
    end
  end

  describe "GET /shopkeeper/v2/promotions/:id" do
    let!(:promotion_one) { create(:promotion, :percent_discount, organization:, description: "description 1") }
    let!(:promotion_two) { create(:promotion, :percent_discount, organization: organization_two, description: "description 2") }
    let!(:branch_five) { create(:branch, organization: organization_two) }
    let!(:coupon) { create(:cupon, :cpf, :percent_discount, branch: branch_five, promotion: promotion_two) }
    let(:serializer_keys) do
      %w[id description contract_custom_text provider rules cashback_rules code
        url discount discount_type quantity start_date end_date start_hour end_hour days_of_week
        cumulative only_debit only_physical redeems_per_cpf one_for_cpf one_per_day infinity
        price average_ticket frequency_in_days integration_code integration_partner
        status redeem_type business organization picture_small_url picture_large_url title dynamic_voucher
        closed_interval cashback_value cashback_type sync_cashback_with_organization redeemed_count usage_instruction]
    end

    it "returns the promotion" do
      get("/shopkeeper/v2/promotions/#{promotion_two.id}", headers:)

      expect(response).to be_successful
      expect(JSON.parse(response.body).keys).to match_array(serializer_keys)
      expect(response_hash["discount"]).to eq(30)
    end

    context "when the current shokpeeper is not admin lecupon" do
      let!(:promotion_three) { create(:promotion, :percent_discount, organization:, description: "description 3") }
      let!(:coupon_two) { create(:cupon, :cpf, :percent_discount, branch: branch_one, promotion: promotion_three) }
      let!(:promotion_four) { create(:promotion, organization:, description: "description 4") }
      let!(:branch_six) { create(:branch, organization:) }
      let!(:coupon_three) { create(:cupon, :cpf, :percent_discount, branch: branch_six, promotion: promotion_four) }
      let!(:shopkeeper) { create(:shopkeeper, :admin_shopkeeper) }

      context "with branches associated" do
        before do
          shopkeeper.branches = [branch_five]
        end

        it "returns only the promotions the current shopkeeper has access to" do
          get("/shopkeeper/v2/promotions/#{promotion_two.id}", headers:)
          expect(response).to be_successful

          get("/shopkeeper/v2/promotions/#{promotion_one.id}", headers:)
          expect(response).not_to be_successful
        end
      end

      describe "creator of the promotion" do
        before do
          shopkeeper.branches = [branch_one]
          promotion_one.update!(creator_id: shopkeeper.id)
        end

        it "returns only the promotions of which the current shopkeeper is the creator and has access to" do
          get("/shopkeeper/v2/promotions/#{promotion_one.id}", headers:)
          expect(response).to be_successful

          get("/shopkeeper/v2/promotions/#{promotion_two.id}", headers:)
          expect(response).not_to be_successful

          get("/shopkeeper/v2/promotions/#{promotion_three.id}", headers:)
          expect(response).to be_successful

          get("/shopkeeper/v2/promotions/#{promotion_four.id}", headers:)
          expect(response).not_to be_successful
        end
      end
    end
  end

  describe "POST /shopkeeper/v2/promotions/:id/import_vouchers" do
    let(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
    let(:headers) { {"X-Shopkeeper-Email": shopkeeper.email, "X-Shopkeeper-Token": shopkeeper.authentication_token} }

    describe "voucher import" do
      let(:file) { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/vouchers.csv")) }
      let(:promotion) { create(:promotion, :coupon_code, :percent_discount) }
      let(:params) { {file:} }

      it "returns a successful response" do
        post("/shopkeeper/v2/promotions/#{promotion.id}/import_vouchers", params:, headers:)

        expect(response).to be_successful
      end
    end

    context "when the promotion does not accept voucher import" do
      let(:file) { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/vouchers.csv")) }
      let(:promotion) { create(:promotion, :whatsapp) }
      let(:params) { {file:} }

      it "returns error" do
        post("/shopkeeper/v2/promotions/#{promotion.id}/import_vouchers", params:, headers:)

        expect(response).to be_unprocessable
      end
    end
  end

  describe "GET /shopkeeper/v2/promotions/:id/should_import_vouchers" do
    let(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
    let(:headers) { {"X-Shopkeeper-Email": shopkeeper.email, "X-Shopkeeper-Token": shopkeeper.authentication_token} }

    context "when the promotion accepts voucher import" do
      let(:promotion) { create(:promotion, :lbc_giftcard) }

      it "returns true" do
        get("/shopkeeper/v2/promotions/#{promotion.id}/should_import_vouchers", headers:)

        expect(response).to be_successful
        expect(response_hash["should_import"]).to eq(true)
      end
    end

    context "when the promotion does not accept voucher import" do
      let(:promotion) { create(:promotion, :whatsapp) }

      it "returns false" do
        get("/shopkeeper/v2/promotions/#{promotion.id}/should_import_vouchers", headers:)

        expect(response).to be_successful
        expect(response_hash["should_import"]).to eq(false)
      end
    end
  end

  describe "GET /shopkeeper/v2/promotions/:id/vouchers" do
    let(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
    let(:headers) { {"X-Shopkeeper-Email": shopkeeper.email, "X-Shopkeeper-Token": shopkeeper.authentication_token} }
    let(:file) { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/vouchers.csv")) }
    let!(:promotion) { create(:promotion, :coupon_code) }
    let(:serializer_keys) do
      %w[id token cpf description began_at expired_at redeemed_at]
    end

    before do
      Sidekiq::Testing.inline! do
        post "/shopkeeper/v2/promotions/#{promotion.id}/import_vouchers", params: {file:}, headers:
      end
    end

    it "returns the vouchers of the promotion" do
      get("/shopkeeper/v2/promotions/#{promotion.id}/vouchers", headers:)

      expect(response).to be_successful
      expect(response_hash.count).to eq(2)
      expect(response_hash.first.keys).to match_array(serializer_keys)
    end

    it "returns the vouchers filtered by token" do
      get("/shopkeeper/v2/promotions/#{promotion.id}/vouchers?token=7e57", headers:)

      expect(response).to be_successful
      expect(response_hash.pluck("token")).to match_array(["778f7e57ff056c70422b2667b50a888a"])
      expect(response_hash.first.keys).to match_array(serializer_keys)
    end
  end

  describe "GET /shopkeeper/v2/promotions/:id/orders" do
    let(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
    let(:headers) { {"X-Shopkeeper-Email": shopkeeper.email, "X-Shopkeeper-Token": shopkeeper.authentication_token} }
    let!(:promotion) { create(:promotion, :percent_discount, :coupon_code) }
    let!(:promotion_two) { create(:promotion, :percent_discount, :dynamic_coupon_code) }
    let!(:organization) { create(:organization) }
    let!(:branch) { create(:branch, organization:) }
    let!(:coupon_one) { create(:cupon, :classic, :percent_discount, code: "ABC", branch:, promotion:) }
    let!(:coupon_two) { create(:cupon, :classic, :percent_discount, branch:, promotion: promotion_two) }
    let!(:coupon_three) { create(:cupon, :classic, :percent_discount, code: "CDE", branch:, promotion:) }
    let!(:branch_online) { create(:branch, :online, organization:) }
    let!(:coupon_online_one) { create(:cupon, :online, :percent_discount, code: "XYZ", branch: branch_online, promotion: promotion_two) }
    let!(:coupon_online_two) { create(:cupon, :online, :percent_discount, branch: branch_online, promotion:) }
    let!(:coupon_online_three) { create(:cupon, :online, :percent_discount, code: "AXY", branch: branch_online, promotion:) }
    let!(:user) { create(:user) }
    let!(:order_one) { create(:order, :percent_discount, user:, cupon: coupon_one, confirmation_key: coupon_one.branch.cnpj) }
    let!(:order_two) { create(:order, :percent_discount, user:, cupon: coupon_two, confirmation_key: coupon_two.branch.cnpj) }
    let!(:order_three) { create(:order, :percent_discount, user:, cupon: coupon_three, confirmation_key: coupon_three.branch.cnpj) }
    let!(:order_four) { create(:order, :percent_discount, user:, cupon: coupon_online_one) }
    let!(:order_five) { create(:order, :percent_discount, user:, cupon: coupon_online_two) }
    let!(:order_six) { create(:order, :percent_discount, user:, cupon: coupon_online_three) }

    let(:serializer_keys) do
      %w[id number coupon_description coupon_discount coupon_number
        organization_name branch_name created_at user_name business_name description discount redeem_code]
    end

    it "returns the orders of the promotion" do
      get("/shopkeeper/v2/promotions/#{promotion.id}/orders", headers:)

      expect(response).to be_successful
      expect(response_hash.first.keys).to match_array(serializer_keys)
      expect(response_hash.pluck("discount")).to match_array(["30", "30", "30", "30"])
      expect(response_hash.pluck("coupon_discount")).to match_array(["30", "30", "30", "30"])
      expect(response_hash.count).to eq(4)
    end
  end

  describe "DELETE /shopkeeper/v2/promotions/:id/vouchers/:voucher_id" do
    let(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
    let(:headers) { {"X-Shopkeeper-Email": shopkeeper.email, "X-Shopkeeper-Token": shopkeeper.authentication_token} }
    let(:file) { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/vouchers.csv")) }
    let!(:promotion) { create(:promotion, :coupon_code) }

    before do
      Sidekiq::Testing.inline! do
        post "/shopkeeper/v2/promotions/#{promotion.id}/import_vouchers", params: {file:}, headers:
      end

      @voucher = Voucher.first
    end

    it "removes the vouchers of the promotion" do
      delete("/shopkeeper/v2/promotions/#{promotion.id}/vouchers/#{@voucher.id}", headers:)

      expect(response).to be_successful
      expect(Voucher.where(id: @voucher.id)).not_to exist
    end

    context "when the voucher is redeemed" do
      before do
        @voucher.update!(redeemed_at: 2.days.ago)
      end

      it "returns error" do
        delete("/shopkeeper/v2/promotions/#{promotion.id}/vouchers/#{@voucher.id}", headers:)

        expect(response).not_to be_successful
        expect(Voucher.where(id: @voucher.id)).to exist
      end
    end
  end

  describe "DELETE /shopkeeper/v2/promotions/:id/orders" do
    let(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
    let(:headers) { {"X-Shopkeeper-Email": shopkeeper.email, "X-Shopkeeper-Token": shopkeeper.authentication_token} }
    let(:file) { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/coupon_codes_make_available.csv")) }
    let(:params) { {file:, password: "8K&]kb[#<PQXPC{e"} }
    let!(:bucket) { create(:voucher_bucket) }
    let!(:promotion) { create(:promotion, :lbc_giftcard, :available, voucher_bucket: bucket) }
    let!(:coupon) { create(:cupon, :with_branch, promotion:) }
    let!(:user) { create(:user) }
    let!(:voucher) do
      create(:voucher, code: "ASDA124", expired_at: 4.months.ago, bucket:)
    end
    let!(:order) { create(:order, voucher:, cupon: coupon, user:, redeem_code: "ASDA124") }

    before do
      travel_to(Time.zone.parse("2020-01-01 10:00"))
    end

    it "removes the orders of the promotion" do
      Sidekiq::Testing.inline! do
        delete("/shopkeeper/v2/promotions/#{promotion.id}/orders", headers:, params:)

        expect(response).to be_successful
        expect(voucher.redeemed_at).to eq(nil)
        expect(Order.where(id: order.id)).not_to exist
      end
    end

    context "with wrong password" do
      before do
        params.merge!(password: "wrong")
      end

      it "returns error" do
        delete("/shopkeeper/v2/promotions/#{promotion.id}/orders", headers:, params:)

        expect(response).not_to be_successful
      end
    end
  end
end
