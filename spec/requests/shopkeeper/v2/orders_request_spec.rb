# frozen_string_literal: true

require "rails_helper"

RSpec.describe Shopkeeper::V2::OrdersController, type: :request do
  let(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
  let(:headers) { {"X-Shopkeeper-Email": shopkeeper.email, "X-Shopkeeper-Token": shopkeeper.authentication_token} }
  let!(:promotion_one) { create(:promotion, provider: Promotion::Provider::COUPON_CODE) }
  let!(:promotion_two) { create(:promotion, provider: Promotion::Provider::LBC_GIFTCARD) }
  let!(:organization) { create(:organization) }
  let!(:branch) { create(:branch, organization:) }
  let!(:coupon_one) { create(:cupon, :classic, code: "ABC", branch:, promotion: promotion_one) }
  let!(:coupon_two) { create(:cupon, :classic, branch:, promotion: promotion_two) }
  let!(:coupon_three) { create(:cupon, :classic, code: "XYZ", branch:, promotion: promotion_one) }
  let!(:branch_online) { create(:branch, :online, organization:) }
  let!(:coupon_online_one) { create(:cupon, :online, branch: branch_online, promotion: promotion_two) }
  let!(:coupon_online_two) { create(:cupon, :online, code: "PTQ", branch: branch_online, promotion: promotion_one) }
  let!(:coupon_online_three) { create(:cupon, :online, code: "ASD", branch: branch_online, promotion: promotion_one) }
  let!(:lecupon) { create(:business, :lecupon, :with_cashback) }
  let!(:publicar) { create(:business, :publicar) }
  let!(:oab) { create(:business, :oab) }
  let!(:user_one) { create(:user, business: lecupon, cpf: "***********") }
  let!(:user_two) { create(:user, business: oab, cpf: "***********") }
  let!(:user_three) { create(:user, business: publicar, cpf: "***********") }
  let!(:order_one) do
    create(:order,
      :percent_discount,
      number: "LC123",
      redeem_code: "ABC123",
      user: user_one,
      cupon: coupon_one,
      confirmation_key: coupon_one.branch.cnpj,
      created_at: "2021-11-28 10:00:00")
  end
  let!(:order_two) do
    create(:order,
      :percent_discount,
      number: "LC456",
      redeem_code: "DEF456",
      user: user_two,
      cupon: coupon_two,
      confirmation_key: coupon_two.branch.cnpj,
      created_at: "2021-11-28 10:00:01")
  end
  let!(:order_three) do
    create(:order,
      :percent_discount,
      number: "LC716",
      redeem_code: "GHI789",
      user: user_one,
      cupon: coupon_three,
      confirmation_key: coupon_three.branch.cnpj,
      created_at: "2021-11-28 10:00:02")
  end
  let!(:order_four) do
    create(:order,
      :percent_discount,
      :online,
      number: "LC012",
      redeem_code: "JKL012",
      user: user_one,
      cupon: coupon_online_one,
      created_at: "2021-11-28 10:00:03")
  end
  let!(:order_five) do
    create(:order,
      :percent_discount,
      number: "LC189",
      redeem_code: "MNEF45",
      user: user_two,
      cupon: coupon_online_two,
      business: oab,
      created_at: "2021-11-28 10:00:04")
  end
  let!(:order_six) do
    create(:order,
      :percent_discount,
      number: "LC481",
      redeem_code: "PQR678",
      user: user_three,
      cupon: coupon_online_three,
      created_at: "2021-11-28 10:00:05")
  end
  let!(:order_seven) do
    create(:order,
      :percent_discount,
      :online,
      number: "LC825",
      redeem_code: "STU901",
      user: user_one,
      cupon: coupon_online_two,
      created_at: "2021-11-28 10:00:06")
  end
  let!(:cashback_one) do
    create(:cashback_record,
      order: order_four,
      order_amount: 50,
      total_commission_amount: 5,
      business_spread_amount: 0.5,
      cashback_amount: 4.5)
  end
  let!(:cashback_two) do
    create(:cashback_record,
      order: order_seven,
      order_amount: 120,
      total_commission_amount: 12,
      business_spread_amount: 1.2,
      cashback_amount: 10.8)
  end
  let!(:order_four_another_cashback) do
    create(:cashback_record,
      external_id: "asd",
      order: order_four,
      order_amount: 50,
      total_commission_amount: 5,
      business_spread_amount: 0.5,
      cashback_amount: 4.5)
  end

  let(:serializer_keys) do
    %w[id number coupon_description coupon_discount
      organization_name branch_name user_name user_cpf business_name promotion_provider
      cashback_amount created_at order_number redeem_type description discount redeem_code]
  end

  describe "GET /shopkeeper/v2/orders" do
    it "returns the orders filtered by promotion provider" do
      get("/shopkeeper/v2/orders?promotion_provider=#{Promotion::Provider::LBC_GIFTCARD}", headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to eq([order_four.id, order_two.id])
      expect(response_hash[0].keys).to match_array(serializer_keys)
      expect(response_hash.pluck("coupon_discount")).to match_array(["30", "30"])
      expect(response_hash.pluck("cashback_amount")).to eq(["9.0", nil])
      expect(response_hash[0]["promotion_provider"]).to eq(Promotion::Provider::LBC_GIFTCARD)
      expect(response_hash[0]["user_cpf"]).to eq("409.836.010-11")
    end

    it "returns the orders filtered by order number or coupon number" do
      get("/shopkeeper/v2/orders?number=89", headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to eq([order_five.id])
      expect(response_hash[0].keys).to match_array(serializer_keys)
      expect(response_hash.pluck("coupon_discount")).to match_array(["30"])
    end

    it "returns the orders filtered by user cpf" do
      get("/shopkeeper/v2/orders?cpf=#{user_two.cpf}", headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to eq([order_five.id, order_two.id])
      expect(response_hash[0].keys).to match_array(serializer_keys)
      expect(response_hash.pluck("coupon_discount")).to match_array(["30", "30"])
      expect(response_hash[0]["user_cpf"]).to eq("469.309.110-38")
    end

    it "returns the orders filtered by business id" do
      get("/shopkeeper/v2/orders?business_id=#{lecupon.id}", headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to eq([order_seven.id, order_four.id, order_three.id, order_one.id])
      expect(response_hash[0].keys).to match_array(serializer_keys)
      expect(response_hash.pluck("coupon_discount")).to match_array(["30", "30", "30", "30"])
      expect(response_hash.pluck("cashback_amount")).to eq(["10.8", "9.0", nil, nil])
      expect(response_hash[0]["user_cpf"]).to eq("409.836.010-11")
    end

    context "when filtering by redeem type" do
      it "returns the orders filtered by redeem type" do
        get("/shopkeeper/v2/orders?redeem_type=#{Enums::OrderRedeemType::ONLINE}", headers:)

        expect(response).to be_successful
        response_hash = JSON.parse(response.body)
        expect(response_hash.pluck("id")).to eq([order_seven.id, order_six.id, order_five.id, order_four.id])
        expect(response_hash[0].keys).to match_array(serializer_keys)
        expect(response_hash.pluck("discount")).to match_array(["30", "30", "30", "30"])
        expect(response_hash.pluck("coupon_discount")).to match_array(["30", "30", "30", "30"])
        expect(response_hash.pluck("cashback_amount")).to eq(["10.8", nil, nil, "9.0"])
        expect(response_hash[0]["user_cpf"]).to eq("409.836.010-11")
      end
    end

    context "when current shopkeeper is not admin lecupon" do
      let(:shopkeeper) { create(:shopkeeper, :admin_shopkeeper) }

      it "returns error" do
        get("/shopkeeper/v2/orders", headers:)

        expect(response).not_to be_successful
      end
    end
  end

  describe "DELETE /shopkeeper/v2/orders/:id" do
    it "returns successful" do
      delete("/shopkeeper/v2/orders/#{order_four.id}",
        params: {password: "8K&]kb[#<PQXPC{e"},
        headers:)

      expect(response).to be_successful
      expect(Order.where(id: order_four.id)).not_to exist
      expect(CashbackRecord.where(id: cashback_one.id)).not_to exist
    end

    context "with wrong password" do
      it "returns error" do
        delete("/shopkeeper/v2/orders/#{order_four.id}", headers:)

        expect(response).not_to be_successful
      end
    end
  end
end
