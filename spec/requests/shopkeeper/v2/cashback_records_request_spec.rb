# frozen_string_literal: true

require "rails_helper"

RSpec.describe Shopkeeper::V2::CashbackRecordsController, type: :request do
  let(:shopkeeper) { create(:shopkeeper, :admin_lecupon) }
  let(:headers) { {"X-Shopkeeper-Email": shopkeeper.email, "X-Shopkeeper-Token": shopkeeper.authentication_token} }

  let!(:organizations) { create_list(:organization, 5, highest_cashback_value: 10.0) }
  let!(:branch_one) { create(:branch, :online, organization: organizations[0]) }
  let!(:branch_two) { create(:branch, :online, organization: organizations[1]) }
  let!(:branch_three) { create(:branch, :online, organization: organizations[2]) }
  let!(:branch_four) { create(:branch, :online, organization: organizations[3]) }
  let!(:branch_five) { create(:branch, :online, organization: organizations[4]) }
  let!(:coupon_one) { create(:cupon, :online, branch: branch_one) }
  let!(:coupon_two) { create(:cupon, :online, branch: branch_two) }
  let!(:coupon_three) { create(:cupon, :online, branch: branch_three) }
  let!(:coupon_four) { create(:cupon, :online, branch: branch_four) }
  let!(:coupon_five) { create(:cupon, :online, branch: branch_five) }
  let!(:coupon_six) { create(:cupon, :online, branch: branch_four) }
  let!(:giftcard) { create(:giftcard, :with_vouchers, branches: [branch_two]) }
  let!(:user_one) { create(:user, :with_cashback, cpf: "63993911059") }
  let!(:user_two) { create(:user, :with_cashback, cpf: "25012279050") }
  let!(:user_three) { create(:user, :with_cashback, cpf: "19293775093") }
  let!(:user_four) { create(:user, :with_cashback, cpf: "60212688065") }
  let!(:user_five) { create(:user, :with_cashback, cpf: "31441855076") }
  let!(:user_six) { create(:user, :with_cashback, cpf: "70881377040") }
  let!(:user_seven) { create(:user, :with_cashback, cpf: "86530206086") }
  let!(:order_one) do
    create(:order, :online, number: "LC987", cupon: coupon_one, redeem_code: "PROMO10", user: user_one)
  end
  let!(:order_two) do
    create(:order, :online, number: "LC986", cupon: coupon_two, redeem_code: "PROMO5", user: user_two)
  end
  let!(:order_three) do
    create(:order, :online, number: "LC134", cupon: coupon_three, redeem_code: "LECUPON10", user: user_three)
  end
  let!(:order_four) do
    create(:order, :online, number: "LC456", cupon: coupon_four, redeem_code: "LECUPON20", user: user_four)
  end
  let!(:order_five) do
    create(:order, :online, number: "LC123", cupon: coupon_five, redeem_code: "AMZN30", user: user_five)
  end
  let!(:order_six) do
    create(:order, :online, number: "LC887", cupon: coupon_six, redeem_code: "CUPOM20", user: user_six)
  end
  let!(:order_seven) do
    create(:order, :online, number: "LC321", cupon: coupon_two, redeem_code: "PROMOLECUPON10", user: user_seven)
  end
  let!(:order_eight) do
    create(:order, :online, number: "LC856", cupon: coupon_four, redeem_code: "PROMO20", user: user_one)
  end
  let!(:order_giftcard) do
    bucket = create(:voucher_bucket)
    voucher = create(:voucher, bucket:)
    create(:order,
      :online,
      :with_giftcard,
      user: user_seven,
      giftcard:,
      business: user_seven.business,
      organization: organizations[1],
      voucher:)
  end
  let!(:cashback_one) do
    create(:cashback_record, order: order_one, batch: "ABC123", created_at: "2021-11-28 10:00:00", user: user_one)
  end
  let!(:cashback_two) do
    create(:cashback_record, order: order_two, batch: "ABC123", created_at: "2021-11-28 10:00:01", user: user_two)
  end
  let!(:cashback_three) do
    create(:cashback_record, order: order_three, batch: "DEF456", created_at: "2021-11-28 10:00:02", user: user_three)
  end
  let!(:cashback_four) do
    create(:cashback_record, order: order_four, batch: "ABC123", created_at: "2021-11-28 10:00:03", user: user_four)
  end
  let!(:cashback_five) do
    create(:cashback_record, order: order_five, batch: "DEF456", created_at: "2021-11-29 10:00:04", user: user_five)
  end
  let!(:cashback_six) do
    create(:cashback_record, order: order_six, batch: "GHI789", created_at: "2021-11-30 10:00:05", user: user_six)
  end
  let!(:cashback_seven) do
    create(:cashback_record, order: order_seven, batch: "GHI789", created_at: "2021-12-01 10:00:06", user: user_seven)
  end
  let!(:cashback_eight) do
    create(:cashback_record, order: order_eight, batch: "JKL012", created_at: "2021-12-01 10:00:07", user: user_three)
  end
  let!(:cashback_giftcard) do
    create(:cashback_record, order: order_giftcard, created_at: "2022-01-01 10:00:07", user: user_seven)
  end

  let(:serializer_keys) do
    %w[id order_number order_id cashback_amount order_amount organization_name branch_name
      batch transaction_status created_at]
  end

  describe "GET /shopkeeper/v2/cashback_records" do
    it "returns the cashback records filtered by organization id" do
      get("/shopkeeper/v2/cashback_records?organization_id=#{organizations[1].id}", headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to eq([cashback_giftcard.id, cashback_seven.id, cashback_two.id])
      expect(response_hash[0].keys).to match_array(serializer_keys)
    end

    it "returns the cashback records filtered by organization id except those with business cahsback disabled" do
      cashback_seven.order.update_columns(business_cashback: false)

      get("/shopkeeper/v2/cashback_records?organization_id=#{organizations[1].id}", headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to eq([cashback_giftcard.id, cashback_two.id])
      expect(response_hash.map(&:keys)).to all match_array serializer_keys
    end

    it "returns the cashback records filtered by branch id" do
      get("/shopkeeper/v2/cashback_records?branch_id=#{branch_two.id}", headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to eq([cashback_giftcard.id, cashback_seven.id, cashback_two.id])
      expect(response_hash[0].keys).to match_array(serializer_keys)
    end

    it "returns the cashback records filtered by order number" do
      get("/shopkeeper/v2/cashback_records?order_number=87", headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to eq([cashback_six.id, cashback_one.id])
      expect(response_hash[0].keys).to match_array(serializer_keys)
    end

    it "returns the cashback records filtered by user cpf" do
      get("/shopkeeper/v2/cashback_records?cpf=377", headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to eq([cashback_eight.id, cashback_six.id, cashback_three.id])
      expect(response_hash[0].keys).to match_array(serializer_keys)
    end

    it "returns the cashback records filtered by created date" do
      get("/shopkeeper/v2/cashback_records?created_at=2021-11-28", headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to eq([cashback_four.id, cashback_three.id, cashback_two.id, cashback_one.id])
      expect(response_hash[0].keys).to match_array(serializer_keys)
    end

    it "returns the cashback records filtered by batch" do
      get("/shopkeeper/v2/cashback_records?batch=DEF456", headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.pluck("id")).to eq([cashback_five.id, cashback_three.id])
      expect(response_hash[0].keys).to match_array(serializer_keys)
    end

    context "when current shopkeeper is not admin lecupon" do
      let(:shopkeeper) { create(:shopkeeper, :admin_shopkeeper) }

      it "returns error" do
        get("/shopkeeper/v2/cashback_records", headers:)

        expect(response).not_to be_successful
      end
    end
  end

  describe "POST /shopkeeper/v2/cashback_records" do
    let!(:order_nine) { create(:order, :online) }
    let(:params) do
      {
        order_id: order_nine.id,
        transaction_status: Enums::CashbackRecordStatus::APPROVED,
        order_amount: 212.88,
        password: "8K&]kb[#<PQXPC{e"
      }
    end

    before do
      allow(CashbackRecord::History::NotificationService).to receive(:call)
    end

    it "returns successful and creates cashback" do
      expect {
        post "/shopkeeper/v2/cashback_records", params:, headers:
      }.to change(CashbackRecord, :count).by(1)

      expect(CashbackRecord::History::NotificationService).to have_received(:call)

      expect(response).to have_http_status(:created)
      response_hash = JSON.parse(response.body)
      expect(response_hash.keys).to match_array(serializer_keys)
      expect(response_hash["transaction_status"]).to eq("Aprovado")
      expect(response_hash["order_amount"]).to eq(212.88)
    end

    it "returns successful and creates cashback but does not send notification when business has cashback disabled" do
      Order.update_all(business_cashback: false)

      expect {
        post "/shopkeeper/v2/cashback_records", params:, headers:
      }.to change(CashbackRecord, :count).by(1)

      expect(CashbackRecord::History::NotificationService).not_to have_received(:call)

      expect(response).to have_http_status(:created)
      response_hash = JSON.parse(response.body)
      expect(response_hash.keys).to match_array(serializer_keys)
      expect(response_hash["transaction_status"]).to eq("Aprovado")
      expect(response_hash["order_amount"]).to eq(212.88)
    end

    context "with unexisting order" do
      it "returns error" do
        params[:order_id] = 0

        post("/shopkeeper/v2/cashback_records", params:, headers:)

        expect(response).to have_http_status(:not_found)

        expect(CashbackRecord::History::NotificationService).not_to have_received(:call)
      end
    end

    context "with missing params" do
      it "returns error" do
        post("/shopkeeper/v2/cashback_records", params: params.except(:order_id), headers:)

        expect(response).to have_http_status(:precondition_failed)

        expect(CashbackRecord::History::NotificationService).not_to have_received(:call)
      end
    end

    context "with wrong password" do
      it "returns error" do
        post("/shopkeeper/v2/cashback_records", params: params.except(:password), headers:)

        expect(response).not_to be_successful

        expect(CashbackRecord::History::NotificationService).not_to have_received(:call)
      end
    end

    context "when current shopkeeper is not admin lecupon" do
      let(:shopkeeper) { create(:shopkeeper, :admin_shopkeeper) }

      it "returns error" do
        post("/shopkeeper/v2/cashback_records", params:, headers:)

        expect(response).to have_http_status(:forbidden)

        expect(CashbackRecord::History::NotificationService).not_to have_received(:call)
      end
    end
  end

  describe "POST /shopkeeper/v2/cashback_records/import" do
    let(:file) { Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/cashback_import.csv")) }
    let(:params) { {file:, password: "8K&]kb[#<PQXPC{e"} }

    it "returns a successful response" do
      post("/shopkeeper/v2/cashback_records/import", params:, headers:)

      expect(response).to be_successful
    end

    describe "validation" do
      let(:params) { {file:, validate: true, password: "8K&]kb[#<PQXPC{e"} }
      let(:serializer_keys) { %w[order_number transaction_status order_amount] }

      it "returns a successful import scheduled response" do
        post("/shopkeeper/v2/cashback_records/import", params:, headers:)

        expect(response).to be_successful
        response_hash = JSON.parse(response.body)
        expect(response_hash["message"]).to eq("A importação de cashback está em processamento")
      end

      context "when current shopkeeper is not admin lecupon" do
        let(:shopkeeper) { create(:shopkeeper, :admin_shopkeeper) }

        it "returns error" do
          post("/shopkeeper/v2/cashback_records/import", params:, headers:)

          expect(response).not_to be_successful
        end
      end

      context "with wrong password" do
        it "returns error" do
          post("/shopkeeper/v2/cashback_records/import", params: params.except(:password), headers:)

          expect(response).not_to be_successful
        end
      end
    end
  end

  describe "DELETE /shopkeeper/v2/cashback_records" do
    let(:batch_params) { {batch: "DEF456", password: "8K&]kb[#<PQXPC{e"} }
    let(:cashback_to_be_deleted_ids) { [cashback_three.id, cashback_five.id] }

    it "deletes cashback records by import batch" do
      delete("/shopkeeper/v2/cashback_records", params: batch_params, headers:)

      expect(response).to have_http_status(:ok)
      expect(CashbackRecord.where(id: cashback_to_be_deleted_ids)).not_to exist
    end

    it "deletes the cashback record by id" do
      delete("/shopkeeper/v2/cashback_records/#{cashback_three.id}",
        params: {password: "8K&]kb[#<PQXPC{e"},
        headers:)

      expect(response).to have_http_status(:ok)
      expect(CashbackRecord.where(id: cashback_three.id)).not_to exist
    end

    context "with missing params" do
      it "returns error" do
        delete("/shopkeeper/v2/cashback_records", params: {password: "8K&]kb[#<PQXPC{e"}, headers:)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(CashbackRecord.where(id: cashback_to_be_deleted_ids).count).to eq(2)
      end
    end

    context "when current shopkeeper is not admin lecupon" do
      let(:shopkeeper) { create(:shopkeeper, :admin_shopkeeper) }

      it "returns error" do
        delete("/shopkeeper/v2/cashback_records", params: batch_params, headers:)

        expect(response).to have_http_status(:forbidden)
        expect(CashbackRecord.where(id: cashback_to_be_deleted_ids).count).to eq(2)
      end
    end

    context "with wrong password" do
      it "returns error" do
        delete("/shopkeeper/v2/cashback_records", params: batch_params.except(:password), headers:)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(CashbackRecord.where(id: cashback_to_be_deleted_ids).count).to eq(2)
      end
    end
  end
end
