require "rails_helper"
require "sidekiq/testing"

RSpec.describe Client::V2::AuthorizedUsers::TagsController, type: :request do
  let!(:business) { create(:business, main_business:) }
  let!(:main_business) { create(:business) }
  let!(:another_business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:serialized_keys) { %w[id name] }
  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    let!(:authorized_user) do
      create(:authorized_user, name: "Any Name", business:, tags: ["Valid Tag 1", "Valid Tag 2"])
    end

    let!(:authorized_user_two) do
      create(:authorized_user, name: "Any Name", business:, tags: ["Valid Tag 3", "Valid Tag 4"])
    end

    let!(:authorized_user_in_another_business) do
      create(:authorized_user, name: "Any Name", business: another_business, tags: ["Invalid Tag 1", "Invalid Tag 2"])
    end

    it "returns all tags utilized by project's authorized users" do
      path = [
        "/client/v2/businesses/#{business.cnpj}/authorized_users/tags",
        "/client/v2/authorized_users/tags"
      ].sample
      get(path, params: {term: "Valid Tag"}, headers:)

      expect(response).to have_http_status(:ok)
      expect(response_hash.count).to eq(4)
      expect(response_hash).to match_array(["Valid Tag 1", "Valid Tag 2", "Valid Tag 3", "Valid Tag 4"])
      expect(response_hash).not_to include("Invalid Tag 1")
      expect(response_hash).not_to include("Invalid Tag 2")
    end
  end
end
