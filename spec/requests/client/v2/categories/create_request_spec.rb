# frozen_string_literal: true

require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::CategoriesController, type: :request do
  let(:business) { create(:business) }
  let(:another_business) { create(:business) }

  let!(:client_employee) { create(:client_employee, role: :admin_client, businesses: [business]) }
  let!(:lecupon_client_employee) { create(:client_employee, role: :admin_lecupon) }

  let(:admin_client_headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  let(:admin_lecupon_headers) do
    {
      "X-ClientEmployee-Email": lecupon_client_employee.email,
      "X-ClientEmployee-Token": lecupon_client_employee.authentication_token
    }
  end

  let(:response_hash) { JSON.parse(response.body) }
  let(:main_category) { create(:category) }

  describe "#create" do
    let(:params) do
      {
        title: "Custom Category",
        image_resolution: "160x60",
        image: "data:image/png;base64,#{Base64.encode64(Rails.root.join("spec/fixtures/samplefile.png").read)}",
        visible: true,
        category_type: Enums::Category::Type::PROMOTION,
        short_description: "foo description",
        template: "Banner 1",
        main_category_id: main_category.id
      }
    end
    context "when user is admin client with tenant id header" do
      it "must create a business exclusive category" do
        post("/client/v2/categories", headers: admin_client_headers, params:)

        expect(response).to be_created
        category = Category.last
        expect(category.image_url).not_to be_nil
        expect(category.deprecated_image_url).to be_nil
        expect(category.main_category).to eq(main_category)
        expect(category.business).to eq(business)
      end
    end

    context "when user is admin client without tenant id header" do
      context "when admin client has multiple businesses" do
        before do
          client_employee.businesses << create(:business)
        end

        it "must render forbidden" do
          post("/client/v2/categories", headers: admin_client_headers.excluding(:"Tenant-id"), params:)

          expect(response).to be_forbidden
        end
      end

      context "when admin client has only one business" do
        it "must create a business exclusive category" do
          post("/client/v2/categories", headers: admin_client_headers.excluding("Tenant-id"), params:)

          expect(response).to be_created
          category = Category.last
          expect(category.image_url).not_to be_nil
          expect(category.deprecated_image_url).to be_nil
          expect(category.main_category).to eq(main_category)
        end
      end
    end

    context "when user is admin lecupon with tenant id header" do
      it "must create a business exclusive category" do
        post("/client/v2/categories", headers: admin_lecupon_headers.merge("Tenant-id": business.cnpj), params:)

        expect(response).to be_created
        category = Category.last
        expect(category.image_url).not_to be_nil
        expect(category.deprecated_image_url).to be_nil
        expect(category.main_category).to eq(main_category)
        expect(category.business).to eq(business)
      end
    end

    context "when user is admin lecupon without tenant id header" do
      it "must create an alloyal category" do
        post("/client/v2/categories", headers: admin_lecupon_headers, params:)

        expect(response).to be_created
        category = Category.last
        expect(category.image_url).not_to be_nil
        expect(category.deprecated_image_url).to be_nil
        expect(category.main_category).to eq(main_category)
        expect(category.business).to eq(nil)
      end
    end

    context "with deprecated image" do
      let(:params) do
        {
          title: "Custom Category",
          image_resolution: "264x140",
          image: "data:image/png;base64,#{Base64.encode64(Rails.root.join("spec/fixtures/samplefile.png").read)}",
          visible: true,
          category_type: Enums::Category::Type::PROMOTION,
          short_description: "foo description",
          template: "Banner 1"
        }
      end

      it "creates business exclusive category" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories",
          "/client/v2/categories"
        ].sample
        post(path, headers: admin_client_headers, params:)

        expect(response).to be_created
        category = Category.last
        expect(category.image_url).to be_nil
        expect(category.deprecated_image_url).not_to be_nil
      end
    end
    context "with sub category as main category" do
      let(:sub_category) { create(:category, main_category: main_category) }

      let(:params) do
        {
          title: "Custom Category",
          image_resolution: "264x140",
          image: "data:image/png;base64,#{Base64.encode64(Rails.root.join("spec/fixtures/samplefile.png").read)}",
          visible: true,
          category_type: Enums::Category::Type::PROMOTION,
          short_description: "foo description",
          template: "Banner 1",
          main_category_id: sub_category.id
        }
      end

      it "must render error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories",
          "/client/v2/categories"
        ].sample
        post(path, headers: admin_client_headers, params:)

        expect(response).to be_unprocessable
        expect(response_hash["error"]).to include(/Não é possível definir uma subcategoria como categoria principal/)
      end
    end

    context "without client_employee authentication" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories",
          "/client/v2/categories"
        ].sample
        post path
      end

      it_behaves_like "unauthorized client_employee"
    end
  end
end
