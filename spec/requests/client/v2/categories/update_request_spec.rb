# frozen_string_literal: true

require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::CategoriesController, type: :request do
  let(:business) { create(:business) }
  let(:another_business) { create(:business) }

  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:main_category) { create(:category, business_id: business.id) }

  describe "#update" do
    let!(:category) { create(:category, business_id: business.id, template: "Banner 1", image: nil) }

    context "when exclusive category belongs to business" do
      let(:params) do
        {
          title: "New Custom Category",
          image_resolution: "160x60",
          image: "data:image/png;base64,#{Base64.encode64(Rails.root.join("spec/fixtures/samplefile.png").read)}",
          category_type: Enums::Category::Type::PROMOTION,
          short_description: "foo description",
          template: "Banner 2",
          main_category_id: main_category.id
        }
      end

      it "updates exclusive category" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/#{category.id}",
          "/client/v2/categories/#{category.id}"
        ].sample
        patch(path, headers:, params:)

        expect(response).to be_ok
        category.reload
        expect(category.title).to eq(params[:title])
        expect(category.template).to eq("Banner 2")
        expect(category.image_url).not_to be_nil
        expect(category.deprecated_image_url).to be_nil
        expect(category.main_category).to eq(main_category)
      end

      context "when inactivating a main category" do
        let!(:sub_category) { create(:category, main_category:, active: true) }
        let!(:sub_category_2) { create(:category, main_category:, active: true) }
        let(:params) do
          {
            active: false
          }
        end

        it "must deactivate its sub categories" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/categories/#{main_category.id}",
            "/client/v2/categories/#{main_category.id}"
          ].sample
          patch(path, headers:, params:)
          expect(response).to be_ok
          main_category.reload
          expect(main_category.sub_categories.pluck(:active)).to eq([false, false])
          expect(main_category.active).to eq(false)
        end
      end
      context "when activating a main category" do
        let(:main_category) { create(:category, business_id: business.id, active: false) }
        let!(:sub_category) { create(:category, main_category:, active: false) }
        let!(:sub_category_2) { create(:category, main_category:, active: false) }
        let(:params) do
          {
            active: true
          }
        end

        it "must activate its sub categories" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/categories/#{main_category.id}",
            "/client/v2/categories/#{main_category.id}"
          ].sample
          patch(path, headers:, params:)
          expect(response).to be_ok
          main_category.reload
          expect(main_category.sub_categories.pluck(:active)).to eq([true, true])
          expect(main_category.active).to eq(true)
        end
      end

      context "with new main category is the own category" do
        let(:params) do
          {
            main_category_id: main_category.id
          }
        end

        it "must render error" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/categories/#{main_category.id}",
            "/client/v2/categories/#{main_category.id}"
          ].sample
          patch(path, headers:, params:)

          expect(response).to be_unprocessable
          expect(response_hash["error"]).to include(/Não é possível definir a propria categoria como subcategoria/)
        end
      end
    end

    context "update deprecated image" do
      let(:params) do
        {
          title: "New Custom Category",
          image_resolution: "264x140",
          image: "data:image/png;base64,#{Base64.encode64(Rails.root.join("spec/fixtures/samplefile.png").read)}",
          category_type: Enums::Category::Type::PROMOTION,
          short_description: "foo description",
          template: "Banner 2"
        }
      end

      it "updates exclusive category" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/#{category.id}",
          "/client/v2/categories/#{category.id}"
        ].sample
        patch(path, headers:, params:)

        expect(response).to be_ok
        category.reload
        expect(category.image_url).to be_nil
        expect(category.deprecated_image_url).not_to be_nil
      end
    end

    context "when exclusive category doest not belong to business" do
      let!(:category) { create(:category, business_id: another_business.id) }

      it "renders not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/#{category.id}",
          "/client/v2/categories/#{category.id}"
        ].sample
        patch(path, headers:)

        expect(response).to be_not_found
      end
    end

    context "when client_employee not related to business" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/#{category.id}",
          "/client/v2/categories/#{category.id}"
        ].sample
        patch(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "without client_employee authentication" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/#{category.id}",
          "/client/v2/categories/#{category.id}"
        ].sample
        patch path
      end

      it_behaves_like "unauthorized client_employee"
    end

    context "when user is admin client with tenant id header" do
      before do
        client_employee.update(role: "admin_client")
      end

      it "must be successfull" do
        patch("/client/v2/categories/#{category.id}", headers:, params: {title: "New Title"})

        expect(response).to be_ok
      end
    end

    context "when user is admin client without tenant id header" do
      context "when admin client has multiple businesses" do
        before do
          client_employee.update!(role: :admin_client)
          client_employee.businesses << create(:business)
        end

        it "must render forbidden" do
          post("/client/v2/categories", headers: headers.excluding(:"Tenant-id"), params: {title: "New Title"})

          expect(response).to be_forbidden
        end
      end

      context "when admin client has only one business" do
        it "must create a business exclusive category" do
          patch("/client/v2/categories/#{category.id}", headers: headers.excluding("Tenant-id"), params: {title: "New Title"})

          expect(response).to be_ok
        end
      end
    end
  end
end
