# frozen_string_literal: true

require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::CategoriesController, type: :request do
  let(:business) { create(:business) }
  let(:another_business) { create(:business) }

  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) do
    %w[id active title visible business_id blocklisted short_description category_type deprecated_image image template main_category_id]
  end

  describe "#show" do
    let!(:category) { create(:category, business_id: business.id) }

    context "when exclusive category belongs to business" do
      it "returns exclusive category" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/#{category.id}",
          "/client/v2/categories/#{category.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.keys).to match_array(serialized_keys)
      end
    end

    context "when exclusive category doest not belong to business" do
      let!(:category) { create(:category, business_id: another_business.id) }

      it "renders not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/#{category.id}",
          "/client/v2/categories/#{category.id}"
        ].sample
        get(path, headers:)

        expect(response).to be_not_found
      end
    end

    context "when client_employee not related to business" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/#{category.id}",
          "/client/v2/categories/#{category.id}"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "without client_employee authentication" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories/#{category.id}",
          "/client/v2/categories/#{category.id}"
        ].sample
        get path
      end

      it_behaves_like "unauthorized client_employee"
    end
  end
end
