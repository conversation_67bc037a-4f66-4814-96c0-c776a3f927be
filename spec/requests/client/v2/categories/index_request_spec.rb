# frozen_string_literal: true

require "rails_helper"
require "support/shared_examples/client_employee_request_shared_examples"

RSpec.describe Client::V2::CategoriesController, type: :request do
  let(:business) { create(:business) }
  let(:another_business) { create(:business) }

  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_hash) { JSON.parse(response.body) }
  let(:serialized_keys) do
    %w[id active title visible business_id blocklisted short_description category_type image deprecated_image template main_category_id main_category_title]
  end

  describe "#index" do
    context "when business has exclusive categories" do
      let!(:category) { create(:category, business_id: business.id, template: "Banner 1") }
      let!(:exclusive_category_another_business) { create(:category, business_id: another_business.id) }
      let!(:non_exclusive_category) { create(:category, business_id: nil) }

      it "returns all categories the business can list" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories",
          "/client/v2/categories"
        ].sample
        get(path, headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        expect(response_hash.pluck("id")).to match_array([category.id, non_exclusive_category.id])
        expect(response_hash.pluck("main_category_id").uniq).to eq([nil])
        expect(response_hash.pluck("main_category_title").uniq).to eq([nil])
      end

      it "sub categories must include main category id and title" do
        sub_category = create(:category, business_id: business.id, template: "Banner 1", main_category: category)
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories",
          "/client/v2/categories"
        ].sample
        get(path, headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        expect(response_hash.pluck("id")).to match_array([category.id, sub_category.id, non_exclusive_category.id])
        expect(response_hash.find { _1["id"] == sub_category.id }["main_category_id"]).to eq(category.id)
        expect(response_hash.find { _1["id"] == sub_category.id }["main_category_title"]).to eq(category.title)
      end

      context "when filtering by visibility" do
        let!(:visible_category) { create(:category, visible: true, business_id: business.id) }
        let!(:invisible_category) { create(:category, visible: false, business_id: business.id) }

        context "when visible filter is true" do
          let(:params) { {visible: true} }

          it "returns only visible categories when visible filter is true" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/categories",
              "/client/v2/categories"
            ].sample
            get(path, headers:, params:)

            expect(response).to be_ok
            expect(response_hash.count).to eq(3)
            expect(response_hash.first["visible"]).to eq(true)
          end
        end

        context "when visible filter is false" do
          let(:params) { {visible: false} }

          it "returns only not visible categories when visible filter is true" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/categories",
              "/client/v2/categories"
            ].sample
            get(path, headers:, params:)

            expect(response).to be_ok
            expect(response_hash.count).to eq(1)
            expect(response_hash.first["visible"]).to eq(false)
          end
        end
      end

      context "when filtering by blocklisted" do
        let!(:visible_category) { create(:category, business_id: business.id) }
        let!(:invisible_category) { create(:category, business_id: business.id) }
        let!(:blocklist) { create(:category_blocklist, business:, category: invisible_category) }

        context "when blocklisted filter is false" do
          let(:params) { {blocklisted: false} }

          it "returns only non blocklisted categories" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/categories",
              "/client/v2/categories"
            ].sample
            get(path, headers:, params:)

            expect(response).to be_ok
            expect(response_hash.pluck("id")).to match_array([category.id, non_exclusive_category.id, visible_category.id])
          end
        end

        context "when blocklisted filter is true" do
          let(:params) { {blocklisted: true} }

          it "returns only blocklisted categories" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/categories",
              "/client/v2/categories"
            ].sample
            get(path, headers:, params:)

            expect(response).to be_ok
            expect(response_hash.pluck("id")).to match_array([invisible_category.id])
          end
        end

        context "when blocklisted filter is not passed" do
          it "returns all categories the business can list" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/categories",
              "/client/v2/categories"
            ].sample
            get(path, headers:)

            expect(response).to be_ok
            expect(response_hash.pluck("id", "blocklisted")).to match_array([
              [category.id, false],
              [non_exclusive_category.id, false],
              [visible_category.id, false],
              [invisible_category.id, true]
            ])
          end
        end
      end

      context "when filtering only main categories" do
        let!(:category) { create(:category, business:) }
        let!(:category_2) { create(:category, business:) }
        let!(:sub_category) { create(:category, business:, main_category: category) }
        let!(:sub_category_2) { create(:category, business:, main_category: category_2) }

        let(:params) do
          {
            only_main_categories: true
          }
        end

        it "returns only main categories" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/categories",
            "/client/v2/categories"
          ].sample
          get(path, headers:, params:)

          expect(response).to be_ok

          expect(response_hash.count).to eq(3)
          expect(response_hash.pluck("id")).to match_array([category.id, category_2.id, non_exclusive_category.id])
        end
      end

      context "when include sub categories" do
        let!(:category) { create(:category, business:) }
        let!(:category_2) { create(:category, business:) }
        let!(:sub_category) { create(:category, business:, main_category: category) }
        let!(:sub_category_2) { create(:category, business:, main_category: category_2) }

        let(:params) do
          {with_sub_categories: true}
        end

        it "returns only main categories" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/categories",
            "/client/v2/categories"
          ].sample
          get(path, headers:, params:)

          expect(response).to be_ok
          expect(response_hash.count).to eq(3)
          expect(response_hash.map(&:keys)).to all(include("categories"))
        end
      end

      context "when filtering by title" do
        let!(:second_category) { create(:category, title: "Other Title", business_id: business.id) }
        let(:params) do
          {
            title: "Other"
          }
        end

        it "returns only categories that match title param" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/categories",
            "/client/v2/categories"
          ].sample
          get(path, headers:, params:)

          expect(response).to be_ok
          expect(response_hash.count).to eq(1)
          expect(response_hash.first["title"]).to eq(second_category.title)
        end
      end

      context "when filtering by exclusive" do
        context "when exclusive filter is false" do
          let(:params) { {exclusive: false} }

          it "returns only non exclusive categories" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/categories",
              "/client/v2/categories"
            ].sample
            get(path, headers:, params:)

            expect(response).to be_ok
            expect(response_hash.pluck("id")).to match_array([non_exclusive_category.id])
          end
        end

        context "when exclusive filter is true" do
          let(:params) { {exclusive: true} }

          it "returns only exclusive categories" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/categories",
              "/client/v2/categories"
            ].sample
            get(path, headers:, params:)

            expect(response).to be_ok
            expect(response_hash.pluck("id")).to match_array([category.id])
          end
        end
      end

      context "when filtering by category_type" do
        let!(:promotion_category) { create(:category, :promotion_category, business_id: business.id) }

        context "when category_type is promotion" do
          let(:params) { {category_type: Enums::Category::Type::PROMOTION} }

          it "returns only non exclusive categories" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/categories",
              "/client/v2/categories"
            ].sample
            get(path, headers:, params:)

            expect(response).to be_ok
            expect(response_hash.pluck("id")).to match_array([promotion_category.id])
          end
        end

        context "when exclusive filter is organization" do
          let(:params) { {category_type: Enums::Category::Type::ORGANIZATION} }

          it "returns only exclusive categories" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/categories",
              "/client/v2/categories"
            ].sample
            get(path, headers:, params:)

            expect(response).to be_ok
            expect(response_hash.pluck("id")).to match_array([category.id, non_exclusive_category.id])
          end
        end
      end

      context "when filtering by template" do
        let(:params) { {template: "Banner 1"} }

        it "renders ok" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/categories",
            "/client/v2/categories"
          ].sample
          get(path, headers:, params:)

          expect(response).to be_ok
          expect(response_hash.pluck("id")).to match_array([category.id])
        end
      end
    end

    context "when business does not have any exclusive category" do
      let!(:exclusive_category_another_business) { create(:category, business_id: another_business.id) }
      let!(:category) { create(:category, business_id: nil) }

      it "returns all non exclusive categories" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories",
          "/client/v2/categories"
        ].sample
        get(path, headers:)

        expect(response).to be_ok
        expect(response_hash.pluck("id")).to eq([category.id])
      end
    end

    context "when client_employee not related to business" do
      let(:client_employee) { create(:client_employee, :admin_client) }

      it "returns not found" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories",
          "/client/v2/categories"
        ].sample
        get(path, headers:)

        expect(response).to have_http_status(:not_found)
        expect(response_hash["error"]).to eq("Business não encontrado")
      end
    end

    context "without client_employee authentication" do
      before do
        path = [
          "/client/v2/businesses/#{business.cnpj}/categories",
          "/client/v2/categories"
        ].sample
        get path
      end

      it_behaves_like "unauthorized client_employee"
    end
  end
end
