# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::FundsController, type: :request do
  let(:business) { create_business }
  let(:user) { create(:user, business:) }
  let(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_body) { JSON.parse(response.body) }

  describe "#destroy" do
    let!(:fund) do
      create(:fund, :points, :pending, user:, amount: 1000, idempotency_key: SecureRandom.uuid)
    end

    context "when point status is cancelable and was not created from purchase" do
      let(:status) { [FundStatus::PENDING, FundStatus::APPROVED].sample }
      let!(:fund) do
        create(:fund, :points, status:, user:, amount: 1000, idempotency_key: SecureRandom.uuid, order: nil)
      end

      it "renders no content" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/funds/#{fund.id}",
          "/client/v2/funds/#{fund.id}"
        ].sample
        delete path, headers:, as: :json

        expect(response).to be_no_content
        expect(fund.reload).to be_canceled
      end
    end

    context "when point is not cancelable" do
      let!(:fund) do
        create(:fund, :points, :available, user:, amount: 1000, idempotency_key: SecureRandom.uuid, order: nil)
      end

      it "renders no content" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/funds/#{fund.id}",
          "/client/v2/funds/#{fund.id}"
        ].sample
        delete path, headers:, as: :json

        expect(response).to have_http_status(:not_found)
        expect(fund.reload).to be_available
      end
    end

    context "when point was created from purchase" do
      let!(:order) do
        promotion = create(:promotion, cashback_value: 3.24)
        cupon = create(:cupon, promotion:)
        create(:order, cupon:, business:)
      end
      let!(:fund) { create(:fund, :points, :pending, user:, order:, order_amount: 13000) }

      it "renders no content" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/funds/#{fund.id}",
          "/client/v2/funds/#{fund.id}"
        ].sample
        delete path, headers:, as: :json

        expect(response).to have_http_status(:not_found)
        expect(fund.reload).to be_pending
      end
    end

    context "when point is from another business" do
      let(:another_business) { create_business }
      let(:user) { create(:user, business: another_business) }
      let!(:fund) do
        create(:fund, :points, :pending, user:, amount: 1000, idempotency_key: SecureRandom.uuid, order: nil)
      end

      it "renders no content" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/funds/#{fund.id}",
          "/client/v2/funds/#{fund.id}"
        ].sample
        delete path, headers:, as: :json

        expect(response).to have_http_status(:not_found)
        expect(fund.reload).to be_pending
      end
    end

    context "without headers" do
      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/funds/#{fund.id}",
          "/client/v2/funds/#{fund.id}"
        ].sample
        delete path, as: :json

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "when business disabled cashback" do
      let(:business) { create_business(cashback: false) }

      it "renders error" do
        delete "/client/v2/businesses/#{business.cnpj}/funds/#{fund.id}", headers:, as: :json

        expect(response).to have_http_status(:forbidden)
      end
    end

    context "when business is not active" do
      let(:business) { create_business(active: false) }

      it "renders error" do
        delete "/client/v2/businesses/#{business.cnpj}/funds/#{fund.id}", headers:, as: :json

        expect(response).to have_http_status(:forbidden)
      end
    end

    context "when business currency is not points" do
      let(:business) { create_business(currency: "BRL") }
      let!(:fund) do
        create(:fund, :points, :pending, user:, amount: 1000, idempotency_key: SecureRandom.uuid, expires_at: 1.day.from_now)
      end

      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/funds/#{fund.id}",
          "/client/v2/funds/#{fund.id}"
        ].sample
        delete path, headers:, as: :json

        expect(response).to have_http_status(:forbidden)
      end
    end
  end

  def create_business(active: true, cashback: true, currency: "points")
    create(
      :business,
      status: active ? Business::Status::ACTIVE : Business::Status::INACTIVE,
      cashback:,
      spread_percent: 0,
      currency:,
      fair_value: 0.03
    )
  end
end
