# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::FundsController, type: :request do
  let(:business) { create_business }
  let(:user) { create(:user, business:) }
  let(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Idempotency-Key" => SecureRandom.uuid,
      "Tenant-id": business.cnpj
    }
  end
  let(:response_body) { JSON.parse(response.body) }
  let(:serializer_keys) { %w[id amount credits_at status] }

  describe "#create" do
    let(:params) { {user_id: user.id} }

    shared_examples "point creation" do
      it "renders created" do
        expect do
          path = [
            "/client/v2/businesses/#{business.cnpj}/funds",
            "/client/v2/funds"
          ].sample
          post path, params:, headers:, as: :json
        end.to change(Fund.points.pending.where(amount: 1000, user:, business:), :count).by(1)

        expect(response).to be_created
        expect(response_body.keys).to match_array serializer_keys
        expect(response_body["amount"]).to eq 1000
        expect(response_body["credits_at"]).to eq 1.day.from_now.beginning_of_day.iso8601(3)
        expect(response_body["status"]).to eq FundStatus::PENDING
      end

      context "when point already exists" do
        before { create(:fund, :points, user:, amount: 1000, idempotency_key: headers["Idempotency-Key"]) }

        it "renders error" do
          expect do
            path = [
              "/client/v2/businesses/#{business.cnpj}/funds",
              "/client/v2/funds"
            ].sample
            post path, params:, headers:, as: :json
          end.not_to change(Fund, :count)

          expect(response).to be_unprocessable
          expect(response_body["error"]).to eq "Chave de idempotência já está em uso"
        end
      end
    end

    shared_examples "missing param error" do
      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/funds",
          "/client/v2/funds"
        ].sample
        post path, params:, headers:, as: :json

        expect(response).to be_precondition_failed
        expect(response_body["error"]).to eq "A requisição falhou devido a ausência de parâmetro: #{param_name}"
      end
    end

    context "with all params finding by user id" do
      let(:params) { {user_id: user.id, credits_at: 1.day.from_now.beginning_of_day, amount: 1000} }

      it_behaves_like "point creation"
    end

    context "with all params finding by user document" do
      let(:params) { {document: user.cpf, credits_at: 1.day.from_now.beginning_of_day, amount: 1000} }

      it_behaves_like "point creation"
    end

    context "with missing amount" do
      let(:params) { {user_id: user.id, credits_at: 1.day.from_now} }
      let(:param_name) { :amount }

      it_behaves_like "missing param error"
    end

    context "with missing credits_at" do
      let(:params) { {user_id: user.id, amount: 1000} }
      let(:param_name) { :credits_at }

      it_behaves_like "missing param error"
    end

    context "without headers" do
      it "renders error" do
        post "/client/v2/businesses/#{business.cnpj}/funds", params:, as: :json

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "when business disabled cashback" do
      let(:business) { create_business(cashback: false) }

      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/funds",
          "/client/v2/funds"
        ].sample
        post path, params:, headers:, as: :json

        expect(response).to have_http_status(:forbidden)
      end
    end

    context "when business currency is not points" do
      let(:business) { create_business(currency: "BRL") }

      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/funds",
          "/client/v2/funds"
        ].sample
        post path, params:, headers:, as: :json

        expect(response).to have_http_status(:forbidden)
      end
    end

    context "when business is not active" do
      let(:business) { create_business(active: false) }

      it "renders error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/funds",
          "/client/v2/funds"
        ].sample
        post path, params:, headers:, as: :json

        expect(response).to have_http_status(:forbidden)
      end
    end
  end

  def create_business(active: true, cashback: true, currency: "points")
    create(
      :business,
      status: active ? Business::Status::ACTIVE : Business::Status::INACTIVE,
      cashback:,
      spread_percent: 0,
      currency:,
      fair_value: 0.03
    )
  end
end
