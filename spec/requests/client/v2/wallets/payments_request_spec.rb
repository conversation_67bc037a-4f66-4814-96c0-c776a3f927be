# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::Wallets::PaymentsController, type: :request do
  let(:response_body) { JSON.parse(response.body) }
  let(:user) { create(:user, business:) }
  let(:headers) do
    client_employee = create(:client_employee, businesses: [business])
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Idempotency-Key" => SecureRandom.uuid,
      "Tenant-id": business.cnpj
    }
  end

  describe "#create" do
    shared_examples "unsuccessful payment" do
      it "returns error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/payments",
          "/client/v2/payments",
          "/client/v2/wallets/payments"
        ].sample
        expect { post path, headers:, params:, as: :json }
          .to not_change(Payout, :count)
          .and not_change(WalletTransaction, :count)
          .and not_change { user_wallet.reload.balance }
          .and not_change { outflow_wallet.reload.balance }

        expect(response).to be_unprocessable
        expect(response_body["error"]).to eq(expected_message)
      end
    end

    context "when payment method is pix" do
      let(:business) { create_business(custom_transfer_type: false) }
      let(:user_wallet) { create(:wallet, :cashback, user:, balance: 16246) }
      let(:outflow_wallet) { create(:wallet, :outflow, :cashback, balance: ********) }

      shared_examples "successful pix payment method" do
        it "withdraws from user wallet and creates pix payout" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/payments",
            "/client/v2/payments",
            "/client/v2/wallets/payments"
          ].sample
          expect { post path, headers:, params:, as: :json }
            .to change(Payout, :count).by(1)
            .and change(WalletTransaction, :count).by(1)
            .and change { user_wallet.reload.balance }.by(-params[:amount].to_i)
            .and change { outflow_wallet.reload.balance }.by(params[:amount].to_i)

          expect(response).to be_created
          wallet_transaction = WalletTransaction.order(:created_at).last
          expect(response_body).to eq(
            "transaction_id" => wallet_transaction.id,
            "amount" => params[:amount].to_i
          )
          expect(wallet_transaction.idempotency_key).to eq(headers["Idempotency-Key"])
          expect(wallet_transaction.wallet).to eq(user_wallet)
          expect(wallet_transaction.currency).to eq("BRL")
          expect(wallet_transaction.payment_method).to eq("pix")
          expect(wallet_transaction.amount).to eq(params[:amount].to_i)
          expect(wallet_transaction.data.stringify_keys).to eq(params[:payment_data].stringify_keys)
          payout = Payout.order(:created_at).last
          expect(payout.user).to eq(user)
          expect(payout.kind).to eq("pix")
          expect(payout.total_amount).to eq(params[:amount].to_i / 100.0)
          expect(payout.receiver_taxpayer_number).to eq(params[:payment_data][:key])
          expect(payout.due_on).to be_nil
        end
      end

      context "and finding user by account id" do
        let(:params) do
          {
            account_id: user_wallet.id,
            amount:,
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "pix",
            payment_data: {
              key: user.cpf
            }
          }
        end

        context "and amount is integer" do
          let(:amount) { FFaker::Number.number(digits: 4) }

          it_behaves_like "successful pix payment method"
        end

        context "and amount is string" do
          let(:amount) { FFaker::Number.number(digits: 4).to_s }

          it_behaves_like "successful pix payment method"
        end
      end

      context "and finding user by user id" do
        let(:params) do
          {
            user_id: user.id,
            amount: FFaker::Number.number(digits: 4),
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "pix",
            payment_data: {
              key: user.cpf
            }
          }
        end

        it_behaves_like "successful pix payment method"
      end

      context "and finding user by document" do
        let(:params) do
          {
            document: user.cpf,
            amount: FFaker::Number.number(digits: 4),
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "pix",
            payment_data: {
              key: user.cpf
            }
          }
        end

        it_behaves_like "successful pix payment method"
      end

      context "and currency is internal to project" do
        let(:params) do
          {
            account_id: user_wallet.id,
            amount: FFaker::Number.number(digits: 4),
            wallet_type: "internal",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "pix",
            payment_data: {
              key: user.cpf
            }
          }
        end
        let(:expected_message) { "Carteira do usuário inválida, Método de pagamento inválido" }

        it_behaves_like "unsuccessful payment"
      end

      context "and tries to create payout with wrong document" do
        let(:params) do
          {
            account_id: user_wallet.id,
            amount: FFaker::Number.number(digits: 4),
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "pix",
            payment_data: {
              key: "123"
            }
          }
        end
        let(:expected_message) { "O CPF do usuário é inválido" }

        it_behaves_like "unsuccessful payment"
      end

      context "and payment data is not sent" do
        let(:params) do
          {
            account_id: user_wallet.id,
            amount: FFaker::Number.number(digits: 4),
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "pix"
          }
        end
        let(:expected_message) { "Método de pagamento inválido" }

        it_behaves_like "unsuccessful payment"
      end

      context "and payment data is invalid" do
        let(:params) do
          {
            account_id: user_wallet.id,
            amount: FFaker::Number.number(digits: 4),
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "pix",
            payment_data: {
              key: "123"
            }
          }
        end
        let(:expected_message) { "O CPF do usuário é inválido" }

        it_behaves_like "unsuccessful payment"
      end

      context "and finding user by wrong account id" do
        let(:params) do
          {
            account_id: "000",
            amount: FFaker::Number.number(digits: 4),
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "pix",
            payment_data: {
              key: user.cpf
            }
          }
        end
        let(:expected_message) { "Carteira do usuário inválida, Método de pagamento inválido" }

        it_behaves_like "unsuccessful payment"
      end

      context "and balance is insufficient" do
        let(:params) do
          {
            account_id: user_wallet.id,
            amount: FFaker::Number.number(digits: 6),
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "pix",
            payment_data: {
              key: user.cpf
            }
          }
        end

        it "renders error" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/payments",
            "/client/v2/payments",
            "/client/v2/wallets/payments"
          ].sample
          expect { post path, headers:, params:, as: :json }
            .to not_change(Payout, :count)
            .and not_change(WalletTransaction, :count)
            .and not_change { user_wallet.reload.balance }
            .and not_change { outflow_wallet.reload.balance }

          expect(response).to be_unprocessable
          expect(response_body["error"]).to eq("Saldo insuficiente")
        end
      end
    end

    context "when payment method is bank slip and business accepts custom transfer type" do
      let(:business) { create_business(custom_transfer_type: true) }
      let(:user_wallet) { create(:wallet, :cashback, user:, balance: 16246) }
      let(:outflow_wallet) { create(:wallet, :outflow, :cashback, balance: ********) }

      shared_examples "successful bank slip payment method" do
        it "withdraws from user wallet and creates bank slip payout" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/payments",
            "/client/v2/payments",
            "/client/v2/wallets/payments"
          ].sample
          expect { post path, headers:, params:, as: :json }
            .to change(Payout, :count).by(1)
            .and change(WalletTransaction, :count).by(1)
            .and change { user_wallet.reload.balance }.by(-params[:amount].to_i)
            .and change { outflow_wallet.reload.balance }.by(params[:amount].to_i)

          expect(response).to be_created
          wallet_transaction = WalletTransaction.order(:created_at).last
          expect(response_body).to eq(
            "transaction_id" => wallet_transaction.id,
            "amount" => params[:amount].to_i
          )
          expect(wallet_transaction.idempotency_key).to eq(headers["Idempotency-Key"])
          expect(wallet_transaction.wallet).to eq(user_wallet)
          expect(wallet_transaction.currency).to eq("BRL")
          expect(wallet_transaction.payment_method).to eq("bank_slip")
          expect(wallet_transaction.amount).to eq(params[:amount].to_i)
          expect(wallet_transaction.data.stringify_keys).to eq(params[:payment_data].stringify_keys)
          payout = Payout.order(:created_at).last
          expect(payout.user).to eq(user)
          expect(payout.kind).to eq("bank_slip")
          expect(payout.total_amount).to eq(params[:amount].to_i / 100.0)
          expect(payout.receiver_taxpayer_number).to eq(params[:payment_data][:barcode_number])
          expect(payout.due_on).to eq(params[:payment_data][:due_date].to_date)
        end
      end

      context "and business accepts custom transfer type, sending only required fields" do
        let(:params) do
          {
            account_id: user_wallet.id,
            amount: FFaker::Number.number(digits: 4),
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "bank_slip",
            payment_data: {
              barcode_number: FFaker::Code.ean,
              due_date: "2024-06-10"
            }
          }
        end

        it_behaves_like "successful bank slip payment method"
      end

      context "and business accepts custom transfer type, sending optional fields" do
        let(:params) do
          {
            account_id: user_wallet.id,
            amount: FFaker::Number.number(digits: 4),
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "bank_slip",
            payment_data: {
              barcode_number: FFaker::Code.ean,
              due_date: "2024-06-10",
              unique_identifier: "18c1952f-aa62-4781-98c8-b5d8b03a591b"
            }
          }
        end

        it_behaves_like "successful bank slip payment method"
      end

      context "and payment data is not sent" do
        let(:params) do
          {
            account_id: user_wallet.id,
            amount: FFaker::Number.number(digits: 4),
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "bank_slip"
          }
        end
        let(:expected_message) { "Método de pagamento inválido" }

        it_behaves_like "unsuccessful payment"
      end

      context "and payment data is invalid" do
        let(:params) do
          {
            account_id: user_wallet.id,
            amount: FFaker::Number.number(digits: 4),
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "bank_slip",
            payment_data: {
              key: user.cpf
            }
          }
        end

        let(:expected_message) { "Método de pagamento inválido" }

        it_behaves_like "unsuccessful payment"
      end

      context "and finding user by wrong account id" do
        let(:params) do
          {
            account_id: "000",
            amount: FFaker::Number.number(digits: 4),
            wallet_type: "external",
            currency: "BRL",
            description: FFaker::LoremBR.phrase,
            op: "bank_slip",
            payment_data: {
              barcode_number: FFaker::Code.ean,
              due_date: "2024-06-10"
            }
          }
        end
        let(:expected_message) { "Carteira do usuário inválida" }

        it_behaves_like "unsuccessful payment"
      end
    end

    context "when payment method is bank slip and business does not accept custom transfer type" do
      let(:business) { create_business(custom_transfer_type: false) }
      let(:user_wallet) { create(:wallet, :cashback, user:, balance: 16246) }
      let(:outflow_wallet) { create(:wallet, :outflow, :cashback, balance: ********) }
      let(:params) do
        {
          account_id: user_wallet.id,
          amount: FFaker::Number.number(digits: 4),
          wallet_type: "external",
          currency: "BRL",
          description: FFaker::LoremBR.phrase,
          op: "bank_slip",
          payment_data: {
            barcode_number: FFaker::Code.ean,
            due_date: "2024-06-10"
          }
        }
      end
      let(:expected_message) { "O CPF do usuário é inválido" }

      it_behaves_like "unsuccessful payment"
    end

    context "when payment method is not sent" do
      let(:business) { create_business(custom_transfer_type: false, cashback_wallet_destination: Wallet::Kind::MEMBERSHIP) }
      let(:user_wallet) { create(:wallet, :membership, user:, balance: 16246) }
      let(:business_wallet) { create(:wallet, :membership, business:, balance: ********) }
      let(:params) do
        {
          account_id: user_wallet.id,
          amount: FFaker::Number.number(digits: 4),
          wallet_type: "internal",
          currency: "BRL",
          description: FFaker::LoremBR.phrase
        }
      end

      it "withdraws from user wallet and does not create payout" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/payments",
          "/client/v2/payments",
          "/client/v2/wallets/payments"
        ].sample
        expect { post path, headers:, params:, as: :json }
          .to not_change(Payout, :count)
          .and change(WalletTransaction, :count).by(1)
          .and change { user_wallet.reload.balance }.by(-params[:amount].to_i)
          .and change { business_wallet.reload.balance }.by(params[:amount].to_i)

        expect(response).to be_created
        wallet_transaction = WalletTransaction.order(:created_at).last
        expect(response_body).to eq(
          "transaction_id" => wallet_transaction.id,
          "amount" => params[:amount].to_i
        )
        expect(wallet_transaction.idempotency_key).to eq(headers["Idempotency-Key"])
        expect(wallet_transaction.wallet).to eq(user_wallet)
        expect(wallet_transaction.currency).to eq("BRL")
        expect(wallet_transaction.payment_method).to eq("default")
        expect(wallet_transaction.amount).to eq(params[:amount].to_i)
        expect(wallet_transaction.data).to be_empty
      end
    end

    context "when business disabled cashback" do
      let(:business) { create_business(custom_transfer_type: true, cashback: false) }

      it "returns error when business disabled cashback" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/payments",
          "/client/v2/payments",
          "/client/v2/wallets/payments"
        ].sample
        post path, headers:, as: :json

        expect(response).to be_forbidden
        expect(response_body["error"]).to eq("Cashback desativado")
      end
    end
  end

  def create_business(custom_transfer_type:, cashback_wallet_destination: :cashback, cashback: true)
    business = create(
      :business,
      cashback:,
      cashback_wallet_destination:,
      spread_percent: 0,
      create_project_config: false
    )
    create(:project_config, business:, custom_user_transfer_account_number: custom_transfer_type)
    business
  end
end
