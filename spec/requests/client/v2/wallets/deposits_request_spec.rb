# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::Wallets::DepositsController, type: :request do
  let(:response_body) { JSON.parse(response.body) }
  let(:user) { create(:user, business:) }
  let!(:client_employee) { create(:client_employee, :admin_lecupon) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Idempotency-Key" => SecureRandom.uuid,
      "Tenant-id": business.cnpj
    }
  end

  describe "#create" do
    shared_examples "unsuccessful payment" do
      it "returns error" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/deposits",
          "/client/v2/deposits",
          "/client/v2/wallets/deposits"
        ].sample
        expect { post path, headers:, params:, as: :json }
          .to not_change { user_wallet.reload.balance }
          .and not_change { inflow_wallet.reload.balance }
          .and not_change { project_wallet.reload.balance }
          .and not_change(WalletTransaction, :count)

        expect(response).to be_unprocessable
        expect(response_body["error"]).to eq(expected_message)
      end
    end

    context "with params correct" do
      let(:currency) { "BRL" }
      let(:kind) { Wallet::Kind::MEMBERSHIP }
      let(:business) { create_business }
      let(:inflow_wallet) { create(:wallet, :inflow, kind:, currency:, balance: 300000) }
      let(:user_wallet) { create(:wallet, kind:, user:, currency:, balance: 16246) }
      let(:project_wallet) { create(:wallet, kind:, business:, currency:, balance: 54806223) }

      shared_examples "successful cash in" do
        it "renders created and cashes in user wallet" do
          path = [
            "/client/v2/businesses/#{business.cnpj}/deposits",
            "/client/v2/deposits",
            "/client/v2/wallets/deposits"
          ].sample
          expect { post path, headers:, params:, as: :json }
            .to change { user_wallet.reload.balance }.by(params[:amount].to_i)
            .and not_change { inflow_wallet.reload.balance }
            .and not_change { project_wallet.reload.balance }
            .and change(WalletTransaction.where(idempotency_key: headers["Idempotency-Key"]), :count).by(1)

          expect(response).to be_created

          transaction_id = WalletTransaction.order(created_at: :desc).pick(:id)
          expect(response_body).to eq(
            "amount" => params[:amount].to_i,
            "transaction_id" => transaction_id
          )
        end
      end

      context "and finding user by account id" do
        let(:params) do
          {
            account_id: user_wallet.id,
            amount:,
            currency: "BRL",
            description: FFaker::LoremBR.phrase
          }
        end

        context "and amount is integer" do
          let(:amount) { FFaker::Number.number(digits: 4) }

          it_behaves_like "successful cash in"
        end

        context "and amount is string" do
          let(:amount) { FFaker::Number.number(digits: 4).to_s }

          it_behaves_like "successful cash in"
        end
      end

      context "and finding user by user id" do
        let(:params) do
          {
            user_id: user.id,
            amount: FFaker::Number.number(digits: 4),
            currency: "BRL",
            description: FFaker::LoremBR.phrase
          }
        end

        it_behaves_like "successful cash in"
      end

      context "and finding user by document" do
        let(:params) do
          {
            document: user.cpf,
            amount: FFaker::Number.number(digits: 4),
            currency: "BRL",
            description: FFaker::LoremBR.phrase
          }
        end

        it_behaves_like "successful cash in"
      end

      context "and project wallet destination is not membership" do
        let(:params) do
          {
            account_id: user_wallet.id,
            amount: FFaker::Number.number(digits: 4),
            currency:,
            description: FFaker::LoremBR.phrase
          }
        end

        context "and currency is BRL" do
          let(:business) { create_business(cashback_wallet_destination: :cashback, currency:) }
          let(:currency) { "BRL" }

          it "returns error when business disabled cashback" do
            path = [
              "/client/v2/businesses/#{business.cnpj}/deposits",
              "/client/v2/deposits",
              "/client/v2/wallets/deposits"
            ].sample
            post path, headers:, params:, as: :json

            expect(response).to be_forbidden
            expect(response_body["error"]).to eq("Você não tem acesso à gestão desta carteira")
          end
        end

        context "and currency is points" do
          let(:currency) { "points" }
          let(:kind) { Wallet::Kind::CASHBACK }
          let(:business) { create_business(cashback_wallet_destination: :cashback, currency:, fair_value: 0.03) }

          it_behaves_like "successful cash in"
        end
      end

      context "and finding user by wrong account id" do
        let(:params) do
          {
            account_id: "000",
            amount: FFaker::Number.number(digits: 4),
            currency: "BRL",
            description: FFaker::LoremBR.phrase
          }
        end
        let(:expected_message) { "Carteira do usuário inválida" }

        it_behaves_like "unsuccessful payment"
      end
    end

    context "when business disabled cashback" do
      let(:business) { create_business(cashback: false) }

      it "returns error when business disabled cashback" do
        path = [
          "/client/v2/businesses/#{business.cnpj}/deposits",
          "/client/v2/deposits",
          "/client/v2/wallets/deposits"
        ].sample
        post path, headers:, as: :json

        expect(response).to be_forbidden
        expect(response_body["error"]).to eq("Cashback desativado")
      end
    end
  end

  def create_business(cashback_wallet_destination: :membership, cashback: true, **)
    create(
      :business,
      cashback_wallet_destination:,
      cashback:,
      spread_percent: 0,
      **
    )
  end
end
