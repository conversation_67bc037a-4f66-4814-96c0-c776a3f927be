# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::WalletsController, type: :request do
  describe "GET /client/v2/wallets" do
    let(:business) { create(:business, cashback: true) }
    let(:user) { create(:user, business:) }
    let(:wallet) { create(:wallet, user:, kind: :cashback, currency: :BRL, balance: 1000) }
    let(:headers) do
      Devise::JWT::TestHelpers.auth_headers({}, create(:client_employee, businesses: [business]))
    end

    it "renders ok" do
      get("/client/v2/wallets/#{wallet.id}", headers:)

      expect(response).to be_ok
      expect(response.parsed_body).to eq "id" => wallet.id, "balance" => wallet.balance, "cpf" => user.cpf
    end

    context "when wallet belongs to user from another business" do
      let(:another_business) { create(:business, cashback: true) }
      let(:another_user) { create(:user, business: another_business) }
      let(:wallet) { create(:wallet, user: another_user, kind: :cashback, currency: :BRL, balance: 1000) }

      it "renders error" do
        get("/client/v2/wallets/#{wallet.id}", headers:)

        expect(response).to be_not_found
      end
    end

    context "when ID is invalid" do
      it "renders error" do
        get("/client/v2/wallets/0", headers:)

        expect(response).to be_not_found
      end
    end
  end
end
