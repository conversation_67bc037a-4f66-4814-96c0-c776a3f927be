# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::TelemedicinePlansController, type: :request do
  let(:serializer_keys) { %w[value text contracted_beneficiaries_options] }
  let(:business) { create(:business) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let(:headers) do
    {
      "X-ClientEmployee-Email": client_employee.email,
      "X-ClientEmployee-Token": client_employee.authentication_token,
      "Tenant-id": business.cnpj
    }
  end

  describe "GET /client/v2/telemedicine_plans" do
    it "return translated form options for telemedicine plans" do
      path = [
        "/client/v2/businesses/#{business.id}/telemedicine_plans",
        "/client/v2/telemedicine_plans"
      ].sample
      get(path, headers:)

      expect(response).to be_successful
      response_hash = JSON.parse(response.body)
      expect(response_hash.first.keys).to match_array(serializer_keys)
      expect(response_hash.pluck("value")).to match_array(Telemedicine::Plans.all)
      copart_plan = response_hash.find { _1["value"] == Telemedicine::Plans::COPART }
      integral_plan = response_hash.find { _1["value"] == Telemedicine::Plans::INTEGRAL }
      integral_plus_plan = response_hash.find { _1["value"] == Telemedicine::Plans::INTEGRAL_PLUS }
      ultra_plan = response_hash.find { _1["value"] == Telemedicine::Plans::ULTRA }
      expect(copart_plan["contracted_beneficiaries_options"]).to match_array([500, 1000, 1500, 2000, 3000, 5000, 7000, 10000, 15000, 20000, 30000, 50000, 100000])
      expect(integral_plan["contracted_beneficiaries_options"]).to match_array([1000, 1500, 2000, 3000, 5000, 7000, 10000, 15000, 20000, 30000, 50000, 100000])
      expect(integral_plus_plan["contracted_beneficiaries_options"]).to match_array([1500, 2000, 3000, 5000, 7000, 10000, 15000, 20000, 30000, 50000, 100000])
      expect(ultra_plan["contracted_beneficiaries_options"]).to match_array([500, 1000, 1500, 2000, 3000, 5000, 7000, 10000, 15000, 20000, 30000, 50000, 100000])
    end

    context "when business has users" do
      before do
        create_list(:authorized_user, 10, business:, telemedicine: true)
        allow(Telemedicine::Plans).to receive(:contracted_beneficiaries_limits_for_plan).and_return([1, 5, 10, 20, 50])
      end

      it "must not show limits below users count" do
        path = [
          "/client/v2/businesses/#{business.id}/telemedicine_plans",
        "/client/v2/telemedicine_plans"
        ].sample
        get(path, headers:)

        expect(response).to be_successful
        response_hash = JSON.parse(response.body)
        expect(response_hash.first.keys).to match_array(serializer_keys)
        expect(response_hash.pluck("value")).to match_array(Telemedicine::Plans.all)
        copart_plan = response_hash.find { _1["value"] == Telemedicine::Plans::COPART }

        expect(copart_plan["contracted_beneficiaries_options"]).to match_array([20, 50])
      end
    end
  end
end
