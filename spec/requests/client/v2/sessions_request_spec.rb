# frozen_string_literal: true

require "rails_helper"

RSpec.describe Client::V2::SessionsController, type: :request do
  let!(:oab) { create(:business, :oab) }
  let!(:publicar) { create(:business, :publicar) }
  let!(:client_employee) { create(:client_employee, migrated: true, businesses: [oab, publicar]) }

  context "when with correct rails credentials" do
    let(:params) { {email: client_employee.email, password: client_employee.password} }
    let(:serializer_keys) { %w[id auth_token email role businesses] }
    let(:business_serializer_key) { %w[id name cnpj active] }

    it "must authenticate" do
      post("/client/v2/sign_in", params:)
      response_hash = JSON.parse(response.body)
      expect(response).to be_successful
      expect(response_hash["businesses"].map(&:keys)).to all(match_array(business_serializer_key))
      expect(response_hash.keys).to match_array(serializer_keys)
    end

    it "must return jwt to /admin/v1" do
      post("/client/v2/sign_in", params:)
      expect(response).to be_successful
      expect(response.headers["Authorization"]).to be_present

      get "/admin/v1/projects", headers: {Authorization: response.headers["Authorization"]}
      expect(response).to be_ok
    end
  end

  context "when with invalid rails credentials" do
    let(:params) { {email: client_employee.email, password: "wrong_password"} }

    let(:serializer_keys) { ["error"] }

    it "must render unauthorized" do
      post("/client/v2/sign_in", params:)

      expect(response).to be_unauthorized
      expect(JSON.parse(response.body).keys).to match_array(serializer_keys)
    end
  end

  context "when client_employee is a system user" do
    let!(:system_client_employee) { create(:client_employee, migrated: true, system_user: true, businesses: [oab, publicar]) }
    let(:params) { {email: system_client_employee.email, password: system_client_employee.password} }

    it "must not authenticate" do
      post("/client/v2/sign_in", params:)

      expect(response).to be_unauthorized
      expect(JSON.parse(response.body)["error"]).to eq("Credenciais inválidas")
    end
  end

  context "when client_employee is not a system user" do
    let!(:regular_client_employee) { create(:client_employee, migrated: true, system_user: false, businesses: [oab, publicar]) }
    let(:params) { {email: regular_client_employee.email, password: regular_client_employee.password} }

    it "must authenticate successfully" do
      post("/client/v2/sign_in", params:)

      expect(response).to be_successful
      expect(response.headers["Authorization"]).to be_present
    end
  end
end
