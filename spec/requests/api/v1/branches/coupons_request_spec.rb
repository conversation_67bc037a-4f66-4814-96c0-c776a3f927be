# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V1::Branches::CouponsController, type: :request do
  describe "GET /api/v1/public_integration/branches/coupons" do
    let!(:business) { create(:business, :with_cashback, spread_percent: 0) }
    let(:signed_user) { create(:user, cpf: "***********", cellphone: "***********", business:) }
    let(:auth_headers) do
      signed_user.create_new_auth_token.merge!("Api-Secret" => business.api_secret)
    end
    let!(:organization) { create(:organization, :active_with_coupon) }
    let!(:branch) { create(:branch, organization:, lat: 1.0, lng: 1.0) }
    let!(:branch_two) { create(:branch, organization:, lat: 1.0, lng: 1.0) }
    let!(:tagging) { create(:tagging, :for_organization, taggable: organization) }
    let!(:cupon) { create(:cupon, :classic, branch:) }
    let(:branch_serializer_keys) do
      %w[closing_time taxpayer_number distance_km full_address id lat lng
        name opening_time telephone zipcode]
    end
    let(:response_hash) { JSON.parse(response.body) }
    let!(:another_business) { create(:business, :oab) }
    let!(:coupon_of_another_business) { create(:cupon, :cpf, branch:, business: another_business) }
    let!(:coupon_two) { create(:cupon, :fixed_code, branch:) }

    it "returns a cupon list given an branch_id" do
      get "/api/v1/public_integration/branches/#{branch.id}/coupons",
        headers: auth_headers,
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.count).to eq(2)
    end

    it "returns a cupon list given an branch_id and page with coupons" do
      get "/api/v1/public_integration/branches/#{branch.id}/coupons?page=1",
        headers: auth_headers,
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.count).to eq(2)
    end

    it "returns empty list for a page with no further coupons" do
      get "/api/v1/public_integration/branches/#{branch.id}/coupons?page=2",
        headers: auth_headers,
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.count).to eq(0)
    end

    it "returns a empty cupon list given an branch_id" do
      get "/api/v1/public_integration/branches/#{branch_two.id}/coupons",
        headers: auth_headers,
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.count).to eq(0)
    end

    context "with coupons on another business" do
      before do
        business_two = create(:business, :publicar)
        Cupon.update_all(business_id: business_two.id)
      end

      it "returns an empty cupon list given an branch_id" do
        get "/api/v1/public_integration/branches/#{branch.id}/coupons",
          headers: auth_headers,
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash.count).to eq(0)
      end
    end

    describe "online branch" do
      let!(:online_branch) { create(:branch, :online, organization:) }
      let!(:online_coupon) { create(:cupon, :online, branch: online_branch) }

      it "returns coupons of online branch" do
        get "/api/v1/public_integration/branches/#{online_branch.id}/coupons", headers: auth_headers, as: :json
        expect(response).to be_ok
        expect(response_hash.pluck("id")).to eq([online_coupon.id])
        expect(response_hash.first["cashback_text"]).to eq("10% de cashback")
      end
    end

    context "given a promotion with tags" do
      let!(:branch) { create(:branch, :online, organization:) }

      let!(:exclusive_promotion_with_matching_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, tags: ["Tag 1", "Tag 2"], business:) }
      let!(:exclusive_coupon_with_matching_tags) { create(:cupon, :online, branch: branch, promotion: exclusive_promotion_with_matching_tags, business:) }

      let!(:unrelated_exclusive_promotion_with_matching_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, tags: ["Tag 1", "Tag 2"], business: another_business) }
      let!(:unrelated_exclusive_coupon_with_matching_tags) { create(:cupon, :online, branch: branch, promotion: unrelated_exclusive_promotion_with_matching_tags, business: another_business) }

      let!(:exclusive_promotion_with_unmatching_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, tags: ["Tag 3"], business:) }
      let!(:exclusive_coupon_with_unmatching_tags) { create(:cupon, :online, branch: branch, promotion: exclusive_promotion_with_unmatching_tags, business:) }

      let!(:alloyal_promotion_with_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, tags: ["Tag 5", "Tag 6"]) }
      let!(:alloyal_coupon_with_tags) { create(:cupon, :online, branch: branch, promotion: alloyal_promotion_with_tags) }

      let!(:exclusive_promotion_without_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, business:) }
      let!(:exclusive_coupon_without_tags) { create(:cupon, :online, branch: branch, promotion: exclusive_promotion_without_tags, business:) }

      let!(:alloyal_promotion_without_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:) }
      let!(:alloyal_coupon_without_tags) { create(:cupon, :online, branch: branch, promotion: alloyal_promotion_without_tags) }

      it "is expected to return all coupons from promotions with tags matching current_user tags" do
        signed_user.authorized_user.update(tags: ["tag 2"])

        get "/api/v1/public_integration/branches/#{branch.id}/coupons", headers: auth_headers, as: :json
        expect(response).to be_ok
        expect(
          [exclusive_coupon_with_matching_tags.id, alloyal_coupon_with_tags.id, alloyal_coupon_without_tags.id, exclusive_coupon_without_tags.id]
        ).to all be_in response_hash.pluck("id")
        expect(response_hash.pluck("id")).not_to include(exclusive_coupon_with_unmatching_tags.id)
        expect(response_hash.pluck("id")).not_to include(unrelated_exclusive_coupon_with_matching_tags.id)
        expect(response_hash.filter { _1["id"] == exclusive_coupon_with_matching_tags.id }.dig(0, "tags")).to eq(["Tag 1", "Tag 2"])
      end
    end
  end
end
