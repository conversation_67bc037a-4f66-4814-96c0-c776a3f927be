# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V1::Categories::CouponsController, type: :request do
  let!(:business) { create(:business) }
  let(:authorized_user) { create(:authorized_user, :with_user, business:, tags: ["Tag 1", "Tag 2"]) }
  let(:user) { authorized_user.user }

  let!(:promotion) { create(:promotion, :available, :percent_discount, tags: ["Tag 1", "Tag 2"]) }
  let!(:category) { create(:category, :promotion_category, promotions: [promotion], business:) }
  let!(:coupon_newer_expires_first) do
    create(:cupon, :percent_discount, promotion:, created_at: 1.day.ago, end_date: 1.year.from_now)
  end
  let!(:coupon_older_expires_last) do
    create(:cupon, :percent_discount, promotion:, created_at: 2.days.ago, end_date: 2.years.from_now)
  end

  let!(:sub_category_promotion) { create(:promotion, :available, :percent_discount, tags: ["Tag 1", "Tag 2"]) }
  let!(:sub_category) { create(:category, :promotion_category, promotions: [sub_category_promotion], business:, main_category: category) }
  let!(:sub_category_coupon) do
    create(:cupon, :percent_discount, promotion: sub_category_promotion, created_at: 3.days.ago, end_date: 3.years.from_now)
  end

  let(:headers) { user.create_new_auth_token.merge!("Api-Secret": business.api_secret) }
  let(:response_hash) { JSON.parse(response.body) }
  let(:serializer_keys) do
    %w[usage_instruction description id template title cashback_text online_payment_text discount start_date end_date infinity
      picture_small_url picture_large_url branch_id organization_id tags]
  end

  describe "GET /api/v1/public_integration/categories/:id/coupons" do
    context "without coordinates" do
      it "returns all uniq coupons by category" do
        get "/api/v1/public_integration/categories/#{category.id}/coupons",
          headers:,
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.pluck("id")).to eq([coupon_newer_expires_first.id, coupon_older_expires_last.id])
        expect(response_hash.first.keys).to match_array(serializer_keys)
        expect(response_hash.pluck("discount")).to match_array([30, 30])
        expect(response_hash.pluck("tags")).to match_array([coupon_newer_expires_first.tags, coupon_older_expires_last.tags])
      end

      context "and filtering by creation date asc" do
        let(:params) { {order_by: "creation_date_asc"} }

        it "renders ok" do
          get "/api/v1/public_integration/categories/#{category.id}/coupons",
            headers:,
            params:,
            as: :json
          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([coupon_older_expires_last.id, coupon_newer_expires_first.id])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end

      context "and filtering by creation date desc" do
        let(:params) { {order_by: "creation_date_desc"} }

        it "renders ok" do
          get "/api/v1/public_integration/categories/#{category.id}/coupons",
            headers:,
            params:,
            as: :json
          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([coupon_newer_expires_first.id, coupon_older_expires_last.id])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end

      context "and filtering by expiration date asc" do
        let(:params) { {order_by: "expiration_date_asc"} }

        it "renders ok" do
          get "/api/v1/public_integration/categories/#{category.id}/coupons",
            headers:,
            params:,
            as: :json
          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([coupon_newer_expires_first.id, coupon_older_expires_last.id])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end

      context "and filtering by expiration date desc" do
        let(:params) { {order_by: "expiration_date_desc"} }

        it "renders ok" do
          get "/api/v1/public_integration/categories/#{category.id}/coupons",
            headers:,
            params:,
            as: :json
          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([coupon_older_expires_last.id, coupon_newer_expires_first.id])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end
    end

    context "with coordinates" do
      let!(:branch_physical) { create(:branch, lat: 1.0, lng: 1.0) }
      let!(:coupon_qrcode) { create(:cupon, :classic, branch: branch_physical, promotion:) }
      let(:params) { {lat: 1.0, lng: 1.0} }

      it "returns near organizations with category" do
        get "/api/v1/public_integration/categories/#{category.id}/coupons",
          headers:,
          params:,
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.pluck("id")).to eq([coupon_qrcode.id])
        expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      end
    end

    context "when filtering by tags" do
      let(:headers) { user.create_new_auth_token.merge!("Api-Secret" => business.api_secret) }
      let!(:organization) { create(:organization, :active_with_coupon) }
      let!(:branch) { create(:branch, :online, organization:) }
      let(:business) { create(:business) }
      let!(:online_promotion_without_tag) { create(:promotion, :online, :available, business:, organization:, redeems_per_cpf: 999) }
      let!(:alloyal_online_promotion) { create(:promotion, :online, :available, organization:, redeems_per_cpf: 999) }

      let!(:online_coupon_without_tag) { create(:cupon, :online, promotion: online_promotion_without_tag, branch:) }
      let!(:alloyal_online_coupon) { create(:cupon, :online, promotion: alloyal_online_promotion, branch:) }
      let(:params) { {redeem_type: "online", page: 1} }

      context "and user does have tags" do
        before do
          authorized_user.update(tags: ["Tag 1"])
        end

        it "must return promotions filtering by tags that exists on user" do
          get("/api/v1/public_integration/categories/#{category.id}/coupons", headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id")).to match_array([coupon_newer_expires_first.id, coupon_older_expires_last.id])
        end
      end

      context "and user does not have tags" do
        before do
          user.authorized_user.update(tags: nil)
          category.promotions = [promotion, alloyal_online_promotion, online_promotion_without_tag]
          category.save
        end

        it "must ignore tags and return everything" do
          get("/api/v1/public_integration/categories/#{category.id}/coupons", headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id")).to match_array([
            coupon_newer_expires_first.id,
            coupon_older_expires_last.id,
            alloyal_online_coupon.id,
            online_coupon_without_tag.id
          ])
        end
      end
    end
  end
end
