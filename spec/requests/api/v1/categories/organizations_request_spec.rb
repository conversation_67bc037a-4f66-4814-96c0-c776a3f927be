require "rails_helper"

RSpec.describe Api::V1::Categories::OrganizationsController, type: :request do
  let!(:business) { create(:business) }
  let!(:user) { create(:user, business:) }
  let!(:category) { create(:category) }
  let!(:sub_category) { create(:category, main_category: category) }
  let!(:organization) { create(:organization, :active_with_coupon, :percent_highest_discount, highest_discount_value: 10, categories: [category]) }
  let!(:sub_category_organization) { create(:organization, :active_with_coupon, :percent_highest_discount, highest_discount_value: 20, categories: [sub_category]) }

  let!(:branch_physical) { create(:branch, organization:, lat: 1.0, lng: 1.0, promotion_redeemable: true) }
  let!(:sub_category_branch_physical) { create(:branch, organization: sub_category_organization, lat: 1.0, lng: 1.0, promotion_redeemable: true) }

  let!(:promotion_qrcode) { create(:promotion, :qrcode, :available, business:, organization:) }
  let!(:coupon_qrcode) { create(:cupon, :classic, promotion: promotion_qrcode, branch: branch_physical) }
  let!(:sub_category_promotion_qrcode) { create(:promotion, :qrcode, :available, business:, organization: sub_category_organization) }
  let!(:sub_category_coupon_qrcode) { create(:cupon, :classic, promotion: sub_category_promotion_qrcode, branch: sub_category_branch_physical) }
  let(:headers) { user.create_new_auth_token.merge!("Api-Secret" => business.api_secret) }
  let(:response_hash) { JSON.parse(response.body) }

  describe "GET /api/v1/public_integration/categories/:id/organizations" do
    context "with coordinates" do
      let(:serializer_keys) do
        %w[id name cover_picture background_picture best_discount_percent
          category_name distance_km instagram_url facebook_url twitter_url
          cashback_percent cashback_text discount_text description favorited
          short_description top_background_image_small top_background_image_large
          top_background_image_v2_small top_background_image_v2_large logo_image_small
          banner_image_large banner_image_small banner_image_v2_large banner_image_v2_small
          logo_image top_background_image]
      end
      let(:params) { {lat: 1.0, lng: 1.0} }

      it "returns near organizations with category" do
        get "/api/v1/public_integration/categories/#{category.id}/organizations",
          headers:,
          params:,
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        expect(response_hash.pluck("id")).to match_array([organization.id, sub_category_organization.id])
        expect(response_hash.pluck("best_discount_percent")).to match_array([10, 20])
      end
    end

    context "without coordinates" do
      let(:serializer_keys) do
        %w[id name cover_picture background_picture best_discount_percent
          category_name instagram_url facebook_url twitter_url
          cashback_percent cashback_text discount_text description favorited
          short_description top_background_image_small top_background_image_large
          top_background_image_v2_small top_background_image_v2_large logo_image_small
          banner_image_large banner_image_small banner_image_v2_large banner_image_v2_small
          logo_image top_background_image]
      end

      it "returns organizations with category" do
        get "/api/v1/public_integration/categories/#{category.id}/organizations",
          headers:,
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash.pluck("id")).to match_array([organization.id, sub_category_organization.id])
        expect(response_hash.pluck("best_discount_percent")).to match_array([10, 20])
      end
    end

    context "with a inactive category" do
      before do
        sub_category.update(active: false)
      end

      it "must not return its organizations" do
        get "/api/v1/public_integration/categories/#{category.id}/organizations",
          headers:,
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash.pluck("id")).to match_array([organization.id])
        expect(response_hash.pluck("id")).not_to include(sub_category_organization.id)
      end
    end
  end
end
