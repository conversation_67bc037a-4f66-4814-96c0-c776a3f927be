# frozen_string_literal: true

require "rails_helper"

RSpec.describe "Categories management", type: :request do
  let!(:business) { create(:business, :lecupon_equipe) }
  let(:signed_user) { create(:user, business:) }
  let(:auth_headers) do
    signed_user.create_new_auth_token.merge!("Api-Secret": business.api_secret)
  end
  let(:category_keys) { %w[id title icon category_type image image_v2 template subcategories] }
  let!(:category_one) { create(:category, created_at: "2021-10-11 10:00:00") }
  let!(:category_two) { create(:category, created_at: "2021-10-12 10:00:00") }
  let!(:organization_one) { create(:organization, :active_with_coupon, categories: [category_one]) }
  let!(:organization_two) { create(:organization, :active_with_coupon, categories: [category_two]) }

  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    context "cashback" do
      let!(:category_cashback) { create(:category, created_at: "2021-10-13 10:00:00", title: "Cashback") }
      let!(:organization_cashback) do
        create(:organization, :active_with_coupon, highest_cashback_value: 10.0, categories: [category_cashback])
      end
      context "accept cashback" do
        let!(:business) { create(:business, :lecupon_equipe, :with_cashback) }

        it "returns list of categories and cashback category" do
          get "/api/v1/public_integration/categories",
            params: {lat: 1.0, lng: 1.0},
            headers: auth_headers,
            as: :json

          expect(response).to have_http_status(:ok)
          expect(response_hash.count).to eq(3)
          expect(response_hash.first.keys).to match_array(category_keys)
        end
      end
      context "not accept cashback" do
        before do
          business.update!(cashback: false)
        end
        it "returns list of categories and not cashback category" do
          get "/api/v1/public_integration/categories",
            params: {lat: 1.0, lng: 1.0},
            headers: auth_headers,
            as: :json

          expect(response).to have_http_status(:ok)
          expect(response_hash.count).to eq(2)
          expect(response_hash.first.keys).to match_array(category_keys)
        end
      end
    end
    context "without blocklist" do
      it "returns list of categories that have coupons" do
        get "/api/v1/public_integration/categories",
          params: {lat: 1.0, lng: 1.0},
          headers: auth_headers,
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash.count).to eq(2)
        expect(response_hash.first.keys).to match_array(category_keys)
      end
    end

    context "with blocklist" do
      let!(:category_blocklist) do
        category = organization_one.categories.first
        create(:category_blocklist, business:, category:)
      end

      it "returns list of categories without blocklist" do
        get "/api/v1/public_integration/categories",
          params: {lat: 1.0, lng: 1.0},
          headers: auth_headers,
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash.count).to eq(1)
      end
    end

    context "when there are categories without coupons" do
      let!(:category_without_coupon) { create(:category) }
      let!(:organization_without_coupon) { create(:organization, categories: [category_without_coupon]) }

      it "returns all categories" do
        get "/api/v1/public_integration/categories",
          params: {lat: 1.0, lng: 1.0},
          headers: auth_headers,
          as: :json

        expect(response).to be_successful
        expect(JSON.parse(response.body).count).to eq(3)
      end
    end

    describe "available categories" do
      let!(:category_three) { create(:category) }

      let!(:branch_one) { create(:branch, organization: organization_one, lat: 1.0, lng: 1.0) }
      let!(:branch_online) { create(:branch, :online, organization: organization_one) }
      let!(:coupon_one) { create(:cupon, :online, branch: branch_online, business:) }
      let!(:coupon_two) { create(:cupon, :classic, branch: branch_one, business:) }

      let!(:branch_two) { create(:branch, organization: organization_two, lat: -20.0, lng: 40.0) }
      let!(:coupon_four) { create(:cupon, :cpf, branch: branch_two, business:) }

      let!(:organization_three) { create(:organization, :active_with_coupon, categories: [category_three]) }
      let!(:branch_online_two) { create(:branch, :online, organization: organization_three) }
      let!(:coupon_three) { create(:cupon, :online, branch: branch_online_two, business:) }

      it "returns all categories ordering by nearby and online organizations with coupons" do
        get "/api/v1/public_integration/categories",
          params: {lat: 1.0, lng: 1.0},
          headers: auth_headers,
          as: :json

        expect(response).to be_successful
        expect(response_hash.count).to eq(3)
        expect(response_hash.pluck("id")).to match_array([category_one.id, category_three.id, category_two.id])
      end
    end

    context "with no lat and lng" do
      let!(:category_three) { create(:category) }

      let!(:branch_one) { create(:branch, organization: organization_one, lat: 1.0, lng: 1.0) }
      let!(:branch_online) { create(:branch, :online, organization: organization_one) }
      let!(:coupon_one) { create(:cupon, :online, branch: branch_online, business:) }
      let!(:coupon_two) { create(:cupon, :classic, branch: branch_one, business:) }

      let!(:branch_two) { create(:branch, organization: organization_two, lat: -20.0, lng: 40.0) }
      let!(:coupon_four) { create(:cupon, :cpf, branch: branch_two, business:) }

      let!(:organization_three) { create(:organization, :active_with_coupon, categories: [category_three]) }
      let!(:branch_online_two) { create(:branch, :online, organization: organization_three) }
      let!(:coupon_three) { create(:cupon, :online, branch: branch_online_two, business:) }

      let!(:category_blocklist) do
        create(:category_blocklist, business:, category_id: category_one.id)
      end

      it "returns all categories that are not blocklisted ordered by created_at" do
        get "/api/v1/public_integration/categories",
          headers: auth_headers,
          as: :json

        expect(response).to be_successful
        expect(response_hash.pluck("id")).to eq([category_two.id, category_three.id])
      end
    end

    context "with category_type" do
      it "returns all categories that are not blocklisted ordered by created_at" do
        get "/api/v1/public_integration/categories",
          headers: auth_headers,
          as: :json,
          params: {category_type: Enums::Category::Type::ORGANIZATION}

        expect(response).to be_successful
        expect(response_hash.pluck("id")).to eq([category_one.id, category_two.id])
      end
    end

    context "when including subcategories" do
      let!(:category) { create(:category) }
      let!(:subcategory) { create(:category, main_category: category) }
      let!(:blocked_subcategory) { create(:category, main_category: category) }
      let!(:category_blocklist) { create(:category_blocklist, category: blocked_subcategory, business:) }
      let(:subcategory_keys) { %w[id title icon category_type image image_v2 template] }

      it "render subcategories as nested resources" do
        get "/api/v1/public_integration/categories",
          headers: auth_headers,
          as: :json,
          params: {include: [:subcategories]}

        expect(response).to be_successful
        expect(response_hash.first.keys).to match_array(category_keys)
        main_category_payload = response_hash.find { _1["id"] == category.id }
        expect(main_category_payload["subcategories"].count).to eq 1
        expect(main_category_payload["subcategories"].first.keys).to match_array(subcategory_keys)
      end

      it "returns empty field when not including subcategories" do
        get "/api/v1/public_integration/categories",
          headers: auth_headers,
          as: :json

        expect(response).to be_successful
        expect(response_hash.first.keys).to match_array(category_keys)
        main_category_payload = response_hash.find { _1["id"] == category.id }
        expect(main_category_payload["subcategories"].pluck("id")).not_to include(blocked_subcategory.id)
        expect(main_category_payload["subcategories"]).to eq [{"id" => subcategory.id}]
      end
    end
  end
end
