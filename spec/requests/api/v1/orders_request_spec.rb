# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V1::OrdersController, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  let!(:business) { create(:business, :lecupon_equipe) }
  let(:signed_user) { create(:user, cpf: "***********", cellphone: "***********", business:) }
  let(:auth_headers) do
    signed_user.create_new_auth_token.merge!("Api-Secret" => business.api_secret)
  end
  let!(:organization) { create(:organization, :active_with_coupon) }
  let!(:branch) { create(:branch, organization:, lat: 1.0, lng: 1.0) }
  let!(:tagging) { create(:tagging, :for_organization, taggable: organization) }
  let!(:cupon_classic) { create(:cupon, :classic, branch:) }
  let!(:cupon_cpf) { create(:cupon, :cpf, branch:) }
  let!(:cupon_dynamic) { create(:cupon, :dynamic, branch:) }

  let(:voucher_bucket_lbc_gift_card) { create(:voucher_bucket) }
  let!(:promotion_lbc_gift_card) { create(:promotion, :lbc_giftcard, :available, voucher_bucket: voucher_bucket_lbc_gift_card) }
  let!(:cupon_lbc_gift_card) { create(:cupon, :lbc_giftcard, promotion: promotion_lbc_gift_card, branch:) }
  let!(:voucher_lbc_gift_card) { create(:voucher, bucket: voucher_bucket_lbc_gift_card) }

  let(:voucher_bucket_fixed) { create(:voucher_bucket) }
  let!(:promotion_fixed) { create(:promotion, :coupon_code, :available, code: "ABC123", voucher_bucket: voucher_bucket_fixed) }
  let!(:cupon_fixed) { create(:cupon, :coupon_code, promotion: promotion_fixed, branch:) }
  let!(:voucher_coupon_code) { create(:voucher, bucket: voucher_bucket_fixed) }

  let(:voucher_bucket) { create(:voucher_bucket) }
  let!(:promotion_voucher) { create(:promotion, :coupon_code, voucher_bucket:) }
  let!(:coupon_voucher) { create(:cupon, :voucher, branch:, promotion: promotion_voucher) }
  let!(:voucher) { create(:voucher, bucket: voucher_bucket) }

  let(:voucher_bucketfixed_code) { create(:voucher_bucket) }
  let!(:promotion_fixed_code) { create(:promotion, :fixed_code, :available, code: "ABC123", voucher_bucket: voucher_bucketfixed_code) }
  let!(:coupon_fixed_code) { create(:cupon, :fixed_code, promotion: promotion_fixed_code, branch:, code: "CODIGOFIXO") }

  let(:redeem_cupon_serializer_keys) do
    %w[id number created_at organization_name description discount redeem_code title
      coupon_description coupon_discount coupon_number paid_at usage_ended_at redeem_instruction
      giftcard_id organization_cover_picture price cashback_type cashback_value]
  end
  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    context "with coupon order" do
      let!(:order) { create(:order, user: signed_user, cupon: cupon_fixed, voucher: voucher_coupon_code) }
      let!(:order_without_coupon) { create(:order_without_coupon, user: signed_user, organization:, branch:) }

      it "list all orders" do
        get "/api/v1/public_integration/orders",
          headers: auth_headers,
          params: {coupon_id: cupon_fixed.id},
          as: :json

        expect(response).to have_http_status(:success)
        expect(response_hash.count).to eq(2)
        expect(response_hash.first.keys).to match_array(redeem_cupon_serializer_keys)
      end
    end

    context "when filtering by giftcard orders" do
      let!(:bucket) { create(:voucher_bucket) }
      let!(:gift_card) { create(:giftcard, :with_vouchers, :with_branches, quantity: 4, voucher_bucket: bucket) }
      let!(:order_reserved) do
        create(:order, :pending, :with_giftcard, user: signed_user, giftcard: gift_card, usage_ended_at: 1.day.from_now, voucher: gift_card.vouchers[0])
      end
      let!(:order_completed_first_to_expire) do
        create(:order, :completed, :with_giftcard, :percent_discount, user: signed_user, giftcard: gift_card, usage_ended_at: 1.day.from_now, voucher: gift_card.vouchers[1])
      end
      let!(:order_completed_second_to_expire) do
        create(:order, :completed, :with_giftcard, :percent_discount, user: signed_user, giftcard: gift_card, usage_ended_at: 3.days.from_now, voucher: gift_card.vouchers[2])
      end
      let!(:order_completed_expired) do
        create(:order, :completed, :with_usage_expired, :percent_discount, :with_giftcard, giftcard: gift_card, user: signed_user, voucher: gift_card.vouchers[3])
      end
      let(:serializer_keys) do
        %w[id number created_at organization_name description discount redeem_code title
          coupon_description coupon_discount paid_at usage_ended_at redeem_instruction
          giftcard_id organization_cover_picture price cashback_type cashback_value]
      end
      let(:params) { {product: "giftcard"} }

      before { Business::UpdateService.new(business:, params: {giftcard: true}).call }

      it "returns giftcard orders sorted by expiration" do
        get("/api/v1/public_integration/orders", headers: auth_headers, params:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.map(&:keys)).to all match_array(serializer_keys)
        expect(response_hash.pluck("coupon_discount")).to eq(["30", "30", "30"])
        expect(response_hash.pluck("discount")).to eq(["30", "30", "30"])
        expect(response_hash.pluck("id")).to eq([
          order_completed_first_to_expire.id,
          order_completed_second_to_expire.id,
          order_completed_expired.id
        ])
      end

      context "and filtering by status other than completed" do
        let(:params) { {status: "pending", product: "giftcard"} }

        it "returns empty" do
          get("/api/v1/public_integration/orders", headers: auth_headers, params:)

          expect(response).to have_http_status(:ok)
          expect(response_hash).to be_empty
        end
      end
    end
  end

  describe "#destroy" do
    before { business.update!(giftcard: true) }

    context "with deletable order for user" do
      let!(:bucket) { create(:voucher_bucket) }
      let!(:gift_card) { create(:giftcard, :with_vouchers, :with_branches, quantity: 1, voucher_bucket: bucket) }
      let!(:order) { create(:order, :pending, :with_giftcard, user: signed_user, giftcard: gift_card, voucher: gift_card.vouchers[0]) }

      it "returns successful" do
        delete "/api/v1/public_integration/orders/#{order.id}", headers: auth_headers

        expect(response).to be_no_content
      end
    end

    context "with no deletable order for user" do
      let!(:order) { create(:order, :completed, user: signed_user) }

      it "returns successful" do
        delete "/api/v1/public_integration/orders/#{order.id}", headers: auth_headers

        expect(response).to be_no_content
      end
    end

    context "with no order for user" do
      it "returns successful" do
        delete "/api/v1/public_integration/orders/0", headers: auth_headers

        expect(response).to be_no_content
      end
    end
  end

  describe "#create" do
    it "renders created" do
      expect do
        post "/api/v1/public_integration/orders",
          headers: auth_headers,
          params: {coupon_id: cupon_fixed.id},
          as: :json
      end.to change(Order, :count).by(1)

      expect(response).to have_http_status(:created)
      expect(response.parsed_body.keys).to match_array %w[id deep_link redeem_code redeem_instruction number]
    end

    context "when try to redeem a coupom that has already been redeemed" do
      let(:active_organization) { create(:organization, active: true, promotion_redeemable: true) }
      let(:active_branch_online) do
        create(
          :branch,
          :online,
          organization: active_organization,
          cnpj: "32.839.587/0001-13",
          active: true
        )
      end

      it "is expected a error on redemption" do
        bucket = create(:voucher_bucket)
        promotion_coupon_code = create(:promotion, :coupon_code, :available, voucher_bucket: bucket)
        voucher = create(:voucher, bucket:)
        coupon = create(:cupon, :fixed, promotion: promotion_coupon_code, branch: active_branch_online)
        create(:order, cupon: coupon, user: signed_user, voucher:)

        post "/api/v1/public_integration/orders",
          headers: auth_headers,
          params: {coupon_id: coupon.id},
          as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body["error"]).to eq("Voucher não disponível para resgate")
      end
    end
  end
end
