# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V1::ProjectConfigsController, type: :request do
  describe "GET /api/v1/public_integration/project_configs/app_config" do
    before do
      allow(Rails.application.credentials).to receive(:android_min_version).and_return("2.1")
      allow(Rails.application.credentials).to receive(:ios_min_version).and_return("2.2")
    end

    let!(:business) { create(:business, create_project_config: false) }
    let!(:project_config) do
      create(
        :project_config,
        business:,
        android_version: "2.1",
        ios_version: "2.1",
        google_identifier: "br.com.android.vantagensclube",
        apple_identifier: "br.com.ios.vantagensclube",
        google_store_url: "https://play.google.com/store/apps/details?id=br.com.android.vantagensclube",
        apple_store_url: "https://apps.apple.com/app/vantagens-clube/id12345678"
      )
    end
    let!(:serialized_keys) do
      %w[name primary_color secondary_color background_color font_color apple_icon_url
        google_icon_url web_icon_url logo_small_url logo_url logo_large_url svg_logo_url
        user_manager sign_up_url user_update_url user_manager_info deposit_destination
        deposit_destination_type cashback_auto_discount store_info ux_cam web_domain
        support support_url help_center_url gtm_tag_id
        facebook_sdk facebook_sdk_app_id facebook_sdk_app_name facebook_sdk_client_token telemedicine_url
        home_search giftcard_faq_url giftcard_config term_of_use_url]
    end

    let(:giftcard_config_keys) do
      %w[limit_by_user limit_by_user_period_interval limit_by_user_period_type]
    end

    let(:auth_headers) do
      {"Api-Secret": business.api_secret}
    end
    let(:response_hash) { JSON.parse(response.body) }

    it "renders a successful response" do
      get "/api/v1/public_integration/project_configs/app_config", headers: auth_headers

      expect(response).to be_successful
      expect(response_hash.keys).to match_array(serialized_keys)
      expect(response_hash["giftcard_config"].keys).to match_array(giftcard_config_keys)
      expect(response_hash["store_info"]).to eq({
        "android" => {
          "version" => "2.1",
          "min_version" => "2.1",
          "check_update" => true,
          "store_url" => "https://play.google.com/store/apps/details?id=br.com.android.vantagensclube"
        },
        "ios" => {
          "min_version" => "2.2",
          "check_update" => false,
          "version" => "2.1",
          "store_url" => "https://apps.apple.com/app/vantagens-clube/id12345678"
        }
      })
    end

    it "renders a successful response with alternate route" do
      get "/api/v1/public_integration/business_apps", headers: auth_headers
      expect(response).to be_successful
      expect(response_hash.keys).to match_array(serialized_keys)
      expect(response_hash["store_info"]).to eq({
        "android" => {
          "version" => "2.1",
          "min_version" => "2.1",
          "check_update" => true,
          "store_url" => "https://play.google.com/store/apps/details?id=br.com.android.vantagensclube"
        },
        "ios" => {
          "min_version" => "2.2",
          "check_update" => false,
          "version" => "2.1",
          "store_url" => "https://apps.apple.com/app/vantagens-clube/id12345678"
        }
      })
    end

    it "must return user_manager as 'internal' if default" do
      project_config.update(user_manager: Enums::UserManager::DEFAULT)

      get "/api/v1/public_integration/business_apps", headers: auth_headers
      expect(response).to be_successful
      expect(response_hash["user_manager"]).to eq("internal")
    end
  end
end
