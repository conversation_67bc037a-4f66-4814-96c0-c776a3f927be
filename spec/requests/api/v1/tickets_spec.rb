require "rails_helper"

RSpec.describe Api::V1::TicketsController, :vcr, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  let!(:oab) { create(:business, :oab) }
  let(:user) { create(:user, cpf: "***********", cellphone: "***********", business: oab) }
  let!(:organization) { create(:organization, active: true, promotion_redeemable: true, highest_cashback_value: 3.5) }
  let!(:branch) { create(:branch, organization:, lat: 1.0, lng: 1.0) }
  let!(:branch_two) { create(:branch, organization:, lat: 1.0, lng: 1.0) }
  let!(:branch_online) { create(:branch, :online, organization:) }
  let!(:cupon_online) do
    create(:cupon,
      :online,
      branch: branch_online)
  end
  let(:default_cupon) do
    cupon_online.update(
      url: "http://example.com/cupon/1234"
    )
  end

  describe "#create" do
    describe "post on create ticket" do
      it "create ticket from a default cupon" do
        default_cupon
        post "/api/v1/cupons/#{cupon_online.id}/tickets?mref=#{user.id}"

        expect(response).to have_http_status(:found)
        expect(response.redirect_url).to eq(Order.last.deep_link)
        expect(Order.last.cashback_value).to eq(cupon_online.promotion.cashback_value)
      end
    end

    describe "get on create ticket" do
      it "create ticket from a default cupon" do
        default_cupon
        get "/api/v1/cupons/#{cupon_online.id}/tickets?mref=#{user.id}"
        expect(response).to have_http_status(:found)
        expect(response.redirect_url).to eq(Order.last.deep_link)
        expect(Order.last.cashback_value).to eq(cupon_online.promotion.cashback_value)
      end

      context "without params" do
        it "redirect to lecupon_backup_link" do
          default_cupon
          get "/api/v1/cupons/#{cupon_online.id}/tickets?mref="
          expect(response).to have_http_status(:found)
          expect(response.redirect_url).to eq(Rails.application.credentials.lecupon_backup_link)
        end
      end
    end

    describe "clickref in order url" do
      context "with awin coupon" do
        let!(:awin_organization) { create(:organization, :active_with_coupon, :with_awin_integration) }
        let!(:awin_branch_online) { create(:branch, :online, organization: awin_organization) }
        let(:awin_coupon) do
          create(
            :cupon,
            :online,
            branch: awin_branch_online,
            integration_code: "ABCD1234",
            integration_partner: IntegrationPartner::AWIN,
            url: "https://www.awin1.com/cread.php?awinaffid=1234&awinmid=4321&ued=https%3A%2F%2Fwww.example.com.br"
          )
        end

        it "creates order url with awin clickref" do
          post "/api/v1/cupons/#{awin_coupon.id}/tickets?mref=#{user.id}"

          expect(Order.last.deep_link.downcase).to include("clickref")
        end
      end

      context "with any coupon other than awin" do
        let!(:organization) { create(:organization, :active_with_coupon) }
        let!(:branch_online) { create(:branch, :online, organization:) }
        let(:online_coupon) do
          create(
            :cupon,
            :online,
            branch: branch_online,
            url: "http://www.example.com/coupon/9876"
          )
        end

        it "does not create ticket with awin clickref" do
          post "/api/v1/cupons/#{online_coupon.id}/tickets?mref=#{user.id}"

          expect(Order.last.deep_link.downcase).not_to include("clickref")
        end
      end
    end

    context "when coupon is not available" do
      before do
        travel_to Time.zone.parse("2021-12-10 10:00:00")

        cupon_online.update!(start_hour: "12:00", end_hour: "20:00")
      end

      it "returns error and does not create ticket" do
        post "/api/v1/cupons/#{cupon_online.id}/tickets?mref=#{user.id}"

        expect(response).to have_http_status(:unprocessable_entity)
        response_hash = JSON.parse(response.body)
        expect(response_hash["error"]).to eq("Cupom fora do horário de utilização")
        expect(Order.count).to eq(0)
      end
    end

    context "with dynamic online voucher coupon" do
      let!(:bucket) { create(:voucher_bucket) }
      let!(:promotion) { create(:promotion, :dynamic_coupon_code, :percent_discount, :available, dynamic_voucher: true, voucher_bucket: bucket) }
      let!(:coupon_dynamic_online) { create(:cupon, :dynamic_online, branch: branch_online, promotion:) }
      let!(:voucher) { create(:voucher, bucket:) }

      context "with voucher already redeemed" do
        let(:headers) { user.create_new_auth_token.merge!("Api-Secret" => oab.api_secret) }
        let!(:redeem_voucher) do
          get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{coupon_dynamic_online.id}",
            headers:,
            as: :json
        end

        it "redirects to order deep link" do
          expect do
            post "/api/v1/cupons/#{coupon_dynamic_online.id}/tickets?mref=#{user.id}"
          end.not_to change(Order, :count)
          expect(response).to have_http_status(:found)
          order = Order.last
          expect(response.redirect_url).to eq(order.deep_link)
          voucher.reload
          expect(order.redeem_code).not_to eq(voucher.code)
          expect(voucher.order).to be_nil
        end
      end

      context "with voucher not redeemed" do
        it "renders error" do
          expect do
            post "/api/v1/cupons/#{coupon_dynamic_online.id}/tickets?mref=#{user.id}"
          end.not_to change(Order, :count)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(voucher.reload.order).to eq(nil)
        end
      end
    end

    context "without dynamic online voucher coupon" do
      let!(:bucket) { create(:voucher_bucket) }
      let!(:promotion) { create(:promotion, :dynamic_coupon_code, :percent_discount, :available, dynamic_voucher: false, voucher_bucket: bucket) }
      let!(:coupon_dynamic_online) { create(:cupon, :dynamic_online, branch: branch_online, promotion:) }
      let!(:voucher) { create(:voucher, bucket:) }

      context "with voucher already redeemed" do
        let(:headers) { user.create_new_auth_token.merge!("Api-Secret" => oab.api_secret) }
        let!(:redeem_voucher) do
          get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{coupon_dynamic_online.id}",
            headers:,
            as: :json
        end

        it "redirects to order deep link" do
          expect do
            post "/api/v1/cupons/#{coupon_dynamic_online.id}/tickets?mref=#{user.id}"
          end.not_to change(Order, :count)
          expect(response).to have_http_status(:found)
          order = Order.last
          expect(response.redirect_url).to eq(order.deep_link)
          voucher.reload
          expect(order.redeem_code).to eq(voucher.code)
          expect(voucher.order).to eq(order)
        end
      end

      context "with voucher not redeemed" do
        it "renders error" do
          expect do
            post "/api/v1/cupons/#{coupon_dynamic_online.id}/tickets?mref=#{user.id}"
          end.not_to change(Order, :count)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(voucher.reload.order).to eq(nil)
        end
      end
    end

    context "when coupon is not found" do
      it "returns not found error" do
        post "/api/v1/cupons/999999/tickets?mref=#{user.id}"

        expect(response).to have_http_status(:not_found)
        response_hash = JSON.parse(response.body)
        expect(response_hash["error"]).to eq("Registro(s) não encontrado(s).")
        expect(response_hash["status"]).to eq(404)
      end
    end
  end
end
