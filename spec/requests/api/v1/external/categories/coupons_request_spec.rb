# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V1::External::Categories::CouponsController, type: :request do
  let!(:business) { create(:business) }
  let!(:promotion) { create(:promotion, :available, :percent_discount, tags: ["Tag 1", "Tag 2"]) }
  let!(:category) { create(:category, :promotion_category, promotions: [promotion], business:) }
  let!(:coupon_newer_expires_first) do
    create(:cupon, :percent_discount, promotion:, created_at: 1.day.ago, end_date: 1.year.from_now)
  end
  let!(:coupon_older_expires_last) do
    create(:cupon, :percent_discount, promotion:, created_at: 2.days.ago, end_date: 2.years.from_now)
  end
  let(:user) { create(:user, business:) }
  let(:headers) { user.create_new_auth_token.merge!({"Api-Secret": business.api_secret}) }
  let(:response_hash) { JSON.parse(response.body) }
  let(:serializer_keys) do
    %w[description id template title cashback_text online_payment_text discount start_date end_date infinity
      picture_small_url picture_large_url branch_id organization_id tags usage_instruction]
  end

  describe "GET /api/v1/external/categories/:id/coupons" do
    context "without coordinates" do
      it "returns all uniq coupons by category" do
        get "/api/v1/external/categories/#{category.id}/coupons",
          headers:,
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash.pluck("id")).to eq([coupon_newer_expires_first.id, coupon_older_expires_last.id])
        expect(response_hash.first.keys).to match_array(serializer_keys)
        expect(response_hash.pluck("discount")).to match_array([30, 30])
        expect(response_hash.pluck("tags")).to match_array([coupon_newer_expires_first.tags, coupon_older_expires_last.tags])
      end

      context "and ordering by creation date asc" do
        let(:params) { {order_by: "creation_date_asc"} }

        it "renders ok" do
          get "/api/v1/external/categories/#{category.id}/coupons",
            headers:,
            params:,
            as: :json
          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([coupon_older_expires_last.id, coupon_newer_expires_first.id])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end

      context "and ordering by creation date desc" do
        let(:params) { {order_by: "creation_date_desc"} }

        it "renders ok" do
          get "/api/v1/external/categories/#{category.id}/coupons",
            headers:,
            params:,
            as: :json
          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([coupon_newer_expires_first.id, coupon_older_expires_last.id])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end

      context "and ordering by expiration date asc" do
        let(:params) { {order_by: "expiration_date_asc"} }

        it "renders ok" do
          get "/api/v1/external/categories/#{category.id}/coupons",
            headers:,
            params:,
            as: :json
          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([coupon_newer_expires_first.id, coupon_older_expires_last.id])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end

      context "and ordering by expiration date desc" do
        let(:params) { {order_by: "expiration_date_desc"} }

        it "renders ok" do
          get "/api/v1/external/categories/#{category.id}/coupons",
            headers:,
            params:,
            as: :json
          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([coupon_older_expires_last.id, coupon_newer_expires_first.id])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end
    end

    context "with coordinates" do
      let!(:branch_physical) { create(:branch, lat: 1.0, lng: 1.0) }
      let!(:coupon_qrcode) { create(:cupon, :classic, branch: branch_physical, promotion:) }
      let(:params) { {lat: 1.0, lng: 1.0} }

      it "returns near organizations with category" do
        get "/api/v1/external/categories/#{category.id}/coupons",
          headers:,
          params:,
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.pluck("id")).to eq([coupon_qrcode.id])
        expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      end
    end

    context "with tags" do
      let(:params) { {tags: ["Tag 1"]} }

      context "when exists a promotion with given tag" do
        it "returns near organizations with category" do
          get "/api/v1/external/categories/#{category.id}/coupons",
            headers:,
            params:,
            as: :json

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to eq([coupon_newer_expires_first.id, coupon_older_expires_last.id])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end

      context "when do not exist a promotion with given tag" do
        let(:params) { {tags: ["Tag 3"]} }

        it "returns near organizations with category" do
          get "/api/v1/external/categories/#{category.id}/coupons",
            headers:,
            params:,
            as: :json

          expect(response).to have_http_status(:ok)
          expect(response_hash).to eq([])
        end
      end

      context "when param tags is not present" do
        let(:params) { {} }

        let!(:alloyal_promotion) { create(:promotion, :available, :percent_discount) }
        let!(:alloyal_coupon) do
          create(:cupon, :percent_discount, promotion: alloyal_promotion, created_at: 1.day.ago, end_date: 1.year.from_now)
        end

        let!(:untagged_promotion) { create(:promotion, :available, :percent_discount) }
        let!(:untagged_coupon) do
          create(:cupon, :percent_discount, promotion: untagged_promotion, created_at: 1.day.ago, end_date: 1.year.from_now)
        end

        let!(:category) { create(:category, :promotion_category, promotions: [promotion, alloyal_promotion, untagged_promotion]) }

        it "returns near organizations with category" do
          get "/api/v1/external/categories/#{category.id}/coupons",
            headers:,
            params:,
            as: :json

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to match_array([coupon_newer_expires_first.id, coupon_older_expires_last.id, alloyal_coupon.id, untagged_coupon.id])
        end
      end
    end

    describe "pagination" do
      let(:params) { {redeem_type: "online", page: 1, per_page: 10} }
      let(:category_two) { create(:category) }
      let!(:uniq_promotions_and_coupons) do
        11.times do |_n|
          uniq_promotion = create(:promotion, :available, :percent_discount)
          create(:cupon, :percent_discount, promotion: uniq_promotion, created_at: 1.day.ago, end_date: 1.year.from_now)
          category_two.promotions << uniq_promotion
        end
      end

      it "must paginate" do
        Promotion::PropagateChanges.call
        get("/api/v1/external/categories/#{category_two.id}/coupons", params:, headers:)
        expect(response).to be_ok
        page_1_response = JSON.parse(response.body)
        expect(page_1_response.count).to eq(params[:per_page])
        params[:page] = 2
        get("/api/v1/external/categories/#{category_two.id}/coupons", params: params, headers:)
        expect(response).to be_ok
        page_2_response = JSON.parse(response.body)
        expect(page_2_response.count).to eq(1)
        expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      end
    end
  end
end
