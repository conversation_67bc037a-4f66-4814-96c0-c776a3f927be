require "rails_helper"

RSpec.describe Api::V1::OrganizationsController, type: :request do
  let!(:business) { create(:business, :with_cashback) }
  let(:signed_user) { create(:user, business:) }
  let(:auth_headers) do
    signed_user.create_new_auth_token.merge!("Api-Secret" => business.api_secret)
  end
  let!(:category) { create(:category) }
  let!(:sub_category) { create(:category, main_category: category) }
  let!(:organization) do
    create(:organization,
      :active_with_coupon,
      :highlighted,
      :percent_highest_discount,
      categories: [category],
      highest_discount_value: 10,
      name: "Ze Delivery",
      promotion_redeemable: true)
  end
  let!(:sub_category_organization) do
    create(:organization,
      :active_with_coupon,
      :highlighted,
      :percent_highest_discount,
      categories: [sub_category],
      highest_discount_value: 10,
      name: "Ze Delivery Sub",
      promotion_redeemable: true)
  end
  let!(:tagging) { create(:tagging, :for_organization, taggable: organization) }
  let!(:branch) { create(:branch, branch_type: Enums::Branch::Type::PHYSICAL, organization:, lat: 1.0, lng: 1.0, promotion_redeemable: true) }
  let!(:online_branch) { create(:branch, :online, organization:, promotion_redeemable: true) }
  let!(:promotion) { create(:promotion, :online, :available, :percent_discount, organization:, cashback_value: 3.0) }
  let!(:cupon) { create(:cupon, :online, :percent_discount, promotion:, branch: online_branch) }
  let!(:sub_category_branch) { create(:branch, :online, organization: sub_category_organization, promotion_redeemable: true) }
  let!(:sub_category_physical_branch) { create(:branch, branch_type: Enums::Branch::Type::PHYSICAL, organization: sub_category_organization, lat: 1.0, lng: 1.0, promotion_redeemable: true) }
  let!(:sub_category_promotion) { create(:promotion, :online, :available, :percent_discount, organization: sub_category_organization, cashback_value: 3.0) }
  let!(:sub_category_cupon) { create(:cupon, :online, :percent_discount, promotion: sub_category_promotion, branch: sub_category_branch) }
  let!(:sub_category_physical_cupon) { create(:cupon, :classic, :percent_discount, promotion: promotion_two, branch: sub_category_physical_branch) }
  let!(:promotion_two) { create(:promotion, :qrcode, :available, :percent_discount, organization:, cashback_value: 3.0) }
  let!(:cupon_two) { create(:cupon, :classic, :percent_discount, promotion: promotion_two, branch:) }
  let(:organization_show_serializer_keys) do
    ["background_picture", "best_discount_percent", "category_name", "cover_picture",
      "facebook_url", "id", "instagram_url", "name", "twitter_url", "cashback_percent",
      "distance_km", "cashback_text", "discount_text", "description", "favorited",
      "short_description", "top_background_image_small", "top_background_image_large",
      "top_background_image_v2_large", "top_background_image_v2_small", "logo_image_small",
      "banner_image_large", "banner_image_small", "banner_image_v2_large", "banner_image_v2_small",
      "logo_image", "top_background_image"]
  end
  let(:organization_store_serializer_keys) do
    ["id", "name", "cover_picture", "background_picture", "best_discount_percent",
      "category_name", "distance_km", "instagram_url", "facebook_url", "twitter_url",
      "cashback_percent", "cashback_text", "discount_text", "description", "favorited",
      "short_description", "top_background_image_small", "top_background_image_large",
      "top_background_image_v2_large", "top_background_image_v2_small", "logo_image_small",
      "banner_image_large", "banner_image_small", "banner_image_v2_large", "banner_image_v2_small",
      "logo_image", "top_background_image"]
  end
  let(:organization_online_serializer_keys) do
    ["id", "name", "cover_picture", "background_picture", "best_discount_percent",
      "category_name", "instagram_url", "facebook_url", "twitter_url",
      "cashback_percent", "cashback_text", "discount_text", "description", "favorited",
      "short_description", "top_background_image_small", "top_background_image_large",
      "top_background_image_v2_large", "top_background_image_v2_small", "logo_image_small",
      "banner_image_large", "banner_image_small", "banner_image_v2_large", "banner_image_v2_small",
      "logo_image", "top_background_image"]
  end
  let(:response_hash) { JSON.parse(response.body) }

  before do
    Promotion::PropagateChanges.call
  end

  describe "GET /api/v1/public_integration/organizations?search" do
    it "returns a list by query string with spaces" do
      get "/api/v1/public_integration/organizations?search= deliver  ",
        headers: auth_headers,
        params: {lat: 1.0, lng: 1.0},
        as: :json

      expect(response).to have_http_status(:ok)
      expect(response_hash.first.keys).to match_array(organization_show_serializer_keys)
      expect(response_hash.pluck("best_discount_percent")).to match_array([30, 30])
    end

    it "returns a list by query string with two words and spaces in between" do
      get "/api/v1/public_integration/organizations?search= ze Deliver  ",
        headers: auth_headers,
        params: {lat: 1.0, lng: 1.0},
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.first.keys).to match_array(organization_show_serializer_keys)
    end

    context "with organization with only online branch" do
      let!(:online_organization_two) { create(:organization, :active_with_coupon, name: "Delivery", promotion_redeemable: true) }
      let!(:online_branch_two) { create(:branch, :online, organization: online_organization_two) }
      let!(:online_promotion_two) { create(:promotion, :online, :available, organization: online_organization_two) }
      let!(:online_coupon_two) { create(:cupon, :online, branch: online_branch_two, promotion: online_promotion_two) }

      it "returns the organizations filtered by name" do
        online_promotion_two.propagate_changes

        get "/api/v1/public_integration/organizations?search= deliver  ",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash.pluck("id", "distance_km")).to match_array([
          [online_organization_two.id, nil], [sub_category_organization.id, 0.0], [organization.id, 0.0]
        ])
      end
    end

    context "when coupon used is on limit" do
      before do
        Promotion.update_all(redeemed_count: 10, quantity: 10)
        Promotion::PropagateChanges.call
      end

      it "returns a empty list by query string" do
        get "/api/v1/public_integration/organizations?search=#{organization.name}",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash).to be_empty
      end
    end

    context "cupons on another business" do
      before do
        business_two = create(:business, :publicar)
        Promotion.update_all(business_id: business_two.id)
        Promotion::PropagateChanges.call
      end

      it "returns a empty list by query string" do
        get "/api/v1/public_integration/organizations?search=#{organization.name}",
          headers: auth_headers,
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash).to be_empty
      end
    end

    context "when business has set profile for organization" do
      it "returns organizations with information from profile" do
        organization_profile = create(
          :organization_profile,
          organization:,
          business:,
          description: FFaker::LoremBR.phrase(5),
          short_description: FFaker::LoremBR.characters(40),
          logo_image: Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png")),
          top_background_image: Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png"))
        )

        get "/api/v1/public_integration/organizations", headers: auth_headers, params: {lat: 1.0, lng: 1.0}, as: :json

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all(match_array(organization_show_serializer_keys))
        org_response_hash = response_hash.find { _1["id"] == organization.id }
        expect(org_response_hash["id"]).to eq(organization.id)
        expect(org_response_hash["description"]).to eq(organization_profile.description)
        expect(org_response_hash["short_description"]).to eq(organization_profile.short_description)
        expect(org_response_hash["top_background_image_v2_small"]).to eq(organization_profile.top_background_image.url(:small))
        expect(org_response_hash["top_background_image_v2_large"]).to eq(organization_profile.top_background_image.url(:large))
        expect(org_response_hash["logo_image"]).to eq(
          "url" => organization_profile.logo_image.url,
          "large" => {"url" => organization_profile.logo_image.url(:large)},
          "small" => {"url" => organization_profile.logo_image.url(:small)}
        )
      end

      it "returns organizations with information from profile and fills missing data by taking from organization" do
        organization_profile = create(
          :organization_profile,
          organization:,
          business:,
          description: nil,
          logo_image: nil,
          top_background_image: nil,
          short_description: FFaker::LoremBR.characters(40)
        )

        get "/api/v1/public_integration/organizations", headers: auth_headers, params: {lat: 1.0, lng: 1.0}, as: :json

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all(match_array(organization_show_serializer_keys))
        org_response_hash = response_hash.find { _1["id"] == organization.id }
        expect(org_response_hash["id"]).to eq(organization.id)
        expect(org_response_hash["description"]).to eq(organization.description)
        expect(org_response_hash["short_description"]).to eq(organization_profile.short_description)
        expect(org_response_hash["top_background_image_v2_small"]).to eq(organization.top_background_image.url(:small))
        expect(org_response_hash["top_background_image_v2_large"]).to eq(organization.top_background_image.url(:large))
        expect(org_response_hash["logo_image"]).to eq(
          "url" => organization.logo_image.url,
          "large" => {"url" => organization.logo_image.url(:large)},
          "small" => {"url" => organization.logo_image.url(:small)}
        )
      end
    end
  end

  describe "GET /api/v1/public_integration/organizations/:id" do
    let!(:organization_two) do
      create(:organization,
        :active_with_coupon,
        :highlighted,
        :percent_highest_discount,
        categories: [category],
        highest_cashback_value: 3.0,
        highest_discount_value: 10,
        promotion_redeemable: true)
    end
    let!(:org_approval) do
      create(:organization_blocklist,
        business:,
        organization: organization_two)
    end

    it "returns an organization with active coupon" do
      get "/api/v1/public_integration/organizations/#{organization.id}",
        headers: auth_headers,
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.keys).to match_array(organization_show_serializer_keys)
      expect(response_hash["cashback_percent"]).to eq(2.7)
      expect(response_hash["cashback_text"]).to eq("2.7% de cashback")
      expect(response_hash["discount_text"]).to eq("Até 30% Off")
    end

    it "returns not found for invalid organization" do
      get "/api/v1/public_integration/organizations/0",
        headers: auth_headers,
        as: :json
      expect(response).to have_http_status(:not_found)
    end

    it "returns not found for blocklisted organization" do
      get "/api/v1/public_integration/organizations/#{organization_two.id}",
        headers: auth_headers,
        as: :json
      expect(response).to have_http_status(:not_found)
    end

    it "returns organizations with information from profile" do
      organization_profile = create(
        :organization_profile,
        organization:,
        business:,
        description: FFaker::LoremBR.phrase(5),
        short_description: FFaker::LoremBR.characters(40),
        logo_image: Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png")),
        top_background_image: Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png"))
      )

      get "/api/v1/public_integration/organizations/#{organization.id}", headers: auth_headers, as: :json

      expect(response).to be_ok
      expect(response_hash.keys).to match_array(organization_show_serializer_keys)
      expect(response_hash["id"]).to eq(organization.id)
      expect(response_hash["description"]).to eq(organization_profile.description)
      expect(response_hash["short_description"]).to eq(organization_profile.short_description)
      expect(response_hash["top_background_image_v2_small"]).to eq(organization_profile.top_background_image.url(:small))
      expect(response_hash["top_background_image_v2_large"]).to eq(organization_profile.top_background_image.url(:large))
      expect(response_hash["logo_image"]).to eq(
        "url" => organization_profile.logo_image.url,
        "large" => {"url" => organization_profile.logo_image.url(:large)},
        "small" => {"url" => organization_profile.logo_image.url(:small)}
      )
    end

    it "returns organizations with information from profile and fills missing data by taking from organization" do
      organization_profile = create(
        :organization_profile,
        organization:,
        business:,
        description: nil,
        logo_image: nil,
        top_background_image: nil,
        short_description: FFaker::LoremBR.characters(40)
      )

      get "/api/v1/public_integration/organizations/#{organization.id}", headers: auth_headers, as: :json

      expect(response).to be_ok
      expect(response_hash.keys).to match_array(organization_show_serializer_keys)
      expect(response_hash["id"]).to eq(organization.id)
      expect(response_hash["description"]).to eq(organization.description)
      expect(response_hash["short_description"]).to eq(organization_profile.short_description)
      expect(response_hash["banner_image_v2_small"]).to eq(organization.banner_image.url(:small))
      expect(response_hash["banner_image_v2_large"]).to eq(organization.banner_image.url(:large))
      expect(response_hash["logo_image"]).to eq(
        "url" => organization.logo_image.url,
        "large" => {"url" => organization.logo_image.url(:large)},
        "small" => {"url" => organization.logo_image.url(:small)}
      )
    end
  end

  describe "GET /api/v1/public_integration/organizations?organization_type=physical&category_id=" do
    it "returns a list of physical stores" do
      get "/api/v1/public_integration/organizations?organization_type=physical",
        headers: auth_headers,
        params: {lat: 1.0, lng: 1.0, page: 1},
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.first.keys).to match_array(organization_store_serializer_keys)
      expect(response_hash.first["best_discount_percent"]).to eq(30)
      expect(response_hash.first["cashback_percent"]).to eq(2.7)
      expect(response_hash.first["cashback_text"]).to eq("2.7% de cashback")
      expect(response_hash.first["discount_text"]).to eq("Até 30% Off")
    end

    it "returns error with wrong page param" do
      get "/api/v1/public_integration/organizations?organization_type=physical",
        headers: auth_headers,
        params: {lat: 1.0, lng: 1.0, page: 0},
        as: :json
      expect(response).to have_http_status(:bad_request)
    end

    it "returns a list of physical stores that belongs to category" do
      get "/api/v1/public_integration/organizations?organization_type=physical&category_id=#{category.id}",
        headers: auth_headers,
        params: {lat: 1.0, lng: 1.0},
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.first.keys).to match_array(organization_store_serializer_keys)
      expect(response_hash.first["cashback_percent"]).to eq(2.7)
      expect(response_hash.first["cashback_text"]).to eq("2.7% de cashback")
      expect(response_hash.first["discount_text"]).to eq("Até 30% Off")
    end

    it "returns ok without lat lng" do
      get "/api/v1/public_integration/organizations?organization_type=physical",
        headers: auth_headers,
        as: :json
      expect(response).to have_http_status(:ok)
    end

    it "returns organization distance grather than 150km" do
      get "/api/v1/public_integration/organizations?organization_type=physical",
        headers: auth_headers,
        params: {lat: 1.0, lng: 1.0, page: 1, distance: 150},
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.first.keys).to match_array(organization_store_serializer_keys)
    end

    it "returns organization distance less than or equal 100km" do
      get "/api/v1/public_integration/organizations?organization_type=physical",
        headers: auth_headers,
        params: {lat: 1.0, lng: 1.0, page: 1, distance: 100},
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.first.keys).to match_array(organization_store_serializer_keys)
    end

    it "returns organization distance lass than 30" do
      get "/api/v1/public_integration/organizations?organization_type=physical",
        headers: auth_headers,
        params: {lat: 1.0, lng: 1.0, page: 1, distance: 20},
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.first.keys).to match_array(organization_store_serializer_keys)
    end

    context "cupons on another business" do
      before do
        business_two = create(:business, :publicar)
        Promotion.update_all(business_id: business_two.id)
      end

      it "returns a empty list of physical stores" do
        get "/api/v1/public_integration/organizations?organization_type=physical",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash).to be_empty
      end

      it "returns a empty list of physical stores that belongs to category" do
        get "/api/v1/public_integration/organizations?organization_type=physical&category_id=#{category.id}",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash).to be_empty
      end
    end

    context "with no business cashback" do
      before do
        business.update!(cashback: false)
      end

      it "returns a list of physical stores without cashback text" do
        get "/api/v1/public_integration/organizations?organization_type=physical",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash.first.keys).to match_array(organization_store_serializer_keys)
        expect(response_hash.first["cashback_percent"]).to be_nil
        expect(response_hash.first["cashback_text"]).to be_nil
      end
    end

    context "when coupon is no longer active" do
      before do
        cupon.destroy!
        cupon_two.promotion.update!(end_date: 1.day.ago)
        cupon_two.promotion.propagate_changes
      end

      it "returns nothing" do
        get "/api/v1/public_integration/organizations?organization_type=physical",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash).to be_empty
      end

      context "but is updated to be active again" do
        before do
          cupon_two.promotion.update!(end_date: 2.days.from_now)
          cupon_two.promotion.propagate_changes
        end

        it "returns a list of physical stores" do
          get "/api/v1/public_integration/organizations?organization_type=physical",
            headers: auth_headers,
            params: {lat: 1.0, lng: 1.0, page: 1},
            as: :json
          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).to match_array([organization.id, sub_category_organization.id])
        end
      end
    end
  end

  describe "GET /api/v1/public_integration/organizations?organization_type=online&category_id=" do
    let!(:organization) do
      create(:organization,
        :active_with_coupon,
        :highlighted,
        :percent_highest_discount,
        categories: [category],
        highest_discount_value: 5,
        promotion_redeemable: true,
        name: "Ze Delivery")
    end

    before do
      cupon_two.destroy!
      Promotion::PropagateChanges.call
    end

    it "returns a list of online stores" do
      get "/api/v1/public_integration/organizations?organization_type=online",
        headers: auth_headers,
        params: {page: 1},
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.first.keys).to match_array(organization_online_serializer_keys)
      expect(response_hash.first["best_discount_percent"]).to eq(30)
      expect(response_hash.first["cashback_percent"]).to eq(2.7)
      expect(response_hash.first["cashback_text"]).to eq("2.7% de cashback")
      expect(response_hash.first["discount_text"]).to eq("Até 30% Off")
    end

    it "returns error with wrong page param" do
      get "/api/v1/public_integration/organizations?organization_type=online",
        headers: auth_headers,
        params: {page: 0},
        as: :json
      expect(response).to have_http_status(:bad_request)
    end

    it "returns a list of online stores that belongs to category" do
      get "/api/v1/public_integration/organizations?organization_type=online&category_id=#{category.id}",
        headers: auth_headers,
        as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.first.keys).to match_array(organization_online_serializer_keys)
      expect(response_hash.first["cashback_percent"]).to eq(2.7)
      expect(response_hash.first["cashback_text"]).to eq("2.7% de cashback")
      expect(response_hash.first["discount_text"]).to eq("Até 30% Off")
    end

    context "cupons on another business" do
      before do
        business_two = create(:business, :publicar)
        Promotion.update_all(business_id: business_two.id)
      end
      it "returns a empty list of online stores" do
        get "/api/v1/public_integration/organizations?organization_type=online",
          headers: auth_headers,
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash).to be_empty
      end

      it "returns a empty list of online stores that belongs to category" do
        get "/api/v1/public_integration/organizations?organization_type=online&category_id=#{category.id}",
          headers: auth_headers,
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash).to be_empty
      end
    end

    context "with no business cashback" do
      before do
        business.update!(cashback: false)
      end

      it "returns a list of online stores without cashback text" do
        get "/api/v1/public_integration/organizations?organization_type=online",
          headers: auth_headers,
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash.first.keys).to match_array(organization_online_serializer_keys)
        expect(response_hash.first["cashback_percent"]).to be_nil
        expect(response_hash.first["cashback_text"]).to be_nil
      end
    end

    it "Not Authorized" do
      get "/api/v1/public_integration/organizations?organization_type=online",
        as: :json
      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe "GET /api/v1/public_integration/organizations?organization_type=online&search=" do
    let!(:organization) { create(:organization, :active_with_coupon, name: "Sample") }
    let!(:online_branch) { create(:branch, :online, organization:) }
    let!(:coupon) { create(:cupon, :online, organization:, branch: online_branch) }
    let!(:unrelated_organization) { create(:organization, :active_with_coupon, name: "Samp") }
    let!(:unrelated_branch) { create(:branch, organization: unrelated_organization) }
    let!(:unrelated_coupon) { create(:cupon, :classic, organization: unrelated_organization, branch: unrelated_branch) }
    let(:params) { {organization_type: "online", search: "Samp"} }

    it "returns online organizations that match search term" do
      get "/api/v1/public_integration/organizations", headers: auth_headers, params:, as: :json
      expect(response).to have_http_status(:ok)
      expect(response_hash.map(&:keys)).to all(match_array(organization_online_serializer_keys))
      expect(response_hash.pluck("id")).to eq([organization.id])
    end
  end

  describe "GET /api/v1/public_integration/organizations?category_id=id" do
    it "returns a list of organizations belonging to category" do
      get "/api/v1/public_integration/organizations?category_id=#{category.id}",
        headers: auth_headers,
        params: {lat: 1.0, lng: 1.0},
        as: :json

      expect(response).to have_http_status(:ok)
      expect(response_hash.first.keys).to match_array(organization_show_serializer_keys)
      expect(response_hash.first["cashback_percent"]).to eq(2.7)
      expect(response_hash.first["cashback_text"]).to eq("2.7% de cashback")
      expect(response_hash.first["discount_text"]).to eq("Até 30% Off")
    end

    context "cupons on another business" do
      before do
        business_two = create(:business, :publicar)
        Promotion.update_all(business_id: business_two.id)
      end
      it "returns a empty list of organizations belonging to category" do
        get "/api/v1/public_integration/organizations?category_id=#{category.id}",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash).to be_empty
      end
    end

    context "with no business cashback" do
      before do
        business.update!(cashback: false)
      end

      it "returns a list of organizations belonging to category without cashback text" do
        get "/api/v1/public_integration/organizations?category_id=#{category.id}",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json
        expect(response).to have_http_status(:ok)
        expect(response_hash.first.keys).to match_array(organization_show_serializer_keys)
        expect(response_hash.first["cashback_percent"]).to be_nil
        expect(response_hash.first["cashback_text"]).to be_nil
      end
    end
  end

  describe "GET /api/v1/public_integration/organizations?product=giftcard" do
    let!(:organization_with_giftcard) { create(:organization, giftcard_redeemable: true) }
    let!(:organization_inactive_with_giftcard) { create(:organization, giftcard_redeemable: true, active: false) }
    let!(:organization_blocklisted_with_giftcard) { create(:organization, giftcard_redeemable: true) }
    let!(:organization_blocklist) do
      create(:organization_blocklist,
        organization: organization_blocklisted_with_giftcard,
        business: signed_user.business)
    end
    let!(:organization_without_giftcard) { create(:organization, giftcard_redeemable: false) }
    let(:serializer_keys) do
      ["background_picture", "best_discount_percent", "category_name", "cover_picture",
        "facebook_url", "id", "instagram_url", "name", "twitter_url", "cashback_percent",
        "cashback_text", "discount_text", "description", "favorited", "short_description",
        "top_background_image_small", "top_background_image_large", "top_background_image_v2_large",
        "top_background_image_v2_small", "logo_image_small", "banner_image_large",
        "banner_image_small", "banner_image_v2_large", "banner_image_v2_small",
        "logo_image", "top_background_image"]
    end
    let(:params) { {product: "giftcard"} }
    let(:response_hash) { JSON.parse(response.body) }

    before { Business::UpdateService.new(business:, params: {giftcard: true}).call }

    it "returns the organizations with giftcard" do
      get "/api/v1/public_integration/organizations", params:, headers: auth_headers

      expect(response).to have_http_status(:ok)
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("id")).to eq([organization_with_giftcard.id])
      expect(response_hash.pluck("cashback_text")).to all(eq(""))
    end

    it "returns organizations with information from profile" do
      organization_profile = create(
        :organization_profile,
        :percent_highest_discount,
        organization: organization_with_giftcard,
        business:,
        description: FFaker::LoremBR.phrase(5),
        highest_discount_value: 11,
        short_description: FFaker::LoremBR.characters(40),
        logo_image: Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png")),
        top_background_image: Rack::Test::UploadedFile.new(Rails.root.join("spec/fixtures/samplefile.png"))
      )

      get("/api/v1/public_integration/organizations", headers: auth_headers, params:, as: :json)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("best_discount_percent")).to match_array([11])
      org_response_hash = response_hash.find { _1["id"] == organization_with_giftcard.id }
      expect(org_response_hash["id"]).to eq(organization_with_giftcard.id)
      expect(org_response_hash["description"]).to eq(organization_profile.description)
      expect(org_response_hash["short_description"]).to eq(organization_profile.short_description)
      expect(org_response_hash["top_background_image_v2_small"]).to eq(organization_profile.top_background_image.url(:small))
      expect(org_response_hash["top_background_image_v2_large"]).to eq(organization_profile.top_background_image.url(:large))
      expect(org_response_hash["logo_image"]).to eq(
        "url" => organization_profile.logo_image.url,
        "large" => {"url" => organization_profile.logo_image.url(:large)},
        "small" => {"url" => organization_profile.logo_image.url(:small)}
      )
    end
  end
end
