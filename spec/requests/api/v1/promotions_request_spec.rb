require "rails_helper"

RSpec.describe Api::V1::PromotionsController, type: :request do
  let!(:business) { create(:business) }
  let(:user) { create(:user, business:) }
  let(:headers) { user.create_new_auth_token.merge!("Api-Secret" => business.api_secret) }
  let(:response_hash) { JSON.parse(response.body) }

  describe "GET /api/v1/public_integration/promotions" do
    let(:serializer_keys) do
      %w[id title rules description url discount start_date end_date
        working_days quantity redeemed_count infinity organization_id
        dynamic_voucher branch_id coupon_id picture tags template usage_instruction]
    end

    context "when redeem_type is physical" do
      let!(:organization) { create(:organization, :active_with_coupon) }
      let!(:online_promotion) { create(:promotion, :online, :available, organization:) }
      let!(:online_branch) { create(:branch, :online, organization:) }
      let!(:online_coupon) { create(:cupon, :online, promotion: online_promotion, branch: online_branch) }

      let(:voucher_bucket) { create(:voucher_bucket) }
      let!(:near_physical_promotion) { create(:promotion, :coupon_code, :available, :percent_discount, code: nil, redeems_per_cpf: 999, voucher_bucket:) }
      let!(:near_organization) { create(:organization, :active_with_coupon, name: "Mercado") }
      let!(:near_branch) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization) }
      let!(:near_physical_coupon) do
        create(:cupon, :coupon_code, promotion: near_physical_promotion, branch: near_branch)
      end
      let!(:near_organization_two) { create(:organization, :active_with_coupon, name: "Armazém") }
      let!(:near_branch_two) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization_two) }
      let!(:near_physical_coupon_two) do
        create(:cupon, :coupon_code, promotion: near_physical_promotion, branch: near_branch_two)
      end
      let!(:near_branch_three) { create(:branch, lat: 10.0, lng: 10.0, organization: near_organization_two) }
      let!(:near_physical_coupon_three) do
        create(:cupon, :coupon_code, promotion: near_physical_promotion, branch: near_branch_three)
      end
      let!(:near_voucher) { create(:voucher, bucket: voucher_bucket) }

      let!(:far_organization) { create(:organization, :active_with_coupon) }
      let!(:far_physical_promotion) { create(:promotion, :coupon_code, :available, organization: far_organization, voucher_bucket:) }
      let!(:far_branch) { create(:branch, lat: 10.0, lng: 10.0, organization: far_organization) }
      let!(:far_physical_coupon) { create(:cupon, :coupon_code, promotion: far_physical_promotion, branch: far_branch) }
      let!(:far_voucher) { create(:voucher, bucket: voucher_bucket) }

      let(:redeem_type) { "physical" }

      context "with lat/lng" do
        let(:params) { {redeem_type:, lat: 1.0, lng: 1.0, page: 1} }

        it "returns the promotions near lat/lng" do
          Promotion::PropagateChanges.call

          get("/api/v1/public_integration/promotions", params:, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id", "branch_id", "coupon_id")).to eq([
            [near_physical_promotion.id, near_branch.id, near_physical_coupon.id]
          ])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
          expect(response_hash.pluck("discount")).to match_array([30])
        end
      end

      context "without voucher" do
        let(:params) { {redeem_type:, lat: 1.0, lng: 1.0, page: 1} }

        before do
          Voucher.delete_all
          Promotion::PropagateChanges.call
        end

        it "returns empty" do
          get("/api/v1/public_integration/promotions", params:, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id")).to be_empty
        end
      end
    end

    context "when redeem_type is online" do
      let!(:organization) { create(:organization, :active_with_coupon) }
      let!(:branch) { create(:branch, :online, organization:) }
      let!(:online_promotion) { create(:promotion, :online, :available, organization:, redeems_per_cpf: 999, tags: ["Tag 1", "Tag 2"]) }
      let!(:online_coupon) { create(:cupon, :online, promotion: online_promotion, branch:) }

      let!(:near_physical_promotion) { create(:promotion, :cpf, :available) }
      let!(:near_organization) { create(:organization, :active_with_coupon) }
      let!(:near_branch) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization) }
      let!(:near_physical_coupon) do
        create(:cupon, :cpf, promotion: near_physical_promotion, branch: near_branch)
      end

      let(:params) { {redeem_type: "online", page: 1} }

      it "returns the online promotions" do
        get("/api/v1/public_integration/promotions", params:, headers:)

        expect(response).to be_ok
        expect(response_hash.pluck("id", "branch_id", "coupon_id")).to eq([
          [online_promotion.id, branch.id, online_coupon.id]
        ])
        expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        expect(response_hash.filter { _1["id"] == online_promotion.id }.dig(0, "tags")).to eq(["Tag 1", "Tag 2"])
      end

      context "when passing near lat/lng" do
        let(:params) { {redeem_type: "online", lat: 1.0, lng: 1.0, page: 1} }

        it "returns the online promotions" do
          get("/api/v1/public_integration/promotions", params:, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id", "branch_id", "coupon_id")).to eq([
            [online_promotion.id, branch.id, online_coupon.id]
          ])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end

      context "when passing far lat/lng" do
        let(:params) { {redeem_type: "online", lat: 10.0, lng: 10.0, page: 1} }

        it "returns only the online promotions" do
          get("/api/v1/public_integration/promotions", params:, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id", "branch_id", "coupon_id")).to eq([
            [online_promotion.id, branch.id, online_coupon.id]
          ])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end
    end

    context "when filtering by category_ids" do
      let!(:online_category) { create(:category) }

      let!(:online_organization) { create(:organization, :active_with_coupon, categories: [online_category]) }
      let!(:online_branch) { create(:branch, :online, organization: online_organization) }
      let!(:online_promotion) { create(:promotion, :online, :available, organization: online_organization, redeems_per_cpf: 999) }
      let!(:online_coupon) { create(:cupon, :online, promotion: online_promotion, branch: online_branch) }

      let!(:physical_category) { create(:category) }

      let!(:physical_organization) { create(:organization, :active_with_coupon, categories: [physical_category]) }
      let!(:physical_promotion) { create(:promotion, :cpf, :available, redeems_per_cpf: 999) }
      let!(:branch) { create(:branch, lat: 1.0, lng: 1.0, organization: physical_organization) }
      let!(:physical_coupon) do
        create(:cupon, :cpf, promotion: physical_promotion, branch:)
      end

      let!(:online_sub_category) { create(:category, main_category: online_category) }

      let!(:sub_category_organization) { create(:organization, :active_with_coupon, categories: [online_sub_category]) }
      let!(:sub_category_branch) { create(:branch, :online, organization: sub_category_organization) }
      let!(:sub_category_promotion) { create(:promotion, :online, :available, organization: sub_category_organization, redeems_per_cpf: 999) }
      let!(:sub_category_coupon) { create(:cupon, :online, promotion: sub_category_promotion, branch: sub_category_branch) }

      context "with online redeem_type" do
        context "with online category" do
          let(:params) { {redeem_type: "online", category_ids: [online_category.id], page: 1} }

          it "returns the online promotions with the category" do
            get("/api/v1/public_integration/promotions", params:, headers:)

            expect(response).to be_ok
            expect(response_hash.pluck("id", "branch_id", "coupon_id")).to match_array([[online_promotion.id, online_branch.id, online_coupon.id]])
            expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
          end
        end

        context "with physical category" do
          let(:params) { {redeem_type: "online", category_ids: [physical_category.id], page: 1} }

          it "returns empty" do
            get("/api/v1/public_integration/promotions", params:, headers:)

            expect(response).to be_ok
            expect(response_hash.pluck("id")).to be_empty
          end
        end
      end

      context "with physical redeem_type" do
        context "with physical category" do
          let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, category_ids: [physical_category.id], page: 1} }

          it "returns the physical promotions with the category" do
            get("/api/v1/public_integration/promotions", params:, headers:)

            expect(response).to be_ok
            expect(response_hash.pluck("id", "branch_id", "coupon_id")).to eq([
              [physical_promotion.id, branch.id, physical_coupon.id]
            ])
            expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
          end
        end

        context "with online category" do
          let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, category_ids: [online_category.id], page: 1} }

          it "returns empty" do
            get("/api/v1/public_integration/promotions", params:, headers:)

            expect(response).to be_ok
            expect(response_hash.pluck("id")).to be_empty
          end
        end
      end

      context "when category is inactive" do
        let(:params) { {redeem_type: "online", category_ids: [online_category.id], page: 1} }

        before do
          online_sub_category.update(active: false)
        end

        it "must not return its promotions" do
          get("/api/v1/public_integration/promotions", params:, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id", "branch_id", "coupon_id")).to match_array([[online_promotion.id, online_branch.id, online_coupon.id]])
          expect(response_hash.pluck("id", "branch_id", "coupon_id")).not_to include([sub_category_promotion.id, sub_category_branch.id, sub_category_coupon.id])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end

      context "when category is blocklisted" do
        let!(:blocklist) { create(:category_blocklist, category: online_category, business:) }
        let(:params) { {redeem_type: "online", category_ids: [online_category.id], page: 1} }

        it "returns empty" do
          get("/api/v1/public_integration/promotions", params:, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id")).to be_empty
        end
      end
    end

    context "when filtering by organization_id" do
      let!(:organization) { create(:organization, :active_with_coupon) }
      let!(:online_promotion) { create(:promotion, :online, :available, organization:, redeems_per_cpf: 999) }
      let!(:online_branch) { create(:branch, :online, organization:) }
      let!(:online_coupon) { create(:cupon, :online, promotion: online_promotion, branch: online_branch) }

      let!(:physical_organization) { create(:organization, :active_with_coupon) }
      let!(:physical_promotion) { create(:promotion, :cpf, :available, redeems_per_cpf: 999) }
      let!(:branch) { create(:branch, lat: 1.0, lng: 1.0, organization: physical_organization) }
      let!(:physical_coupon) do
        create(:cupon, :cpf, promotion: physical_promotion, branch:)
      end

      context "when redeem_type is online" do
        context "with online organization" do
          let(:params) { {redeem_type: "online", organization_id: organization.id, page: 1} }

          it "returns the promotions filtered" do
            get("/api/v1/public_integration/promotions", params:, headers:)

            expect(response).to be_ok
            expect(response_hash.pluck("id", "branch_id", "coupon_id")).to eq([
              [online_promotion.id, online_branch.id, online_coupon.id]
            ])
            expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
          end
        end

        context "with physical organization" do
          let(:params) { {redeem_type: "online", organization_id: branch.organization_id, page: 1} }

          it "returns empty" do
            get("/api/v1/public_integration/promotions", params:, headers:)

            expect(response).to be_ok
            expect(response_hash.pluck("id")).to be_empty
          end
        end
      end

      context "when redeem_type is physical" do
        context "with lat/lng and physical organization" do
          let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, organization_id: physical_organization.id, page: 1} }

          it "returns the promotion filtered" do
            get("/api/v1/public_integration/promotions", params:, headers:)

            expect(response).to be_ok
            expect(response_hash.pluck("id", "branch_id", "coupon_id")).to eq([
              [physical_promotion.id, branch.id, physical_coupon.id]
            ])
            expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
          end
        end

        context "with lat/lng and online organization" do
          let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, organization_id: organization.id, page: 1} }

          it "returns empty" do
            get("/api/v1/public_integration/promotions", params:, headers:)

            expect(response).to be_ok
            expect(response_hash.pluck("id")).to be_empty
          end
        end
      end
    end

    context "when filtering by distance_km" do
      let!(:organization) { create(:organization, :active_with_coupon) }
      let!(:online_promotion) { create(:promotion, :online, :available, :percent_discount, organization:) }
      let!(:online_branch) { create(:branch, :online, organization:) }
      let!(:online_coupon) { create(:cupon, :online, :percent_discount, promotion: online_promotion, branch: online_branch) }

      let!(:near_organization) { create(:organization, :active_with_coupon) }
      let!(:near_physical_promotion) { create(:promotion, :cpf, :available, :percent_discount, redeems_per_cpf: 999) }
      let!(:near_branch) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization) }
      let!(:near_physical_coupon) do
        create(:cupon, :cpf, :percent_discount, promotion: near_physical_promotion, branch: near_branch)
      end

      let!(:far_organization) { create(:organization, :active_with_coupon) }
      let!(:far_physical_promotion) { create(:promotion, :cpf, :available, :percent_discount) }
      let!(:far_branch) { create(:branch, lat: 10.0, lng: 10.0, organization: far_organization) }
      let!(:far_physical_coupon) { create(:cupon, :cpf, :percent_discount, promotion: far_physical_promotion, branch: far_branch) }

      let!(:regionalized_organization) { create(:organization, :active_with_coupon) }
      let!(:regionalized_promotion) { create(:promotion, :regionalized, :available, :percent_discount, organization: regionalized_organization, redeems_per_cpf: 999, discount_value: 20) }
      let!(:regionalized_branch) { create(:branch, lat: 1.0, lng: 1.0, organization: regionalized_organization) }
      let!(:regionalized_coupon) do
        create(:cupon, :online, :percent_discount, promotion: regionalized_promotion, branch: regionalized_branch, discount_value: 20)
      end

      context "when less than or equal to 30 km" do
        let(:params) { {redeem_type: "physical", lat: 1.0, lng: 1.0, page: 1, distance_km: 29} }

        it "returns the physical promotions less or equal to 30 km" do
          get("/api/v1/public_integration/promotions", params:, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id")).to eq([near_physical_promotion.id, regionalized_promotion.id])
          expect(response_hash.pluck("id", "branch_id", "coupon_id")).to eq([
            [near_physical_promotion.id, near_branch.id, near_physical_coupon.id],
            [regionalized_promotion.id, regionalized_branch.id, regionalized_coupon.id]
          ])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end
    end

    context "when filtering by term" do
      let!(:physical_category) { create(:category, title: "Farmácia") }
      let!(:physical_tag) { create(:tag, name: "remédio") }

      let!(:near_organization_cpf) do
        create(
          :organization,
          :active_with_coupon,
          name: "Drogasil",
          categories: [physical_category]
        )
      end
      let!(:near_promotion_cpf) { create(:promotion, :cpf, :available, redeems_per_cpf: 999) }
      let!(:near_branch_cpf) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization_cpf) }
      let!(:near_coupon_cpf) do
        create(:cupon, :cpf, promotion: near_promotion_cpf, branch: near_branch_cpf)
      end

      let!(:near_organization_qrcode) do
        create(
          :organization,
          :active_with_coupon,
          name: "Araujo",
          categories: [physical_category],
          tags: [physical_tag]
        )
      end
      let!(:near_promotion_qrcode) { create(:promotion, :qrcode, :available, redeems_per_cpf: 999) }
      let!(:near_branch_qrcode) { create(:branch, lat: 1.0, lng: 1.0, organization: near_organization_qrcode) }
      let!(:near_coupon_qrcode) do
        create(:cupon, :classic, promotion: near_promotion_qrcode, branch: near_branch_qrcode)
      end

      let(:default_params) { {lat: 1.0, lng: 1.0, page: 1, redeem_type: "physical"} }

      context "with organization name" do
        let(:params) { default_params.merge(term: "droga") }

        it "returns the promotions filtered by name" do
          get("/api/v1/public_integration/promotions", params:, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id")).to eq([near_promotion_cpf.id])
          expect(response_hash.pluck("id", "branch_id", "coupon_id")).to eq([
            [near_promotion_cpf.id, near_branch_cpf.id, near_coupon_cpf.id]
          ])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end

      context "with category title" do
        let(:params) { default_params.merge(term: "armacia") }

        it "returns the promotions filtered by name" do
          get("/api/v1/public_integration/promotions", params:, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id")).to eq([near_promotion_cpf.id, near_promotion_qrcode.id])
          expect(response_hash.pluck("id", "branch_id", "coupon_id")).to eq([
            [near_promotion_cpf.id, near_branch_cpf.id, near_coupon_cpf.id],
            [near_promotion_qrcode.id, near_branch_qrcode.id, near_coupon_qrcode.id]
          ])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end

      context "with tag title" do
        let(:params) { default_params.merge(term: "remedio") }

        it "returns the promotions filtered by name" do
          get("/api/v1/public_integration/promotions", params:, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id", "branch_id", "coupon_id")).to eq([
            [near_promotion_qrcode.id, near_branch_qrcode.id, near_coupon_qrcode.id]
          ])
          expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
        end
      end
    end

    describe "filter by tags" do
      let!(:organization) { create(:organization, :active_with_coupon) }
      let!(:branch) { create(:branch, :online, organization:) }
      let(:business) { create(:business) }
      let!(:online_promotion_one_with_tag) { create(:promotion, :online, :available, business:, organization:, redeems_per_cpf: 999, tags: ["Tag 1"]) }
      let!(:online_promotion_two_with_tag) { create(:promotion, :online, :available, business:, organization:, redeems_per_cpf: 999, tags: ["Tag 2", "Tag 3"]) }

      let!(:online_promotion_without_tag) { create(:promotion, :online, :available, business:, organization:, redeems_per_cpf: 999) }
      let!(:alloyal_online_promotion) { create(:promotion, :online, :available, organization:, redeems_per_cpf: 999) }

      let!(:online_coupon_one_with_tag) { create(:cupon, :online, promotion: online_promotion_one_with_tag, branch:) }
      let!(:online_coupon_two_with_tag) { create(:cupon, :online, promotion: online_promotion_two_with_tag, branch:) }
      let!(:online_coupon_without_tag) { create(:cupon, :online, promotion: online_promotion_without_tag, branch:) }
      let!(:alloyal_online_coupon) { create(:cupon, :online, promotion: alloyal_online_promotion, branch:) }
      let(:params) { {redeem_type: "online", page: 1} }

      context "and user does have tags" do
        before do
          user.authorized_user.update(tags: ["Tag 3"])
        end

        it "must return promotions filtering by tags that exists on user" do
          get("/api/v1/public_integration/promotions", params:, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id")).to match_array([online_promotion_two_with_tag.id])
        end
      end

      context "and user does not have tags" do
        let(:headers) { user.create_new_auth_token.merge!("Api-Secret" => business.api_secret) }

        before do
          user.authorized_user.update(tags: [])
        end

        it "must ignore tags and return everything" do
          get("/api/v1/public_integration/promotions", params:, headers:)

          expect(response).to be_ok
          expect(response_hash.pluck("id")).to match_array([
            online_promotion_one_with_tag.id,
            online_promotion_two_with_tag.id,
            alloyal_online_promotion.id,
            online_promotion_without_tag.id
          ])
        end
      end
    end
  end
end
