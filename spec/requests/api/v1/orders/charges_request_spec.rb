require "rails_helper"

RSpec.describe Api::V1::Orders::ChargesController, :vcr, type: :request do
  let!(:user) { create(:user, cpf: "294.527.340-35", email: "<EMAIL>") }
  let(:headers) { user.create_new_auth_token.merge!("Api-Secret" => user.business.api_secret) }
  let(:response_hash) { JSON.parse(response.body) }

  describe "POST /api/v1/public_integration/orders/:id/charges" do
    let!(:branch) { create(:branch, :online) }
    let(:voucher_bucket) { create(:voucher_bucket) }
    let!(:giftcard) { create(:giftcard, branches: [branch], price: 129.99, voucher_bucket:) }
    let!(:voucher) { create(:voucher, bucket: voucher_bucket) }

    context "when paying order reserved for user" do
      let!(:order) { create(:order, :with_giftcard, user:, giftcard:, voucher:) }

      it "returns successful" do
        post("/api/v1/public_integration/orders/#{order.id}/charges", headers:)

        expect(response).to be_created
        expect(response_hash["message"]).to eq("Ocorreu algum erro na sua tentativa de compra do Gift Card.")
      end
    end
  end
end
