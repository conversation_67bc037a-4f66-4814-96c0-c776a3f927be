# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V1::CouponsController, type: :request do
  let!(:business) { create(:business, :with_cashback) }
  let(:user) { create(:user, business:) }
  let(:headers) { user.create_new_auth_token.merge("Api-Secret": business.api_secret) }
  let!(:organization) { create(:organization, :active_with_coupon) }
  let!(:branch) { create(:branch, organization:, lat: 1.0, lng: 1.0) }
  let!(:branch_online) { create(:branch, :online, organization:) }
  let(:category_one) { create(:category) }
  let(:category_two) { create(:category, main_category_id: category_one.id) }

  let!(:promotion_online) { create(:promotion, :available, :online, :percent_discount, categories: [category_one, category_two]) }
  let!(:cupon_online) { create(:cupon, :online, :percent_discount, branch: branch_online, code: "PROMO10", promotion: promotion_online) }
  let!(:promotion_fixed_discount) { create(:promotion, :available, :online, :fixed_discount) }
  let!(:cupon_fixed_discount) { create(:cupon, :online, :fixed_discount, branch: branch_online, promotion: promotion_fixed_discount) }

  let!(:promotion_cpf) { create(:promotion, :available, :cpf, :percent_discount, categories: [category_one, category_two]) }
  let!(:cupon_cpf) { create(:cupon, :cpf, :percent_discount, branch:, promotion: promotion_cpf) }

  let!(:promotion_qrcode) { create(:promotion, :available, :qrcode, :percent_discount, categories: [category_one, category_two]) }
  let!(:cupon_classic) { create(:cupon, :classic, :percent_discount, branch:, promotion: promotion_qrcode) }

  let!(:promotion_code) { create(:promotion, :available, :coupon_code, :percent_discount, tags: ["Fixed"], categories: [category_one, category_two]) }
  let!(:cupon_fixed) { create(:cupon, :fixed, :percent_discount, branch:, promotion: promotion_code) }

  let!(:promotion_dynamic) { create(:promotion, :available, :dynamic, :percent_discount, categories: [category_one, category_two]) }
  let!(:cupon_dynamic) { create(:cupon, :dynamic, :percent_discount, branch:, promotion: promotion_dynamic) }

  let!(:promotion_cpf_two) { create(:promotion, :available, :cpf, :percent_discount, categories: [category_one, category_two]) }
  let!(:cupon_cpf_two) { create(:cupon, :cpf, :percent_discount, branch:, promotion: promotion_cpf_two) }

  let!(:promotion_lbc_giftcard) { create(:promotion, :available, :lbc_giftcard, :percent_discount, categories: [category_one, category_two]) }
  let!(:cupon_lbc_giftcard) { create(:cupon, :lbc_giftcard, :percent_discount, branch:, promotion: promotion_lbc_giftcard) }

  let!(:promotion_dynamic_online) { create(:promotion, :dynamic_coupon_code, :percent_discount, :available, dynamic_voucher: true, categories: [category_one, category_two]) }
  let!(:cupon_dynamic_online) { create(:cupon, :dynamic_online, :percent_discount, code: nil, branch: branch_online, promotion: promotion_dynamic_online) }

  let(:cupon_serializer_keys) do
    %w[description id template title cashback_text online_payment_text discount start_date end_date infinity
      picture_small_url picture_large_url branch_id organization_id tags usage_instruction categories]
  end
  let(:coupon_code_serializer_keys) do
    %w[working_days description id nearest_address organization_cover_image
      organization_name template title rules cashback_text discount start_date end_date infinity
      branch_id organization_id picture_small_url picture_large_url tags usage_instruction categories]
  end
  let(:coupon_default_serializer_keys) do
    %w[description id organization_cover_image organization_name template
      title working_days rules cashback_text discount start_date end_date infinity
      branch_id organization_id picture_small_url picture_large_url tags usage_instruction categories]
  end
  let(:coupon_link_serializer_keys) do
    %w[activation_url code description id organization_cover_image organization_name
      rules template title cashback_text discount start_date end_date working_days infinity
      branch_id organization_id picture_small_url picture_large_url tags usage_instruction categories]
  end
  let(:coupon_qrcode_serializer_keys) do
    %w[id template working_days organization_name organization_cover_image title rules description cashback_text discount start_date end_date infinity
      branch_id organization_id picture_small_url picture_large_url tags usage_instruction categories]
  end
  let(:working_days_keys) do
    %w[week_day is_available start_hour end_hour]
  end

  let(:included_category_keys) do
    %w[id category_type icon image image_v2 template title]
  end

  describe "GET /api/v1/coupons/:id" do
    it "return a coupon_code coupon" do
      expect do
        get "/api/v1/public_integration/coupons/#{cupon_fixed.id}",
          headers:,
          params: {lat: 1.0, lng: 1.0},
          as: :json
      end.to change(Order, :count).by(0)
        .and change(Voucher, :count).by(0)

      expect(response).to have_http_status(:ok)
      expect(response.parsed_body.keys).to match_array(coupon_code_serializer_keys)
      expect(response.parsed_body["discount"]).to eq(30)
      expect(response.parsed_body["working_days"].first.keys).to match_array(working_days_keys)
      expect(response.parsed_body["cashback_text"]).to eq("9% de cashback")
      expect(response.parsed_body["tags"]).to match_array(["Fixed"])
      expect(response.parsed_body["categories"].first.keys).to eq(["id"])
    end

    it "return a default coupon" do
      expect do
        get "/api/v1/public_integration/coupons/#{cupon_cpf.id}",
          headers:,
          params: {lat: 1.0, lng: 1.0},
          as: :json
      end.to change(Order, :count).by(0)
        .and change(Voucher, :count).by(0)

      expect(response).to have_http_status(:ok)
      expect(response.parsed_body.keys).to match_array(coupon_default_serializer_keys)
      expect(response.parsed_body["discount"]).to eq(30)
      expect(response.parsed_body["working_days"].first.keys).to match_array(working_days_keys)
      expect(response.parsed_body["cashback_text"]).to eq("9% de cashback")
    end

    it "return link coupon with code" do
      expect do
        get "/api/v1/public_integration/coupons/#{cupon_online.id}",
          headers:,
          as: :json
      end.to change(Order, :count).by(0)
        .and change(Voucher, :count).by(0)

      expect(response).to have_http_status(:ok)
      expect(response.parsed_body.keys).to match_array(coupon_link_serializer_keys)
      expect(response.parsed_body["discount"]).to eq(30)
      expect(response.parsed_body["working_days"].first.keys).to match_array(working_days_keys)
      expect(response.parsed_body["cashback_text"]).to eq("9% de cashback")
      expect(response.parsed_body["code"]).to eq("PROMO10")
    end

    it "return a qrcode coupon" do
      expect do
        get "/api/v1/public_integration/coupons/#{cupon_classic.id}",
          headers:,
          params: {lat: 1.0, lng: 1.0},
          as: :json
      end.to change(Order, :count).by(0)
        .and change(Voucher, :count).by(0)

      expect(response).to have_http_status(:ok)
      expect(response.parsed_body.keys).to match_array(coupon_qrcode_serializer_keys)
      expect(response.parsed_body["discount"]).to eq(30)
      expect(response.parsed_body["working_days"].first.keys).to match_array(working_days_keys)
      expect(response.parsed_body["cashback_text"]).to eq("9% de cashback")
    end

    it "returns a dynamic online coupon without coupon code" do
      expect do
        get "/api/v1/public_integration/coupons/#{cupon_dynamic_online.id}",
          headers:,
          params: {lat: 1.0, lng: 1.0},
          as: :json
      end.to change(Order, :count).by(0)
        .and change(Voucher, :count).by(0)

      expect(response).to have_http_status(:ok)
      expect(response.parsed_body.keys).to match_array(coupon_link_serializer_keys)
      expect(response.parsed_body["discount"]).to eq(30)
      expect(response.parsed_body["working_days"].first.keys).to match_array(working_days_keys)
      expect(response.parsed_body["code"]).not_to be_present
      expect(response.parsed_body["cashback_text"]).to eq("9% de cashback")
    end

    context "given a exclusive promotion with tags" do
      let(:organization) { create(:organization, active: true, promotion_redeemable: true) }
      let(:branch_online) { create(:branch, :online, organization:, active: true) }
      let(:promotion) { create(:promotion, :available, :percent_discount, redeem_type: Enums::PromotionRedeemType::DISCOUNT, tags: ["Tag 1", "Tag 2"], business:) }
      let(:coupon) { create(:cupon, :percent_discount, branch: branch_online, promotion:) }

      it "when current user matches any tag" do
        user.authorized_user.update(tags: ["Tag 2", "Tag 3"])
        get "/api/v1/public_integration/coupons/#{coupon.id}", headers:, as: :json
        expect(response.parsed_body["tags"]).to match_array(["Tag 1", "Tag 2"])
        expect(response).to be_successful
      end

      it "when current user does not match any tag" do
        get "/api/v1/public_integration/coupons/#{coupon.id}", headers:, as: :json
        expect(response).to have_http_status(:not_found)
      end
    end

    context "category subresource" do
      context "when including category" do
        it "must return 'categories' as array of objects with only ids" do
          get "/api/v1/public_integration/coupons/#{cupon_fixed.id}",
            headers:,
            params: {lat: 1.0, lng: 1.0},
            as: :json

          expect(response).to have_http_status(:ok)
          expect(response.parsed_body.keys).to match_array(coupon_code_serializer_keys)
          expect(response.parsed_body["categories"].first.keys).to eq(["id"])
        end
      end

      context "when not including category" do
        it "must return 'categories' as array of objects with only ids" do
          get "/api/v1/public_integration/coupons/#{cupon_fixed.id}",
            headers:,
            params: {lat: 1.0, lng: 1.0, include: :categories},
            as: :json

          expect(response).to have_http_status(:ok)
          expect(response.parsed_body.keys).to match_array(coupon_code_serializer_keys)
          expect(response.parsed_body["categories"].first.keys).to eq(included_category_keys)
        end
      end
    end
  end
end
