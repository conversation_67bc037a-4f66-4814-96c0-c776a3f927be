# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V1::UsersController, type: :request do
  let!(:business) { create(:business, :lecupon_equipe) }
  let(:response_hash) { JSON.parse(response.body) }

  describe "#token" do
    let!(:request_header) do
      {"Content-Type": "application/json", "Api-Secret": business.api_secret}
    end
    let(:user_serializer_keys) do
      %w[cellphone taxpayer_number email facebook id name premium profile_complete business cpf
        full_address lat lng default_auth_flow telemedicine]
    end
    let(:business_serializer_keys) do
      %w[cashback cashback_transfer_receiver cashback_requestable_amount giftcard biometry organization_manage]
    end

    context "user on database" do
      context "user in subdomain" do
        let!(:user) { create(:user, cpf: "***********", business:) }

        it "returns a user with token on header" do
          post "/api/v1/public_integration/users/token",
            headers: request_header,
            params: {cpf: "336.358.488-16", cellphone: "+5531992132224", email: FFaker::Internet.email},
            as: :json

          expect(response).to have_http_status(:ok)
          expect(User.count).to eq(1)
          expect(response_hash.keys).to match_array(user_serializer_keys)
          expect(response_hash["business"].keys).to match_array(business_serializer_keys)
        end
      end

      context "user in deactivated subdomain" do
        let(:deactivated_business) { create(:business, :lecupon, status: Business::Status::SUSPENDED_BY_OVERDUE) }
        let(:business_user) { create(:user, business: deactivated_business) }

        it "does not return user with token" do
          post "/api/v1/public_integration/users/token",
            headers: {"Api-Secret": deactivated_business.api_secret},
            params: {cpf: business_user.cpf, cellphone: business_user.cellphone, email: business_user.email},
            as: :json

          expect(response).not_to be_successful
        end
      end

      context "user without business affiliation" do
        let(:another_business) { create(:business) }
        let!(:user) { create(:user, cpf: "***********", business: another_business) }

        it "does not return user with token" do
          post "/api/v1/public_integration/users/token",
            headers: request_header,
            params: {cpf: user.cpf, cellphone: user.cellphone, email: user.email},
            as: :json

          expect(response).not_to be_successful
        end
      end
    end

    context "user out database" do
      context "user in subdomain" do
        let!(:authorized_user) { create(:authorized_user, cpf: "***********", business:) }

        it "returns a user with token on header" do
          post "/api/v1/public_integration/users/token",
            headers: request_header,
            params: {cpf: "336358.488-16", cellphone: "+5531992132224", email: FFaker::Internet.email},
            as: :json

          expect(response).to have_http_status(:ok)
          expect(User.count).to eq(1)
          expect(response_hash.keys).to match_array(user_serializer_keys)
          expect(response_hash["business"].keys).to match_array(business_serializer_keys)
        end
      end

      context "user out subdomain" do
        it "returns a user with token on header" do
          post "/api/v1/public_integration/users/token",
            headers: request_header,
            params: {cpf: "***********"},
            as: :json

          expect(User.count).to eq(0)
          expect(response).to have_http_status(:not_found)
        end
      end
    end
  end

  describe "#register_device" do
    let!(:user) { create(:user, business:) }
    let(:headers) do
      user.create_new_auth_token.merge!("Api-Secret": business.api_secret)
    end
    let(:params) do
      {
        token: "ABCD1234",
        environment: true,
        type: 1
      }
    end

    it "registers the device for the user" do
      patch "/api/v1/public_integration/users/register_device", headers:, params:, as: :json

      expect(response).to be_successful
      expect(user.reload.device_tokens["registereds"]).to eq(1)
      expect(user.device_tokens["authorizeds"]).to eq(1)
      expect(user.device_tokens["tokens"].size).to eq(1)
    end

    context "when passing invalid params" do
      let(:params) do
        {
          environment: true,
          type: 1
        }
      end

      it "fails to register the device for the user" do
        patch "/api/v1/public_integration/users/register_device", headers:, params:, as: :json

        expect(response).to be_precondition_failed
      end
    end
  end

  describe "#reset_password" do
    let!(:user) do
      create(:user,
        cpf: "***********",
        email: "<EMAIL>",
        business:)
    end
    let(:headers) { {"Api-Secret" => business.api_secret} }
    let(:params) { {cpf: "884.631.650-95"} }

    it "resets the user password" do
      expect do
        patch "/api/v1/public_integration/users/reset_password", headers:, params:, as: :json
        user.reload
      end.to change(user, :encrypted_password)

      expect(response).to be_ok
      expect(response_hash["message"]).to eq("Enviamos um e-mail para joh*****@*****le.com com as instruções de recuperação da senha")
      expect(UserMailer.deliveries.count).to eq(1)
    end

    context "without cpf param" do
      it "does not reset the user password" do
        patch "/api/v1/public_integration/users/reset_password", headers:, params: {}, as: :json

        expect(response).not_to be_successful
        expect(UserMailer.deliveries.count).to eq(0)
      end
    end

    context "when user does not exist" do
      it "returns succesful but does not reset the password" do
        patch "/api/v1/public_integration/users/reset_password",
          headers:,
          params: {cpf: "***********"},
          as: :json

        expect(response).to be_ok
        expect(UserMailer.deliveries.count).to eq(0)
      end
    end

    context "when business app does not have email sender set" do
      before { business.mailer_config.update_columns(email_sender: nil) }

      it "resets the user password" do
        expect do
          patch "/api/v1/public_integration/users/reset_password", headers:, params:, as: :json
          user.reload
        end.to change(user, :encrypted_password)

        expect(response).to be_unprocessable
        expect(response_hash["error"]).to eq("SMTP From address may not be blank: nil")
        expect(UserMailer.deliveries.count).to eq(0)
      end
    end
  end

  describe "#create" do
    let(:headers) { {"Api-Secret": business.api_secret} }
    let(:serializer_keys) do
      %w[cellphone taxpayer_number email cpf facebook id name premium profile_complete business full_address lat lng default_auth_flow telemedicine]
    end
    let(:business_serializer_keys) do
      %w[cashback cashback_transfer_receiver cashback_requestable_amount giftcard biometry organization_manage]
    end
    let(:params) do
      {
        name: FFaker::Name.name,
        email: FFaker::Internet.email,
        cpf: FFaker::IdentificationBR.cpf,
        password: FFaker::Internet.password,
        cellphone: FFaker::PhoneNumberBR.mobile_phone_number
      }
    end

    context "when its cpf is banned" do
      before do
        BannedCpf.create(cpf: params[:cpf])
      end

      it "must be unauthorized" do
        post "/api/v1/public_integration/users", headers:, params:, as: :json

        expect(response).to be_unauthorized
        expect(response_hash["error"]).to eq("Este CPF está banido")
      end
    end

    context "with affiliation" do
      context "when user is not registered" do
        let!(:authorized_user) { create(:authorized_user, cpf: params[:cpf], business:) }

        it "returns successful and creates the user" do
          post "/api/v1/public_integration/users",
            headers:,
            params:,
            as: :json

          expect(response).to be_successful
          expect(User.count).to eq(1)
          expect(User.first.affiliated?(business.id)).to be true
          expect(response_hash.keys).to match_array(serializer_keys)
          expect(response_hash["cellphone"]).to eq(params[:cellphone].delete("^0-9"))
          expect(response_hash["business"].keys).to match_array(business_serializer_keys)
        end
      end

      context "when user is already registered" do
        let!(:user) { create(:user, cpf: params[:cpf], business:) }

        it "must render error" do
          post "/api/v1/public_integration/users",
            headers:,
            params:,
            as: :json

          expect(response).to be_unprocessable
          expect(response_hash["error"]).to eq(I18n.t("user.sign_up.validate.already_registered"))
        end
      end
    end

    context "with no affiliation" do
      it "returns error and does not create the user" do
        post "/api/v1/public_integration/users",
          headers:,
          params:,
          as: :json

        expect(response).not_to be_successful
        expect(User.count).to eq(0)
      end
    end

    context "when the business has custom sign up" do
      let!(:oab) { create(:business, :oab, name: "OABMG", create_project_config: false) }
      let!(:project_config) { create(:project_config, business: oab, custom_sign_up: true) }
      let(:headers) { {"Api-Secret": oab.api_secret} }

      before do
        stub_request(:post, %r{(.*)/hostservice.oabmg.org.br/(.*)}).to_return(
          status: 200,
          body: {"CodigoRetorno" => 0}.to_json
        )
      end

      it "returns successful and creates the user" do
        post "/api/v1/public_integration/users",
          headers:,
          params: params.merge!(number: "123456"),
          as: :json

        expect(response).to be_successful
        expect(User.count).to eq(1)
        expect(User.first.affiliated?(oab.id)).to be true
        expect(response_hash.keys).to match_array(serializer_keys)
        expect(response_hash["business"].keys).to match_array(business_serializer_keys)
      end

      it "returns error and does not create the user when not passing all the params" do
        post "/api/v1/public_integration/users",
          headers:,
          params: params.except(:number),
          as: :json

        expect(response).not_to be_successful
        expect(User.count).to eq(0)
      end

      context "when the custom sign up returns error" do
        before do
          stub_request(:post, %r{(.*)/hostservice.oabmg.org.br/(.*)}).to_return(
            status: 200,
            body: {
              "CodigoRetorno" => 1,
              "MensagemRetorno" => "O número de inscrição não foi encontrado. Favor consultar a secretaria."
            }.to_json
          )
        end

        it "returns error and does not create the user" do
          post "/api/v1/public_integration/users",
            headers:,
            params: params.merge!(number: "123456"),
            as: :json

          expect(response).not_to be_successful
          expect(User.count).to eq(0)
          expect(response_hash["error"]).to eq("O número de inscrição não foi encontrado. Favor consultar a secretaria.")
        end
      end
    end

    context "when user management type is no sign up" do
      before { business.project_config.update!(user_manager: Enums::UserManager::NO_SIGNUP) }

      it "returns error" do
        post "/api/v1/public_integration/users",
          headers:,
          params:,
          as: :json

        expect(response).to be_unprocessable
        expect(response_hash["error"]).to include(I18n.t("user.sign_up.validate.no_signup"))
      end
    end

    context "when user manager type is single_sign_on" do
      let!(:business) { create(:business, cnpj: "62500855000139", name: "Associacao Dos Advogados De Sao Paulo") }
      let(:jwt) { "eyJhbGciOiJSUzI1NiIsImtpZCI6IjFFQ0FBODcwOUE2OTYzNEJCQUVCMzgxN0RGQTA0QjhGN0VGQzIwREEiLCJ0eXAiOiJKV1QiLCJ4NXQiOiJIc3FvY0pwcFkwdTY2emdYMzZCTGozNzhJTm8ifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XWcRISsF4-kdVKzaEh9gxlAu2I66pt6EAblIO1L8E7FNB0AgHJsbDHKMVRcigavcnuLOtvTOqHJ6lIKCW4uXHSuw09ybLO-pPEeX3HLXQVaryEz_e1CNNTvTPmy10m9ybYNLMIjyzSDOk-hlf92gstNd7kJ8UP4YRjfPBdTOswk3xXMmwsVMpEtClGejCbPTyYwhTDYVQU8CaRgttw5eNxDaDkXyejij_EMYusj7pCH0BTNOf3USZQaKc4jDmnXS0CMG4TtcH-wUL3WSpitKO1TKa1gbpn-FeYwdo7KvgR1P5qJB6stnqBJlUcOFOBmKz6h2NFtj_VKjOq5_WFj6VA" }
      let(:decoded_jwt) { CustomSso::Aasp::Decode.decode(access_token: jwt) }

      before do
        business.project_config.update!(
          user_manager: Enums::UserManager::SINGLE_SIGN_ON,
          auth_integration_type: Enums::ProjectConfig::AuthIntegrationType::AASP
        )
      end

      context "when with valid jwt and existing user" do
        let(:params) do
          {access_token: jwt}
        end

        let!(:user) { create(:user, business:, external_id: "5257822") }

        it "must render succesfull with auth headers and api v2 access_token" do
          post "/api/v1/public_integration/users",
            headers:,
            params:,
            as: :json

          expect(response).to be_successful
          expect(response_hash.keys).to match_array(serializer_keys)
          expect(response_hash["business"].keys).to match_array(business_serializer_keys)
          expect(response.headers["Authorization"]).to be_present
          _method, api_v2_access_token = response.headers["Authorization"].split
          jwt_jti = Warden::JWTAuth::TokenDecoder.new.call(api_v2_access_token)["jti"]
          expect(AllowlistedJwt.where(jti: jwt_jti)).to be_present
        end
      end

      context "when with valid jwt and all params to register user" do
        let(:params) do
          {access_token: jwt, cpf: "***********"}
        end

        it "must render succesfull with auth headers" do
          post "/api/v1/public_integration/users",
            headers:,
            params:,
            as: :json

          expect(response).to be_successful
          expect(response_hash.keys).to match_array(serializer_keys)
          expect(response_hash["cpf"]).to eq(params[:cpf])
          expect(response_hash["business"].keys).to match_array(business_serializer_keys)
        end
      end

      context "when with valid jwt but without all params to register user" do
        let(:params) do
          {access_token: jwt}
        end

        it "must render unprocessable entity with user data" do
          post "/api/v1/public_integration/users",
            headers:,
            params:,
            as: :json

          expect(response).to be_unprocessable
          expect(response_hash.keys).to match_array(%w[cpf name email cellphone])
          expect(response_hash["cpf"]).to eq(nil)
          expect(response_hash["name"]).to eq(Utils::NameNormalizer.call(decoded_jwt["NomePessoaFisica"]))
        end
      end
    end
  end

  describe "#update" do
    let!(:user) do
      create(:user,
        name: "John Doe",
        cpf: "***********",
        email: "<EMAIL>",
        cellphone: "***********",
        business:)
    end
    let(:headers) do
      user.create_new_auth_token.merge!("Api-Secret": business.api_secret)
    end
    let(:serializer_keys) do
      %w[cellphone taxpayer_number cpf email facebook id name premium profile_complete business full_address lat lng default_auth_flow telemedicine]
    end
    let(:business_serializer_keys) do
      %w[cashback cashback_transfer_receiver cashback_requestable_amount giftcard biometry organization_manage]
    end
    let(:params) do
      {
        name: "Jack Dude",
        email: "<EMAIL>",
        cpf: "***********",
        cellphone: "3598765484"
      }
    end

    it "returns successful and updates the user" do
      patch "/api/v1/public_integration/users",
        headers:,
        params:,
        as: :json

      expect(response).to have_http_status(:ok)
      expect(response_hash.keys).to match_array(serializer_keys)
      expect(response_hash["business"].keys).to match_array(business_serializer_keys)
      user.reload
      expect(user.name).to eq("Jack Dude")
      expect(user.email).to eq("<EMAIL>")
      expect(user.cpf).to eq("***********")
      expect(user.cellphone).to eq("3598765484")
    end

    context "with no affiliation" do
      before do
        user.update!(active: false)
      end

      it "returns error and does not update the user" do
        patch "/api/v1/public_integration/users",
          headers:,
          params:,
          as: :json

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "#me" do
    let!(:user) do
      create(:user,
        name: "John Doe",
        cpf: "***********",
        email: "<EMAIL>",
        cellphone: "***********",
        business:)
    end

    let(:serializer_keys) do
      %w[cellphone taxpayer_number cpf email facebook id name premium profile_complete business full_address lat lng default_auth_flow telemedicine]
    end
    let(:business_serializer_keys) do
      %w[cashback cashback_transfer_receiver cashback_requestable_amount giftcard biometry organization_manage]
    end

    context "using api_secret to access" do
      let(:headers) do
        user.create_new_auth_token.merge!("Api-Secret": business.api_secret)
      end

      it "returns successful the user" do
        get "/api/v1/public_integration/users/me",
          headers:,
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.keys).to match_array(serializer_keys)
        expect(response_hash["business"].keys).to match_array(business_serializer_keys)
      end

      describe "business cashback" do
        let!(:business_child) do
          create(:business,
            name: "Business Child",
            main_business: business,
            cnpj: "10589414000151",
            cashback: false)
        end

        context "when user is in parent business" do
          context "and parent business has cashback enabled and child business has cashback disabled" do
            before do
              business.update!(
                cashback: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
            end

            it "returns successful the user with business cashback true" do
              get "/api/v1/public_integration/users/me",
                headers:,
                as: :json

              expect(response).to have_http_status(:ok)
              expect(response_hash.keys).to match_array(serializer_keys)
              expect(response_hash["business"].keys).to match_array(business_serializer_keys)
              expect(response_hash["business"]["cashback"]).to be true
            end
          end

          context "and parent business has cashback disabled and child business has cashback enabled" do
            before do
              business_child.update!(
                cashback: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
            end

            it "returns successful the user with business cashback false" do
              get "/api/v1/public_integration/users/me",
                headers:,
                as: :json

              expect(response).to have_http_status(:ok)
              expect(response_hash.keys).to match_array(serializer_keys)
              expect(response_hash["business"].keys).to match_array(business_serializer_keys)
              expect(response_hash["business"]["cashback"]).to be false
            end
          end

          context "and parent business has giftcart enabled and child business has giftcart enabled" do
            before do
              business.update!(
                giftcard: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
              business_child.update!(
                giftcard: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
            end

            it "returns successful the user with cashback true" do
              get "/api/v1/public_integration/users/me",
                headers:,
                as: :json

              expect(response).to have_http_status(:ok)
              expect(response_hash.keys).to match_array(serializer_keys)
              expect(response_hash["business"].keys).to match_array(business_serializer_keys)
              expect(response_hash["business"]["giftcard"]).to be true
            end
          end

          context "and parent business has giftcart disabled and child business has giftcart enabled" do
            before do
              business_child.update!(
                giftcard: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
            end

            it "returns successful the user with business cashback false" do
              get "/api/v1/public_integration/users/me",
                headers:,
                as: :json

              expect(response).to have_http_status(:ok)
              expect(response_hash.keys).to match_array(serializer_keys)
              expect(response_hash["business"].keys).to match_array(business_serializer_keys)
              expect(response_hash["business"]["giftcard"]).to be false
            end
          end
        end

        context "when user is in child business" do
          let(:headers) do
            user.create_new_auth_token.merge!("Api-Secret": business.api_secret)
          end

          before do
            user.update!(business: business_child)
          end

          context "and parent business has cashback enabled and child business has cashback enabled" do
            before do
              business.update!(
                cashback: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
              business_child.update!(
                cashback: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
            end

            it "returns successful the user with cashback true" do
              get "/api/v1/public_integration/users/me",
                headers:,
                as: :json

              expect(response).to have_http_status(:ok)
              expect(response_hash.keys).to match_array(serializer_keys)
              expect(response_hash["business"].keys).to match_array(business_serializer_keys)
              expect(response_hash["business"]["cashback"]).to be true
            end
          end

          context "and parent business has cashback enabled and child business has cashback disabled" do
            before do
              business.update!(
                cashback: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
            end

            it "returns successful the user with business cashback false" do
              get "/api/v1/public_integration/users/me",
                headers:,
                as: :json

              expect(response).to have_http_status(:ok)
              expect(response_hash.keys).to match_array(serializer_keys)
              expect(response_hash["business"].keys).to match_array(business_serializer_keys)
              expect(response_hash["business"]["cashback"]).to be false
            end
          end

          context "and parent business has cashback disabled and child business has cashback enabled" do
            before do
              business_child.update!(
                cashback: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
            end

            it "returns successful the user with business cashback false" do
              get "/api/v1/public_integration/users/me",
                headers:,
                as: :json

              expect(response).to have_http_status(:ok)
              expect(response_hash.keys).to match_array(serializer_keys)
              expect(response_hash["business"].keys).to match_array(business_serializer_keys)
              expect(response_hash["business"]["cashback"]).to be false
            end
          end

          context "and parent business has giftcart enabled and child business has giftcart enabled" do
            before do
              business.update!(
                giftcard: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
              business_child.update!(
                giftcard: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
            end

            it "returns successful the user with cashback true" do
              get "/api/v1/public_integration/users/me",
                headers:,
                as: :json

              expect(response).to have_http_status(:ok)
              expect(response_hash.keys).to match_array(serializer_keys)
              expect(response_hash["business"].keys).to match_array(business_serializer_keys)
              expect(response_hash["business"]["giftcard"]).to be true
            end
          end

          context "and parent business has giftcart enabled and child business has giftcart disabled" do
            before do
              business.update!(
                giftcard: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
            end

            it "returns successful the user with business cashback false" do
              get "/api/v1/public_integration/users/me",
                headers:,
                as: :json

              expect(response).to have_http_status(:ok)
              expect(response_hash.keys).to match_array(serializer_keys)
              expect(response_hash["business"].keys).to match_array(business_serializer_keys)
              expect(response_hash["business"]["giftcard"]).to be false
            end
          end

          context "and parent business has giftcart disabled and child business has giftcart enabled" do
            before do
              business_child.update!(
                giftcard: true,
                cashback_wallet_destination: Wallet::Kind::CASHBACK,
                cashback_requestable_amount: 20
              )
            end

            it "returns successful the user with business cashback false" do
              get "/api/v1/public_integration/users/me",
                headers:,
                as: :json

              expect(response).to have_http_status(:ok)
              expect(response_hash.keys).to match_array(serializer_keys)
              expect(response_hash["business"].keys).to match_array(business_serializer_keys)
              expect(response_hash["business"]["giftcard"]).to be false
            end
          end
        end
      end

      context "with user_destroy_request" do
        let(:serializer_keys) do
          %w[cellphone taxpayer_number cpf email facebook id name premium profile_complete business destroy_request full_address lat lng default_auth_flow telemedicine]
        end

        before do
          create(:user_destroy_request, user:, request_status: Enums::UserDestroyRequest::Status::CANCELED)
          create(:user_destroy_request, user:, request_status: Enums::UserDestroyRequest::Status::CANCELED)

          create(:user_destroy_request, user:)
        end

        it "returns successful the user with user_destroy_request" do
          get "/api/v1/public_integration/users/me",
            headers:,
            as: :json
          expect(response).to have_http_status(:ok)
          expect(response_hash.keys).to match_array(serializer_keys)
          expect(response_hash["business"].keys).to match_array(business_serializer_keys)
          expect(response_hash["destroy_request"].keys).to match_array(["created_at"])
          expect(response_hash["destroy_request"].count).to eq(1)
        end
      end
    end

    context "using web_domain to access" do
      let!(:project_config) { create(:project_config, business:, web_domain: "business") }
      let(:headers) do
        user.create_new_auth_token("web").merge!("Web-Domain": project_config.web_domain)
      end

      it "returns successful the user" do
        get "/api/v1/public_integration/users/me",
          headers:,
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.keys).to match_array(serializer_keys)
        expect(response_hash["business"].keys).to match_array(business_serializer_keys)
      end
    end
  end
end
