require "rails_helper"
require "sidekiq/testing"

RSpec.describe Api::V1::Organizations::OrdersController, :vcr, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  let!(:business) { create(:business, :lecupon_equipe, :with_cashback) }
  let(:signed_user) { create(:user, cpf: "***********", cellphone: "***********", business:) }
  let(:auth_headers) do
    signed_user.create_new_auth_token.merge!("Api-Secret" => business.api_secret)
  end
  let!(:organization) { create(:organization, :active_with_coupon) }
  let!(:branch) { create(:branch, organization:, lat: 1.0, lng: 1.0) }
  let!(:tagging) { create(:tagging, :for_organization, taggable: organization) }
  let!(:cupon_classic) { create(:cupon, :classic, :percent_discount, branch:) }
  let!(:cupon_cpf) { create(:cupon, :cpf, :percent_discount, branch:) }
  let!(:dynamic_bucket) { create(:voucher_bucket) }
  let!(:dynamic_promotion) { create(:promotion, :dynamic, :available, voucher_bucket: dynamic_bucket) }
  let!(:cupon_dynamic) { create(:cupon, :dynamic, :percent_discount, branch:, promotion: dynamic_promotion) }

  let(:voucher_bucket_lbc_gift_card) { create(:voucher_bucket) }
  let!(:promotion_lbc_gift_card) { create(:promotion, :lbc_giftcard, :available, voucher_bucket: voucher_bucket_lbc_gift_card) }
  let!(:cupon_lbc_gift_card) { create(:cupon, :lbc_giftcard, :percent_discount, branch:, promotion: promotion_lbc_gift_card) }
  let!(:voucher_lbc_gift_card) { create(:voucher, bucket: voucher_bucket_lbc_gift_card) }

  let!(:bucket_fixed) { create(:voucher_bucket) }
  let!(:promotion_fixed) { create(:promotion, :coupon_code, :available, voucher_bucket: bucket_fixed) }
  let!(:cupon_fixed) { create(:cupon, :coupon_code, :percent_discount, branch:, promotion: promotion_fixed) }
  let!(:voucher_coupon_code) { create(:voucher, bucket: bucket_fixed) }

  let(:voucher_bucket) { create(:voucher_bucket) }
  let!(:promotion_voucher) { create(:promotion, :coupon_code, voucher_bucket:) }
  let!(:coupon_voucher) { create(:cupon, :voucher, :percent_discount, branch:, promotion: promotion_voucher) }
  let!(:voucher_voucher) { create(:voucher, bucket: voucher_bucket) }

  let!(:coupon_fixed_code) { create(:cupon, :fixed_code, :percent_discount, branch:, code: "CODIGOFIXO") }

  let(:redeem_cupon_serializer_keys) do
    %w[id number created_at organization_name description discount redeem_code title
      coupon_description coupon_discount coupon_number paid_at usage_ended_at redeem_instruction
      giftcard_id organization_cover_picture price cashback_type cashback_value]
  end

  describe "/api/v1/public_integration/organizations/:id/orders" do
    context "create with success" do
      it "create order using a classic coupon(qrcode)" do
        expect do
          post "/api/v1/public_integration/organizations/#{organization.id}/orders",
            headers: auth_headers,
            params: {coupon_id: cupon_classic.id, confirmation_key: branch.cnpj},
            as: :json
        end.to change(WebhookSender::EventWorker.jobs, :size).by(1)

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.keys).to match_array(redeem_cupon_serializer_keys)
        expect(response.parsed_body["created_at"]).to eq(
          Order.last.created_at.in_time_zone(branch.timezone).strftime("%d/%m/%Y %H:%M")
        )
        expect(Order.count).to eq(1)
        expect(response.parsed_body["coupon_discount"]).to eq("30")
      end

      it "does not create order using a classic coupon(qrcode) and without confirmation key" do
        post "/api/v1/public_integration/organizations/#{organization.id}/orders",
          headers: auth_headers,
          params: {coupon_id: cupon_classic.id, confirmation_key: nil},
          as: :json

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it "create order using a dynamic coupon" do
        expect do
          post "/api/v1/public_integration/organizations/#{organization.id}/orders",
            headers: auth_headers,
            params: {coupon_id: cupon_dynamic.id},
            as: :json
        end.to change(WebhookSender::EventWorker.jobs, :size).by(1)

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.keys).to match_array(redeem_cupon_serializer_keys)
        expect(response.parsed_body["coupon_discount"]).to eq("30")
        expect(response.parsed_body["created_at"]).to eq(
          Order.last.created_at.in_time_zone(branch.timezone).strftime("%d/%m/%Y %H:%M")
        )
        expect(Order.count).to eq(1)
      end

      context "with LBC gift card coupon" do
        context "and business enabled LBC gift card" do
          it "renders successful and creates order" do
            expect do
              post "/api/v1/public_integration/organizations/#{organization.id}/orders",
                headers: auth_headers,
                params: {coupon_id: cupon_lbc_gift_card.id},
                as: :json
            end.to change(Order, :count).by(1)
              .and change(WebhookSender::EventWorker.jobs, :size).by(1)

            expect(response).to have_http_status(:ok)
            expect(response.parsed_body.keys).to match_array(redeem_cupon_serializer_keys)
            expect(response.parsed_body["coupon_discount"]).to eq("30")
            expect(response.parsed_body["created_at"]).to eq(
              Order.last.created_at.in_time_zone(branch.timezone).strftime("%d/%m/%Y %H:%M")
            )
          end
        end

        context "and business disabled LBC gift card" do
          before { business.update!(lbc_giftcard: false) }

          it "renders error and does not create order" do
            expect do
              post "/api/v1/public_integration/organizations/#{organization.id}/orders",
                headers: auth_headers,
                params: {coupon_id: cupon_lbc_gift_card.id},
                as: :json
            end.to change(Order, :count).by(0)
              .and change(WebhookSender::EventWorker.jobs, :size).by(0)

            expect(response).to have_http_status(:unprocessable_entity)
            expect(response.parsed_body["error"]).to eq(I18n.t("voucher.redeem.lbc_giftcard.deactivated"))
          end
        end
      end

      it "create order using a coupon code coupon" do
        expect do
          post "/api/v1/public_integration/organizations/#{organization.id}/orders",
            headers: auth_headers,
            params: {coupon_id: cupon_fixed.id},
            as: :json
        end.to change(WebhookSender::EventWorker.jobs, :size).by(1)

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.keys).to match_array(redeem_cupon_serializer_keys)
        expect(response.parsed_body["coupon_discount"]).to eq("30")
        expect(Order.count).to eq(1)
        expect(response.parsed_body["created_at"]).to eq(
          Order.last.created_at.in_time_zone(branch.timezone).strftime("%d/%m/%Y %H:%M")
        )
      end

      context "when redeeming voucher coupon" do
        it "redeems voucher coupon" do
          expect do
            post "/api/v1/public_integration/organizations/#{organization.id}/orders",
              headers: auth_headers,
              params: {coupon_id: coupon_voucher.id},
              as: :json
          end.to change(WebhookSender::EventWorker.jobs, :size).by(1)
          expect(response).to have_http_status(:ok)
          expect(response.parsed_body.keys).to match_array(redeem_cupon_serializer_keys)
          expect(response.parsed_body["coupon_discount"]).to eq("30")
          expect(Order.count).to eq(1)
        end

        context "when coupon was redeemed long time ago" do
          before do
            travel_to(1.year.ago) do
              post "/api/v1/public_integration/organizations/#{organization.id}/orders",
                headers: auth_headers,
                params: {coupon_id: coupon_voucher.id},
                as: :json
            end
          end

          it "returns error" do
            expect do
              post "/api/v1/public_integration/organizations/#{organization.id}/orders",
                headers: auth_headers,
                params: {coupon_id: coupon_voucher.id},
                as: :json
            end.to change(Order, :count).by(0)
              .and change(Voucher, :count).by(0)
              .and change(WebhookSender::EventWorker.jobs, :size).by(0)

            expect(response).to have_http_status(:unprocessable_entity)
            expect(response.parsed_body["error"]).to eq("Voucher não disponível para resgate")
          end
        end
      end

      it "redeems fixed code coupon" do
        expect do
          post "/api/v1/public_integration/organizations/#{organization.id}/orders",
            headers: auth_headers,
            params: {coupon_id: coupon_fixed_code.id},
            as: :json
        end.to change(WebhookSender::EventWorker.jobs, :size).by(1)

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.keys).to match_array(redeem_cupon_serializer_keys)
        expect(response.parsed_body["coupon_discount"]).to eq("30")
        expect(Order.count).to eq(1)
        expect(response.parsed_body["created_at"]).to eq(
          Order.last.created_at.in_time_zone(branch.timezone).strftime("%d/%m/%Y %H:%M")
        )
        expect(response.parsed_body["redeem_code"]).to eq("CODIGOFIXO")
        expect(response.parsed_body["coupon_number"]).to eq("CODIGOFIXO")
      end

      it "must increment promotion redeem count" do
        old_promotion_redeemed_count = coupon_voucher.promotion.redeemed_count
        expect do
          post "/api/v1/public_integration/organizations/#{organization.id}/orders",
            headers: auth_headers,
            params: {coupon_id: coupon_voucher.id},
            as: :json
        end.to change(WebhookSender::EventWorker.jobs, :size).by(1)

        expect(response).to have_http_status(:ok)
        coupon_voucher.reload
        expect(coupon_voucher.promotion.redeemed_count).to eq(old_promotion_redeemed_count + 1)
      end
    end

    context "when organization is blocklisted" do
      let!(:org_approval) do
        create(:organization_blocklist,
          business:,
          organization:)
      end

      it "returns error and does not create order" do
        expect do
          post "/api/v1/public_integration/organizations/#{organization.id}/orders",
            headers: auth_headers,
            params: {coupon_id: cupon_classic.id, confirmation_key: branch.cnpj},
            as: :json
        end.to change(WebhookSender::EventWorker.jobs, :size).by(0)

        expect(response).to have_http_status(:not_found)
        expect(Order.count).to eq(0)
      end
    end

    context "try redeem on used cupon" do
      context "free used per day" do
        let!(:used_cupon) do
          create(:cupon, :fixed, :percent_discount, code: "ABC", branch:, redeems_per_cpf: 2, frequency_in_days: 0, promotion: promotion_fixed)
        end

        let!(:order) { create(:order, user: signed_user, cupon: used_cupon, voucher: voucher_coupon_code) }
        let!(:voucher_coupon_code_one) { create(:voucher, bucket: bucket_fixed) }
        let!(:voucher_coupon_code_two) { create(:voucher, bucket: bucket_fixed) }

        it "can create order using a used coupon" do
          expect do
            post "/api/v1/public_integration/organizations/#{organization.id}/orders",
              headers: auth_headers,
              params: {coupon_id: used_cupon.id},
              as: :json
          end.to change(WebhookSender::EventWorker.jobs, :size).by(1)
          expect(response).to have_http_status(:ok)
          expect(response.parsed_body.keys).to match_array(redeem_cupon_serializer_keys)
          expect(response.parsed_body["coupon_discount"]).to eq("30")
          expect(Order.count).to eq(2)
        end
      end

      context "one per day" do
        let!(:used_cupon) do
          create(:cupon, :fixed, code: "ABC", branch:, redeems_per_cpf: 1, frequency_in_days: 1, promotion: promotion_fixed)
        end
        let!(:bucket) { create(:voucher_bucket) }
        let!(:order) { create(:order, user: signed_user, cupon: used_cupon, voucher: voucher_coupon_code) }
        let!(:voucher_coupon_code) { create(:voucher, bucket:) }

        it "can not create order using a used coupon" do
          post "/api/v1/public_integration/organizations/#{organization.id}/orders",
            headers: auth_headers,
            params: {coupon_id: used_cupon.id},
            as: :json

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context "one per 2 day" do
        let!(:used_cupon) do
          create(:cupon, :fixed, :percent_discount, code: "ABC", branch:, redeems_per_cpf: 1, frequency_in_days: 2, promotion: promotion_fixed)
        end

        let!(:bucket) { create(:voucher_bucket) }
        let!(:order) { create(:order, user: signed_user, cupon: used_cupon, created_at: 3.days.ago, voucher: voucher_coupon_code) }
        let!(:voucher_coupon_code_one) { create(:voucher, bucket: bucket_fixed) }
        let!(:voucher_coupon_code_two) { create(:voucher, bucket: bucket_fixed) }

        it "can create order using a used coupon" do
          expect do
            post "/api/v1/public_integration/organizations/#{organization.id}/orders",
              headers: auth_headers,
              params: {coupon_id: used_cupon.id},
              as: :json
          end.to change(WebhookSender::EventWorker.jobs, :size).by(1)

          expect(response).to have_http_status(:ok)
          expect(response.parsed_body.keys).to match_array(redeem_cupon_serializer_keys)
          expect(response.parsed_body["coupon_discount"]).to eq("30")
          expect(Order.count).to eq(2)
        end
      end

      context "one per 7 day used" do
        let!(:used_cupon) do
          create(:cupon, :fixed, code: "ABC", branch:, redeems_per_cpf: 1, frequency_in_days: 7, promotion: promotion_fixed)
        end
        let!(:bucket) { create(:voucher_bucket) }
        let!(:order) { create(:order, user: signed_user, cupon: used_cupon, voucher: voucher_coupon_code) }
        let!(:voucher_coupon_code) { create(:voucher, bucket:) }

        it "can not create order using a used coupon" do
          post "/api/v1/public_integration/organizations/#{organization.id}/orders",
            headers: auth_headers,
            params: {coupon_id: used_cupon.id},
            as: :json

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context "one per 7 day old use" do
        let!(:used_cupon) do
          create(:cupon, :fixed, :percent_discount, code: "ABC", branch:, redeems_per_cpf: 1, frequency_in_days: 7, promotion: promotion_fixed)
        end
        let!(:order) { create(:order, user: signed_user, cupon: used_cupon, created_at: 8.days.ago, voucher: voucher_coupon_code) }
        let!(:voucher_coupon_code_one) { create(:voucher, bucket: bucket_fixed) }
        let!(:voucher_coupon_code_two) { create(:voucher, bucket: bucket_fixed) }

        it "can create order using a used coupon" do
          expect do
            post "/api/v1/public_integration/organizations/#{organization.id}/orders",
              headers: auth_headers,
              params: {coupon_id: used_cupon.id},
              as: :json
          end.to change(WebhookSender::EventWorker.jobs, :size).by(1)
          expect(response).to have_http_status(:ok)
          expect(response.parsed_body.keys).to match_array(redeem_cupon_serializer_keys)
          expect(response.parsed_body["coupon_discount"]).to eq("30")
          expect(Order.count).to eq(2)
        end
      end
    end

    context "with unredeemable coupon type" do
      let!(:coupon_cpf) { create(:cupon, :cpf, branch:) }

      it "returns error and does not redeem the coupon" do
        expect do
          post "/api/v1/public_integration/organizations/#{branch.organization_id}/orders",
            headers: auth_headers,
            params: {coupon_id: coupon_cpf.id},
            as: :json
        end.to change(WebhookSender::EventWorker.jobs, :size).by(0)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(Order.count).to eq(0)
      end
    end

    context "when redeeming online voucher coupon" do
      let!(:branch) { create(:branch, :online, organization:) }
      let!(:coupon) { create(:cupon, :dynamic_online, branch:, organization:) }
      let(:params) { {coupon_id: coupon.id} }

      it "renders error" do
        post "/api/v1/public_integration/organizations/#{organization.id}/orders",
          headers: auth_headers,
          params:,
          as: :json

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
end
