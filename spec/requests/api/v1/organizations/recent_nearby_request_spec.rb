require "rails_helper"

RSpec.describe Api::V1::Organizations::RecentController, type: :request do
  let!(:business) { create(:business, :lecupon_equipe, :with_cashback) }
  let(:signed_user) { create(:user, cpf: "***********", cellphone: "***********", business:) }
  let(:auth_headers) do
    signed_user.create_new_auth_token.merge!("Api-Secret" => business.api_secret)
  end
  let!(:category) { create(:category) }
  let!(:organization) do
    create(:organization,
      :active_with_coupon,
      :highlighted,
      :percent_highest_discount,
      categories: [category],
      highest_discount_value: 10,
      promotion_redeemable: true,
      name: "Ze Delivery")
  end
  let!(:tagging) { create(:tagging, :for_organization, taggable: organization) }
  let!(:branch) { create(:branch, organization:, lat: 1.0, lng: 1.0, promotion_redeemable: true) }
  let!(:online_branch) { create(:branch, :online, organization:) }
  let!(:promotion) { create(:promotion, :online, :percent_discount, organization:, cashback_value: 3.0) }
  let!(:cupon) { create(:cupon, :online, :percent_discount, promotion:, branch: online_branch) }
  let!(:promotion_two) { create(:promotion, :qrcode, :percent_discount, organization:, cashback_value: 3.0) }
  let!(:cupon_two) { create(:cupon, :classic, promotion: promotion_two, branch:) }
  let(:organization_show_serializer_keys) do
    ["background_picture", "best_discount_percent", "category_name", "cover_picture",
      "facebook_url", "id", "instagram_url", "name", "twitter_url", "cashback_percent",
      "distance_km", "cashback_text", "discount_text", "description", "favorited",
      "short_description", "top_background_image_small", "top_background_image_large",
      "top_background_image_v2_large", "top_background_image_v2_small", "logo_image_small",
      "banner_image_large", "banner_image_small", "banner_image_v2_large", "banner_image_v2_small",
      "logo_image", "top_background_image"]
  end
  let(:response_hash) { JSON.parse(response.body) }

  before do
    Promotion::PropagateChanges.call
  end

  describe "GET /api/v1/public_integration/organizations/recent_nearby" do
    it "returns a list of recent nearby organizations" do
      get "/api/v1/public_integration/organizations/recent_nearby",
        headers: auth_headers,
        params: {lat: 1.0, lng: 1.0},
        as: :json

      expect(response).to have_http_status(:ok)
      expect(response_hash.first.keys).to match_array(organization_show_serializer_keys)
      expect(response_hash.pluck("best_discount_percent")).to match_array([30])
      expect(response_hash.first["cashback_percent"]).to eq(2.7)
      expect(response_hash.first["cashback_text"]).to eq("2.7% de cashback")
      expect(response_hash.first["discount_text"]).to eq("Até 30% Off")
    end

    it "returns error with wrong page param" do
      get "/api/v1/public_integration/organizations/recent_nearby",
        headers: auth_headers,
        params: {lat: 1.0, lng: 1.0, page: 0},
        as: :json

      expect(response).to have_http_status(:bad_request)
    end

    context "cupons on another business" do
      before do
        business_two = create(:business, :publicar)
        Promotion.update_all(business_id: business_two.id)
      end

      it "returns an empty list of recent nearby organizations" do
        get "/api/v1/public_integration/organizations/recent_nearby",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash).to be_empty
      end
    end

    context "with no business cashback" do
      before do
        business.update!(cashback: false)
      end

      it "returns a list of recent nearby organizations without cashback text" do
        get "/api/v1/public_integration/organizations/recent_nearby",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.first.keys).to match_array(organization_show_serializer_keys)
        expect(response_hash.pluck("best_discount_percent")).to match_array([30])
        expect(response_hash.first["cashback_percent"]).to be_nil
        expect(response_hash.first["cashback_text"]).to be_nil
      end
    end
  end
end
