require "rails_helper"

RSpec.describe Api::V1::Organizations::GiftcardsController, type: :request do
  let!(:business) { create(:business, :with_cashback, giftcard: true) }
  let!(:user) { create(:user, business:) }
  let!(:organization) { create(:organization, giftcard_redeemable: true) }
  let!(:organization_inactive_with_giftcard) { create(:organization, giftcard_redeemable: true, active: false) }
  let!(:organization_blocklisted_with_giftcard) { create(:organization, giftcard_redeemable: true) }
  let!(:organization_blocklist) do
    create(:organization_blocklist,
      organization: organization_blocklisted_with_giftcard,
      business: user.business)
  end
  let!(:organization_without_giftcard) { create(:organization, giftcard_redeemable: false) }
  let!(:branch) { create(:branch, organization:) }
  let!(:bucket) { create(:voucher_bucket) }
  let!(:giftcard) { create(:giftcard, :with_vouchers, quantity: 1, branches: [branch], voucher_bucket: bucket) }
  let!(:unavailable_giftcard) { create(:giftcard, branches: [branch], available: false) }
  let(:headers) { user.create_new_auth_token.merge!("Api-Secret" => business.api_secret) }
  let(:serializer_keys) { %w[id title price redeem_instruction cashback] }
  let(:cashback_serializer_keys) { %w[type value] }
  let(:response_hash) { JSON.parse(response.body) }

  describe "GET /api/v1/public_integration/organizations/:organization_id/giftcards" do
    it "returns giftcards of organization" do
      get("/api/v1/public_integration/organizations/#{organization.id}/giftcards", headers:)

      expect(response).to have_http_status(:ok)
      expect(response_hash.map(&:keys)).to all(match_array(serializer_keys))
      expect(response_hash.pluck("id")).to eq([giftcard.id])
      expect(response_hash.pluck("cashback").map(&:keys)).to all(match_array(cashback_serializer_keys))
    end

    context "with inactive organization" do
      it "returns error" do
        get("/api/v1/public_integration/organizations/#{organization_inactive_with_giftcard.id}/giftcards",
          headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "with blocklisted organization" do
      it "returns error" do
        get("/api/v1/public_integration/organizations/#{organization_blocklisted_with_giftcard.id}/giftcards",
          headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when has not giftcards left" do
      before do
        Giftcard::Order.reserve(user:, giftcard:, voucher: giftcard.vouchers.first)
        giftcard.refresh_availability
      end

      it "returns empty array" do
        get("/api/v1/public_integration/organizations/#{organization.id}/giftcards", headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash).to eq([])
      end
    end
  end
end
