# frozen_string_literal: true

require "rails_helper"
require "sidekiq/testing"

RSpec.describe Api::V1::Organizations::CouponsController, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  let!(:business) { create(:business, :lecupon_equipe, :with_cashback) }
  let(:signed_user) { create(:user, cpf: "***********", cellphone: "***********", business:) }
  let(:auth_headers) { signed_user.create_new_auth_token }
  let!(:organization) { create(:organization, :active_with_coupon) }
  let!(:branch) { create(:branch, organization:, lat: 1.0, lng: 1.0) }
  let!(:branch_two) { create(:branch, organization:, lat: 1.0, lng: 1.0) }
  let!(:branch_online) { create(:branch, :online, organization:) }
  let!(:tagging) { create(:tagging, :for_organization, taggable: organization) }
  let(:category_one) { create(:category) }
  let(:category_two) { create(:category, main_category_id: category_one.id) }

  let!(:promotion_online) { create(:promotion, :available, :online, :percent_discount, tags: ["Online"], categories: [category_one, category_two]) }
  let!(:cupon_online) { create(:cupon, :online, :percent_discount, branch: branch_online, code: "PROMO10", promotion: promotion_online) }
  let!(:promotion_fixed_discount) { create(:promotion, :available, :online, :fixed_discount, categories: [category_one, category_two]) }
  let!(:cupon_fixed_discount) { create(:cupon, :online, :fixed_discount, branch: branch_online, promotion: promotion_fixed_discount) }

  let!(:promotion_cpf) { create(:promotion, :available, :cpf, :percent_discount, categories: [category_one, category_two]) }
  let!(:cupon_cpf) { create(:cupon, :cpf, :percent_discount, branch:, promotion: promotion_cpf) }

  let!(:promotion_qrcode) { create(:promotion, :available, :qrcode, :percent_discount, tags: ["Physical"], categories: [category_one, category_two]) }
  let!(:cupon_classic) { create(:cupon, :classic, :percent_discount, branch:, promotion: promotion_qrcode) }

  let!(:promotion_code) { create(:promotion, :available, :coupon_code, :percent_discount, categories: [category_one, category_two]) }
  let!(:cupon_fixed) { create(:cupon, :fixed, :percent_discount, branch:, promotion: promotion_code) }

  let!(:promotion_dynamic) { create(:promotion, :available, :dynamic, :percent_discount, categories: [category_one, category_two]) }
  let!(:cupon_dynamic) { create(:cupon, :dynamic, :percent_discount, branch:, promotion: promotion_dynamic) }

  let!(:promotion_cpf_two) { create(:promotion, :available, :cpf, :percent_discount, categories: [category_one, category_two]) }
  let!(:cupon_cpf_two) { create(:cupon, :cpf, :percent_discount, branch:, promotion: promotion_cpf_two) }

  let!(:promotion_lbc_giftcard) { create(:promotion, :available, :lbc_giftcard, :percent_discount, categories: [category_one, category_two]) }
  let!(:cupon_lbc_giftcard) { create(:cupon, :lbc_giftcard, :percent_discount, branch:, promotion: promotion_lbc_giftcard) }

  let(:cupon_index_serializer_keys) do
    %w[description id template title cashback_text online_payment_text discount start_date end_date infinity
      picture_small_url picture_large_url branch_id organization_id tags usage_instruction]
  end
  let(:coupon_code_serializer_keys) do
    %w[working_days description id nearest_address organization_cover_image
      organization_name template title rules cashback_text discount start_date end_date infinity
      branch_id organization_id picture_small_url picture_large_url tags usage_instruction categories]
  end
  let(:coupon_default_serializer_keys) do
    %w[description id organization_cover_image organization_name template
      title working_days rules cashback_text discount start_date end_date infinity
      branch_id organization_id picture_small_url picture_large_url tags usage_instruction categories]
  end
  let(:coupon_link_serializer_keys) do
    %w[activation_url code description id organization_cover_image organization_name
      rules template title cashback_text discount start_date end_date working_days infinity
      branch_id organization_id picture_small_url picture_large_url tags usage_instruction categories]
  end
  let(:coupon_qrcode_serializer_keys) do
    %w[id template working_days organization_name organization_cover_image title rules description cashback_text discount start_date end_date infinity
      branch_id organization_id picture_small_url picture_large_url tags usage_instruction categories]
  end
  let(:working_days_keys) do
    %w[week_day is_available start_hour end_hour]
  end

  let(:included_category_keys) do
    %w[id category_type icon image image_v2 template title]
  end

  let(:response_hash) { JSON.parse(response.body) }

  before do
    auth_headers.merge!("Api-Secret": business.api_secret)
  end

  describe "GET /api/v1/public_integration/organizations/:id/coupons" do
    context "coupons on the same business" do
      it "return a list of online coupons available on business" do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
          headers: auth_headers,
          params: {usage_type: "online"},
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.count).to eq(2)
        expect(response_hash.first.keys).to match_array(cupon_index_serializer_keys)
        expect(response_hash.pluck("discount")).to match_array([30, 0])
        expect(response_hash.first["cashback_text"]).to eq("9% de cashback")
        expect(response_hash.first["tags"]).to match_array(["Online"])
      end

      it "return a list of online coupons available on business" do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
          headers: auth_headers,
          params: {usage_type: "online", lat: 1.0, lng: 1.0},
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.count).to eq(2)
        expect(response_hash.first.keys).to match_array(cupon_index_serializer_keys)
        expect(response_hash.pluck("discount")).to match_array([0, 30])
        expect(response_hash.first["cashback_text"]).to eq("9% de cashback")
        expect(response_hash.first["tags"]).to match_array(["Online"])
      end

      it "return a list of physical coupons" do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
          headers: auth_headers,
          params: {usage_type: "physical", lat: 1.0, lng: 1.0},
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.count).to eq(6)
        expect(response_hash.first.keys).to match_array(cupon_index_serializer_keys)
        expect(response_hash.pluck("discount")).to match_array([30, 30, 30, 30, 30, 30])
        expect(response_hash.first["cashback_text"]).to eq("9% de cashback")
      end

      it "returns organization distance grather than 150km" do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
          headers: auth_headers,
          params: {usage_type: "physical", lat: 1.0, lng: 1.0, page: 1, distance: 150},
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.first.keys).to match_array(cupon_index_serializer_keys)
        expect(response_hash.pluck("discount")).to match_array([30, 30, 30, 30, 30, 30])
      end

      it "returns organization distance less than or equal 100km" do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
          headers: auth_headers,
          params: {usage_type: "physical", lat: 1.0, lng: 1.0, page: 1, distance: 100},
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.first.keys).to match_array(cupon_index_serializer_keys)
        expect(response_hash.pluck("discount")).to match_array([30, 30, 30, 30, 30, 30])
      end

      it "returns organization distance lass than 30" do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
          headers: auth_headers,
          params: {usage_type: "physical", lat: 1.0, lng: 1.0, page: 1, distance: 20},
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.first.keys).to match_array(cupon_index_serializer_keys)
        expect(response_hash.pluck("discount")).to match_array([30, 30, 30, 30, 30, 30])
      end

      context "when coupon used is bigger or equal to quantity" do
        before do
          Promotion.update_all(redeemed_count: 11, quantity: 10)
          Promotion::PropagateChanges.call
        end

        it "return a empty list of online coupons" do
          get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
            headers: auth_headers,
            params: {usage_type: "online"},
            as: :json

          expect(response).to have_http_status(:ok)
          expect(response_hash.count).to eq(0)
        end

        it "return a empty list of physical coupons" do
          get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
            headers: auth_headers,
            params: {usage_type: "physical", lat: 1.0, lng: 1.0},
            as: :json

          expect(response).to have_http_status(:ok)
          expect(response_hash.count).to eq(0)
        end
      end

      context "with regionalized promotion" do
        let!(:promotion) { create(:promotion, :available, :percent_discount, :regionalized) }
        let!(:coupon) { create(:cupon, :online, :percent_discount, promotion:, branch:) }

        it "returns coupons other than regionalized" do
          get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
            headers: auth_headers,
            params: {usage_type: "online", lat: 1.0, lng: 1.0},
            as: :json

          expect(response).to have_http_status(:ok)
          expect(response_hash.pluck("id")).not_to include([coupon.id])
          expect(response_hash.pluck("discount")).to match_array([0, 30])
        end
      end
    end

    context "when promotion has tags" do
      let(:another_business) { create(:business) }
      let(:authorized_user) { create(:authorized_user, business:, tags: ["Tag 2"]) }
      let(:signed_user) { create(:user, business:, authorized_user:) }
      let!(:branch) { create(:branch, :physical, organization:, lat: 25.0, lng: 25.0) }

      let!(:exclusive_promotion_with_matching_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, tags: ["Tag 1", "Tag 2"], business:) }
      let!(:exclusive_coupon_with_matching_tags) { create(:cupon, :cpf, :percent_discount, branch:, promotion: exclusive_promotion_with_matching_tags, business:) }

      let!(:unrelated_exclusive_promotion_with_matching_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, tags: ["Tag 1", "Tag 2"], business: another_business) }
      let!(:unrelated_exclusive_coupon_with_matching_tags) { create(:cupon, :cpf, :percent_discount, branch:, promotion: unrelated_exclusive_promotion_with_matching_tags, business: another_business) }

      let!(:exclusive_promotion_with_unmatching_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, tags: ["Tag 3"], business:) }
      let!(:exclusive_coupon_with_unmatching_tags) { create(:cupon, :cpf, :percent_discount, branch:, promotion: exclusive_promotion_with_unmatching_tags, business:) }

      let!(:alloyal_promotion_with_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, tags: ["Tag 5", "Tag 6"]) }
      let!(:alloyal_coupon_with_tags) { create(:cupon, :cpf, :percent_discount, branch:, promotion: alloyal_promotion_with_tags) }

      let!(:exclusive_promotion_without_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, business:) }
      let!(:exclusive_coupon_without_tags) { create(:cupon, :cpf, :percent_discount, branch:, promotion: exclusive_promotion_without_tags, business:) }

      let!(:alloyal_promotion_without_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:) }
      let!(:alloyal_coupon_without_tags) { create(:cupon, :cpf, :percent_discount, branch:, promotion: alloyal_promotion_without_tags) }

      let(:params) { {usage_type: Enums::Branch::Type::PHYSICAL, lat: 25.0, lng: 25.0} }

      it "renders ok" do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
          headers: auth_headers,
          params:,
          as: :json

        expect(response).to be_ok
        expect(
          [exclusive_coupon_with_matching_tags.id, alloyal_coupon_with_tags.id, alloyal_coupon_without_tags.id, exclusive_coupon_without_tags.id]
        ).to all be_in response_hash.pluck("id")
        expect(response_hash.pluck("id")).not_to include(exclusive_coupon_with_unmatching_tags.id)
        expect(response_hash.pluck("id")).not_to include(unrelated_exclusive_coupon_with_matching_tags.id)
      end
    end

    context "cupons on another business" do
      before do
        business_two = create(:business, :publicar)
        Cupon.update_all(business_id: business_two.id)
      end

      it "return a empty list of online coupons" do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
          headers: auth_headers,
          params: {usage_type: "online"},
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.count).to eq(0)
      end

      it "return a empty list of physical coupons" do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
          headers: auth_headers,
          params: {usage_type: "physical", lat: 1.0, lng: 1.0},
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response_hash.count).to eq(0)
      end
    end

    context "when business is deactivated" do
      let(:business) { create(:business, :lecupon, status: Business::Status::SUSPENDED_BY_OVERDUE) }
      let(:signed_user) { create(:user, business:) }
      let(:auth_headers) { signed_user.create_new_auth_token }
      let(:headers) { auth_headers.merge("Api-Secret" => business.api_secret) }

      it "does not authenticate" do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons",
          headers:,
          params: {usage_type: "online"},
          as: :json

        expect(response).to be_unauthorized
      end
    end
  end

  describe "GET /api/v1/public_integration/organizations/:id/coupons/:id" do
    it "return a coupon_code coupon" do
      expect do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_fixed.id}",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json
      end.to change(Order, :count).by(0)
        .and change(Voucher, :count).by(0)
        .and change(WebhookSender::EventWorker.jobs, :size).by(0)
      expect(response).to have_http_status(:ok)
      expect(response_hash.keys).to match_array(coupon_code_serializer_keys)
      expect(response_hash["discount"]).to eq(30)
      expect(response_hash["working_days"].first.keys).to match_array(working_days_keys)
      expect(response_hash["cashback_text"]).to eq("9% de cashback")
    end

    it "return a default coupon" do
      expect do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_cpf.id}",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json
      end.to change(Order, :count).by(0)
        .and change(Voucher, :count).by(0)
        .and change(WebhookSender::EventWorker.jobs, :size).by(0)
      expect(response).to have_http_status(:ok)
      expect(response_hash.keys).to match_array(coupon_default_serializer_keys)
      expect(response_hash["discount"]).to eq(30)
      expect(response_hash["working_days"].first.keys).to match_array(working_days_keys)
      expect(response_hash["cashback_text"]).to eq("9% de cashback")
    end

    it "return link coupon with code" do
      expect do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_online.id}",
          headers: auth_headers,
          as: :json
      end.to change(Order, :count).by(0)
        .and change(Voucher, :count).by(0)
        .and change(WebhookSender::EventWorker.jobs, :size).by(0)
      expect(response).to have_http_status(:ok)
      expect(response_hash.keys).to match_array(coupon_link_serializer_keys)
      expect(response_hash["discount"]).to eq(30)
      expect(response_hash["working_days"].first.keys).to match_array(working_days_keys)
      expect(response_hash["cashback_text"]).to eq("9% de cashback")
      expect(response_hash["code"]).to eq("PROMO10")
    end

    it "return a qrcode coupon" do
      expect do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_classic.id}",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json
      end.to change(Order, :count).by(0)
        .and change(Voucher, :count).by(0)
        .and change(WebhookSender::EventWorker.jobs, :size).by(0)
      expect(response).to have_http_status(:ok)
      expect(response_hash.keys).to match_array(coupon_qrcode_serializer_keys)
      expect(response_hash["discount"]).to eq(30)
      expect(response_hash["working_days"].first.keys).to match_array(working_days_keys)
      expect(response_hash["cashback_text"]).to eq("9% de cashback")
    end

    context "category subresource" do
      context "when including category" do
        it "must return 'categories' as array of objects with only ids" do
          get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_classic.id}",
            headers: auth_headers,
            params: {lat: 1.0, lng: 1.0},
            as: :json

          expect(response).to have_http_status(:ok)
          expect(response.parsed_body.keys).to match_array(coupon_qrcode_serializer_keys)
          expect(response.parsed_body["categories"].first.keys).to eq(["id"])
        end
      end

      context "when not including category" do
        it "must return 'categories' as array of objects with only ids" do
          get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_classic.id}",
            headers: auth_headers,
            params: {lat: 1.0, lng: 1.0, include: :categories},
            as: :json

          expect(response).to have_http_status(:ok)
          expect(response.parsed_body.keys).to match_array(coupon_qrcode_serializer_keys)
          expect(response.parsed_body["categories"].first.keys).to eq(included_category_keys)
        end
      end
    end

    context "when redeeming dynamic online coupon" do
      let!(:bucket) { create(:voucher_bucket) }
      let!(:promotion) { create(:promotion, :dynamic_coupon_code, :percent_discount, :available, dynamic_voucher: true, voucher_bucket: bucket) }
      let!(:cupon_dynamic_online) { create(:cupon, :dynamic_online, :percent_discount, branch: branch_online, promotion:) }
      let!(:voucher) { create(:voucher, bucket:) }

      it "returns a dynamic online coupon with link serializer containing coupon code" do
        expect do
          get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_dynamic_online.id}",
            headers: auth_headers,
            params: {lat: 1.0, lng: 1.0},
            as: :json
        end.to change(Order, :count).by(1)
          .and change(Voucher, :count).by(1)
          .and change(WebhookSender::EventWorker.jobs, :size).by(1)
        expect(response).to have_http_status(:ok)
        expect(response_hash.keys).to match_array(coupon_link_serializer_keys)
        expect(response_hash["discount"]).to eq(30)
        expect(response_hash["working_days"].first.keys).to match_array(working_days_keys)
        expect(response_hash["code"]).to be_present
        expect(response_hash["cashback_text"]).to eq("9% de cashback")
      end
    end

    context "when redeeming dynamic online coupon" do
      let!(:bucket) { create(:voucher_bucket) }
      let!(:promotion) { create(:promotion, :dynamic_coupon_code, :percent_discount, :available, dynamic_voucher: true, voucher_bucket: bucket) }
      let!(:cupon_dynamic_online) { create(:cupon, :dynamic_online, :percent_discount, branch: branch_online, promotion:) }
      let!(:voucher) { create(:voucher, bucket:) }

      it "returns a dynamic online coupon with link serializer containing coupon code" do
        expect do
          get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_dynamic_online.id}",
            headers: auth_headers,
            params: {lat: 1.0, lng: 1.0},
            as: :json
        end.to change(WebhookSender::EventWorker.jobs, :size).by(1)
        expect(response).to have_http_status(:ok)
        expect(response_hash.keys).to match_array(coupon_link_serializer_keys)
        expect(response_hash["discount"]).to eq 30
        expect(response_hash["working_days"].first.keys).to match_array(working_days_keys)
        expect(response_hash["code"]).to be_present
        expect(response_hash["cashback_text"]).to eq("9% de cashback")
      end
    end

    context "when redeeming dynamic online coupon with a expirated voucher" do
      let!(:bucket) { create(:voucher_bucket) }
      let!(:promotion) { create(:promotion, :dynamic_coupon_code, :available, dynamic_voucher: false, voucher_bucket: bucket) }
      let!(:voucher) { create(:voucher, bucket:, expired_at: 1.day.ago) }
      let!(:cupon_dynamic_online) { create(:cupon, :dynamic_online, branch: branch_online, promotion:) }

      it "does not generate a redeem voucher" do
        expect do
          get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_dynamic_online.id}",
            headers: auth_headers,
            params: {lat: 1.0, lng: 1.0},
            as: :json
        end.to change(Order, :count).by(0)
          .and change(Voucher, :count).by(0)
          .and change(WebhookSender::EventWorker.jobs, :size).by(0)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response_hash["error"]).to include("Voucher não disponível para resgate")
      end
    end

    context "when redeeming dynamic online coupon with available voucher" do
      let!(:bucket) { create(:voucher_bucket) }
      let!(:promotion) { create(:promotion, :dynamic_coupon_code, :available, dynamic_voucher: false, voucher_bucket: bucket) }
      let!(:voucher) { create(:voucher, bucket:) }
      let!(:cupon_dynamic_online) { create(:cupon, :dynamic_online, branch: branch_online, promotion:) }

      it "does not generate a redeem voucher" do
        create(:order, user: signed_user, cupon: cupon_dynamic_online, voucher:)
        promotion.propagate_changes

        expect do
          get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_dynamic_online.id}",
            headers: auth_headers,
            params: {lat: 1.0, lng: 1.0},
            as: :json
        end.to change(Order, :count).by(0)
          .and change(Voucher, :count).by(0)
          .and change(WebhookSender::EventWorker.jobs, :size).by(0)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response_hash["error"]).to include("Voucher não disponível para resgate")
      end
    end

    it "returns a lbc giftcard coupon with coupon code serializer" do
      expect do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_lbc_giftcard.id}",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json
      end.to change(Order, :count).by(0)
        .and change(Voucher, :count).by(0)
        .and change(WebhookSender::EventWorker.jobs, :size).by(0)
      expect(response).to have_http_status(:ok)
      expect(response_hash.keys).to match_array(coupon_code_serializer_keys)
      expect(response_hash["discount"]).to eq(30)
      expect(response_hash["cashback_text"]).to eq("9% de cashback")
    end

    it "returns a voucher coupon with coupon code serializer" do
      expect do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_fixed.id}",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json
      end.to change(Order, :count).by(0)
        .and change(Voucher, :count).by(0)
        .and change(WebhookSender::EventWorker.jobs, :size).by(0)
      expect(response).to have_http_status(:ok)
      expect(response_hash.keys).to match_array(coupon_code_serializer_keys)
      expect(response_hash["discount"]).to eq(30)
      expect(response_hash["cashback_text"]).to eq("9% de cashback")
    end

    context "with unmapped serializer for the coupon type" do
      let!(:promotion) { create(:promotion, :percent_discount) }
      let!(:coupon_different_type) { create(:cupon, :percent_discount, c_type: "DIFFERENT", branch:, promotion:) }

      it "returns the coupon with default serializer" do
        expect do
          get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{coupon_different_type.id}",
            headers: auth_headers,
            params: {lat: 1.0, lng: 1.0},
            as: :json
        end.to change(Order, :count).by(0)
          .and change(Voucher, :count).by(0)
          .and change(WebhookSender::EventWorker.jobs, :size).by(0)
        expect(response).to have_http_status(:ok)
        expect(response_hash.keys).to match_array(coupon_default_serializer_keys)
        expect(response_hash["discount"]).to eq(30)
        expect(response_hash["cashback_text"]).to eq("9% de cashback")
      end
    end

    context "when organization is blocklisted" do
      let!(:org_approval) do
        create(:organization_blocklist,
          business:,
          organization:)
      end

      it "returns error" do
        get "/api/v1/public_integration/organizations/#{organization.id}/coupons/#{cupon_fixed.id}",
          headers: auth_headers,
          params: {lat: 1.0, lng: 1.0},
          as: :json

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
