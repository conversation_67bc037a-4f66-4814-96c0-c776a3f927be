require "rails_helper"

RSpec.describe Api::V2::CategoriesController, type: :request do
  let(:user) { create(:user) }
  let!(:headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret": user.business.api_secret, "App-Version" => "4.0.5", aud: "web"}, user, aud: "web")
  end

  describe "#show" do
    context "when the version is above 4.0.4" do
      let(:serialized_keys) { %w[id title image] }

      it "must return successfully" do
        category = create(:category)

        get("/api/v2/categories/#{category.id}", headers:)

        expect(response).to be_ok
        expect(response_hash.keys).to match_array(serialized_keys)
      end

      it "must paginate the request" do
        categories = create_list(:category, 6)
        params = {page: 1, per_page: 5}

        get("/api/v2/categories/#{categories.first.id}", params:, headers:)

        expect(response).to have_http_status(:bad_request)
        expect(response_hash["error"]).to eq("Parâmetro per_page deve conter um valor entre 30 e 100")
      end

      context "when category is not visible" do
        let(:category_not_visible) { create(:category, visible: false) }

        it "must return not found" do
          get("/api/v2/categories/#{category_not_visible.id}", headers:)

          expect(response).to be_not_found
        end
      end

      context "when is a cashback category and business do not have cashback" do
        let(:cashback_category) { create(:category, title: "Cashback") }

        it "must return not found" do
          user.update(cashback: false)
          get("/api/v2/categories/#{cashback_category.id}", headers:)

          expect(response).to be_not_found
        end
      end

      context "when category is blocked on business" do
        let(:category_blocklisted) { create(:category) }

        it "must return not found" do
          create(:category_blocklist, category: category_blocklisted, business: user.business)

          get("/api/v2/categories/#{category_blocklisted.id}", headers:)
          expect(response).to be_not_found
        end
      end

      context "given a business with only exclusive categories" do
        let(:business) { create(:business, project_config:) }
        let(:project_config) { create(:project_config, only_exclusive_categories: true) }

        context "when category is not exclusive to business" do
          let(:non_exclusive_category) { create(:category, business:) }

          it "must return not found" do
            get("/api/v2/categories/#{non_exclusive_category.id}", headers:)
            expect(response).to be_not_found
          end
        end
      end
    end

    context "when the version is less than or equal to 4.0.5" do
      let!(:headers) do
        Devise::JWT::TestHelpers.auth_headers({"Api-Secret": user.business.api_secret, "App-Version" => "4.0.4", aud: "web"}, user, aud: "web")
      end

      let(:serialized_keys) { %w[id title icon image home_pinned] }

      it "must return successfully" do
        category = create(:category)

        get("/api/v2/categories/#{category.id}", headers:)

        expect(response).to be_ok
        expect(response_hash.keys).to match_array(serialized_keys)
      end
    end
  end

  describe "#index" do
    context "when the version is above 3.7.0" do
      let(:serialized_keys) { %w[id title image] }

      it "filters categories by home pinned" do
        category_home_pinned = create(:category, :home_pinned)
        category_not_home_pinned = create(:category, home_pinned: false)
        params = {home_pinned: true}

        get("/api/v2/categories", params:, headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        expect(response_hash.pluck("id"))
          .to include(category_home_pinned.id)
          .and not_include(category_not_home_pinned.id)
      end

      it "returns only visible categories" do
        category_visible = create(:category, visible: true)
        category_not_visible = create(:category, visible: false)

        get("/api/v2/categories", headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        expect(response_hash.pluck("id"))
          .to include(category_visible.id)
          .and not_include(category_not_visible.id)
      end

      it "returns categories ordered by position" do
        category_position_nil = create(:category, position: nil)
        category_position_1 = create(:category, position: 1)
        category_position_2 = create(:category, position: 2)

        get("/api/v2/categories", headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        expect(response_hash.pluck("id")).to eq([
          category_position_1.id,
          category_position_2.id,
          category_position_nil.id
        ])
      end

      it "does not return blocklisted categories" do
        category_blocklisted = create(:category)
        category_not_blocklisted = create(:category)
        create(:category_blocklist, category: category_blocklisted, business: user.business)

        get("/api/v2/categories", headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        expect(response_hash.pluck("id"))
          .to include(category_not_blocklisted.id)
          .and not_include(category_blocklisted.id)
      end

      it "returns categories not blocklisted and visible, filtered by home pinned and ordered by position" do
        category_1 = create(:category, :home_pinned, position: 1)
        category_2 = create(:category, :home_pinned, position: 2)
        category_3 = create(:category, :home_pinned, position: nil)
        category_not_home_pinned = create(:category, home_pinned: false, position: 3, visible: true)
        category_not_visible = create(:category, home_pinned: false, position: 4, visible: false)
        category_blocklisted = create(:category)
        create(:category_blocklist, category: category_blocklisted, business: user.business)
        params = {home_pinned: true}

        get("/api/v2/categories", params:, headers:)

        expect(response).to have_http_status(:ok)
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
        expect(response_hash.pluck("id"))
          .to eq([category_1.id, category_2.id, category_3.id])
          .and not_include(category_not_home_pinned.id)
          .and not_include(category_not_visible.id)
          .and not_include(category_blocklisted.id)
      end

      context "business with exclusive categories only" do
        let(:business) { create(:business, project_config:) }
        let(:project_config) { create(:project_config, only_exclusive_categories: true) }
        let(:user) { create(:user, business:) }

        it "return only exclusive categoires" do
          category_1 = create(:category)
          category_2 = create(:category, business:)

          get("/api/v2/categories", headers:)

          expect(response).to have_http_status(:ok)
          expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
          expect(response_hash.pluck("id"))
            .to include(category_2.id)
            .and not_include(category_1.id)
        end
      end

      context "when is a cashback category and business do not have cashback" do
        let(:cashback_category) { create(:category, title: "Cashback") }

        it "must not include cashback category" do
          user.update(cashback: false)
          get("/api/v2/categories", headers:)

          expect(response).to be_successful
          expect(response_hash).to be_empty
        end
      end
    end

    context "when the version is less than or equal to 3.7.0" do
      let!(:headers) do
        Devise::JWT::TestHelpers.auth_headers({"Api-Secret": user.business.api_secret, "App-Version" => "3.7.0", aud: "web"}, user, aud: "web")
      end

      let(:serialized_keys) { %w[id title icon image home_pinned] }

      it "must return successfully" do
        create(:category)

        get("/api/v2/categories", headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
      end
    end
  end

  def response_hash
    JSON.parse(response.body)
  end
end
