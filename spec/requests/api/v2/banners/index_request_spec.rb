# frozen_string_literal: true

require "devise/jwt/test_helpers"
require "rails_helper"

RSpec.describe Api::V2::BannersController, type: :request do
  let!(:business) { create(:business, banner: true) }
  let(:serializer_keys) { %w[id image kind open_new_tab url position provider external navigation_option] }
  let!(:authorized_user) { create(:authorized_user, :with_user, business:) }
  let(:user) { authorized_user.user }
  let!(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#index" do
    context "when business has deactivated banners" do
      it "must return empty array when version is less than or equal 3.4.7" do
        headers = Devise::JWT::TestHelpers.auth_headers({
          "Api-Secret" => business.api_secret,
          "App-Version" => "3.4.7",
          "aud" => "api"
        }, user)
        business.update(banner: false, giftcard: true, prize_draw: true, telemedicine: true)
        create_banner_with_all_providers

        get "/api/v2/banners", headers:, as: :json

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body).to eq([])
      end

      it "must return forbidden when version is greater than 3.4.7" do
        headers = Devise::JWT::TestHelpers.auth_headers({
          "Api-Secret" => business.api_secret,
          "App-Version" => "3.5.1",
          "aud" => "api"
        }, user)
        business.update(banner: false, giftcard: true, prize_draw: true, telemedicine: true)
        create_banner_with_all_providers

        get "/api/v2/banners", headers:, as: :json

        expect(response).to have_http_status(:forbidden)
        expect(json_parse_response_body["error"]).to eq("Acesso negado")
      end
    end

    context "when all providers are enabled" do
      let(:image_serialized_keys) { %w[large small] }
      let(:open_new_tab_serialized_keys) { %w[mobile web] }

      it "expected return all" do
        business.update(giftcard: true, prize_draw: true, telemedicine: true)
        authorized_user.update(telemedicine: true)
        create_banner_with_all_providers
        get "/api/v2/banners", headers:, as: :json

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.map(&:keys)).to all(match_array(serializer_keys))
        expect(json_parse_response_body.first["image"].keys).to match_array(image_serialized_keys)
        expect(json_parse_response_body.first["open_new_tab"].keys).to match_array(open_new_tab_serialized_keys)
        expect(json_parse_response_body.count).to eq(7)
        expect(json_parse_response_body.pluck("provider").sort).to match_array(Navigation.providers.values.sort)
        banner_external_link = Navigation.kind_banner.external_link_provider.first
        returned_banner_external_link = json_parse_response_body.find { _1["id"] == banner_external_link.id }
        expect(returned_banner_external_link["url"]).to eq(
          "#{Rails.application.routes.default_url_options[:host]}/api/v2/menu_items/#{banner_external_link.id}/redirect?mref=#{user.id}"
        )
      end

      it "returns banners that are nearby to user" do
        business.update(giftcard: true, prize_draw: true, telemedicine: true)
        authorized_user.update(telemedicine: true)
        create_banner_with_all_providers

        get "/api/v2/banners", params: {lat: -19.123, lng: -43.456}, headers:, as: :json

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.map(&:keys)).to all(match_array(serializer_keys))
        expect(json_parse_response_body.count).to eq(8)
      end
    end

    context "when prize draw is disabled" do
      it "expected not returns prize draw banners" do
        business.update(giftcard: true, prize_draw: true, telemedicine: true)
        authorized_user.update(telemedicine: true)
        create_banner_with_all_providers
        business.update(prize_draw: false)

        get "/api/v2/banners", headers:, as: :json

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.count).to eq(6)
        expect(json_parse_response_body.pluck("provider")).not_to include(Navigation.providers[:prize_draw])
      end
    end

    context "when telemedicine is disabled" do
      it "expected not returns telemedicine banners" do
        business.update(giftcard: true, prize_draw: true, telemedicine: true)
        user.authorized_user.reload.update!(telemedicine: false)
        create_banner_with_all_providers

        get "/api/v2/banners", headers:, as: :json

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.count).to eq(6)
        expect(json_parse_response_body.pluck("provider")).not_to include(Navigation.providers[:telemedicine])
      end

      it "expected not returns telemedicine banners" do
        business.update(giftcard: true, prize_draw: true, telemedicine: true)
        authorized_user.update(telemedicine: true)
        create_banner_with_all_providers
        business.update(telemedicine: false)

        get "/api/v2/banners", headers:, as: :json

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.count).to eq(6)
        expect(json_parse_response_body.pluck("provider")).not_to include(Navigation.providers[:telemedicine])
      end
    end

    context "when gift card is disabled" do
      it "expected not returns gift card banners" do
        business.update(giftcard: true, prize_draw: true, telemedicine: true)
        authorized_user.update(telemedicine: true)
        create_banner_with_all_providers
        business.update(giftcard: false)

        get "/api/v2/banners", headers:, as: :json

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.count).to eq(6)
        expect(json_parse_response_body.pluck("provider")).not_to include(Navigation.providers[:gift_card])
      end
    end

    let(:create_banner_with_all_providers) do
      category = build_stubbed(:category)
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = create(:promotion, organization:)
      create(:cupon, branch:, promotion:)

      create(:banner, :category, business:, metadata: {category_id: category.id})
      create(:banner, :external_link, business:)
      create(:banner, :external_app, business:)
      create(:banner, :gift_card, business:, metadata: {organization_id: organization.id})
      create(:banner, :prize_draw, business:)
      create(:banner,
        :organization_coupon,
        business:,
        metadata: {
          organization_id: organization.id,
          branch_id: branch.id,
          promotion_id: promotion.id
        })
      create(:banner, :telemedicine, business:)

      banner_with_geo = create(:banner, :category, business:, metadata: {category_id: category.id})
      banner_with_geo.create_geolocation!(
        address: FFaker::AddressBR.full_address,
        lat: -19.123,
        long: -43.456,
        range_type: NavigationGeolocation::RangeType::KM,
        range_value: 10
      )

      banner_with_geo_far = create(:banner, :category, business:, metadata: {category_id: category.id})
      banner_with_geo_far.create_geolocation!(
        address: FFaker::AddressBR.full_address,
        lat: -20.654,
        long: -44.789,
        range_type: NavigationGeolocation::RangeType::KM,
        range_value: 10
      )
    end

    context "when without authorization headers" do
      it "must render error" do
        get "/api/v2/banners", headers: {"Web-Domain": business.project_config.web_domain}

        expect(json_parse_response_body["error"]).to be_present
      end
    end
  end
end
