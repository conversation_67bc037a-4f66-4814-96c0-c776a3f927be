# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::Checkout::PlansController, type: :request do
  let!(:business) { create(:business) }
  let!(:user) { create(:user, business:) }
  let!(:subscription_config) { create(:subscription_config, business:) }
  let!(:plan) { create(:plan, subscription_config:, title: "Assinatura - Básico", price: 50.00, points: 10000, recurrence: "monthly", benefit_description: "+10000 pontos todo mês") }
  let!(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => user.business.api_secret}, user) }
  let!(:credit_card) { create(:credit_card, user: user, main: true, last4: "4321") }
  let(:params) { {credit_card_id: credit_card.id} }

  describe "GET #show" do
    context "when the plan exists" do
      it "returns http success" do
        get("/api/v2/checkout/plans/#{plan.id}", headers: headers, params:)
        expect(response).to have_http_status(:success)
      end

      it "returns the correct plan data" do
        expected_response = {
          resume: {
            logo_project: nil,
            name: "Assinatura - Básico",
            price: "R$ 50,00",
            price_label: "mês"
          },
          payment: {
            recurrence: "Mensal",
            type: "Cartão de crédito",
            associated_card: "#{CreditCardValidations::Detector.brand_name(credit_card.brand.to_sym)} ****4321"
          },
          additional: {
            benefits: [
              "+10000 pontos todo mês"
            ],
            message_info: "Os pontos serão adicionados à sua conta automaticamente dentro de 1 dia."
          }
        }.deep_stringify_keys

        get("/api/v2/checkout/plans/#{plan.id}", headers: headers, params:)

        json_response = JSON.parse(response.body)
        expect(json_response["resume"]["logo_project"]).to eq "/uploads/project_config/svg_logo/#{business.project_config.id}/samplefile.png"
        expect(json_response["resume"]["name"]).to eq(expected_response["resume"]["name"])
        expect(json_response["resume"]["price"]).to eq(expected_response["resume"]["price"])
        expect(json_response["resume"]["price_label"]).to eq(expected_response["resume"]["price_label"])
        expect(json_response["payment"]["recurrence"]).to eq(expected_response["payment"]["recurrence"])
        expect(json_response["payment"]["type"]).to eq(expected_response["payment"]["type"])
        expect(json_response["payment"]["associated_card"]).to eq(expected_response["payment"]["associated_card"])
        expect(json_response["additional"]["benefits"][0]).to eq(expected_response["additional"]["benefits"][0])
        expect(json_response["additional"]["message_info"]).to eq(expected_response["additional"]["message_info"])
      end
    end

    context "when the plan does not exist" do
      it "returns http not found" do
        get("/api/v2/checkout/plans/9999", headers: headers, params:)
        expect(response).to have_http_status(:not_found)
      end
    end

    context "when credit card is from another user" do
      let(:another_user) { create(:user, business:) }
      let(:another_credit_card) { create(:credit_card, user: another_user, main: true, last4: "1234") }
      let(:params) { {credit_card_id: another_credit_card.id} }

      it "returns http not found" do
        get("/api/v2/checkout/plans/#{plan.id}", headers:, params:)

        expect(response).to be_not_found
      end
    end

    context "when the plan belongs to another subscription_config" do
      let(:other_subscription_config) { create(:subscription_config, business: business) }
      let(:other_plan) { create(:plan, subscription_config: other_subscription_config) }

      it "returns http not found" do
        get("/api/v2/checkout/plans/#{other_plan.id}", headers: headers)
        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
