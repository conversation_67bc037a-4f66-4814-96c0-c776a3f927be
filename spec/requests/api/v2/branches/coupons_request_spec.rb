require "rails_helper"

RSpec.describe Api::V2::Branches::CouponsController, type: :request do
  let!(:business) { create(:business, :with_cashback) }
  let(:another_business) { create(:business, :with_cashback) }
  let!(:user) { create(:user, business:) }
  let!(:headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "App-Version" => "4.0.0", "aud" => "api"}, user)
  end

  let(:coupon_serializer_without_discount_keys) do
    %w[
      id
      description
      endeded_at
      redeem_type
      started_at
      title
      branch
      fund
      organization
      usage_instruction
    ]
  end
  let(:coupon_serializer_with_discount_keys) do
    %w[
      id
      description
      endeded_at
      redeem_type
      started_at
      title
      branch
      fund
      organization
      discount
      usage_instruction
    ]
  end
  let(:branch_serializer_keys) do
    %w[id]
  end
  let(:discount_serializer_keys) do
    %w[
      type
      value
    ]
  end
  let(:organization_serializer_keys) do
    %w[id]
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "GET /api/v2/branches/:branch_id/coupons" do
    let(:category) { create(:category) }
    let(:organization) { create(:organization, active: true, promotion_redeemable: true, categories: [category]) }

    context "given inavalid branch_type params" do
      let(:branch_online) { create(:branch, :online, organization:, active: true) }

      it "return error when branch_type is invalid" do
        get "/api/v2/branches/#{branch_online.id}/coupons", headers:, params: {branch_type: "10"}

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_parse_response_body["errors"]["branch_type"].first).to include("Ops! Parâmetro inválido. Os valores aceitos são: 'physical, online'")
      end

      it "return error when branch_type is empty" do
        get("/api/v2/branches/#{branch_online.id}/coupons", headers:)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_parse_response_body["errors"]["branch_type"].first).to include("Ops! Parâmetro inválido. Os valores aceitos são: 'physical, online'")
      end
    end

    context "given a online branch" do
      let(:branch_online) { create(:branch, :online, organization:, active: true) }

      context "when redeem_type is discount" do
        it "is expected to return all coupons with discount column" do
          promotion_discount = create(:promotion, :online, :available, redeem_type: Enums::PromotionRedeemType::DISCOUNT)
          create(:cupon, :percent_discount, branch: branch_online, promotion: promotion_discount)

          get "/api/v2/branches/#{branch_online.id}/coupons",
            headers:,
            params: {branch_type: "online"}

          expect(response).to have_http_status(:success)
          expect(json_parse_response_body.count).to eq(1)
          expect(json_parse_response_body.first.keys).to match_array(coupon_serializer_with_discount_keys)
          expect(json_parse_response_body.first["branch"].keys).to match_array(branch_serializer_keys)
          expect(json_parse_response_body.pluck("fund")).to all eq "value" => 9.0, "type" => "percent"
          expect(json_parse_response_body.first["discount"].keys).to match_array(discount_serializer_keys)
          expect(json_parse_response_body.pluck("discount")).to all eq "value" => 30.0, "type" => "percent"
          expect(json_parse_response_body.first["organization"].keys).to match_array(organization_serializer_keys)
        end

        context "when currency is points and cashback type is percent" do
          let!(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.0125) }

          it "renders ok" do
            promotion = create(:promotion, :online, :available, cashback_type: Cashback::Type::PERCENT, cashback_value: 10)
            create(:cupon, branch: branch_online, promotion:)

            get "/api/v2/branches/#{branch_online.id}/coupons",
              headers:,
              params: {branch_type: "online"}

            expect(response).to be_ok
            expect(json_parse_response_body.count).to eq(1)
            expect(json_parse_response_body.pluck("fund")).to all eq "value" => 8.0, "type" => "parity"
          end
        end

        context "when currency is points and cashback type is fixed" do
          let!(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.0125) }

          it "renders ok" do
            promotion = create(:promotion, :online, :available, cashback_type: Cashback::Type::FIXED, cashback_value: 10)
            create(:cupon, branch: branch_online, promotion:)

            get "/api/v2/branches/#{branch_online.id}/coupons",
              headers:,
              params: {branch_type: "online"}

            expect(response).to be_ok
            expect(json_parse_response_body.count).to eq(1)
            expect(json_parse_response_body.pluck("fund")).to all eq "value" => 800, "type" => "fixed"
          end
        end

        context "when serializing for app version up until 3.3.999" do
          let(:headers) do
            Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "App-Version" => "3.3.999"}, user)
          end
          let(:old_coupon_serializer_keys) do
            %w[id description endeded_at redeem_type started_at title branch cashback organization discount]
          end

          it "must render discount as value instead of object" do
            promotion_discount = create(:promotion, :online, :available, redeem_type: Enums::PromotionRedeemType::DISCOUNT)
            create(:cupon, :percent_discount, branch: branch_online, promotion: promotion_discount)

            get "/api/v2/branches/#{branch_online.id}/coupons",
              headers:,
              params: {branch_type: "online"}

            expect(response).to have_http_status(:success)
            expect(json_parse_response_body.count).to eq(1)
            expect(json_parse_response_body.map(&:keys)).to all(match_array(old_coupon_serializer_keys))
            expect(json_parse_response_body.first["branch"].keys).to match_array(branch_serializer_keys)
            expect(json_parse_response_body.pluck("cashback")).to all eq "value" => 9.0, "type" => "percent"
            expect(json_parse_response_body.first["discount"]).to eq(30)
            expect(json_parse_response_body.first["organization"].keys).to match_array(organization_serializer_keys)
          end
        end
      end

      context "when serializing for app version up until 3.999.999" do
        let(:headers) do
          Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "App-Version" => "3.999.999"}, user)
        end
        let(:old_coupon_serializer_keys) do
          %w[id description endeded_at redeem_type started_at title branch cashback organization discount]
        end

        it "must render discount as value instead of object" do
          promotion_discount = create(:promotion, :online, :available, redeem_type: Enums::PromotionRedeemType::DISCOUNT)
          create(:cupon, :percent_discount, branch: branch_online, promotion: promotion_discount)

          get "/api/v2/branches/#{branch_online.id}/coupons",
            headers: headers,
            params: {branch_type: "online"}

          expect(response).to have_http_status(:success)
          expect(json_parse_response_body.count).to eq(1)
          expect(json_parse_response_body.map(&:keys)).to all(match_array(old_coupon_serializer_keys))
          expect(json_parse_response_body.first["branch"].keys).to match_array(branch_serializer_keys)
          expect(json_parse_response_body.pluck("cashback")).to all eq "value" => 9.0, "type" => "percent"
          expect(json_parse_response_body.first["discount"]).to eq "type" => "percent", "value" => 30.0
          expect(json_parse_response_body.first["organization"].keys).to match_array(organization_serializer_keys)
        end
      end

      context "when redeem_type is benefit" do
        it "is expected to return all coupons" do
          promotion_benefit = create(:promotion, :online, :available, redeem_type: Enums::PromotionRedeemType::BENEFIT)
          create(:cupon, branch: branch_online, promotion: promotion_benefit)

          get "/api/v2/branches/#{branch_online.id}/coupons",
            headers:,
            params: {branch_type: "online"}

          expect(response).to have_http_status(:success)
          expect(json_parse_response_body.count).to eq(1)
          expect(json_parse_response_body.first.keys).to match_array(coupon_serializer_without_discount_keys)
          expect(json_parse_response_body.first["branch"].keys).to match_array(branch_serializer_keys)
          expect(json_parse_response_body.pluck("fund")).to all eq "value" => 9.0, "type" => "percent"
          expect(json_parse_response_body.first["discount"]).to eq(nil)
          expect(json_parse_response_body.first["organization"].keys).to match_array(organization_serializer_keys)
        end
      end

      context "with active and deactive coupons" do
        it "is expected to return active coupons from this branch" do
          create(:cupon, branch: branch_online, active: true)
          create(:cupon, :online, branch: branch_online, active: false)

          get "/api/v2/branches/#{branch_online.id}/coupons",
            headers:,
            params: {branch_type: "online"}

          expect(json_parse_response_body.count).to eq(1)
        end
      end

      context "with redeemable and unredeemable" do
        it "is expected to return redeemables coupons from this branch" do
          create(:cupon, branch: branch_online, active: true, infinity: true)
          create(:cupon, :online, branch: branch_online, active: true, quantity: 2, redeemed_count: 1)

          get "/api/v2/branches/#{branch_online.id}/coupons",
            headers:,
            params: {branch_type: "online"}

          expect(json_parse_response_body.count).to eq(2)
        end
      end

      context "given a organization with coupons" do
        context "with not exclusive and exclusive coupon" do
          it "is expected to return all coupons from this branch" do
            create(:cupon, :online, branch: branch_online, business_id: business.id)
            create(:cupon, :online, branch: branch_online, business_id: business.id)

            get "/api/v2/branches/#{branch_online.id}/coupons",
              headers:,
              params: {branch_type: "online"}

            expect(json_parse_response_body.count).to eq(2)
          end
        end

        context "with not exclusive and exclusive coupon to another business" do
          it "is expected to return only coupons from this branch" do
            another_business = create(:business)
            coupon_another_business = create(:cupon, :online, branch: branch_online, business_id: another_business.id)
            coupon_another_business.promotion.update!(business: another_business)
            coupon_same_business = create(:cupon, :online, branch: branch_online, business_id: business.id)
            coupon_same_business.promotion.update!(business:)

            get "/api/v2/branches/#{branch_online.id}/coupons",
              headers:,
              params: {branch_type: "online"}

            expect(json_parse_response_body.count).to eq(1)
          end
        end
      end

      context "given a promotion with tags" do
        let!(:exclusive_promotion_with_matching_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, tags: ["Tag 1", "Tag 2"], business:) }
        let!(:exclusive_coupon_with_matching_tags) { create(:cupon, :online, branch: branch_online, promotion: exclusive_promotion_with_matching_tags, business:) }

        let!(:unrelated_exclusive_promotion_with_matching_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, tags: ["Tag 1", "Tag 2"], business: another_business) }
        let!(:unrelated_exclusive_coupon_with_matching_tags) { create(:cupon, :cpf, :percent_discount, branch: branch_online, promotion: unrelated_exclusive_promotion_with_matching_tags, business: another_business) }

        let!(:exclusive_promotion_with_unmatching_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, tags: ["Tag 3"], business:) }
        let!(:exclusive_coupon_with_unmatching_tags) { create(:cupon, :online, branch: branch_online, promotion: exclusive_promotion_with_unmatching_tags, business:) }

        let!(:alloyal_promotion_with_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, tags: ["Tag 6", "Tag 5"]) }
        let!(:alloyal_coupon_with_tags) { create(:cupon, :online, branch: branch_online, promotion: alloyal_promotion_with_tags) }

        let!(:exclusive_promotion_without_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:, business:) }
        let!(:exclusive_coupon_without_tags) { create(:cupon, :online, branch: branch_online, promotion: exclusive_promotion_without_tags, business:) }

        let!(:alloyal_promotion_without_tags) { create(:promotion, :available, :cpf, :percent_discount, organization:) }
        let!(:alloyal_coupon_without_tags) { create(:cupon, :online, branch: branch_online, promotion: alloyal_promotion_without_tags) }

        it "is expected to return all coupons from promotions with tags matching current_user tags" do
          user.authorized_user.update(tags: ["tag 2"])

          get "/api/v2/branches/#{branch_online.id}/coupons",
            headers:,
            params: {branch_type: "online"}

          expect(response).to have_http_status(:success)
          expect(json_parse_response_body.pluck("id")).to match_array [exclusive_coupon_with_matching_tags.id, alloyal_coupon_with_tags.id, alloyal_coupon_without_tags.id, exclusive_coupon_without_tags.id]
          expect(json_parse_response_body.pluck("id")).not_to include(exclusive_coupon_with_unmatching_tags.id)
          expect(json_parse_response_body.pluck("id")).not_to include(unrelated_exclusive_coupon_with_matching_tags.id)
        end
      end
    end

    context "given a physical branch" do
      let(:branch_online) { create(:branch, :online, organization:, active: true) }
      let(:branch_physical) { create(:branch, :physical, organization:, lat: 1.0, lng: 1.0, active: true) }
      let(:promotion) { create(:promotion, :available) }

      it "is expected to return only coupons from banch physical" do
        create(:cupon, branch: branch_physical, promotion:)
        create(:cupon, branch: branch_online, promotion:)

        get "/api/v2/branches/#{branch_physical.id}/coupons",
          headers:,
          params: {branch_type: "physical", distance: {km_range: 30, lat: 1.0, lng: 1.0}}

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.count).to eq(1)
      end

      it "returns empty in page 2" do
        create(:cupon, branch: branch_physical, promotion:)

        get "/api/v2/branches/#{branch_physical.id}/coupons",
          headers:,
          params: {
            branch_type: "physical",
            distance: {km_range: 30, lat: 1.0, lng: 1.0},
            page: 2
          }

        expect(response).to have_http_status(:ok)
        expect(json_parse_response_body.count).to eq(0)
      end
    end

    context "when is embedded project" do
      let(:business) { create(:business, :with_cashback, embedded: true) }
      let(:branch_physical) { create(:branch, :physical, organization:, lat: 1.0, lng: 1.0, active: true) }
      let(:promotion) { create(:promotion, :available) }

      it "must return redeem_type none" do
        create(:cupon, branch: branch_physical, promotion:)

        get "/api/v2/branches/#{branch_physical.id}/coupons",
          headers:,
          params: {branch_type: "physical", distance: {km_range: 30, lat: 1.0, lng: 1.0}}

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.first["redeem_type"]).to eq("none")
      end
    end

    context "not_found response" do
      let(:branch_physical) { create(:branch, :physical, organization:, lat: 1.0, lng: 1.0, active: true) }

      context "given a request greather than 100 km away from the physical branch" do
        specify do
          promotion = create(:promotion, :available)
          create(:cupon, branch: branch_physical, promotion:)

          get "/api/v2/branches/#{branch_physical.id}/coupons",
            headers:,
            params: {
              branch_type: "physical",
              distance: {km_range: 100, lat: 1.68, lng: 1.68}
            }

          expect(response).to have_http_status(:not_found)
        end
      end

      context "given a blocklisted organization" do
        specify do
          branch_online = create(:branch, :online, organization:, active: true)
          create(:organization_blocklist, business:, organization:)

          get "/api/v2/branches/#{branch_online.id}/coupons",
            headers:,
            params: {branch_type: "online"}

          expect(response).to have_http_status(:not_found)
        end
      end

      context "given a organization with blocklisted category" do
        specify do
          branch_online = create(:branch, :online, organization:, active: true)
          create(:category_blocklist, business:, category:)

          get "/api/v2/branches/#{branch_online.id}/coupons",
            headers:,
            params: {branch_type: "online"}

          expect(response).to have_http_status(:not_found)
        end
      end
    end
  end
end
