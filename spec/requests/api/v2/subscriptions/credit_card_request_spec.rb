# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::Subscriptions::CreditCardController, type: :request do
  describe "PATCH /api/v2/subscriptions/:id/credit_card" do
    let(:user) { create(:user) }
    let!(:credit_card_main) { create(:credit_card, brand: :visa, user:, main: true) }
    let!(:credit_card_not_main) { create(:credit_card, brand: :mastercard, user:, main: false) }
    let!(:subscription) { create(:subscription, user:, credit_card: credit_card_main, active: true) }

    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => user.business.api_secret}, user) }
    let(:params) { {credit_card_id: credit_card_not_main.id} }

    it "renders ok" do
      expect { patch("/api/v2/subscriptions/#{subscription.id}/credit_card", headers:, params:) }
        .to change { subscription.reload.credit_card_id }.to(credit_card_not_main.id)
        .and change { credit_card_not_main.reload.main }.to(true)
        .and change { credit_card_main.reload.main }.to(false)

      expect(response).to be_ok
    end

    context "with invalid subscription id" do
      let!(:unrelated_subscription) { create(:subscription, user: create(:user)) }

      it "renders error" do
        expect { patch("/api/v2/subscriptions/#{unrelated_subscription.id}/credit_card", headers:, params:) }
          .to not_change { subscription.reload.credit_card }
          .and not_change { credit_card_not_main.reload.main }
          .and not_change { credit_card_main.reload.main }

        expect(response).to be_not_found
      end
    end

    context "with inactive subscription" do
      let!(:subscription) { create(:subscription, user:, credit_card: credit_card_main, active: false) }

      it "renders error" do
        expect { patch("/api/v2/subscriptions/#{subscription.id}/credit_card", headers:, params:) }
          .to not_change { subscription.reload.credit_card }
          .and not_change { credit_card_not_main.reload.main }
          .and not_change { credit_card_main.reload.main }

        expect(response).to be_not_found
      end
    end

    context "with invalid credit card token" do
      let!(:unrelated_credit_card) { create(:credit_card, brand: :visa, user: create(:user), main: true) }
      let(:params) { {credit_card_id: unrelated_credit_card.id} }

      it "renders error" do
        expect { patch("/api/v2/subscriptions/#{subscription.id}/credit_card", headers:, params:) }
          .to not_change { subscription.reload.credit_card }
          .and not_change { credit_card_not_main.reload.main }
          .and not_change { credit_card_main.reload.main }

        expect(response).to be_not_found
      end
    end
  end
end
