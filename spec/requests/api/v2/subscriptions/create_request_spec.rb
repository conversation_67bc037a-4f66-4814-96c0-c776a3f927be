# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::SubscriptionsController, :vcr, type: :request do
  describe "POST /api/v2/subscriptions" do
    let(:business) do
      create(
        :business,
        global_pay_external_id: "4195319",
        fair_value: 0.0125,
        currency: :points,
        payment_soft_description: "BUSINESS"
      )
    end
    let(:user) do
      create(
        :user,
        business:,
        name: "ADRIANO PESSOA SOUZA",
        cpf: "***********",
        email: "<EMAIL>",
        cellphone: "***********"
      )
    end
    let(:subscription_config) { create(:subscription_config, business:) }
    let(:plan) { create(:plan, subscription_config:, price: 50.21, points: 1000) }
    let(:credit_card) { create(:credit_card, user:) }
    let(:user_params) do
      {
        name: user.name,
        document: user.cpf,
        email: user.email,
        phone: user.cellphone
      }
    end
    let(:address_params) do
      {
        postal_code: "45810000",
        street: "<PERSON><PERSON>",
        number: "55",
        complement: "APT 104 BL 02",
        neighborhood: "Trevo",
        city: "Belo Horizonte",
        state: "MG"
      }
    end
    let(:card_params) do
      {
        security_code: "321",
        exp_month: 12,
        exp_year: 25,
        holder: "ADRIANOP SOUZA",
        number: "****************"
      }
    end
    let(:anti_fraud_params) do
      {
        session_id: "fc10b881-d9a0-4ab1-a6fd-a102db188f49"
      }
    end
    let(:params) do
      {
        plan_id: plan.id,
        payment_details: {
          user: user_params,
          address: address_params,
          card: card_params,
          anti_fraud: anti_fraud_params
        }
      }
    end
    let(:idempotency_key) { SecureRandom.uuid }
    let(:headers) do
      Devise::JWT::TestHelpers.auth_headers(
        {"Api-Secret" => business.api_secret, "Idempotency-Key" => idempotency_key},
        user
      )
    end
    let(:remote_ip) { "*************" }

    it "renders created" do
      expect { post("/api/v2/subscriptions", headers:, params:, env: {REMOTE_ADDR: remote_ip}) }
        .to change(Payment.captured.where(value: 50.21).where.not(split_data: nil).where(soft_description: "BUSINESS*ASSINATURA"), :count).by(1)
        .and change(user.subscriptions.active.where(plan:, points: 1000), :count).by(1)
        .and change(SubscriptionInstallment.where(price: 50.21, status: :paid), :count).by(1)
        .and change(user.funds.points.approved.where.not(subscription: nil).where(amount: 1000), :count).by(1)

      expect(response).to be_created
      expect(response.parsed_body["message"]).to eq "<p>Parabéns! Sua assinatura está ativa e você já pode aproveitar todos os benefícios do seu plano.</p><br> <p>Seus pontos serão creditados na sua conta automaticamente em até 7 dias.</p>"

      alloyal_seller_id = "4195317"
      business_seler_id = "4195319"
      expect(Payment.order(:created_at).last.split_data).to eq([
        {
          "items" => [
            {
              "code" => "subscription",
              "amount" => "1.81",
              "description" => "subscription"
            }
          ],
          "amount" => "1.81",
          "sellerId" => alloyal_seller_id
        },
        {
          "items" => [
            {
              "code" => "subscription",
              "amount" => "48.4",
              "description" => "subscription"
            }
          ],
          "amount" => "48.4",
          "sellerId" => business_seler_id
        }
      ])
    end

    context "when business does not have payment split" do
      let(:business) do
        create(
          :business,
          global_pay_external_id: "4195319",
          fair_value: 0.0125,
          currency: :points,
          payment_soft_description: "BUSINESS",
          payment_split: false
        )
      end

      it "renders created" do
        expect { post("/api/v2/subscriptions", headers:, params:, env: {REMOTE_ADDR: remote_ip}) }
          .to change(Payment.captured.where(value: 50.21).where.not(split_data: nil).where(soft_description: "BUSINESS*ASSINATURA"), :count).by(1)
          .and change(user.subscriptions.active.where(plan:, points: 1000), :count).by(1)
          .and change(SubscriptionInstallment.where(price: 50.21, status: :paid), :count).by(1)
          .and change(user.funds.points.approved.where.not(subscription: nil).where(amount: 1000), :count).by(1)

        expect(response).to be_created
        expect(response.parsed_body["message"]).to eq "<p>Parabéns! Sua assinatura está ativa e você já pode aproveitar todos os benefícios do seu plano.</p><br> <p>Seus pontos serão creditados na sua conta automaticamente em até 7 dias.</p>"

        alloyal_seller_id = "4195317"
        expect(Payment.order(:created_at).last.split_data).to eq([
          {
            "items" => [
              {
                "code" => "subscription",
                "amount" => "50.21",
                "description" => "subscription"
              }
            ],
            "amount" => "50.21",
            "sellerId" => alloyal_seller_id
          }
        ])
      end
    end

    context "when user has active subscription" do
      let(:lower_plan) { create(:plan, subscription_config:, price: 19.99, points: 200) }
      let(:subscription) { create(:subscription, subscription_config:, user:, plan: lower_plan, active: true) }

      it "renders created" do
        expect { post("/api/v2/subscriptions", headers:, params:, env: {REMOTE_ADDR: remote_ip}) }
          .to change(Payment.captured.where(value: 50.21).where.not(split_data: nil).where(soft_description: "BUSINESS*ASSINATURA"), :count).by(1)
          .and change(user.subscriptions.active.where(plan:, points: 1000), :count).by(1)
          .and change(SubscriptionInstallment.where(price: 50.21, status: :paid), :count).by(1)
          .and change(user.funds.points.approved.where.not(subscription: nil).where(amount: 1000), :count).by(1)
          .and change { subscription.reload.active }.to(false)

        expect(response).to be_created
      end

      context "and payment fails" do
        let(:card_params) do
          {security_code: "321", exp_month: 9, exp_year: 24, holder: "ADRIANO P SOUZA", number: "****************"}
        end

        it "renders unprocessable" do
          expect { post("/api/v2/subscriptions", headers:, params:, env: {REMOTE_ADDR: remote_ip}) }
            .to change(user.subscriptions.where(active: false), :count)
            .and not_change(SubscriptionInstallment, :count)
            .and not_change(Fund, :count)
            .and not_change { subscription.reload.active }

          expect(response).to be_unprocessable
        end
      end
    end

    context "when business is sub business" do
      let(:sub_business) { create(:business, main_business: business) }
      let(:user) do
        create(
          :user,
          business: sub_business,
          name: "ADRIANO PESSOA SOUZA",
          cpf: "***********",
          email: "<EMAIL>",
          cellphone: "***********"
        )
      end

      it "renders created" do
        expect { post("/api/v2/subscriptions", headers:, params:, env: {REMOTE_ADDR: remote_ip}) }
          .to change(Payment.captured.where(value: 50.21).where.not(split_data: nil).where(soft_description: "BUSINESS*ASSINATURA"), :count).by(1)
          .and change(Subscription.where(points: 1000), :count).by(1)
          .and change(user.funds.points.approved.where.not(subscription: nil).where(amount: 1000), :count).by(1)

        expect(response).to be_created
        expect(response.parsed_body["message"]).to eq "<p>Parabéns! Sua assinatura está ativa e você já pode aproveitar todos os benefícios do seu plano.</p><br> <p>Seus pontos serão creditados na sua conta automaticamente em até 7 dias.</p>"

        alloyal_seller_id = "4195317"
        business_seler_id = "4195319"
        expect(Payment.order(:created_at).last.split_data).to eq([
          {
            "items" => [
              {
                "code" => "subscription",
                "amount" => "1.81",
                "description" => "subscription"
              }
            ],
            "amount" => "1.81",
            "sellerId" => alloyal_seller_id
          },
          {
            "items" => [
              {
                "code" => "subscription",
                "amount" => "48.4",
                "description" => "subscription"
              }
            ],
            "amount" => "48.4",
            "sellerId" => business_seler_id
          }
        ])
      end
    end

    shared_examples "payment not processed" do
      it "renders error" do
        expect { post("/api/v2/subscriptions", headers:, params:, env: {REMOTE_ADDR: remote_ip}) }
          .to not_change(Payment, :count)
          .and not_change(Subscription, :count)
          .and not_change(Fund, :count)

        expect(response).to have_http_status(expected_status)
        expect(response.parsed_body["error"]).to eq expected_error_message
      end
    end

    context "with invalid user params" do
      let(:user_params) { {} }
      let(:expected_status) { :unprocessable_entity }
      let(:expected_error_message) { "Nome completo não pode ficar em branco, CPF não pode ficar em branco, E-mail não pode ficar em branco, Telefone não pode ficar em branco" }

      it_behaves_like "payment not processed"
    end

    context "with invalid address params" do
      let(:address_params) { {} }
      let(:expected_status) { :unprocessable_entity }
      let(:expected_error_message) { "CEP não pode ficar em branco, Endereço não pode ficar em branco, Número não pode ficar em branco, Bairro não pode ficar em branco, Cidade não pode ficar em branco, Estado não pode ficar em branco" }

      it_behaves_like "payment not processed"
    end

    context "with invalid card params" do
      let(:card_params) { {} }
      let(:expected_status) { :unprocessable_entity }
      let(:expected_error_message) { "CVV não pode ficar em branco, Mês de vencimento não pode ficar em branco, Ano de vencimento não pode ficar em branco, Nome impresso no cartão não pode ficar em branco, Número do cartão não pode ficar em branco" }

      it_behaves_like "payment not processed"
    end

    context "with invalid anti fraud params" do
      let(:anti_fraud_params) { {} }
      let(:expected_status) { :unprocessable_entity }
      let(:expected_error_message) { "Session não pode ficar em branco" }

      it_behaves_like "payment not processed"
    end

    context "when credit card is invalid" do
      let(:card_params) do
        {
          security_code: "321",
          exp_month: 11,
          exp_year: 25,
          holder: "ADRIANOP SOUZA",
          number: "5067224275805500",
          credit_card_id: 1
        }
      end

      it "renders error" do
        expect { post("/api/v2/subscriptions", headers:, params:, env: {REMOTE_ADDR: remote_ip}) }
          .to change(Payment.denied.where.not(subscription: nil).where(reason_denied: "Cartão suspenso"), :count)
          .and change(user.subscriptions.inactive, :count)
          .and not_change(Fund, :count)

        expect(response).to be_unprocessable
        expect(response.parsed_body["error"]).to eq "Cartão suspenso"
      end
    end
  end
end
