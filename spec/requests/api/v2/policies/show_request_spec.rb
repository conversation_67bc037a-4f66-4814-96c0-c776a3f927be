# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::PoliciesController, type: :request do
  describe "#show" do
    let(:json_parse_response_body) { JSON.parse(response.body) }

    context "with valid params" do
      let!(:business) { create(:business) }
      let!(:project_config) { create(:project_config, business:, policy_url: "project.xpto.com.br") }
      let(:headers) { {"Web-Domain": "project.xpto.com.br"} }

      it "returns success" do
        _default_policy = create(:policy, :privacy_policy_kind, content: "_default_policy")
        _business_policy = create(:policy, :privacy_policy_kind, business:, content: "_business_policy")

        get "/api/v2/policies/privacy_policy", headers: headers

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body["content"]).to eq("_business_policy")
      end
    end

    context "with invalid params" do
      it "must be unprocessable" do
        get "/api/v2/policies/privacy_policy"

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "when have not found project config" do
      let!(:business) { create(:business) }
      let!(:project_config) { create(:project_config, business:, policy_url: "project.xpto.com.br") }
      let(:headers) { {"Web-Domain": "FOO.com.br"} }

      it "must be not_found" do
        get "/api/v2/policies/privacy_policy", headers: headers

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when business does not have policty" do
      let!(:business) { create(:business) }
      let!(:anothher_business) { create(:business) }
      let!(:project_config) { create(:project_config, business:, policy_url: "project.xpto.com.br") }
      let(:headers) { {"Web-Domain": "project.xpto.com.br"} }

      it "returns default policy" do
        _default_policy = create(:policy, :privacy_policy_kind, content: "_default_policy")
        _business_policy = create(:policy, :privacy_policy_kind, business: anothher_business, content: "_business_policy")

        get "/api/v2/policies/privacy_policy", headers: headers

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body["content"]).to eq("_default_policy")
      end
    end
  end
end
