require "rails_helper"

RSpec.describe Api::V2::EarnPoints::PopularOrganizationsController, type: :request do
  describe "GET /api/v2/earn_points/popular_organizations" do
    let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.03) }
    let(:user) { create(:user, business:) }

    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }
    let(:response_body) { JSON.parse(response.body) }
    let(:serializer_keys) { %w[id highest_fund logo name] }

    context "when organizations meet all conditions" do
      let!(:org_percent) { create_organization(highest_cashback_type: "percent", highest_cashback_value: 45.5) }
      let!(:org_fixed) { create_organization(highest_cashback_type: "fixed", highest_cashback_value: 9.97) }
      let!(:org_not_pinned) do
        create_organization(highest_cashback_type: "percent", highest_cashback_value: 45.5, pined: false)
      end
      let!(:org_no_value) { create_organization(highest_cashback_type: nil, highest_cashback_value: nil) }

      let!(:org_only_profile_with_value) do
        create_organization(highest_cashback_type: nil, highest_cashback_value: nil)
      end
      let!(:org_profile) do
        create(
          :organization_profile,
          organization: org_only_profile_with_value,
          business:,
          highest_cashback_type: "percent",
          highest_cashback_value: 5
        )
      end

      let!(:org_unrelated) do
        create_organization(
          highest_cashback_type: "percent",
          highest_cashback_value: 5,
          pined: true,
          business: create(:business)
        )
      end

      it "renders ok" do
        get("/api/v2/earn_points/popular_organizations", headers:)

        expect(response).to be_ok
        expect(response_body.map(&:keys)).to all(match_array(serializer_keys))
        expect(response_body.pluck("id")).to eq([
          org_percent.id,
          org_fixed.id,
          org_only_profile_with_value.id
        ])
        expect(response_body.pluck("highest_fund")).to match_array([
          {"type" => "parity", "value" => 15.1},
          {"type" => "fixed", "value" => 332.0},
          {"type" => "parity", "value" => 1.6}
        ])
      end
    end

    context "when business has cashback disabled" do
      let(:business) { create(:business, cashback: false) }

      it "renders forbidden" do
        get("/api/v2/earn_points/popular_organizations", headers:)

        expect(response).to be_forbidden
      end
    end

    context "when business currency is not points" do
      let(:business) { create(:business, :with_cashback, currency: "BRL") }

      it "renders forbidden" do
        get("/api/v2/earn_points/popular_organizations", headers:)

        expect(response).to be_forbidden
      end
    end
  end

  def create_organization(highest_cashback_type:, highest_cashback_value:, pined: true, business: nil)
    organization = create(
      :organization,
      pined:,
      active: true,
      highest_cashback_type:,
      highest_cashback_value:,
      promotion_redeemable: true
    )

    branch = create(:branch, :online, organization:, promotion_redeemable: true)

    promotion = create(:promotion, :available, :online, organization:, business:)

    create(:cupon, :online, promotion:, branch:, business:)

    organization
  end
end
