require "rails_helper"

RSpec.describe Api::V2::EarnPoints::NewOrganizationsController, type: :request do
  describe "GET /api/v2/earn_points/new_organizations" do
    let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.03) }
    let(:user) { create(:user, business:) }

    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }
    let(:response_body) { JSON.parse(response.body) }
    let(:serializer_keys) { %w[id highest_fund logo name] }

    context "when organizations meet all conditions" do
      let!(:org_newer) do
        create_organization(highest_cashback_type: "percent", highest_cashback_value: 10, created_at: 1.day.ago)
      end
      let!(:org_older) do
        create_organization(highest_cashback_type: "percent", highest_cashback_value: 20, created_at: 2.months.ago)
      end
      let!(:org_no_value) do
        create_organization(highest_cashback_type: nil, highest_cashback_value: nil, created_at: 1.day.ago)
      end

      let!(:org_only_profile_with_value_newer) do
        create_organization(highest_cashback_type: nil, highest_cashback_value: nil, created_at: 2.days.ago)
      end
      let!(:org_profile_with_value) do
        create(
          :organization_profile,
          organization: org_only_profile_with_value_newer,
          business:,
          highest_cashback_type: "percent",
          highest_cashback_value: 5
        )
      end

      let!(:org_with_profile_lower_value) do
        create_organization(highest_cashback_type: "percent", highest_cashback_value: 6, created_at: 3.months.ago)
      end
      let!(:profile_with_lower_value) do
        create(
          :organization_profile,
          organization: org_with_profile_lower_value,
          business:,
          highest_cashback_type: "percent",
          highest_cashback_value: 5
        )
      end

      let!(:org_unrelated) do
        create_organization(
          highest_cashback_type: "percent",
          highest_cashback_value: 5,
          business: create(:business),
          created_at: 1.day.ago
        )
      end

      it "renders ok" do
        get("/api/v2/earn_points/new_organizations", headers:)

        expect(response).to be_ok
        expect(response_body.map(&:keys)).to all(match_array(serializer_keys))
        expect(response_body.pluck("id")).to eq([org_newer.id, org_only_profile_with_value_newer.id, org_older.id, org_with_profile_lower_value.id])
        expect(response_body.pluck("highest_fund")).to match_array([
          {"type" => "parity", "value" => 3.3},
          {"type" => "parity", "value" => 6.6},
          {"type" => "parity", "value" => 1.6},
          {"type" => "parity", "value" => 2}
        ])
      end
    end

    context "when business has cashback disabled" do
      let(:business) { create(:business, cashback: false) }

      it "renders forbidden" do
        get("/api/v2/earn_points/new_organizations", headers:)

        expect(response).to be_forbidden
      end
    end

    context "when business currency is not points" do
      let(:business) { create(:business, :with_cashback, currency: "BRL") }

      it "renders forbidden" do
        get("/api/v2/earn_points/new_organizations", headers:)

        expect(response).to be_forbidden
      end
    end
  end

  def create_organization(highest_cashback_type:, highest_cashback_value:, created_at:, business: nil)
    organization = create(
      :organization,
      active: true,
      highest_cashback_type:,
      highest_cashback_value:,
      promotion_redeemable: true,
      created_at:
    )

    branch = create(:branch, :online, organization:, promotion_redeemable: true)

    promotion = create(:promotion, :available, :online, organization:, business:)

    create(:cupon, :online, promotion:, branch:, business:)

    organization
  end
end
