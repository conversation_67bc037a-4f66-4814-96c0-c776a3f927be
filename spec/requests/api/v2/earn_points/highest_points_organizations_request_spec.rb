require "rails_helper"

RSpec.describe Api::V2::EarnPoints::HighestPointsOrganizationsController, type: :request do
  describe "GET /api/v2/earn_points/highest_points_organizations" do
    let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.03) }
    let(:user) { create(:user, business:) }

    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }
    let(:response_body) { JSON.parse(response.body) }
    let(:serializer_keys) { %w[id highest_fund logo name] }

    context "when organizations meet all conditions" do
      let!(:org_percent) { create_organization(highest_cashback_type: "percent", highest_cashback_value: 5) }
      let!(:org_fixed) { create_organization(highest_cashback_type: "fixed", highest_cashback_value: 9.97) }
      let!(:org_no_value) { create_organization(highest_cashback_type: nil, highest_cashback_value: nil) }

      let!(:org_only_profile_with_value) do
        create_organization(highest_cashback_type: nil, highest_cashback_value: nil)
      end
      let!(:org_profile_with_value) do
        create(
          :organization_profile,
          organization: org_only_profile_with_value,
          business:,
          highest_cashback_type: "percent",
          highest_cashback_value: 15
        )
      end

      let!(:org_percent_profile) { create_organization(highest_cashback_type: "percent", highest_cashback_value: 3) }
      let!(:org_profile) do
        create(
          :organization_profile,
          organization: org_percent_profile,
          business:,
          highest_cashback_type: "percent",
          highest_cashback_value: 10
        )
      end

      let!(:org_unrelated) do
        create_organization(
          highest_cashback_type: "percent",
          highest_cashback_value: 5,
          business: create(:business)
        )
      end

      it "renders ok" do
        get("/api/v2/earn_points/highest_points_organizations", headers:)

        expect(response).to be_ok
        expect(response_body.map(&:keys)).to all(match_array(serializer_keys))
        expect(response_body.pluck("id")).to eq([
          org_only_profile_with_value.id,
          org_percent_profile.id,
          org_fixed.id,
          org_percent.id
        ])
        expect(response_body.pluck("highest_fund")).to eq([
          {"type" => "parity", "value" => 5.0},
          {"type" => "parity", "value" => 3.3},
          {"type" => "fixed", "value" => 332.0},
          {"type" => "parity", "value" => 1.6}
        ])
      end
    end

    context "when business has cashback disabled" do
      let(:business) { create(:business, cashback: false) }

      it "renders forbidden" do
        get("/api/v2/earn_points/highest_points_organizations", headers:)

        expect(response).to be_forbidden
      end
    end

    context "when business currency is not points" do
      let(:business) { create(:business, :with_cashback, currency: "BRL") }

      it "renders forbidden" do
        get("/api/v2/earn_points/highest_points_organizations", headers:)

        expect(response).to be_forbidden
      end
    end
  end

  def create_organization(highest_cashback_type:, highest_cashback_value:, business: nil)
    organization = create(
      :organization,
      active: true,
      highest_cashback_type:,
      highest_cashback_value:,
      promotion_redeemable: true
    )

    branch = create(:branch, :online, organization:, promotion_redeemable: true)

    promotion = create(:promotion, :available, :online, organization:, business:)

    create(:cupon, :online, promotion:, branch:, business:)

    organization
  end
end
