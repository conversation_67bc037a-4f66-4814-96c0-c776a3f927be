require "rails_helper"

RSpec.describe Api::V2::Categories::OrganizationsController, type: :request do
  let(:business) { create(:business, :with_cashback) }
  let(:user) { create(:user, business:) }

  let!(:category) { create(:category) }
  let!(:sub_category) { create(:category, main_category: category) }
  let!(:organization) do
    create(:organization, :active_with_coupon, :with_cashback, :percent_highest_discount, categories: [category])
  end
  let!(:sub_category_organization) do
    create(:organization, :active_with_coupon, :with_cashback, :percent_highest_discount, categories: [sub_category])
  end
  let!(:branch_physical) { create(:branch, organization:, lat: 1.0, lng: 1.0, promotion_redeemable: true) }
  let!(:promotion_qrcode) { create(:promotion, :qrcode, :available, business:, organization:) }
  let!(:coupon_qrcode) { create(:cupon, :classic, promotion: promotion_qrcode, branch: branch_physical) }

  let!(:sub_category_branch_physical) { create(:branch, organization: sub_category_organization, lat: 1.0, lng: 1.0, promotion_redeemable: true) }
  let!(:sub_category_promotion_qrcode) { create(:promotion, :qrcode, :available, business:, organization: sub_category_organization) }
  let!(:sub_category_coupon_qrcode) { create(:cupon, :classic, promotion: sub_category_promotion_qrcode, branch: sub_category_branch_physical) }

  let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }
  let(:response_hash) { JSON.parse(response.body) }

  describe "GET /api/v2/categories/:id/organizations" do
    context "with coordinates" do
      let(:params) { {distance: {lat: 1.0, lng: 1.0}} }

      it "renders ok" do
        get("/api/v2/categories/#{category.id}/organizations", headers:, params:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all match_array %w[id logo name distance highest_discount highest_fund]
        expect(response_hash.pluck("id")).to match_array [organization.id, sub_category_organization.id]
      end
    end

    context "without coordinates" do
      it "renders ok" do
        get("/api/v2/categories/#{category.id}/organizations", headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all match_array %w[id logo name highest_discount highest_fund]
        expect(response_hash.pluck("id")).to match_array [organization.id, sub_category_organization.id]
      end
    end

    context "when category has no organizations" do
      let!(:other_category) { create(:category) }

      it "renders ok" do
        get("/api/v2/categories/#{other_category.id}/organizations", headers:)

        expect(response).to be_ok
        expect(response_hash).to be_empty
      end
    end

    context "when category is inactive" do
      before do
        sub_category.update(active: false)
      end

      it "must return not return its organizations" do
        get("/api/v2/categories/#{category.id}/organizations", headers:)

        expect(response).to be_ok
        expect(response_hash.pluck("id")).to match_array [organization.id]
        expect(response_hash.pluck("id")).not_to include sub_category_organization.id
      end
    end

    context "when organization does not have discount" do
      let!(:organization) { create(:organization, :active_with_coupon, :with_cashback, categories: [category]) }
      let!(:sub_category_organization) { create(:organization, :active_with_coupon, :with_cashback, categories: [sub_category]) }

      it "renders ok" do
        get("/api/v2/categories/#{category.id}/organizations", headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all match_array %w[id logo name highest_fund]
        expect(response_hash.pluck("id")).to match_array [organization.id, sub_category_organization.id]
      end
    end

    context "when organization does not have cashback" do
      let!(:organization) do
        create(:organization, :active_with_coupon, :percent_highest_discount, categories: [category])
      end
      let!(:sub_category_organization) do
        create(:organization, :active_with_coupon, :percent_highest_discount, categories: [sub_category])
      end

      it "renders ok" do
        get("/api/v2/categories/#{category.id}/organizations", headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all match_array %w[id logo name highest_discount]
        expect(response_hash.pluck("id")).to match_array [organization.id, sub_category_organization.id]
      end
    end

    context "when business does not have cashback" do
      let(:business) { create(:business, cashback: false) }

      it "renders ok" do
        get("/api/v2/categories/#{category.id}/organizations", headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all match_array %w[id logo name highest_discount]
        expect(response_hash.pluck("id")).to match_array [organization.id, sub_category_organization.id]
      end
    end

    context "with pagination" do
      let!(:category) { create(:category, :with_organizations) }

      before do
        ids = category.organizations.pluck(:id)
        allow(Organization).to receive(:cached_redeemable_ids).and_return(ids)
      end

      context "and the request contains page and per_page" do
        it "renders ok" do
          get("/api/v2/categories/#{category.id}/organizations", headers:, params: {page: 1, per_page: 27})

          expect(response).to have_http_status(:bad_request)
          expect(response_hash["error"]).to eq("Parâmetro per_page deve conter um valor entre 30 e 100")
        end
      end

      context "and the request don't contain page or per_page" do
        it "renders ok" do
          get("/api/v2/categories/#{category.id}/organizations", headers:)

          expect(response).to be_ok
          expect(response_hash.size).to eq 30
        end
      end
    end
  end
end
