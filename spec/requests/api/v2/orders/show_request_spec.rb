# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::OrdersController do
  let(:business) { create(:business, :with_cashback, spread_percent: 0) }
  let(:user) { create(:user, business:) }
  let(:app_version) { "4.2.0" }
  let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "App-Version" => app_version, "aud" => "api"}, user) }

  describe "GET /api/v2/orders/:id" do
    let(:organization) { create(:organization) }
    let(:branch) { create(:branch, organization:) }
    let(:promotion) { create(:promotion, :percent_discount, :fixed_code, cashback_type: :percent, cashback_value: 10) }
    let(:coupon) { create(:cupon, :percent_discount, :fixed_code, promotion:, branch:, code: "ABC123") }
    let(:order) { create(:order, user:, cupon: coupon) }

    it "renders ok" do
      get("/api/v2/orders/#{order.id}", headers:)

      expect(response).to be_ok
      expect(response.parsed_body.keys).to match_array(
        %w[id allowed_payment_methods benefit created_at deep_link discount expires_at prices product redemption_code status supplier]
      )

      expect(response.parsed_body).to include(
        "id" => order.id,
        "benefit" => {
          "data" => {"currency" => "BRL", "type" => "percent", "value" => 10.0},
          "type" => "fund"
        },
        "discount" => {"type" => "percent", "value" => 30.0},
        "deep_link" => nil,
        "product" => {
          "service" => "coupon",
          "title" => coupon.title,
          "description" => coupon.description,
          "usage_expiration_date" => nil,
          "was_changed" => false
        },
        "status" => nil,
        "supplier" => {
          "id" => organization.id,
          "logo" => {
            "large" => "http://example.com/images/fallback/organization/logo_image/large_samplefile.jpg",
            "small" => "http://example.com/images/fallback/organization/logo_image/small_samplefile.jpg"
          },
          "name" => organization.name
        },
        "redemption_code" => "ABC123"
      )
    end

    context "when app version is less than 4.1.2" do
      let(:app_version) { "4.1.1" }

      it "renders ok" do
        get("/api/v2/orders/#{order.id}", headers:)

        expect(response).to be_ok
        expect(response.parsed_body.keys).to match_array(
          %w[id created_at deep_link number paid_at price redeem_code usage_ended_at fund coupon discount organization]
        )
        expect(response.parsed_body["fund"]).to eq "value" => 10.0, "type" => "percent"
        expect(response.parsed_body["redeem_code"]).to be_present
        expect(response.parsed_body["coupon"]).to include "type" => "coupon"
      end
    end

    context "when business has disabled cashback" do
      let(:business) { create(:business, cashback: false) }

      it "renders ok and does not return cashback info" do
        get("/api/v2/orders/#{order.id}", headers:)

        expect(response).to be_ok
        expect(response.parsed_body.keys).to match_array(
          %w[id allowed_payment_methods created_at deep_link discount expires_at prices product redemption_code status supplier]
        )
      end
    end

    context "when order is not paid" do
      let!(:branch) { create(:branch, :online, organization:) }
      let(:voucher_bucket) { create(:voucher_bucket) }
      let!(:giftcard) { create(:giftcard, branches: [branch], price: 50.21, voucher_bucket:) }
      let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
      let(:order) { create(:order, :with_giftcard, user:, giftcard:, voucher:) }

      it "renders ok" do
        get("/api/v2/orders/#{order.id}", headers:)

        expect(response).to be_ok

        expect(response.parsed_body).to include(
          "id" => order.id,
          "allowed_payment_methods" => ["credit_card"],
          "benefit" => {
            "data" => {"currency" => "BRL", "type" => "percent", "value" => 10.0},
            "type" => "fund"
          },
          "deep_link" => nil,
          "prices" => [{"currency" => "BRL", "amount" => 50.21}],
          "redemption_code" => nil,
          "status" => "pending",
          "supplier" => {
            "id" => organization.id,
            "logo" => {
              "large" => "http://example.com/images/fallback/organization/logo_image/large_samplefile.jpg",
              "small" => "http://example.com/images/fallback/organization/logo_image/small_samplefile.jpg"
            },
            "name" => organization.name
          }
        )
        expect(response.parsed_body.keys).not_to include "payment"
      end
    end

    context "when order is paid using credit card" do
      let!(:branch) { create(:branch, :online, organization:) }
      let(:voucher_bucket) { create(:voucher_bucket) }
      let!(:giftcard) { create(:giftcard, branches: [branch], price: 50.21, voucher_bucket:) }
      let!(:voucher) { create(:voucher, bucket: voucher_bucket, code: "TND4H1A2") }
      let(:order) { create(:order, :with_giftcard, :completed, user:, giftcard:, voucher:) }
      let!(:payment) { create(:payment, :captured, order:, value: order.price, payment_method: :credit_card) }

      it "renders ok" do
        get("/api/v2/orders/#{order.id}", headers:)

        expect(response).to be_ok

        expect(response.parsed_body).to include(
          "id" => order.id,
          "allowed_payment_methods" => ["credit_card"],
          "benefit" => {
            "data" => {"currency" => "BRL", "type" => "percent", "value" => 10.0},
            "type" => "fund"
          },
          "deep_link" => nil,
          "payment" => {"amount" => 50.21, "currency" => "BRL", "method" => "credit_card"},
          "prices" => [{"currency" => "BRL", "amount" => 50.21}],
          "redemption_code" => "TND4H1A2",
          "status" => "completed",
          "supplier" => {
            "id" => organization.id,
            "logo" => {
              "large" => "http://example.com/images/fallback/organization/logo_image/large_samplefile.jpg",
              "small" => "http://example.com/images/fallback/organization/logo_image/small_samplefile.jpg"
            },
            "name" => organization.name
          }
        )
      end
    end

    context "when order is paid using points" do
      let(:business) { create(:business, :with_cashback, spread_percent: 0, currency: :points, fair_value: 0.0125) }
      let!(:fund_redemption_config) { create(:fund_redemption_config, :points, :giftcard, business:, fair_value: 0.03) }

      let!(:branch) { create(:branch, :online, organization:) }
      let(:voucher_bucket) { create(:voucher_bucket) }
      let!(:giftcard) { create(:giftcard, branches: [branch], price: 100, voucher_bucket:) }
      let!(:voucher) { create(:voucher, bucket: voucher_bucket, code: "TND4H1A2") }

      let(:order) { create(:order, :with_giftcard, :completed, user:, giftcard:, voucher:) }

      let!(:payment) { create(:payment, :captured, order:, value: order.price, payment_method: :wallet_withdrawal) }

      it "renders ok" do
        get("/api/v2/orders/#{order.id}", headers:)

        expect(response).to be_ok

        expect(response.parsed_body.keys).not_to include "benefit"
        expect(response.parsed_body).to include(
          "id" => order.id,
          "allowed_payment_methods" => ["wallet_withdrawal"],
          "deep_link" => nil,
          "payment" => {"amount" => 3333.0, "currency" => "points", "method" => "wallet_withdrawal"},
          "prices" => [{"currency" => "points", "amount" => 3333.0}],
          "redemption_code" => "TND4H1A2",
          "status" => "completed",
          "supplier" => {
            "id" => organization.id,
            "logo" => {
              "large" => "http://example.com/images/fallback/organization/logo_image/large_samplefile.jpg",
              "small" => "http://example.com/images/fallback/organization/logo_image/small_samplefile.jpg"
            },
            "name" => organization.name
          }
        )
      end
    end

    context "when cannot access order" do
      let(:voucher_bucket) { create(:voucher_bucket) }
      let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
      let!(:another_order) { create(:order, :completed, :with_giftcard, user: create(:user), voucher:) }

      it "renders error" do
        get("/api/v2/orders/#{another_order.id}", headers:)

        expect(response).to be_not_found
      end
    end
  end
end
