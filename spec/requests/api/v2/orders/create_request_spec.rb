# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::OrdersController, type: :request do
  describe "POST /api/v2/orders" do
    let!(:business) { create(:business, giftcard: true, currency: :BRL) }
    let!(:organization) { create(:organization, cashback_type: "percent", cashback_value: 10.0) }
    let!(:branch) { create(:branch, organization:) }
    let!(:user) { create(:user, business:) }
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }
    let(:params) { {service: "giftcard", product_id: giftcard.id} }

    context "with giftcard available and not reserved" do
      let(:voucher_bucket) { create(:voucher_bucket) }
      let!(:giftcard) { create(:giftcard, branches: [branch], available: true, title: "Cinema", price: 100, voucher_bucket:) }
      let!(:voucher) { create(:voucher, bucket: voucher_bucket) }

      it "renders created" do
        expect { post("/api/v2/orders", headers:, params:) }
          .to change(user.orders.where(status: Order::Status::PENDING, giftcard:, payment_gateway: :global_pay, price: 100), :count).by(1)
          .and change { giftcard.reload.available }.to(false)

        expect(response).to be_created

        order = Order.order(:created_at).last
        expect(response.parsed_body).to eq "id" => order.id
      end

      context "and business currency is points" do
        let!(:business) { create(:business, giftcard: true, currency: :points, fair_value: 0.0125) }
        let!(:fund_redemption_config) { create(:fund_redemption_config, :points, :giftcard, business:, fair_value: 0.03) }

        it "renders created" do
          expect { post("/api/v2/orders", headers:, params:) }
            .to change(user.orders.where(status: Order::Status::PENDING, giftcard:, payment_gateway: :internal, price: 3333), :count).by(1)
            .and change { giftcard.reload.available }.to(false)

          expect(response).to be_created

          order = Order.order(:created_at).last
          expect(response.parsed_body).to eq "id" => order.id
        end
      end
    end

    context "with giftcard available and reserved" do
      let(:voucher_bucket) { create(:voucher_bucket) }
      let!(:giftcard) { create(:giftcard, branches: [branch], available: true, voucher_bucket:) }
      let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
      let!(:order) { create(:order, :with_giftcard, user:, giftcard:, voucher:) }

      it "renders error" do
        expect { post("/api/v2/orders", headers:, params:) }
          .to not_change(Order, :count)
          .and not_change { giftcard.reload.available }

        expect(response).to be_unprocessable
        expect(response.parsed_body).to eq "error" => "Voucher não disponível para resgate"
      end
    end

    context "with giftcard unavailable" do
      let(:voucher_bucket) { create(:voucher_bucket) }
      let!(:giftcard) { create(:giftcard, branches: [branch], available: false, voucher_bucket:) }
      let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
      let!(:order) { create(:order, :with_giftcard, user:, giftcard:, voucher:) }

      it "returns error" do
        expect { post("/api/v2/orders", headers:, params:) }
          .to not_change(Order, :count)
          .and not_change { giftcard.reload.available }

        expect(response).to be_not_found
      end
    end

    context "with invalid service" do
      let(:params) { {service: "invalid", product_id: 1} }

      it "returns error" do
        expect { post("/api/v2/orders", headers:, params:) }.not_to change(Order, :count)

        expect(response).to be_unprocessable
        expect(response.parsed_body).to eq "error" => "Serviço inválido"
      end
    end
  end
end
