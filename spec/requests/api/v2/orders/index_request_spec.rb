# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::OrdersController do
  let(:business) { create(:business) }
  let(:user) { create(:user, business:) }
  let(:app_version) { "4.2.0" }
  let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "App-Version" => app_version, "aud" => "api"}, user) }

  describe "GET /api/v2/orders" do
    let(:organization) { create(:organization) }
    let(:branch) { create(:branch, organization:) }
    let(:promotion) { create(:promotion, :percent_discount, cashback_type: :percent, cashback_value: 10) }
    let(:coupon) { create(:cupon, :percent_discount, promotion:, branch:) }
    let!(:coupon_order) { create(:order, user:, cupon: coupon) }

    context "when business has enabled cashback" do
      let(:business) { create(:business, :with_cashback, spread_percent: 0) }

      let(:params) { {product: "coupon"} }

      it "renders ok" do
        get("/api/v2/orders", params:, headers:)

        expect(response).to be_ok
        expect(response.parsed_body.map(&:keys)).to all(match_array(
          %w[id benefit created_at discount product status supplier]
        ))

        order_payload = response.parsed_body.find { |payload| payload["id"] == coupon_order.id }
        expect(order_payload).to include(
          "id" => coupon_order.id,
          "benefit" => {
            "data" => {"currency" => "BRL", "type" => "percent", "value" => 10.0},
            "type" => "fund"
          },
          "discount" => {"type" => "percent", "value" => 30.0},
          "product" => {
            "service" => "coupon",
            "title" => coupon.title,
            "description" => coupon.description,
            "usage_expiration_date" => nil,
            "was_changed" => false
          },
          "status" => nil,
          "supplier" => {
            "id" => organization.id,
            "logo" => {
              "large" => "http://example.com/images/fallback/organization/logo_image/large_samplefile.jpg",
              "small" => "http://example.com/images/fallback/organization/logo_image/small_samplefile.jpg"
            },
            "name" => organization.name
          }
        )
      end

      context "when currency is points" do
        let(:business) { create(:business, :with_cashback, spread_percent: 0, currency: :points, fair_value: 0.0125) }

        it "renders ok and returns fund info" do
          get("/api/v2/orders", params:, headers:)

          expect(response).to be_ok
          expect(response.parsed_body.map(&:keys)).to all(match_array(
            %w[id benefit created_at discount product status supplier]
          ))

          order_payload = response.parsed_body.find { |payload| payload["id"] == coupon_order.id }
          expect(order_payload["benefit"]).to eq(
            "data" => {"currency" => "points", "type" => "parity", "value" => 8.0},
            "type" => "fund"
          )
        end

        context "when order is paid" do
          let!(:fund_redemption_config) { create(:fund_redemption_config, :points, :giftcard, business:, fair_value: 0.03) }

          let!(:branch) { create(:branch, :online, organization:) }
          let(:voucher_bucket) { create(:voucher_bucket) }
          let!(:giftcard) { create(:giftcard, branches: [branch], price: 100, voucher_bucket:) }
          let!(:voucher) { create(:voucher, bucket: voucher_bucket, code: "TND4H1A2") }

          let(:order) { create(:order, :with_giftcard, :completed, user:, giftcard:, voucher:) }

          let!(:payment) { create(:payment, :captured, order:, value: order.price, payment_method: :wallet_withdrawal) }

          it "renders ok" do
            get("/api/v2/orders", headers:)

            expect(response).to be_ok

            order_payload = response.parsed_body.find { |payload| payload["id"] == order.id }
            expect(order_payload.keys).not_to include "benefit"
            expect(order_payload).to include(
              "id" => order.id,
              "payment" => {"amount" => 3333.0, "currency" => "points", "method" => "wallet_withdrawal"},
              "product" => {
                "description" => order.description,
                "service" => "giftcard",
                "title" => order.title,
                "usage_expiration_date" => 3.months.from_now.strftime("%Y-%m-%d"),
                "was_changed" => false
              },
              "status" => "completed",
              "supplier" => {
                "id" => organization.id,
                "logo" => {
                  "large" => "http://example.com/images/fallback/organization/logo_image/large_samplefile.jpg",
                  "small" => "http://example.com/images/fallback/organization/logo_image/small_samplefile.jpg"
                },
                "name" => organization.name
              }
            )
          end
        end
      end

      context "when app version is less than 4.0.0" do
        let(:app_version) { "3.999.999" }
        let(:serializer_keys) do
          %w[id created_at deep_link number paid_at price redeem_code usage_ended_at cashback coupon discount organization]
        end

        it "renders ok and returns cashback info" do
          get("/api/v2/orders", params:, headers:)

          expect(response).to be_ok
          expect(response.parsed_body.map(&:keys)).to all match_array serializer_keys
          expect(response.parsed_body.pluck("cashback")).to all match_array "value" => 10.0, "type" => "percent"
        end
      end

      context "when app version is less than 4.1.2" do
        let(:app_version) { "4.1.1" }
        let(:serializer_keys) do
          %w[id created_at deep_link number paid_at price redeem_code usage_ended_at fund coupon discount organization]
        end

        it "renders ok and returns fund info" do
          get("/api/v2/orders", params:, headers:)

          expect(response).to be_ok
          expect(response.parsed_body.map(&:keys)).to all match_array serializer_keys
          expect(response.parsed_body.pluck("fund")).to all match_array "value" => 10.0, "type" => "percent"
        end
      end

      context "when order is paid" do
        let!(:branch) { create(:branch, :online, organization:) }
        let(:voucher_bucket) { create(:voucher_bucket) }
        let!(:giftcard) { create(:giftcard, branches: [branch], price: 50.21, voucher_bucket:) }
        let!(:voucher) { create(:voucher, bucket: voucher_bucket, code: "TND4H1A2") }
        let(:order) { create(:order, :with_giftcard, :completed, user:, giftcard:, voucher:) }
        let!(:payment) { create(:payment, :captured, order:, value: order.price, payment_method: :credit_card) }

        it "renders ok" do
          get("/api/v2/orders", headers:)

          expect(response).to be_ok

          order_payload = response.parsed_body.find { |payload| payload["id"] == order.id }
          expect(order_payload).to include(
            "id" => order.id,
            "benefit" => {
              "data" => {"currency" => "BRL", "type" => "percent", "value" => 10.0},
              "type" => "fund"
            },
            "payment" => {"amount" => 50.21, "currency" => "BRL", "method" => "credit_card"},
            "product" => {
              "description" => order.description,
              "service" => "giftcard",
              "title" => order.title,
              "usage_expiration_date" => 3.months.from_now.strftime("%Y-%m-%d"),
              "was_changed" => false
            },
            "status" => "completed",
            "supplier" => {
              "id" => organization.id,
              "logo" => {
                "large" => "http://example.com/images/fallback/organization/logo_image/large_samplefile.jpg",
                "small" => "http://example.com/images/fallback/organization/logo_image/small_samplefile.jpg"
              },
              "name" => organization.name
            }
          )
        end
      end
    end

    context "when the request contains a page and per_page parameter" do
      let(:params) { {page: 1, per_page: 3} }
      let!(:coupon_orders) { create_list(:order, 6, user:, cupon: coupon) }

      it "renders ok" do
        get("/api/v2/orders", params:, headers:)

        expect(response).to have_http_status(:bad_request)
        expect(response.parsed_body["error"]).to eq("Parâmetro per_page deve conter um valor entre 30 e 100")
      end
    end

    context "when coupon had updates" do
      it "renders ok and returns coupon_changed true" do
        promotion.update!(cashback_value: 12)

        get("/api/v2/orders", headers:)

        expect(response).to be_ok
        order_payload = response.parsed_body.find { |payload| payload["id"] == coupon_order.id }
        expect(order_payload.dig("product", "was_changed")).to be true
      end
    end

    context "when business has disabled cashback" do
      let(:business) { create(:business, cashback: false) }

      let(:params) { {product: "coupon"} }

      it "renders ok and does not return cashback info" do
        get("/api/v2/orders", params:, headers:)

        expect(response).to be_ok
        expect(response.parsed_body.map(&:keys)).to all(match_array(
          %w[id created_at discount product status supplier]
        ))
      end
    end

    context "when filtering by coupon orders" do
      let(:voucher_bucket) { create(:voucher_bucket) }
      let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
      let!(:giftcard_order) { create(:order, :completed, :with_giftcard, user:, voucher:) }

      let(:params) { {product: "coupon"} }

      it "renders ok" do
        get("/api/v2/orders", params:, headers:)

        expect(response).to be_ok
        expect(response.parsed_body.pluck("id"))
          .to include(coupon_order.id)
          .and not_include(giftcard_order.id)

        expect(response.parsed_body.map { |payload| payload.dig("product", "service") }).to all eq "coupon"
      end
    end

    context "when filtering by giftcard orders" do
      let(:params) { {product: "giftcard"} }

      let(:voucher_bucket) { create(:voucher_bucket) }
      let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
      let!(:giftcard_order) { create(:order, :completed, :with_giftcard, user:, voucher:) }

      it "renders ok" do
        get("/api/v2/orders", params:, headers:)

        expect(response).to be_ok
        expect(response.parsed_body.pluck("id"))
          .to include(giftcard_order.id)
          .and not_include(coupon_order.id)

        expect(response.parsed_body.map { |payload| payload.dig("product", "service") }).to all eq "giftcard"
      end
    end

    context "without filters" do
      let!(:bucket) { create(:voucher_bucket) }
      let!(:voucher) { create(:voucher, bucket:) }
      let!(:giftcard_order) { create(:order, :completed, :with_giftcard, user:, created_at: coupon_order.created_at - 1.day, voucher:) }

      it "renders ok" do
        get("/api/v2/orders", headers:)

        expect(response).to be_ok
        expect(response.parsed_body.pluck("id")).to match_array([coupon_order.id, giftcard_order.id])
      end
    end
  end
end
