# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::OrdersController, type: :request do
  let(:user) { create(:user, business:) }
  let(:business) { create(:business) }
  let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => user.business.api_secret}, user) }

  describe "DELETE /api/v2/orders/:id" do
    context "with deletable order for user" do
      let(:voucher_bucket) { create(:voucher_bucket) }
      let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
      let!(:order) { create(:order, :pending, :with_giftcard, user:, voucher:) }

      it "renders no content" do
        expect do
          delete("/api/v2/orders/#{order.id}", headers:)
        end.to change(Order.where(id: order.id), :count).by(-1)

        expect(response).to be_no_content
      end
    end

    context "with no deletable order for user" do
      let(:voucher_bucket) { create(:voucher_bucket) }
      let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
      let!(:order) { create(:order, :pending, :with_giftcard, user:, voucher:) }
      let!(:payment) { create(:payment, :authorized, :credit_card, order:, value: order.price) }

      it "renders no content" do
        expect do
          delete("/api/v2/orders/#{order.id}", headers:)
        end.to not_change(Order, :count)

        expect(response).to be_no_content
      end
    end

    context "with no order for user" do
      it "renders no content" do
        expect do
          delete("/api/v2/orders/0", headers:)
        end.to not_change(Order, :count)

        expect(response).to be_no_content
      end
    end
  end
end
