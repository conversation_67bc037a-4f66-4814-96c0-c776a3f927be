# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::Orders::PaymentsController, :vcr, type: :request do
  let(:business) { create(:business, :with_cashback) }
  let(:user) do
    create(
      :user,
      business:,
      name: "ADRIANO PESSOA SOUZA",
      cpf: "***********",
      email: "<EMAIL>",
      cellphone: "***********"
    )
  end
  let!(:branch) { create(:branch, :online) }
  let(:voucher_bucket) { create(:voucher_bucket) }
  let!(:giftcard) { create(:giftcard, branches: [branch], price: 50.21, voucher_bucket:) }
  let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
  let(:order) { create(:order, :with_giftcard, user:, giftcard:, payment_gateway: :global_pay, voucher:) }
  let(:idempotency_key) { SecureRandom.uuid }
  let(:headers) do
    Devise::JWT::TestHelpers.auth_headers(
      {"Api-Secret" => business.api_secret, "Idempotency-Key" => idempotency_key},
      user
    )
  end
  let(:remote_ip) { "*************" }
  let(:user_params) do
    {
      name: user.name,
      document: user.cpf,
      email: user.email,
      phone: user.cellphone
    }
  end
  let(:address_params) do
    {
      postal_code: "45810000",
      street: "Rua Jose Lacerda",
      number: "55",
      complement: "APT 104 BL 02",
      neighborhood: "Trevo",
      city: "Belo Horizonte",
      state: "MG"
    }
  end
  let(:card_params) do
    {
      security_code: "321",
      exp_month: 12,
      exp_year: 25,
      holder: "ADRIANOP SOUZA",
      number: "****************"
    }
  end
  let(:anti_fraud_params) do
    {
      session_id: "fc10b881-d9a0-4ab1-a6fd-a102db188f49"
    }
  end
  let(:payment_method) { Payment::PaymentMethod::CREDIT_CARD }
  let(:params) do
    {
      payment_method:,
      payment_details: {
        user: user_params,
        address: address_params,
        card: card_params,
        anti_fraud: anti_fraud_params
      }
    }
  end

  it "renders created" do
    expect { post("/api/v2/orders/#{order.id}/payments", headers:, params:, env: {REMOTE_ADDR: remote_ip}) }
      .to change(Payment.captured.where(order:, value: 50.21), :count).by(1)
      .and change { order.reload.status }.to(Order::Status::COMPLETED)
      .and change(order.cashback_records.where(user:, order_amount: 50.21), :count).by(1)

    expect(response).to be_created
    expect(response.parsed_body).to eq "message" => "Sua compra foi confirmada e seu pagamento está sob análise. Assim que for confirmada, certifique-se de ler as instruções e seguir as etapas indicadas para resgatar seu gift card com sucesso."
  end

  context "when credit card must be saved for future purchases" do
    let(:params) do
      {
        payment_method:,
        payment_details: {
          user: user_params,
          address: address_params,
          card: card_params,
          anti_fraud: anti_fraud_params,
          store_card: true
        }
      }
    end

    it "renders created" do
      expect { post("/api/v2/orders/#{order.id}/payments", headers:, params:, env: {REMOTE_ADDR: remote_ip}) }
        .to change(Payment.captured.where(order:, value: 50.21), :count).by(1)
        .and change { order.reload.status }.to(Order::Status::COMPLETED)
        .and change(order.cashback_records.where(user:, order_amount: 50.21), :count).by(1)
        .and change(user.credit_cards, :count).by(1)

      expect(response).to be_created
      expect(response.parsed_body).to eq "message" => "Sua compra foi confirmada e seu pagamento está sob análise. Assim que for confirmada, certifique-se de ler as instruções e seguir as etapas indicadas para resgatar seu gift card com sucesso."
    end
  end

  context "when only credit card tokenization returns error" do
    let(:global_pay_api_mock) { instance_double(GlobalPay::Api, payment: payment_response, store_card: store_card_response) }
    let(:payment_response) do
      GlobalPay::ApiResponse.new(status: 201, json: {
        "status" => 201,
        "message" => "Pay Credit Card",
        "data" => {
          "orderCode" => "561cb534a5304",
          "paymentId" => "020091487401111734280002361881640000000000",
          "authorizationCode" => "955726",
          "statusText" => "PAID",
          "amount" => "50.21",
          "gateway" => {
            "paymentAuthorization" => {
              "returnCode" => "0",
              "description" => "Sucesso",
              "paymentId" => "020091487401111734280002361881640000000000",
              "authorizationCode" => "955726",
              "orderNumber" => "561cb534a5304",
              "amount" => 5021,
              "releaseAt" => "2025-01-11T14:34:28.2605125-03:00"
            }
          }
        },
        "id" => "4195317",
        "companyName" => "LECUPON S.A.",
        "documentNumber" => "26989697000169"
      })
    end
    let(:store_card_response) do
      GlobalPay::ApiResponse.new(status: 400, error_message: "Não foi possível tokenizar o cartão", json: {
        "status" => 400,
        "data" => [{"description" => "Não foi possível tokenizar o cartão"}]
      })
    end
    let(:params) do
      {
        payment_method:,
        payment_details: {
          user: user_params,
          address: address_params,
          card: card_params,
          anti_fraud: anti_fraud_params,
          store_card: true
        }
      }
    end

    before do
      allow(GlobalPay::Api).to receive(:new).and_return(global_pay_api_mock)
    end

    it "renders created" do
      expect { post("/api/v2/orders/#{order.id}/payments", headers:, params:, env: {REMOTE_ADDR: remote_ip}) }
        .to change(Payment.captured.where(order:, value: 50.21), :count).by(1)
        .and change { order.reload.status }.to(Order::Status::COMPLETED)
        .and change(order.cashback_records.where(user:, order_amount: 50.21), :count).by(1)
        .and not_change(CreditCard, :count)

      expect(global_pay_api_mock).to have_received(:store_card)

      expect(response).to be_created
      expect(response.parsed_body).to eq "message" => "Sua compra foi confirmada e seu pagamento está sob análise. Assim que for confirmada, certifique-se de ler as instruções e seguir as etapas indicadas para resgatar seu gift card com sucesso."
    end
  end

  shared_examples "payment not processed" do
    it "renders error" do
      expect { post("/api/v2/orders/#{order.id}/payments", headers:, params:, env: {REMOTE_ADDR: remote_ip}) }
        .to not_change(Payment, :count)
        .and not_change { order.reload.status }
        .and not_change(CashbackRecord, :count)

      expect(response).to have_http_status(expected_status)
      expect(response.parsed_body["error"]).to eq expected_error_message
    end
  end

  context "when order is not payable" do
    let!(:payment) { create(:payment, :authorized, order:, value: order.price, payment_method:) }
    let(:expected_status) { :not_found }
    let(:expected_error_message) { "Registro(s) não encontrado(s)." }

    it_behaves_like "payment not processed"
  end

  context "with invalid payment method" do
    let(:payment_method) { "wrong" }
    let(:expected_status) { :unprocessable_entity }
    let(:expected_error_message) { "'wrong' is not a valid payment_method" }

    it_behaves_like "payment not processed"
  end

  context "with invalid user params" do
    let(:user_params) { {} }
    let(:expected_status) { :unprocessable_entity }
    let(:expected_error_message) { "Nome completo não pode ficar em branco, CPF não pode ficar em branco, E-mail não pode ficar em branco, Telefone não pode ficar em branco" }

    it_behaves_like "payment not processed"
  end

  context "with invalid address params" do
    let(:address_params) { {} }
    let(:expected_status) { :unprocessable_entity }
    let(:expected_error_message) { "CEP não pode ficar em branco, Endereço não pode ficar em branco, Número não pode ficar em branco, Bairro não pode ficar em branco, Cidade não pode ficar em branco, Estado não pode ficar em branco" }

    it_behaves_like "payment not processed"
  end

  context "with invalid card params" do
    let(:card_params) { {} }
    let(:expected_status) { :unprocessable_entity }
    let(:expected_error_message) { "CVV não pode ficar em branco, Mês de vencimento não pode ficar em branco, Ano de vencimento não pode ficar em branco, Nome impresso no cartão não pode ficar em branco, Número do cartão não pode ficar em branco" }

    it_behaves_like "payment not processed"
  end

  context "with invalid anti fraud params" do
    let(:anti_fraud_params) { {} }
    let(:expected_status) { :unprocessable_entity }
    let(:expected_error_message) { "Session não pode ficar em branco" }

    it_behaves_like "payment not processed"
  end
end
