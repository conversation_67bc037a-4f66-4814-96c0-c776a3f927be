# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::CouponsController, type: :request do
  let(:business) { create(:business, :with_cashback) }
  let(:user) { create(:user, business:) }
  let!(:headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "App-Version" => "4.0.0"}, user)
  end

  let(:coupon_serializer_keys) do
    %w[
      id
      coupon_type
      description
      endeded_at
      redeem_type
      redeemable_hours
      rules
      started_at
      title
      branch
      fund
      organization
      discount
      usage_instruction
    ]
  end
  let(:redeemable_hours) do
    %w[
      end_hour
      start_hour
      week_day
    ]
  end
  let(:discount_serializer_keys) do
    %w[
      type
      value
    ]
  end
  let(:organization_serializer_keys) do
    %w[
      id
      logo_image_large
      logo_image_small
      name
    ]
  end
  let(:branch_serializer_keys) do
    %w[id branch_type]
  end

  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "GET /api/v2/coupons/:id" do
    let(:category) { create(:category) }
    let(:organization) { create(:organization, active: true, promotion_redeemable: true, categories: [category]) }
    let(:branch_online) { create(:branch, :online, organization:, active: true) }
    let(:promotion) { create(:promotion, :available, :percent_discount, redeem_type: Enums::PromotionRedeemType::DISCOUNT) }
    let(:create_coupon) { create(:cupon, :percent_discount, branch: branch_online, promotion:, rules: "Alloyal/{{client_name}}", description: "Alloyal/{{client_name}}") }

    context "given a active organization and branch" do
      it "return a valid coupon" do
        coupon = create_coupon

        get("/api/v2/coupons/#{coupon.id}", headers:)

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.keys).to match_array(coupon_serializer_keys)
        expect(json_parse_response_body["redeemable_hours"].first.keys).to match_array(redeemable_hours)
        expect(json_parse_response_body["discount"].keys).to match_array(discount_serializer_keys)
        expect(json_parse_response_body["discount"]["value"]).to eq(30.0)
        expect(json_parse_response_body["fund"]).to eq("type" => "percent", "value" => 9.0)
        expect(json_parse_response_body["organization"].keys).to match_array(organization_serializer_keys)
        expect(json_parse_response_body["branch"].keys).to match_array(branch_serializer_keys)
        expect(json_parse_response_body["rules"]).to eq("Alloyal/#{business.name}")
        expect(json_parse_response_body["description"]).to eq("Alloyal/#{business.name}")
      end

      it "return a valid coupon" do
        coupon = create_coupon
        coupon.promotion.update_columns(cashback_type: nil, cashback_value: 0.0)

        get("/api/v2/coupons/#{coupon.id}", headers:)

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.keys).to match_array(coupon_serializer_keys)
        expect(json_parse_response_body["redeemable_hours"].first.keys).to match_array(redeemable_hours)
        expect(json_parse_response_body["fund"]).to eq(nil)
        expect(json_parse_response_body["discount"]["value"]).to eq(30.0)
        expect(json_parse_response_body["organization"].keys).to match_array(organization_serializer_keys)
        expect(json_parse_response_body["branch"].keys).to match_array(branch_serializer_keys)
      end

      context "when currency is points" do
        let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.0125) }

        it "renders ok" do
          coupon = create_coupon

          get "/api/v2/coupons/#{coupon.id}", headers: headers

          expect(response).to be_ok
          expect(json_parse_response_body.keys).to match_array(coupon_serializer_keys)
          expect(json_parse_response_body["fund"]).to eq("type" => "parity", "value" => 8.0)
        end
      end
    end

    context "given a inactive coupon" do
      let(:create_inactive_coupon) { create(:cupon, :percent_discount, branch: branch_online, promotion:, active: false) }

      specify do
        coupon = create_inactive_coupon

        get("/api/v2/coupons/#{coupon.id}", headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "given a inactive organization" do
      let(:inactive_organization) { create(:organization, active: false, promotion_redeemable: true, categories: [category]) }
      let(:branch_online) { create(:branch, :online, organization: inactive_organization, active: true) }
      let(:promotion) { create(:promotion, :available, redeem_type: Enums::PromotionRedeemType::DISCOUNT) }
      let(:create_coupon) { create(:cupon, :percent_discount, branch: branch_online, promotion:) }

      specify do
        coupon = create_coupon

        get("/api/v2/coupons/#{coupon.id}", headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "given a inactive branch" do
      let(:organization) { create(:organization, active: true, promotion_redeemable: true, categories: [category]) }
      let(:inactive_branch_online) { create(:branch, :online, organization:, active: false) }
      let(:promotion) { create(:promotion, :available, redeem_type: Enums::PromotionRedeemType::DISCOUNT) }
      let(:create_coupon) { create(:cupon, :percent_discount, branch: inactive_branch_online, promotion:) }

      specify do
        coupon = create_coupon

        get("/api/v2/coupons/#{coupon.id}", headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "given a exclusive promotion with tags" do
      let(:organization) { create(:organization, active: true, promotion_redeemable: true, categories: [category]) }
      let(:branch_online) { create(:branch, :online, organization:, active: true) }
      let(:promotion) { create(:promotion, :available, :percent_discount, redeem_type: Enums::PromotionRedeemType::DISCOUNT, tags: ["Tag 1", "Tag 2"], business:) }
      let(:coupon) { create(:cupon, :percent_discount, branch: branch_online, promotion:) }

      it "when current user matches any tag" do
        user.authorized_user.update(tags: ["Tag 2"])
        get("/api/v2/coupons/#{coupon.id}", headers:)

        expect(response).to be_successful
        expect(json_parse_response_body.keys).to match_array(coupon_serializer_keys)
      end

      it "when current user does not match any tag" do
        get("/api/v2/coupons/#{coupon.id}", headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "given a organization blocklisted" do
      specify do
        coupon = create_coupon
        create(:organization_blocklist, business:, organization:)

        get("/api/v2/coupons/#{coupon.id}", headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "given a category blocklisted" do
      specify do
        coupon = create_coupon
        create(:category_blocklist, business:, category:)

        get("/api/v2/coupons/#{coupon.id}", headers:)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "when is embedded project" do
      let(:business) { create(:business, :with_cashback, embedded: true) }
      it "must return redeem_type none" do
        coupon = create_coupon
        get("/api/v2/coupons/#{coupon.id}", headers:)

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.keys).to match_array(coupon_serializer_keys)
        expect(json_parse_response_body["discount"]["value"]).to eq(30.0)
        expect(json_parse_response_body["redeem_type"]).to eq("none")
      end
    end

    context "when serializing for older app versions" do
      let(:headers) do
        Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "App-Version" => "3.3.999"}, user)
      end
      let(:old_serializer_keys) do
        %w[id coupon_type description endeded_at redeem_type redeemable_hours rules started_at title branch
          cashback organization discount]
      end

      it "must render discount as value instead of object" do
        coupon = create_coupon

        get("/api/v2/coupons/#{coupon.id}", headers:)

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.keys).to match_array(old_serializer_keys)
        expect(json_parse_response_body["redeemable_hours"].first.keys).to match_array(redeemable_hours)
        expect(json_parse_response_body["discount"]).to eq(30)
        expect(json_parse_response_body["cashback"]).to eq("type" => "percent", "value" => 9.0)
        expect(json_parse_response_body["organization"].keys).to match_array(organization_serializer_keys)
        expect(json_parse_response_body["branch"].keys).to match_array(branch_serializer_keys)
      end
    end
  end
end
