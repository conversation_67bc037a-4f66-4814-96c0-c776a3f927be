# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::SubscriptionController, type: :request do
  describe "GET /api/v2/subscription" do
    let!(:business) { create(:business) }
    let!(:user) { create(:user, business:) }
    let!(:subscription_config) { create(:subscription_config, business:, cancellation_description: "Lamentamos sua saída") }
    let!(:plan) { create(:plan, subscription_config:, business:, price: 19.99, points: 1000, benefit_description: "+1000 pontos") }
    let!(:credit_card) do
      create(
        :credit_card,
        user:,
        main: true,
        number: "****************",
        brand: "visa",
        last4: "4693"
      )
    end
    let!(:subscription) { create(:subscription, active: true, user:, subscription_config:, plan:, credit_card:) }
    let!(:subscription_installment) { create(:subscription_installment, subscription:, due_at: DateTime.new(2025, 4, 5, 10), created_at: DateTime.new(2025, 4, 5, 10)) }

    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

    before do
      subscription_config.update!(recommended_plan: plan)
    end

    it "renders ok" do
      get("/api/v2/subscription", headers:)

      expect(response).to be_ok
      expect(response.parsed_body).to eq(
        "billing" => {
          "status" => "active",
          "message" => "Próxima fatura: 05 de Maio de 2025"
        },
        "plan" => {
          "id" => plan.id,
          "button" => {
            "text" => "Assinar"
          },
          "name" => plan.title,
          "recurrence" => "Mensal",
          "price_label" => "mês",
          "price" => "R$ 19,99",
          "recommended" => true,
          "benefit" => "+1000 pontos",
          "theme" => {
            "card_color" => plan.background_color,
            "title_color" => plan.font_color
          }
        },
        "credit_card_info" => {
          "id" => credit_card.id,
          "brand" => {
            "id" => "visa",
            "name" => "Visa",
            "logo" => "https://assets.alloyal.com.br/credit_card_brands/visa.png"
          },
          "last4" => "4693",
          "main" => true
        },
        "cancellation_description" => "Lamentamos sua saída"
      )
    end

    context "when subscription is not active" do
      before { subscription.cancel! }

      it "renders error" do
        get("/api/v2/subscription", headers:)

        expect(response).to be_not_found
      end
    end
  end

  describe "DELETE /api/v2/subscription" do
    let!(:business) { create(:business) }
    let!(:user) { create(:user, business: business) }
    let!(:subscription_config) { create(:subscription_config, business: business) }
    let!(:plan) { create(:plan, subscription_config: subscription_config, business: business) }
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => user.business.api_secret}, user) }

    context "when user has active subscription" do
      let!(:subscription) { create(:subscription, user:, subscription_config:, plan:, active: true) }

      it "renders ok and cancels subscription" do
        expect { delete "/api/v2/subscription", headers: headers }
          .to change { subscription.reload.active }.to(false)

        expect(response).to be_ok
        expect(response.parsed_body["message"]).to eq("Assinatura cancelada com sucesso!")
      end

      context "when reason is informed" do
        let(:params) { {reason: "reason"} }

        it "cancels subscription with reason" do
          expect { delete "/api/v2/subscription", headers: headers, params: params }
            .to change { subscription.reload.active }.to(false)
            .and change { subscription.reload.reason }.to("reason")
        end
      end

      context "when subscription config has cancel destination business set" do
        let!(:sub_business) { create(:business, main_business: business) }
        let!(:subscription_config) { create(:subscription_config, business:, cancel_destination_business: sub_business) }

        it "cancels subscription and migrates user to cancel destination business" do
          expect { delete "/api/v2/subscription", headers: headers }
            .to change { subscription.reload.active }.to(false)
            .and change { user.reload.business }.to(sub_business)

          expect(response).to be_ok
          expect(response.parsed_body["message"]).to eq("Assinatura cancelada com sucesso!")
        end
      end
    end

    context "when user does not have active subscription" do
      let!(:subscription) { create(:subscription, user:, subscription_config:, plan:, active: false) }

      it "renders not found" do
        delete "/api/v2/subscription", headers: headers

        expect(response).to be_not_found
      end
    end
  end
end
