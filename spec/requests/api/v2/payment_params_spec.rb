# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::PaymentParams do
  context "when payment method is credit card" do
    subject(:request_params) { described_class.new(user:, ip: anti_fraud_params[:ip], payment_method:, payment_details:) }

    let(:user) { build(:user) }
    let(:user_params) do
      {
        name: "<PERSON>",
        document: "123.456.789-00",
        email: "<EMAIL>",
        phone: "+5511999999999"
      }
    end
    let(:address_params) do
      {
        postal_code: "12345-678",
        street: "Main Street",
        number: "123",
        complement: "Apt 4B",
        neighborhood: "Downtown",
        city: "São Paulo",
        state: "SP"
      }
    end
    let(:card_params) do
      {
        security_code: "123",
        exp_month: "12",
        exp_year: "25",
        holder: "<PERSON>",
        number: "****************"
      }
    end
    let(:anti_fraud_params) do
      {
        ip: "127.0.0.1",
        session_id: "abc123"
      }
    end
    let(:payment_method) { Payment::PaymentMethod::CREDIT_CARD }
    let(:payment_details) do
      {
        user: user_params,
        address: address_params,
        card: card_params,
        anti_fraud: anti_fraud_params,
        store_card: [true, false].sample
      }
    end

    describe "#to_hash" do
      it "returns a hash with all params" do
        hash = request_params.to_hash

        expect(hash.keys).to eq %i[credit_card anti_fraud_params store_card]

        credit_card = hash[:credit_card]
        expect(credit_card).to be_a CreditCard
        expect(credit_card).to be_new_record
        expect(credit_card.billing_name).to eq(user_params[:name])
        expect(credit_card.billing_document).to eq(user_params[:document].delete("^0-9"))
        expect(credit_card.billing_email).to eq(user_params[:email])
        expect(credit_card.billing_phone).to eq(user_params[:phone].delete("^0-9"))
        expect(credit_card.billing_postal_code).to eq(address_params[:postal_code])
        expect(credit_card.billing_street).to eq(address_params[:street])
        expect(credit_card.billing_number).to eq(address_params[:number])
        expect(credit_card.billing_complement).to eq(address_params[:complement])
        expect(credit_card.billing_neighborhood).to eq(address_params[:neighborhood])
        expect(credit_card.billing_city).to eq(address_params[:city])
        expect(credit_card.billing_state).to eq(address_params[:state])
        expect(credit_card.security_code).to eq(card_params[:security_code])
        expect(credit_card.exp_month).to eq(card_params[:exp_month])
        expect(credit_card.exp_year).to eq(card_params[:exp_year])
        expect(credit_card.holder).to eq(card_params[:holder])
        expect(credit_card.number).to eq(card_params[:number])

        expect(hash[:anti_fraud_params]).to eq(anti_fraud_params)
        expect(hash[:store_card]).to eq(payment_details[:store_card])
      end
    end
  end
end
