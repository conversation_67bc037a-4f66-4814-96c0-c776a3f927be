# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::FundRedemptionConfigsController, type: :request do
  describe "GET /api/v2/fund_redemption_config" do
    let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.03) }
    let(:service) { FundRedemption.services[:bank_transfer] }
    let(:user) { create(:user, business:) }
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }
    let(:response_body) { JSON.parse(response.body) }

    context "when config exists" do
      let!(:fund_redemption_config) do
        create(:fund_redemption_config, :points, business:, service:, fair_value: 0.03, min_amount: 1000, multiple: 100)
      end
      let(:params) { {service:} }

      it "renders ok" do
        get("/api/v2/fund_redemption_config", headers:, params:)

        expect(response).to be_ok
        expect(response_body).to eq(
          "min_amount" => fund_redemption_config.min_amount,
          "multiple" => fund_redemption_config.multiple,
          "fair_value" => 0.03
        )
      end
    end

    context "when config does not exist" do
      let(:params) { {service:} }

      it "renders error" do
        get("/api/v2/fund_redemption_config", headers:, params:)

        expect(response).to be_not_found
      end
    end
  end
end
