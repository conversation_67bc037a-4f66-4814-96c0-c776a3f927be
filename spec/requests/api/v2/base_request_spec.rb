# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::BaseController, type: :request do
  let!(:business) { create(:business) }
  let(:user) { create(:user, business:) }
  let(:response_hash) { JSON.parse(response.body) }

  before do
    Rails.application.routes.disable_clear_and_finalize = true
    Rails.application.routes.draw do
      get "/api_v2_base_test", to: "api_v2_base_test#test"
    end
    class_stub = Class.new(Api::V2::BaseController) do
      def test
        head :ok
      end
    end
    stub_const("ApiV2BaseTestController", class_stub)
  end
  describe "#authenticate_user_oncurrent_business!" do
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

    context "when user belongs to any project's business" do
      it "must be authorized" do
        get("/api_v2_base_test", headers:)

        expect(response).to be_successful
      end
    end

    context "when user does not belongs to any project's business" do
      let(:user) { create(:user, business: another_business) }
      let!(:another_business) { create(:business) }

      it "must unauthorize request" do
        get("/api_v2_base_test", headers:)

        expect(response).to be_unauthorized
        expect(response_hash["error"]).to eq(I18n.t("devise.failure.invalid_business_headers"))
      end
    end
  end

  describe "#authenticate_current_business!" do
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

    context "when business is active" do
      it "must be authorized" do
        get("/api_v2_base_test", headers:)

        expect(response).to be_successful
      end
    end

    context "when business is inactive" do
      let!(:business) { create(:business, status: Business::Status::SUSPENDED_BY_OVERDUE) }

      it "must unauthorize request" do
        get("/api_v2_base_test", headers:)

        expect(response).to be_unauthorized
        expect(response_hash["error"]).to eq(business.message_by_type(Enums::CustomTextType::INACTIVE_BUSINESS))
      end
    end

    context "when finding business by its cloudfront distribution url" do
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Web-Domain" => "abcxyz.cloudfront.net"}, user) }
      let!(:web_application) { create(:web_application, business: business, cloudfront_distribution_url: "abcxyz.cloudfront.net") }

      it "must be authorized" do
        get("/api_v2_base_test", headers:)

        expect(response).to be_successful
      end
    end
  end

  describe "#token_header!" do
    context "with valid Web Domain header" do
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

      it "must be authorized" do
        get("/api_v2_base_test", headers:)

        expect(response).to be_successful
      end
    end

    context "with valid Api-Secret header" do
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

      it "must be authorized" do
        get("/api_v2_base_test", headers:)

        expect(response).to be_successful
      end
    end

    context "with wrong header" do
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Web-Domain" => "wrong"}, user) }

      it "must unauthorize request" do
        get("/api_v2_base_test", headers:)

        expect(response).to be_unauthorized
        expect(response_hash["error"]).to eq(I18n.t("devise.failure.invalid_business_headers"))
      end
    end
    context "without header" do
      it "must unauthorize request" do
        get "/api_v2_base_test"

        expect(response).to be_unauthorized
        expect(response_hash["error"]).to eq(I18n.t("devise.failure.missing_business_headers"))
      end
    end
  end

  describe "wrong aud header error" do
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "aud" => "api"}, user) }

    it "must render unauthorized" do
      headers["aud"] = "web"
      get("/api_v2_base_test", headers:)

      expect(response).to be_unauthorized
      expect(response.body).to eq(I18n.t("devise.failure.wrong_aud"))
    end
  end

  describe "revoked token error" do
    let!(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }
    let!(:auth_headers_overrider) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

    it "must render unauthorized" do
      get("/api_v2_base_test", headers:)

      expect(response).to be_unauthorized
      expect(response.body).to eq(I18n.t("devise.failure.revoked_token"))
    end
  end
end
