# frozen_string_literal: true

require "rails_helper"
require "sidekiq/testing"

RSpec.describe Api::V2::Users::Telemedicine::AccessesController, type: :request do
  let!(:business) { create(:business, telemedicine: true) }
  let(:authorized_user) { create(:authorized_user, :active_to_telemedicine, business:) }
  let(:user) { create(:user, business:, authorized_user:) }
  let!(:headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user)
  end
  let(:response_hash) { JSON.parse(response.body) }

  before do
    business.telemedicine_config.update!(url: "https://anyurl.com")
  end

  describe "#create" do
    context "when user has active telemedicine", :vcr do
      let!(:telemedicine_beneficiary) { create(:telemedicine_beneficiary, :active, authorized_user:, external_id: "6413606") }

      it "must generate telemedicine smart link and redirect to it" do
        Sidekiq::Testing.inline! do
          expect do
            expect(Conexa::Client).to receive(:generate_smart_link).with(authorized_user.telemedicine_external_id, business.telemedicine_config.plan).and_call_original
            post("/api/v2/users/telemedicine/access", headers:)

            expect(response).to have_http_status(:ok)
            expect(response.redirect_url).to include("https://qa-embed-paciente.conexasaude.com.br/redirecionar")
            authorized_user.reload
          end.to change(authorized_user.telemedicine_activities.where(entry_type: :accessed), :count).by(1)
        end
      end
    end

    context "when user is not registered on conexa yet" do
      before { authorized_user.update!(telemedicine_external_id: nil) }

      it "must be unprocessable" do
        post("/api/v2/users/telemedicine/access", headers:)

        expect(response).to be_forbidden
      end
    end

    context "when user does not have active telemedicine" do
      before { authorized_user.update!(telemedicine_enabled: false) }

      it "must be forbidden" do
        post("/api/v2/users/telemedicine/access", headers:)

        expect(response).to be_forbidden
      end
    end

    context "when business does not have active telemedicine" do
      let!(:business) { create(:business, telemedicine: false) }
      let!(:telemedicine_beneficiary) { create(:telemedicine_beneficiary, :active, cpf: user.cpf) }

      it "must be forbidden" do
        post("/api/v2/users/telemedicine/access", headers:)

        expect(response).to be_forbidden
      end
    end
  end
end
