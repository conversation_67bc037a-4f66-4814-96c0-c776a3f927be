# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::CreditCardsController, type: :request do
  describe "DELETE /api/v2/credit_cards/:id" do
    let(:user) { create(:user) }
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => user.business.api_secret}, user) }

    context "when credit card is not main" do
      let!(:credit_card_main) { create(:credit_card, user:, main: true) }
      let!(:credit_card_not_main) { create(:credit_card, user:, main: false) }

      it "renders no content" do
        expect { delete("/api/v2/credit_cards/#{credit_card_not_main.id}", headers:) }
          .to change(CreditCard.where(id: credit_card_not_main), :count).by(-1)

        expect(response).to be_no_content
      end
    end

    context "when credit card is main" do
      let!(:credit_card) { create(:credit_card, user:, main: true) }

      it "renders error" do
        expect { delete("/api/v2/credit_cards/#{credit_card.id}", headers:) }
          .to not_change(CreditCard, :count)

        expect(response).to be_not_found
      end
    end

    context "when credit card is from another user" do
      let(:unrelated_user) { create(:user) }
      let!(:credit_card) { create(:credit_card, user: unrelated_user, main: false) }

      it "renders error" do
        expect { delete("/api/v2/credit_cards/#{credit_card.id}", headers:) }
          .to not_change(CreditCard, :count)

        expect(response).to be_not_found
      end
    end

    context "when credit card does not exist" do
      let!(:credit_card) { create(:credit_card, user:, main: false) }

      it "renders error" do
        expect { delete("/api/v2/credit_cards/0", headers:) }
          .to not_change(CreditCard, :count)

        expect(response).to be_not_found
      end
    end
  end
end
