# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::CreditCardsController, :vcr, type: :request do
  describe "POST /api/v2/credit_cards" do
    let(:user) { create(:user) }
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => user.business.api_secret}, user) }
    let(:user_params) do
      {
        name: "  ADRIANO PESSOA SOUZA ",
        document: " 909.360.340-09  ",
        email: " <EMAIL> ",
        phone: "  (31) 9 9985-5214 "
      }
    end
    let(:address_params) do
      {
        postal_code: "45810000",
        street: "Rua Jose Lacerda",
        number: "55",
        complement: "APT 104 BL 02",
        neighborhood: "Trevo",
        city: "Belo Horizonte",
        state: "MG"
      }
    end
    let(:card_params) do
      {
        security_code: "321",
        exp_month: 2,
        exp_year: 26,
        holder: " ADRIANOP SOUZA ",
        number: "  4761 7390 0101 0036 "
      }
    end
    let(:params) do
      {
        user: user_params,
        address: address_params,
        card: card_params
      }
    end

    it "renders created" do
      expect { post("/api/v2/credit_cards", headers:, params:) }
        .to change(user.credit_cards.where(main: true, brand: :visa, last4: "0036"), :count).by(1)

      expect(response).to be_created
      expect(response.parsed_body.keys).to match_array %w[id last4 brand main]
    end

    shared_examples "card not tokenized" do
      it "renders error" do
        expect { post("/api/v2/credit_cards", headers:, params:) }
          .to not_change(CreditCard, :count)

        expect(response).to have_http_status(expected_status)
        expect(response.parsed_body["error"]).to eq expected_error_message
      end
    end

    context "with invalid user params" do
      let(:user_params) { {} }
      let(:expected_status) { :unprocessable_entity }
      let(:expected_error_message) { "Nome completo não pode ficar em branco, CPF não pode ficar em branco, E-mail não pode ficar em branco, Telefone não pode ficar em branco" }

      it_behaves_like "card not tokenized"
    end

    context "with invalid address params" do
      let(:address_params) { {} }
      let(:expected_status) { :unprocessable_entity }
      let(:expected_error_message) { "CEP não pode ficar em branco, Endereço não pode ficar em branco, Número não pode ficar em branco, Bairro não pode ficar em branco, Cidade não pode ficar em branco, Estado não pode ficar em branco" }

      it_behaves_like "card not tokenized"
    end

    context "with invalid card params" do
      let(:card_params) { {} }
      let(:expected_status) { :unprocessable_entity }
      let(:expected_error_message) { "CVV não pode ficar em branco, Mês de vencimento não pode ficar em branco, Ano de vencimento não pode ficar em branco, Nome impresso no cartão não pode ficar em branco, Número do cartão não pode ficar em branco" }

      it_behaves_like "card not tokenized"
    end

    context "with wrong security code" do
      let(:card_params) do
        {
          security_code: "32",
          exp_month: 2,
          exp_year: 26,
          holder: "ADRIANOP SOUZA",
          number: "****************"
        }
      end
      let(:expected_status) { :unprocessable_entity }
      let(:expected_error_message) { "CVV inválido." }

      it_behaves_like "card not tokenized"
    end

    context "with wrong card number" do
      let(:card_params) do
        {
          security_code: "321",
          exp_month: 2,
          exp_year: 26,
          holder: "ADRIANOP SOUZA",
          number: "5067224275805500"
        }
      end
      let(:expected_status) { :unprocessable_entity }
      let(:expected_error_message) { "Número do cartão inválido." }

      it_behaves_like "card not tokenized"
    end
  end
end
