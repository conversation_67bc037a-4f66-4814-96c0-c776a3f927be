# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::CreditCardsController, type: :request do
  describe "GET /api/v2/credit_cards" do
    let(:user) { create(:user) }
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => user.business.api_secret}, user) }
    let!(:credit_card_main) { create(:credit_card, brand: :visa, user:, main: true) }
    let!(:credit_card_not_main) { create(:credit_card, brand: :mastercard, user:, main: false) }
    let(:unrelated_user) { create(:user) }
    let!(:unrelated_credit_card) { create(:credit_card, brand: :visa, user: unrelated_user, main: true) }

    it "renders ok" do
      get("/api/v2/credit_cards", headers:)

      expect(response).to be_ok
      expect(response.parsed_body).to match_array([
        {
          "id" => credit_card_main.id,
          "last4" => credit_card_main.last4,
          "brand" => {
            "id" => "visa",
            "name" => "Visa",
            "logo" => "https://assets.alloyal.com.br/credit_card_brands/visa.png"
          },
          "main" => true
        },
        {
          "id" => credit_card_not_main.id,
          "last4" => credit_card_not_main.last4,
          "brand" => {
            "id" => "mastercard",
            "name" => "MasterCard",
            "logo" => "https://assets.alloyal.com.br/credit_card_brands/mastercard.png"
          },
          "main" => false
        }
      ])
    end
  end
end
