# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::GiftCards::OrganizationsController, type: :request do
  let(:business) { create(:business, :with_cashback, giftcard: true) }
  let(:user) { create(:user, business:) }

  let!(:organization) { create(:organization, :with_cashback, :percent_highest_discount, giftcard_redeemable: true) }
  let!(:organization_inactive_with_giftcard) { create(:organization, giftcard_redeemable: true, active: false) }
  let!(:organization_blocklisted_with_giftcard) { create(:organization, giftcard_redeemable: true) }
  let!(:organization_blocklist) do
    create(:organization_blocklist, organization: organization_blocklisted_with_giftcard, business:)
  end
  let!(:organization_without_giftcard) { create(:organization, giftcard_redeemable: false) }

  let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }
  let(:response_hash) { JSON.parse(response.body) }

  describe "GET /api/v2/gift_cards/organizations" do
    it "renders ok" do
      get("/api/v2/gift_cards/organizations", headers:)

      expect(response).to be_ok
      expect(response_hash.map(&:keys)).to all match_array %w[id logo name highest_discount highest_fund]
      expect(response_hash.pluck("id")).to eq [organization.id]
    end

    context "when organization does not have discount" do
      let!(:organization) { create(:organization, :with_cashback, giftcard_redeemable: true) }

      it "renders ok" do
        get("/api/v2/gift_cards/organizations", headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all match_array %w[id logo name highest_fund]
        expect(response_hash.pluck("id")).to eq [organization.id]
      end
    end

    context "when organization does not have cashback" do
      let!(:organization) { create(:organization, :percent_highest_discount, giftcard_redeemable: true) }

      it "renders ok" do
        get("/api/v2/gift_cards/organizations", headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all match_array %w[id logo name highest_discount]
        expect(response_hash.pluck("id")).to eq [organization.id]
      end
    end

    context "when business does not have cashback" do
      let(:business) { create(:business, cashback: false) }

      it "renders ok" do
        get("/api/v2/gift_cards/organizations", headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all match_array %w[id logo name highest_discount]
        expect(response_hash.pluck("id")).to eq [organization.id]
      end
    end

    context "when currency is points" do
      let(:business) { create(:business, :with_cashback, giftcard: true, currency: :points, fair_value: 0.03) }
      let!(:organization) { create(:organization, :with_cashback, giftcard_redeemable: true) }
      let!(:branch) { create(:branch, :online, organization:) }
      let!(:fund_redemption_config) { create(:fund_redemption_config, :points, :giftcard, business:, fair_value: 0.03) }
      let!(:available_giftcards) do
        [30, 60].each { |price| create(:giftcard, branches: [branch], price:, voucher_bucket: create(:voucher_bucket)) }
      end
      let!(:unavailable_giftcard) do
        create(:giftcard, branches: [branch], price: 20, voucher_bucket: create(:voucher_bucket), available: false)
      end

      before { organization.refresh_giftcard_min_price }

      it "renders ok" do
        get("/api/v2/gift_cards/organizations", headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all match_array %w[id logo name min_price]
        expect(response_hash.pluck("id")).to eq [organization.id]
        expect(response_hash.pluck("min_price")).to eq [1000]
      end
    end
  end
end
