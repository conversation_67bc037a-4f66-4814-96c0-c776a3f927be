require "rails_helper"

RSpec.describe Api::V2::SubscriptionsController, type: :request do
  include ActiveSupport::NumberHelper
  describe "GET #index" do
    let!(:business) { create(:business) }
    let!(:user) { create(:user, business: business) }
    let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => user.business.api_secret}, user) }

    context "when the user is authenticated" do
      it "returns http success" do
        subscription_config = create(:subscription_config, business:)
        plan = create(:plan, subscription_config:, business:)
        subscription = create(:subscription, active: true, user:, subscription_config:, plan:)
        create(:subscription_installment, subscription:)

        get("/api/v2/subscriptions", headers: headers)

        expect(response).to be_ok
      end

      it "returns the subscription data with correct attributes" do
        subscription_config = create(:subscription_config, business:, cancellation_description: "Lamentamos sua saída!")
        plan = create(:plan, subscription_config:, business:)
        subscription = create(:subscription, user:, subscription_config:, plan:, active: false)
        create(:subscription_installment, subscription:)

        get("/api/v2/subscriptions", headers: headers)
        json_response = JSON.parse(response.body)

        expect(json_response["subscription"]["title"]).to eq(subscription_config.title)
        expect(json_response["subscription"]["description"]).to eq(subscription_config.description)
        expect(json_response["subscription"]["background_image"]["large"]).to eq(subscription_config.image_desktop.url)
        expect(json_response["subscription"]["background_image"]["small"]).to eq(subscription_config.image_mobile.url)
        expect(json_response["subscription"]["terms_of_use"]).to eq("URL")
        expect(json_response["subscription"]["current_subscription"]).to eq(nil)
        expect(json_response["subscription"]["cancellation_description"]).to eq("Lamentamos sua saída!")
      end

      it "returns the supports data with correct attributes" do
        subscription_config = create(:subscription_config, business:)
        plan_one = create(:plan, business:)
        plan_two = create(:plan, business:)
        subscription_group = create(:subscription_group, subscription_config:)
        create(:subscription_group_plan, subscription_group:, plan: plan_one)
        create(:subscription_group_plan, subscription_group:, plan: plan_two)
        card_support_one = create(:card, business:, view_type: "support")
        card_support_two = create(:card, business:, view_type: "support")
        subscription = create(:subscription, active: true, user:, subscription_config:, plan: plan_one)
        create(:subscription_installment, subscription:)

        get("/api/v2/subscriptions", headers: headers)
        json_response = JSON.parse(response.body)
        expect(json_response["supports"]["title"]).to eq("Vantagens das assinaturas disponíveis")
        expect(json_response["supports"]["cards"].size).to eq(2)
        expect(json_response["supports"]["cards"][0]["title"]).to eq(card_support_one.title)
        expect(json_response["supports"]["cards"][0]["description"]).to eq(card_support_one.description)
        expect(json_response["supports"]["cards"][1]["title"]).to eq(card_support_two.title)
        expect(json_response["supports"]["cards"][1]["description"]).to eq(card_support_two.description)
      end

      it "returns the group_sections data with correct attributes" do
        subscription_config = create(:subscription_config, business:)
        plan = create(:plan, business:)
        subscription_group = create(:subscription_group, subscription_config:)
        create(:subscription_group_plan, subscription_group: subscription_group, plan:)
        subscription = create(:subscription, active: true, user:, subscription_config:, plan: plan)
        create(:subscription_installment, subscription:)

        get("/api/v2/subscriptions", headers: headers)
        json_response = JSON.parse(response.body)

        expect(json_response["group_sections"]["title"]).to eq("Escolha a assinatura")
        expect(json_response["group_sections"]["groups"].size).to eq(1)

        expect(json_response["group_sections"]["groups"][0]["id"]).to eq(subscription_group.id)
        expect(json_response["group_sections"]["groups"][0]["name"]).to eq(subscription_group.name)
        expect(json_response["group_sections"]["groups"][0]["display_name"]).to eq(subscription_group.name)
      end

      it "returns the plans data with correct attributes with subscription inactive" do
        subscription_config = create(:subscription_config, business:)
        plan_one = create(:plan, business:, benefit_description: "+1000 pontos todo mês", points: 1000)
        plan_two = create(:plan, business:, benefit_description: "+2000 pontos todo mês", points: 2000)
        subscription_group = create(:subscription_group, subscription_config:)
        create(:subscription_group_plan, subscription_group:, plan: plan_one)
        create(:subscription_group_plan, subscription_group:, plan: plan_two)
        subscription = create(:subscription, user:, subscription_config:, plan: plan_one, active: false)
        create(:subscription_installment, subscription:)

        get("/api/v2/subscriptions", headers: headers)
        json_response = JSON.parse(response.body)

        plans_group1 = json_response["group_sections"]["groups"][0]["plans"][0]
        plans_group2 = json_response["group_sections"]["groups"][0]["plans"][1]

        expect(json_response["group_sections"]["groups"][0]["plans"].size).to eq(2)

        expect(plans_group1["id"]).to eq(plan_one.id)
        expect(plans_group1["button"]["text"]).to eq("Assinar #{plan_one.title.capitalize}")
        expect(plans_group1["name"]).to eq(plan_one.title)
        expect(plans_group1["recurrence"]).to eq(I18n.t("plan.recurrence.#{plan_one.recurrence}"))
        expect(plans_group1["price_label"]).to eq(I18n.t("plan.modality.#{plan_one.recurrence}"))
        expect(plans_group1["price"]).to eq(number_to_currency(plan_one.price))
        expect(plans_group1["benefit"]).to eq("+1000 pontos todo mês")
        expect(plans_group1["theme"]["card_color"]).to eq(plan_one.background_color)
        expect(plans_group1["theme"]["title_color"]).to eq(plan_one.font_color)

        expect(plans_group2["id"]).to eq(plan_two.id)
        expect(plans_group2["button"]["text"]).to eq("Assinar #{plan_two.title.capitalize}")
        expect(plans_group2["name"]).to eq(plan_two.title)
        expect(plans_group2["recurrence"]).to eq(I18n.t("plan.recurrence.#{plan_two.recurrence}"))
        expect(plans_group2["price_label"]).to eq(I18n.t("plan.modality.#{plan_two.recurrence}"))
        expect(plans_group2["price"]).to eq(number_to_currency(plan_two.price))
        expect(plans_group2["benefit"]).to eq("+2000 pontos todo mês")
        expect(plans_group2["theme"]["card_color"]).to eq(plan_two.background_color)
        expect(plans_group2["theme"]["title_color"]).to eq(plan_two.font_color)
      end

      it "returns the plans data with correct attributes with subscription active" do
        subscription_config = create(:subscription_config, business:)
        plan_one = create(:plan, business:, benefit_description: "+1000 pontos todo mês", points: 1000)
        plan_two = create(:plan, business:, benefit_description: "+2000 pontos todo mês", points: 2000)
        subscription_group = create(:subscription_group, subscription_config:)
        create(:subscription_group_plan, subscription_group:, plan: plan_one)
        create(:subscription_group_plan, subscription_group:, plan: plan_two)
        subscription = create(:subscription, user:, subscription_config:, plan: plan_one, active: true)
        create(:subscription_installment, subscription:)

        get("/api/v2/subscriptions", headers: headers)
        json_response = JSON.parse(response.body)

        plans_group1 = json_response["group_sections"]["groups"][0]["plans"][0]
        plans_group2 = json_response["group_sections"]["groups"][0]["plans"][1]

        expect(json_response["group_sections"]["groups"][0]["plans"].size).to eq(2)

        expect(plans_group1["id"]).to eq(plan_one.id)
        expect(plans_group1["button"]["text"]).to eq("Plano Atual")
        expect(plans_group1["name"]).to eq(plan_one.title)
        expect(plans_group1["recurrence"]).to eq(I18n.t("plan.recurrence.#{plan_one.recurrence}"))
        expect(plans_group1["price_label"]).to eq(I18n.t("plan.modality.#{plan_one.recurrence}"))
        expect(plans_group1["price"]).to eq(number_to_currency(plan_one.price))
        expect(plans_group1["benefit"]).to eq("+1000 pontos todo mês")
        expect(plans_group1["theme"]["card_color"]).to eq(plan_one.background_color)
        expect(plans_group1["theme"]["title_color"]).to eq(plan_one.font_color)

        expect(plans_group2["id"]).to eq(plan_two.id)
        expect(plans_group2["button"]["text"]).to eq("Alterar para o #{plan_two.title.capitalize}")
        expect(plans_group2["name"]).to eq(plan_two.title)
        expect(plans_group2["recurrence"]).to eq(I18n.t("plan.recurrence.#{plan_two.recurrence}"))
        expect(plans_group2["price_label"]).to eq(I18n.t("plan.modality.#{plan_two.recurrence}"))
        expect(plans_group2["price"]).to eq(number_to_currency(plan_two.price))
        expect(plans_group2["benefit"]).to eq("+2000 pontos todo mês")
        expect(plans_group2["theme"]["card_color"]).to eq(plan_two.background_color)
        expect(plans_group2["theme"]["title_color"]).to eq(plan_two.font_color)
      end

      it "returns the faqs data with correct attributes" do
        subscription_config = create(:subscription_config, business:)
        plan_one = create(:plan, business:)
        plan_two = create(:plan, business:)
        subscription_group = create(:subscription_group, subscription_config:)
        create(:subscription_group_plan, subscription_group:, plan: plan_one)
        create(:subscription_group_plan, subscription_group:, plan: plan_two)
        card_faq_one = create(:card, business:, view_type: "faq")
        card_faq_two = create(:card, business:, view_type: "faq")
        subscription = create(:subscription, user:, subscription_config:, plan: plan_one, active: true)
        create(:subscription_installment, subscription:)

        get("/api/v2/subscriptions", headers: headers)
        json_response = JSON.parse(response.body)

        expect(json_response["faqs"]["title"]).to eq("Vantagens de ter uma assinatura")
        expect(json_response["faqs"]["cards"].size).to eq(2)
        expect(json_response["faqs"]["cards"][0]["title"]).to eq(card_faq_one.title)
        expect(json_response["faqs"]["cards"][0]["description"]).to eq(card_faq_one.description)
        expect(json_response["faqs"]["cards"][1]["title"]).to eq(card_faq_two.title)
        expect(json_response["faqs"]["cards"][1]["description"]).to eq(card_faq_two.description)
      end

      it "returns current subscription nil" do
        subscription_config = create(:subscription_config, business:)
        plan_one = create(:plan, business:)
        plan_two = create(:plan, business:)
        subscription_group = create(:subscription_group, subscription_config:)
        create(:subscription_group_plan, subscription_group:, plan: plan_one)
        create(:subscription_group_plan, subscription_group:, plan: plan_two)
        create(:card, business:, view_type: "faq")
        create(:card, business:, view_type: "faq")
        subscription = create(:subscription, user:, subscription_config:, plan: plan_one, active: false)
        create(:subscription_installment, subscription:)

        get("/api/v2/subscriptions", headers: headers)
        json_response = JSON.parse(response.body)

        expect(json_response["subscription"]["current_subscription"]).to eq(nil)
      end

      it "returns current subscription with status active" do
        subscription_config = create(:subscription_config, business:)
        plan_one = create(:plan, business:)
        plan_two = create(:plan, business:)
        subscription_group = create(:subscription_group, subscription_config:)
        create(:subscription_group_plan, subscription_group:, plan: plan_one)
        create(:subscription_group_plan, subscription_group:, plan: plan_two)
        create(:card, business:, view_type: "faq")
        create(:card, business:, view_type: "faq")
        subscription = create(:subscription, user:, subscription_config:, plan: plan_one, active: true)
        create(:subscription_installment, subscription:)

        get("/api/v2/subscriptions", headers: headers)
        json_response = JSON.parse(response.body)

        expect(json_response["subscription"]["current_subscription"]).not_to eq(nil)
        expect(json_response["subscription"]["current_subscription"]["id"]).to eq(subscription.id)
        expect(json_response["subscription"]["current_subscription"]["plan_id"]).to eq(subscription.plan_id)
        expect(json_response["subscription"]["current_subscription"]["name"]).to eq(plan_one.title)
        expect(json_response["subscription"]["current_subscription"]["billing"]["status"]).to eq("active")
      end

      it "returns the current subscription with due date formatted" do
        subscription_config = create(:subscription_config, business:)
        plan = create(:plan, subscription_config:, business:)
        subscription = create(:subscription, user:, subscription_config:, plan:, active: true)
        create(:subscription_installment, subscription:)

        get("/api/v2/subscriptions", headers: headers)
        json_response = JSON.parse(response.body)
        due_at = subscription.payment_next_date

        expect(json_response["subscription"]["current_subscription"]).not_to eq(nil)
        expect(json_response["subscription"]["current_subscription"]["billing"]["message"]).to eq("Próxima fatura: #{I18n.l(due_at, format: '%d de %B de %Y')}")
        expect(json_response["subscription"]["current_subscription"]["billing"]["due_date"]).to eq(due_at.strftime("%d/%m/%Y"))
      end
    end

    context "when the user is not authenticated" do
      let(:user) { create(:user, api_token: nil) }

      it "returns http unauthorized" do
        create(:subscription_config, business: business)
        headers = Devise::JWT::TestHelpers.auth_headers({"Authorization" => user.api_token}, user)

        get("/api/v2/subscriptions", headers: headers)
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
