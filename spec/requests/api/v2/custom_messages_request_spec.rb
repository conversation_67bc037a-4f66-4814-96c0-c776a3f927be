require "rails_helper"

RSpec.describe Api::V2::CustomMessagesController, type: :request do
  let(:serializer_keys) { %w[value text] }
  let(:response_hash) { JSON.parse(response.body) }

  describe "GET /api/v2/custom_messages" do
    context "when business is inactive" do
      let!(:business) { create(:business, active: false, cashback_requestable_amount: 20) }
      let(:user) { create(:user, business:, cpf: "***********") }
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

      it "returns screen texts for the business" do
        get("/api/v2/custom_messages", headers:)

        expect(response).to be_successful
      end
    end
    context "when user is cashback transfer receiver" do
      let!(:business) do
        create(:business,
          :with_cashback,
          currency: "points",
          fair_value: 0.03,
          cashback_requestable_amount: 30)
      end
      let(:user) { create(:user, business:, cpf: "***********") }
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

      let(:serialized_custom_text_types) do
        Enums::CustomTextType.screen_texts.select do |message_type|
          !message_type.match?(/cashback_batch/) && !message_type.match?(/cashback_discount/)
        end.push("wallet_balance_in_transfer", "wallet_balance_requestable", "wallet_balance_pending", "wallet_balance_transfer_to_user")
      end

      context "when user has points to expire the next 15 days" do
        let!(:fund) { create(:fund, :points, :available, user:, credited: true, amount: 1500, expires_at: 14.days.from_now) }

        it "returns screen texts for the business with placeholders applied" do
          get("/api/v2/custom_messages", headers:)

          expect(response).to be_ok
          expect(response_hash.map(&:keys)).to all eq serializer_keys
          message_types = response_hash.map { _1["value"] }
          expect(message_types).to match_array(serialized_custom_text_types)
          expect(message_types).to include(/wallet_balance/)
          expect(message_types).not_to include(/cashback_batch/)
          expect(message_types).not_to include(/cashback_discount/)
          pending_message = response_hash.find { _1["value"] == "wallet_balance_pending" }
          expect(pending_message["text"]).to eq("Você poderá resgatar seu dinheiro a partir de R$ 30,00")
          withdrawal_info = response_hash.find { _1["value"] == "wallet_withdrawal_info" }
          expect(withdrawal_info["text"]).to eq("A transferência do saldo total será feita para o CPF da pessoa logada")
        end
      end

      context "when user does not point to expire the next 15 days" do
        let!(:fund) { create(:fund, :points, :available, user:, credited: true, amount: 1500, expires_at: 16.days.from_now) }

        it "returns screen texts for the business with placeholders applied" do
          get("/api/v2/custom_messages", headers:)

          expect(response).to be_ok
          expect(response_hash.map(&:keys)).to all eq serializer_keys
          expect(response_hash.pluck("value")).not_to include "wallet_point_expiration"
        end
      end
    end

    context "when business is cashback transfer receiver" do
      let!(:business) do
        create(:business,
          :with_cashback,
          cashback_wallet_destination: Wallet::Kind::MEMBERSHIP,
          cashback_requestable_amount: 30)
      end
      let(:user) { create(:user, business:, cpf: "***********") }
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

      it "returns screen texts for the business with placeholders applied" do
        get("/api/v2/custom_messages", headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all eq serializer_keys
        message_types = response_hash.map { _1["value"] }
        expect(message_types).to include(/wallet_balance/)
        expect(message_types).not_to include(/cashback_batch/)
        expect(message_types).not_to include(/cashback_discount/)
        pending_message = response_hash.find { _1["value"] == "wallet_balance_pending" }
        expect(pending_message["text"]).to eq("Você poderá usar seu dinheiro a partir de R$ 30,00")
        withdrawal_info = response_hash.find { _1["value"] == "wallet_withdrawal_info" }
        expect(withdrawal_info["text"]).to eq("A transferência do saldo total será feita para o CPF da pessoa logada")
      end
    end

    context "when business customized a screen text" do
      let!(:business) do
        create(:business,
          :with_cashback,
          cashback_wallet_destination: Wallet::Kind::CASHBACK,
          cashback_requestable_amount: 30)
      end
      let(:text) { "Você poderá usar seu cashback a partir de {{cashback_requestable_amount}}" }
      let!(:custom_text) do
        create(:custom_text, business:, message_type: :cashback_batch_pending, message: text)
      end
      let(:user) { create(:user, business:, cpf: "***********") }
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

      it "returns business customized text with placeholders applied" do
        get("/api/v2/custom_messages", headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all eq serializer_keys
        message_types = response_hash.map { _1["value"] }
        expect(message_types).to include(/wallet_balance/)
        expect(message_types).not_to include(/cashback_batch/)
        expect(message_types).not_to include(/cashback_discount/)
        pending_message = response_hash.find { _1["value"] == "wallet_balance_pending" }
        expect(pending_message["text"]).to eq("Você poderá usar seu cashback a partir de R$ 30,00")
        withdrawal_info = response_hash.find { _1["value"] == "wallet_withdrawal_info" }
        expect(withdrawal_info["text"]).to eq("A transferência do saldo total será feita para o CPF da pessoa logada")
        wallet_cashback_redemption = response_hash.find { _1["value"] == "wallet_cashback_redemption" }
        expect(wallet_cashback_redemption["text"]).to eq("Você poderá solicitar o resgate do seu dinheiro quando alcançar R$ 30,00 de saldo.")
      end
    end

    context "when business customized a screen text and does not have cashback" do
      let(:business) { create(:business, cashback: false, cashback_requestable_amount: nil) }
      let!(:custom_text) do
        create(:custom_text, business:, message_type: :login_button_text, message: "Acessar")
      end
      let(:user) { create(:user, business:) }
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret}, user) }

      it "renders ok" do
        get("/api/v2/custom_messages", headers:)

        expect(response).to be_ok
        expect(response_hash.map(&:keys)).to all match_array serializer_keys
        expect(response_hash).to include("value" => custom_text.message_type, "text" => custom_text.message)
      end
    end

    context "without user" do
      let(:business) { create(:business) }

      it "renders ok" do
        get "/api/v2/custom_messages", headers: {"Api-Secret" => business.api_secret}

        expect(response).to be_ok
      end
    end

    context "with no headers" do
      it "renders error" do
        get "/api/v2/custom_messages"

        expect(response).to be_unauthorized
      end
    end
  end
end
