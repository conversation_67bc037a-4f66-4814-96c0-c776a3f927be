# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::Integration::WebPostos::CancelamentoVendaController, type: :request do
  let(:headers) { {Authorization: "Bearer #{create(:integration_token).token}"} }
  let(:branch) { create(:branch, cnpj: "12345678901234") }
  let(:bucket) { create(:voucher_bucket) }
  let(:promotion) { create(:promotion, web_postos: true, voucher_bucket: bucket) }
  let(:order) { create(:order, :completed, branch:, promotion:) }
  let!(:voucher) { create(:voucher, bucket:, order:) }

  describe "#create" do
    let(:params) do
      {
        codigoEmpresa: branch.cnpj,
        codigoVenda: "123",
        idTransacao: order.id
      }
    end

    context "when order exists" do
      context "with voucher" do
        it "cancels the order and voucher" do
          post("/api/integration/webhooks/web_postos/cancelamento_venda", headers:, params:)

          expect(response).to have_http_status(:ok)
          expect(order.reload).to be_status_canceled
          expect(voucher.reload.canceled_at).to be_present
          expect(JSON.parse(response.body)).to eq({
            "codigoEmpresa" => branch.cnpj,
            "codigoVenda" => "123",
            "idTransacao" => order.id.to_s
          })
        end
      end

      context "without voucher" do
        it "cancels only the order" do
          post("/api/integration/webhooks/web_postos/cancelamento_venda", headers:, params:)

          expect(response).to have_http_status(:ok)
          expect(order.reload).to be_status_canceled
          expect(JSON.parse(response.body)).to eq({
            "codigoEmpresa" => branch.cnpj,
            "codigoVenda" => "123",
            "idTransacao" => order.id.to_s
          })
        end
      end
    end

    context "when order does not exist" do
      let(:params) do
        {
          codigoEmpresa: branch.cnpj,
          codigoVenda: "123",
          idTransacao: 999999
        }
      end

      it "returns not found status" do
        post("/api/integration/webhooks/web_postos/cancelamento_venda", headers:, params:)

        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)).to eq({"error" => "Registro não encontrado."})
      end
    end

    context "when order is already completed" do
      let(:completed_order) { create(:order, :pending, branch: branch, promotion: promotion) }
      let(:params) do
        {
          codigoEmpresa: branch.cnpj,
          codigoVenda: "123",
          idTransacao: completed_order.id
        }
      end

      it "returns not found status" do
        post("/api/integration/webhooks/web_postos/cancelamento_venda", headers:, params:)

        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)).to eq({"error" => "Registro não encontrado."})
      end
    end

    context "when order promotion is not web_postos" do
      let(:non_web_postos_promotion) { create(:promotion, web_postos: false) }
      let(:order) { create(:order, :canceled, branch:, promotion: non_web_postos_promotion) }
      let(:params) do
        {
          codigoEmpresa: branch.cnpj,
          codigoVenda: "123",
          idTransacao: order.id
        }
      end

      it "returns not found status" do
        post("/api/integration/webhooks/web_postos/cancelamento_venda", headers:, params:)

        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)).to eq({"error" => "Registro não encontrado."})
      end
    end
  end
end
