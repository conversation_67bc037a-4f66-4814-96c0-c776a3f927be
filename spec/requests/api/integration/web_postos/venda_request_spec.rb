# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::Integration::WebPostos::VendaController, type: :request do
  let(:headers) { {Authorization: "Bearer #{create(:integration_token).token}"} }
  let(:branch) { create(:branch, cnpj: "12345678901234") }
  let(:promotion) { create(:promotion, web_postos: true) }
  let(:order) { create(:order, branch:, promotion:) }

  describe "#create" do
    context "when order exists" do
      it "updates order status to completed" do
        params = {
          codigoEmpresa: branch.cnpj,
          codigoVenda: "XPTO",
          idTransacao: order.id
        }

        expect {
          post("/api/integration/webhooks/web_postos/venda", headers:, params:)
        }.to change { order.reload.status }.to("completed")

        expect(response).to have_http_status(:ok)
        expect(response.body).to eq({
          codigoEmpresa: branch.cnpj,
          codigoVenda: "XPTO",
          idTransacao: order.id.to_s
        }.to_json)
      end
    end

    context "when order does not exist" do
      it "returns not found" do
        params = {
          codigoEmpresa: "99999999999999",
          idTransacao: "non_existent_id"
        }

        post("/api/integration/webhooks/web_postos/venda", headers:, params:)

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
