# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::Integration::UsersController, type: :request do
  let!(:oab) { create(:business, :oab) }
  let!(:user) { create(:user, cpf:, business: oab) }
  let(:cpf) { "***********" }
  def auth_header
    {Authorization: "Bearer #{create(:integration_token).token}"}
  end

  describe "GET /empresas/:cpf" do
    let!(:araujo) { create(:organization, name: "Drogaria Araujo") }
    it "returns successful, with created user" do
      get "/empresas/#{cpf}"
      expect(response).to have_http_status(:ok)
      response_hash = JSON.parse(response.body)
      expect(response_hash.keys).to match_array([
        "Vida", "Existente", "Mensagem"
      ])
      expect(response_hash["Existente"]).to be_truthy
    end

    it "returns successful, with not created user" do
      get "/empresas/***********"
      expect(response).to have_http_status(:ok)
      response_hash = JSON.parse(response.body)
      expect(response_hash.keys).to match_array([
        "Vida", "Existente", "Mensagem"
      ])
      expect(response_hash["Existente"]).to be_falsey
    end
  end

  describe "GET /api/lecupon/:cpf" do
    let!(:extrafarma) { create(:organization, name: "Extrafarma") }

    it "returns unauthorized for invalid credentials" do
      get "/api/lecupon/#{cpf}", headers: {Authorization: "Bearer 123"}
      expect(response).to have_http_status(:unauthorized)
    end

    it "returns successful, with created user" do
      get "/api/lecupon/#{cpf}", headers: auth_header
      expect(response).to have_http_status(:ok)
      response_value = JSON.parse(response.body)
      expect(response_value).to be_truthy
    end

    it "returns successful, with not created user" do
      get "/api/lecupon/***********", headers: auth_header
      expect(response).to have_http_status(:ok)
      response_value = JSON.parse(response.body)
      expect(response_value).to be_falsey
    end
  end

  describe "GET /api/integration/users/:cpf" do
    let!(:organization) { create(:organization, name: "Panvel") }

    it "returns successful with truthy response value" do
      get "/api/integration/users/#{cpf}", headers: auth_header, params: {integration: "panvel"}
      expect(response).to have_http_status(:ok)
      response_value = JSON.parse(response.body)
      expect(response_value).to eq({"status" => "Elegível", "cpf" => user.cpf, "name" => user.name})
    end

    context "when cpf does not exist" do
      it "returns successful with falsey response_value" do
        get "/api/integration/users/#{cpf}", headers: auth_header, params: {integration: "xpto"}
        expect(response).to have_http_status(:ok)
        response_value = JSON.parse(response.body)
        expect(response_value).to eq({"status" => "Não elegível"})
      end
    end

    context "when auth user does not exist" do
      it "returns successful with falsey response_value" do
        get "/api/integration/users/123", headers: auth_header, params: {integration: "panvel"}
        expect(response).to have_http_status(:ok)
        response_value = JSON.parse(response.body)
        expect(response_value).to eq({"status" => "Não elegível"})
      end
    end

    context "when is unknown integration exist" do
      let!(:rd_organization) { create(:organization, name: "Droga Raia") }

      it "returns successful with default response_value" do
        get "/api/integration/users/#{cpf}", headers: auth_header, params: {integration: "rd"}

        expect(response).to have_http_status(:ok)
        response_hash = JSON.parse(response.body)
        expect(response_hash.keys).to match_array(["cpf", "name", "status"])
        expect(response_hash["status"]).to eq("Elegível")
      end
    end
  end
end
