# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::Integration::VoucherAvailabilityController, type: :request do
  let(:headers) { {Authorization: "Bearer #{create(:integration_token).token}"} }

  describe "#create" do
    let(:serialized_keys) do
      %w[idTransacao mensagemErro produtos tipoCodigo valorCashBack valorPorUnidadeDesconto]
    end

    context "when voucher code does not exist" do
      let(:business) { create(:business) }
      let(:user) { create(:user, business:) }
      let(:produtos) do
        {codigoVoucher: "XPTO-VOUCHER", codigoEmpresa: cnpj}
      end

      it "returns not found" do
        voucher, cnpj = create_voucher(business:, code: "JM80WZVX", user:)
        params = {codigoVoucher: "XPTO-VOUCHER", codigoEmpresa: cnpj}

        post("/api/integration/voucher_availability", headers:, params:)

        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body["mensagemErro"]).to eq("Registro não encontrado.")
        expect(voucher.used_at).to be_nil
      end
    end

    context "when voucher has already been verified" do
      let(:business) { create(:business) }
      let(:user) { create(:user, business:) }

      it "returns not found" do
        voucher, cnpj = create_voucher(business:, code: "JM80WZVX", used_at: DateTime.current.yesterday, user:)
        params = {codigoVoucher: "JM80WZVX", codigoEmpresa: cnpj}

        expect do
          post("/api/integration/voucher_availability", headers:, params:)
        end.to not_change { voucher.reload.used_at }

        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body["mensagemErro"]).to eq("Registro não encontrado.")
      end
    end

    context "when voucher from web_postos promotion flag has not yeat been verified" do
      let(:business) { create(:business) }
      let(:user) { create(:user, business:) }

      it "returns ok" do
        voucher, cnpj = create_voucher(business:, code: "JM80WZVX", user:, web_postos: true)
        params = {
          codigoVoucher: "JM80WZVX",
          codigoEmpresa: cnpj,
          produtos: [
            {
              valorVenda: 129.75,
              quantidade: 25,
              valorUnitario: 5.19
            },
            {
              valorVenda: 63.84,
              quantidade: 12.3,
              valorUnitario: 5.19
            }
          ]
        }

        post("/api/integration/voucher_availability", headers:, params:)

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.keys).to eq(serialized_keys)
        expect(response.parsed_body["produtos"].count).to eq(2)
        expect(response.parsed_body["valorCashBack"]).to eq(0.0)
        expect(response.parsed_body["valorPorUnidadeDesconto"]).to eq(19.36)
        expect(voucher.reload.used_at).not_to be_nil
      end

      it "returns ok with discount type fixed" do
        voucher, cnpj = create_voucher(business:, code: "JM80WZVX", user:, web_postos: true, discount_type: "fixed")
        params = {
          codigoVoucher: "JM80WZVX",
          codigoEmpresa: cnpj,
          produtos: [
            {
              valorVenda: 129.75,
              quantidade: 25,
              valorUnitario: 5.19
            },
            {
              valorVenda: 63.84,
              quantidade: 12.3,
              valorUnitario: 5.19
            }
          ]
        }

        post("/api/integration/voucher_availability", headers:, params:)

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.keys).to eq(serialized_keys)
        expect(response.parsed_body["produtos"].count).to eq(2)
        expect(response.parsed_body["valorCashBack"]).to eq(0.0)
        expect(response.parsed_body["valorPorUnidadeDesconto"]).to eq(10.0)
        expect(voucher.reload.used_at).not_to be_nil
      end
    end

    context "when voucher from promotion without web_postos flag has not yeat been verified" do
      let(:business) { create(:business) }
      let(:user) { create(:user, business:) }
      let(:serialized_keys) do
        %w[cnpj coupon_id description discount_type discount_value created_at voucher_number]
      end

      it "returns ok" do
        _voucher, cnpj = create_voucher(business:, code: "JM80WZVX", user:, web_postos: false)
        params = {codigoVoucher: "JM80WZVX", codigoEmpresa: cnpj}

        post("/api/integration/voucher_availability", headers:, params:)

        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body["mensagemErro"]).to eq("Registro não encontrado.")
      end
    end

    context "with invalid params" do
      it "return error when cnpj is invalid" do
        params = {
          codigoVoucher: "ALLGAS-VOUCHER-1234",
          codigoEmpresa: "INVALIDCNPJ"
        }
        post("/api/integration/voucher_availability", headers:, params:)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body["mensagemErro"]).to eq("codigoEmpresa é inválido")
      end

      it "return error when code is empty" do
        params = {
          codigoVoucher: "",
          codigoEmpresa: FFaker::IdentificationBR.cnpj
        }

        post("/api/integration/voucher_availability", headers:, params:)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body["mensagemErro"]).to eq("codigoVoucher é obrigatório")
      end
    end

    def create_voucher(business:, code:, user:, used_at: nil, web_postos: false, discount_type: "percent")
      organization = create(:organization)
      create(:organization_profile, organization:, business:)
      branch = create(:branch, organization:)
      bucket = create(:voucher_bucket)
      promotion = create(
        :promotion,
        :percent_discount,
        organization:,
        business:,
        dynamic_voucher: true,
        code: "ABC123",
        voucher_type: "dynamic",
        provider: nil,
        provider_type: "regionalized",
        status: "no_voucher",
        voucher_bucket: bucket,
        web_postos:
      )
      coupon = create(:cupon, :fixed, promotion:, branch:)
      voucher = create(:voucher, bucket:, code:, used_at:)
      cnpj = branch.cnpj
      create(:order, user:, cupon: coupon, voucher:, discount_value: 10, discount_type:)

      [voucher, cnpj]
    end
  end
end
