require "rails_helper"

RSpec.describe Api::Integration::SignInController, type: :request do
  describe "#create" do
    let!(:integration_token) { create(:integration_token, :with_credentials) }
    let(:valid_params) do
      {
        username: "user123",
        password: "pass123"
      }
    end

    context "with valid credentials" do
      before do
        post "/api/integration/sign_in", params: valid_params
      end

      it "returns success status and user data" do
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body["id"]).to eq(integration_token.id)
        expect(response.parsed_body["username"]).to eq(integration_token.username)
      end

      it "returns Authorization header with Bear<PERSON> token" do
        expect(response.headers["Authorization"]).to eq("Bearer #{integration_token.token}")
      end
    end

    context "with invalid credentials" do
      before do
        post "/api/integration/sign_in", params: {username: "user123", password: "wrong_password"}
      end

      it "returns unauthorized status" do
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body["error"]).to eq("Credenciais inválidas.")
      end

      it "returns error message" do
        expect(JSON.parse(response.body)["error"]).to eq(I18n.t("devise.failure.invalid"))
      end
    end

    context "with missing parameters" do
      it "returns bad request status" do
        post "/api/integration/sign_in", params: {username: "user123"}

        expect(response).to have_http_status(:bad_request)
        expect(response.parsed_body["error"]).to eq("A requisição falhou devido a ausência de parâmetro: senha")
      end
    end
  end
end
