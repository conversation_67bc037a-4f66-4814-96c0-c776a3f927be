# frozen_string_literal: true

require "rails_helper"

RSpec.describe Admin::V1::Navigations::CategoriesController, type: :request do
  describe "#index" do
    let(:json_parse_response_body) { JSON.parse(response.body) }

    context "when account is admin_lecupon" do
      let!(:admin) { create(:client_employee, :admin_lecupon) }
      let!(:headers) { Devise::JWT::TestHelpers.auth_headers({}, admin) }
      let(:serialized_keys) { %w[id name] }

      context "organization_coupon" do
        it "return active categories" do
          project = create(:business)
          exclusive_active_category = create(:category, :active, title: "Informática", business: project)
          active_category = create(:category, :active, title: "Pão de Queijo fresco")
          _inactive_category = create(:category, :inactive, business: project)
          _inactive_category = create(:category, :inactive)

          params = {project_id: project.id, search: "queijo"}

          get("/admin/v1/navigations/categories", headers:, params:)

          expect(response).to have_http_status(:success)
          expect(json_parse_response_body.map(&:keys)).to all(match_array(serialized_keys))
          expect(json_parse_response_body.count).to eq(2)
          expect(json_parse_response_body.pluck("id")).to match_array([active_category.id, exclusive_active_category.id])
        end
      end

      it "no returns blocklisted categories" do
        project = create(:business)
        active_category = create(:category, :active, business: project)
        create(:category_blocklist, category: active_category, business: project)

        params = {project_id: project.id, search: "queijo"}

        get("/admin/v1/navigations/categories", headers:, params:)

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.count).to eq(0)
      end

      it "returns blocklisted categories from another project" do
        project = create(:business)
        active_category = create(:category, :active, title: "Informática", business: project)
        another_project = create(:business)
        create(:category_blocklist, category: active_category, business: another_project)

        params = {project_id: project.id, search: "informa"}

        get("/admin/v1/navigations/categories", headers:, params:)

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.map(&:keys)).to all(match_array(serialized_keys))
        expect(json_parse_response_body.count).to eq(1)
        expect(json_parse_response_body.first["id"]).to eq(active_category.id)
      end

      it "no returns categories from another project" do
        project = create(:business)
        another_project = create(:business)
        _active_category = create(:category, :active, title: "Informática", business: another_project)

        params = {project_id: project.id, search: "informa"}

        get("/admin/v1/navigations/categories", headers:, params:)

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.count).to eq(0)
      end

      context "when project config have only_exclusive_categories true" do
        it "expect return only exclusive categories" do
          project_config = create(:project_config, only_exclusive_categories: true)
          project = create(:business, project_config:)
          own_category = create(:category, :active, title: "Informática", business: project)
          _own_inactive_category = create(:category, :inactive, title: "Informática", business: project)
          _xpto_category = create(:category, :active)

          params = {project_id: project.id, search: "informa"}

          get("/admin/v1/navigations/categories", headers:, params:)

          expect(response).to have_http_status(:success)
          expect(json_parse_response_body.map(&:keys)).to all(match_array(serialized_keys))
          expect(json_parse_response_body.count).to eq(1)
          expect(json_parse_response_body.first["id"]).to eq(own_category.id)
        end
      end

      context "when params are invalid" do
        it "expected to return forbiden" do
          params = {project_id: nil}

          get("/admin/v1/navigations/categories", headers:, params:)

          expect(response).to have_http_status(:not_found)
        end
      end
    end

    context "when account is admin_client" do
      let(:project) { create(:business) }
      let!(:admin) { create(:client_employee, businesses: [project]) }
      let!(:headers) { Devise::JWT::TestHelpers.auth_headers({}, admin) }
      let(:serialized_keys) { %w[id name] }

      context "when the parameters are from another project" do
        it "expected to return forbiden" do
          another_project = create(:business)

          params = {project_id: another_project.id}

          get("/admin/v1/navigations/categories", headers:, params:)

          expect(response).to have_http_status(:not_found)
        end
      end
    end

    context "when is not authenticated" do
      it "must be unauthorized" do
        get "/admin/v1/navigations/categories"

        expect(response).to be_unauthorized
      end
    end
  end
end
