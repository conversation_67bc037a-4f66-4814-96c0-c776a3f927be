# frozen_string_literal: true

require "devise/jwt/test_helpers"
require "rails_helper"

RSpec.describe Admin::V1::Navigations::BannerProvidersController, type: :request do
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#index" do
    context "when account is admin_lecupon" do
      let!(:admin) { create(:client_employee, :admin_lecupon) }
      let!(:headers) { Devise::JWT::TestHelpers.auth_headers({}, admin) }

      let!(:project) { create(:business) }
      let(:provider_serialized_keys) { %w[text value] }

      it "expected return all" do
        project.update(giftcard: true, prize_draw: true, telemedicine: true)
        params = {project_id: project.id}

        get("/admin/v1/navigations/banner_providers", headers:, params:)

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.map(&:keys)).to all(match_array(provider_serialized_keys))
        expect(json_parse_response_body.pluck("value").sort).to match_array(Navigation.providers.values.sort)
      end

      context "when prize draw is disabled" do
        it "expected not returns prize draw banners" do
          project.update(giftcard: true, prize_draw: false, telemedicine: true)
          params = {project_id: project.id}

          get("/admin/v1/navigations/banner_providers", headers:, params:)

          expect(response).to have_http_status(:success)
          expect(json_parse_response_body.count).to eq(6)
          expect(json_parse_response_body.pluck("provider")).not_to include(Navigation.providers[:prize_draw])
        end
      end

      context "when telemedicine is disabled" do
        it "expected not returns telemedicine banners" do
          project.update(giftcard: true, prize_draw: true, telemedicine: false)
          params = {project_id: project.id}

          get("/admin/v1/navigations/banner_providers", headers:, params:)

          expect(response).to have_http_status(:success)
          expect(json_parse_response_body.count).to eq(6)
          expect(json_parse_response_body.pluck("provider")).not_to include(Navigation.providers[:telemedicine])
        end
      end

      context "when gift card is disabled" do
        it "expected not returns gift card banners" do
          project.update(giftcard: false, prize_draw: true, telemedicine: true)
          params = {project_id: project.id}

          get("/admin/v1/navigations/banner_providers", headers:, params:)

          expect(response).to have_http_status(:success)
          expect(json_parse_response_body.count).to eq(6)
          expect(json_parse_response_body.pluck("provider")).not_to include(Navigation.providers[:gift_card])
        end
      end
    end

    context "when account is admin_client" do
      let(:project) { create(:business) }
      let!(:admin) { create(:client_employee, businesses: [project]) }
      let!(:headers) { Devise::JWT::TestHelpers.auth_headers({}, admin) }
      let(:serialized_keys) { %w[id ends_at georeferenced kind position provider starts_at url] }

      let(:provider_serialized_keys) { %w[text value] }

      it "expected return all" do
        project.update(giftcard: true, prize_draw: true, telemedicine: true)
        params = {project_id: project.id}

        get("/admin/v1/navigations/banner_providers", headers:, params:)

        expect(response).to have_http_status(:success)
        expect(json_parse_response_body.map(&:keys)).to all(match_array(provider_serialized_keys))
        expect(json_parse_response_body.pluck("value").sort).to match_array(Navigation.providers.values.sort)
      end

      context "when the parameters are from another project" do
        it "expected to return not_found" do
          another_project = create(:business)
          params = {project_id: another_project.id}

          get("/admin/v1/navigations/banner_providers", headers:, params:)

          expect(response).to have_http_status(:not_found)
        end
      end
    end

    context "when is not authenticated" do
      it "must be unauthorized" do
        get "/admin/v1/navigations/banner_providers"

        expect(response).to be_unauthorized
      end
    end
  end
end
