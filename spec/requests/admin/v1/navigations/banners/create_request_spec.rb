# frozen_string_literal: true

require "rails_helper"

RSpec.describe Admin::V1::Navigations::BannersController, type: :request do
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#create" do
    let(:banner_serialized_keys) do
      %w[
        id
        destination
        ends_at
        geolocation
        georeferenced
        image
        mobile_navigation_option
        web_navigation_option
        position
        provider
        starts_at
      ]
    end
    let(:image_serialized_keys) { %w[large small] }
    let(:geolocation_serialized_keys) { %w[id address lat long range_type range_value] }
    let(:settings_serialized_keys) do
      %w[
        cnpj_param_name
        cpf_param_name
        custom_field_1_param_name
        custom_field_2_param_name
        custom_field_3_param_name
        custom_field_4_param_name
        custom_field_5_param_name
        custom_field_6_param_name
        custom_field_7_param_name
        custom_field_8_param_name
        email_param_name
        header_param_name
        header_param_value
      ]
    end

    context "when account is admin_lecupon" do
      let!(:admin) { create(:client_employee, :admin_lecupon) }
      let!(:headers) { Devise::JWT::TestHelpers.auth_headers({}, admin) }

      let(:project) { create(:business, home_banner: false) }
      context "with external_app provider do" do
        let(:image) do
          tempfile = Tempfile.new(["product_image", ".png"], "tmp")
          Rack::Test::UploadedFile.new(tempfile)
        end

        it "expected create banner and geolocation" do
          params = {
            project_id: project.id,
            image:,
            provider: Navigation.providers[:external_app],
            destination: "tel:1234567",
            mobile_navigation_option: Navigation.mobile_navigation_options[:browser],
            web_navigation_option: Navigation.web_navigation_options[:blank],
            starts_at: DateTime.tomorrow,
            ends_at: DateTime.current + 1.month,
            georeferenced: true,
            address: FFaker::AddressBR.full_address,
            lat: 1.0,
            long: 1.0,
            range_value: 30
          }

          expect do
            post("/admin/v1/navigations/banners", headers: headers, params:)
          end.to change(Navigation.kind_banner, :count).by(1)
            .and change(NavigationGeolocation, :count).by(1)
          expect(response).to have_http_status(:created)
          expect(json_parse_response_body.keys).to match_array(banner_serialized_keys)
          expect(json_parse_response_body["image"].keys).to match_array(image_serialized_keys)
          expect(json_parse_response_body["geolocation"].keys).to match_array(geolocation_serialized_keys)
          expect(json_parse_response_body["destination"]).to eq("tel:1234567")
          expect(project.reload.home_banner).to eq(true)
        end
      end

      context "with teledemicine provider" do
        let(:image) do
          tempfile = Tempfile.new(["product_image", ".png"], "tmp")
          Rack::Test::UploadedFile.new(tempfile)
        end

        it "expected create banner and geolocation" do
          project.update(telemedicine: true)
          params = {
            project_id: project.id,
            image:,
            provider: "telemedicine",
            starts_at: DateTime.tomorrow,
            ends_at: DateTime.current + 1.month,
            georeferenced: true,
            address: FFaker::AddressBR.full_address,
            lat: 1.0,
            long: 1.0,
            range_value: 30
          }

          expect do
            post("/admin/v1/navigations/banners", headers:, params:)
          end.to change(Navigation.kind_banner, :count).by(1)
            .and change(NavigationGeolocation, :count).by(1)
          expect(response).to have_http_status(:created)
          expect(json_parse_response_body.keys).to match_array(banner_serialized_keys)
          expect(json_parse_response_body["image"].keys).to match_array(image_serialized_keys)
          expect(json_parse_response_body["geolocation"].keys).to match_array(geolocation_serialized_keys)
          expect(json_parse_response_body["destination"]).to eq("/telemedicine")
        end

        it "expected create banner wiht external_link data" do
          banner_serialized_keys.push("settings")
          project.update(telemedicine: true)
          params = {
            project_id: project.id,
            image:,
            provider: Navigation.providers[:external_link],
            starts_at: DateTime.tomorrow,
            ends_at: DateTime.current + 1.month,
            georeferenced: false,
            destination: "https://www.google.com"
          }

          expect do
            post("/admin/v1/navigations/banners", headers:, params:)
          end.to change(Navigation.kind_banner, :count).by(1)
            .and not_change(NavigationGeolocation, :count)
          expect(response).to have_http_status(:created)
          expect(json_parse_response_body.keys).to match_array(banner_serialized_keys)
          expect(json_parse_response_body["image"].keys).to match_array(image_serialized_keys)
          expect(json_parse_response_body["geolocation"]).to eq(nil)
          expect(json_parse_response_body["settings"].keys).to match_array(settings_serialized_keys)
          expect(json_parse_response_body["destination"]).to eq("https://www.google.com")
        end

        it "expected create banner and not create geolocation" do
          project.update(telemedicine: true)
          params = {
            project_id: project.id,
            image:,
            provider: Navigation.providers[:telemedicine],
            starts_at: DateTime.current,
            georeferenced: false
          }

          expect do
            post("/admin/v1/navigations/banners", headers:, params:)
          end.to change(Navigation.kind_banner, :count).by(1)
            .and not_change(NavigationGeolocation, :count)
          expect(response).to have_http_status(:created)
          expect(json_parse_response_body.keys).to match_array(banner_serialized_keys)
          expect(json_parse_response_body["image"].keys).to match_array(image_serialized_keys)
          expect(json_parse_response_body["geolocation"]).to eq(nil)
          expect(json_parse_response_body["destination"]).to eq("/telemedicine")
        end

        it "expected create category banner" do
          category = create(:category)
          category_banner_serialized_keys = [
            "id", "category", "destination", "ends_at", "geolocation", "georeferenced", "image", "mobile_navigation_option", "web_navigation_option", "position", "provider", "starts_at"
          ]

          params = {
            project_id: project.id,
            image:,
            provider: Navigation.providers[:category],
            starts_at: DateTime.current,
            georeferenced: false,
            category_id: category.id
          }

          expect do
            post("/admin/v1/navigations/banners", headers:, params:)
          end.to change(Navigation.kind_banner, :count).by(1)
            .and not_change(NavigationGeolocation, :count)
          expect(response).to have_http_status(:created)
          expect(json_parse_response_body.keys).to match_array(category_banner_serialized_keys)
          expect(json_parse_response_body["image"].keys).to match_array(image_serialized_keys)
          expect(json_parse_response_body["destination"]).to eq("/categories/#{category.id}/organizations")
        end

        it "expected create organization_coupon banner" do
          organization = create(:organization)
          branch = create(:branch, organization:)
          promotion = create(:promotion, organization:)
          coupon = create(:cupon, branch:, promotion:)
          org_coupon_banner_serialized_keys = [
            "id", "ends_at", "starts_at", "branch", "organization", "promotion", "destination", "georeferenced", "image", "position", "provider", "mobile_navigation_option", "web_navigation_option", "geolocation"
          ]

          params = {
            project_id: project.id,
            image:,
            provider: Navigation.providers[:organization_coupon],
            starts_at: DateTime.tomorrow,
            georeferenced: false,
            organization_id: organization.id,
            branch_id: branch.id,
            promotion_id: promotion.id
          }

          expect do
            post("/admin/v1/navigations/banners", headers:, params:)
          end.to change(Navigation.kind_banner, :count).by(1)
            .and not_change(NavigationGeolocation, :count)
          expect(response).to have_http_status(:created)
          expect(json_parse_response_body.keys).to match_array(org_coupon_banner_serialized_keys)
          expect(json_parse_response_body["image"].keys).to match_array(image_serialized_keys)
          expect(json_parse_response_body["destination"]).to eq("/organizations/#{organization.id}/branches/#{branch.id}/coupons/#{coupon.id}")
        end

        it "expected return forbiden when project does not allow provider" do
          project.update(telemedicine: false)
          params = {
            project_id: project.id,
            image:,
            provider: Navigation.providers[:telemedicine],
            georeferenced: false
          }

          expect do
            post("/admin/v1/navigations/banners", headers:, params:)
          end.to not_change(Navigation.kind_banner, :count)
            .and not_change(NavigationGeolocation, :count)
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end
    end

    context "when account is admin_client" do
      let(:project) { create(:business) }
      let!(:admin) { create(:client_employee, businesses: [project]) }
      let!(:headers) { Devise::JWT::TestHelpers.auth_headers({}, admin) }

      let(:image) do
        tempfile = Tempfile.new(["product_image", ".png"], "tmp")
        Rack::Test::UploadedFile.new(tempfile)
      end

      it "expected to post only your own banner" do
        project.update(telemedicine: true)
        params = {
          project_id: project.id,
          image:,
          provider: Navigation.providers[:telemedicine],
          starts_at: DateTime.tomorrow,
          georeferenced: true,
          address: FFaker::AddressBR.full_address,
          lat: 1.0,
          long: 1.0,
          range_value: 30
        }

        expect do
          post("/admin/v1/navigations/banners", headers:, params:)
        end.to change(Navigation.kind_banner, :count).by(1)
          .and change(NavigationGeolocation, :count).by(1)
        expect(response).to have_http_status(:created)
        expect(json_parse_response_body.keys).to match_array(banner_serialized_keys)
        expect(json_parse_response_body["image"].keys).to match_array(image_serialized_keys)
        expect(json_parse_response_body["geolocation"].keys).to match_array(geolocation_serialized_keys)
        expect(json_parse_response_body["destination"]).to eq("/telemedicine")
      end

      it "expected to return forbiden when the parameters are from another business" do
        another_project = create(:business)
        another_project.update(telemedicine: true)
        params = {
          project_id: another_project.id,
          image:,
          provider: Navigation.providers[:telemedicine],
          georeferenced: false
        }

        expect do
          post("/admin/v1/navigations/banners", headers:, params:)
        end.to not_change(Navigation.kind_banner, :count)
          .and not_change(NavigationGeolocation, :count)
        expect(response).to have_http_status(:not_found)
      end
    end

    context "when is not authenticated" do
      it "must be unauthorized" do
        post "/admin/v1/navigations/banners"

        expect(response).to be_unauthorized
      end
    end
  end
end
