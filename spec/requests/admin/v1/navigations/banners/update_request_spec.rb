# frozen_string_literal: true

require "rails_helper"

RSpec.describe Admin::V1::Navigations::BannersController, type: :request do
  let(:json_parse_response_body) { JSON.parse(response.body) }

  describe "#create" do
    let(:banner_serialized_keys) do
      %w[
        id
        destination
        ends_at
        geolocation
        georeferenced
        image
        mobile_navigation_option
        web_navigation_option
        position
        provider
        starts_at
      ]
    end
    let(:image_serialized_keys) { %w[large small] }
    let(:geolocation_serialized_keys) { %w[id address lat long range_type range_value] }
    let(:settings_serialized_keys) do
      %w[
        cnpj_param_name
        cpf_param_name
        custom_field_1_param_name
        custom_field_2_param_name
        custom_field_3_param_name
        custom_field_4_param_name
        custom_field_5_param_name
        custom_field_6_param_name
        custom_field_7_param_name
        custom_field_8_param_name
        email_param_name
        header_param_name
        header_param_value
      ]
    end

    context "when account is admin_lecupon" do
      let!(:admin) { create(:client_employee, :admin_lecupon) }
      let!(:headers) { Devise::JWT::TestHelpers.auth_headers({}, admin) }

      let(:project) { create(:business) }

      context "with teledemicine provider" do
        let(:image) do
          tempfile = Tempfile.new(["product_image", ".png"], "tmp")
          Rack::Test::UploadedFile.new(tempfile)
        end

        it "expected update banner and geolocation" do
          expect(Business::Cache).to receive(:invalidate).with([project.id])
          project.update(telemedicine: true)
          banner = create(:banner, :telemedicine, business: project)
          params = {
            project_id: project.id,
            image:,
            provider: "telemedicine",
            starts_at: DateTime.tomorrow + 1.day,
            ends_at: DateTime.current + 2.months,
            georeferenced: true,
            address: FFaker::AddressBR.full_address,
            lat: 1.0,
            long: 1.0,
            range_value: 30
          }

          expect do
            put("/admin/v1/navigations/banners/#{banner.id}", headers:, params:)
          end.to not_change { banner.reload.provider }
            .and not_change { banner.business }
            .and change { banner.starts_at }
            .and change { banner.ends_at }
            .and change { banner.georeferenced }.to(true)
            .and change(NavigationGeolocation, :count).by(1)
          expect(response).to have_http_status(:success)
          expect(json_parse_response_body.keys).to match_array(banner_serialized_keys)
          expect(json_parse_response_body["image"].keys).to match_array(image_serialized_keys)
          expect(json_parse_response_body["geolocation"].keys).to match_array(geolocation_serialized_keys)
          expect(json_parse_response_body["destination"]).to eq("/telemedicine")
        end

        it "expect remove geolocation" do
          banner_serialized_keys.push("settings")
          project.update(telemedicine: true)
          banner = create(:banner, :telemedicine, :georeferenced, business: project)
          create(:banner_geolocation, navigation: banner)

          params = {
            project_id: project.id,
            image:,
            provider: "telemedicine",
            starts_at: DateTime.tomorrow,
            ends_at: DateTime.current + 1.month,
            georeferenced: false
          }

          expect do
            put("/admin/v1/navigations/banners/#{banner.id}", headers:, params:)
          end.to not_change { banner.reload.provider }
            .and not_change { banner.business }
            .and change(NavigationGeolocation, :count).by(-1)
          expect(response).to have_http_status(:success)
        end

        it "expect does not remove geolocation" do
          banner_serialized_keys.push("settings")
          project.update(telemedicine: true)
          banner = create(:banner, :telemedicine, :georeferenced, business: project)
          create(:banner_geolocation, navigation: banner)

          params = {
            project_id: project.id,
            image:,
            provider: "telemedicine",
            starts_at: DateTime.tomorrow,
            ends_at: DateTime.current + 1.month
          }

          expect do
            put("/admin/v1/navigations/banners/#{banner.id}", headers:, params:)
          end.to not_change { banner.reload.provider }
            .and not_change { banner.business }
            .and not_change(NavigationGeolocation, :count)
          expect(response).to have_http_status(:success)
        end

        it "expect return not_found when try update provider" do
          banner_serialized_keys.push("settings")
          project.update(telemedicine: true)
          banner = create(:banner, :telemedicine, business: project)
          params = {
            project_id: project.id,
            image:,
            provider: Navigation.providers[:external_link],
            starts_at: DateTime.tomorrow,
            ends_at: DateTime.current + 1.month,
            georeferenced: false,
            destination: "https://www.google.com"
          }

          expect do
            put("/admin/v1/navigations/banners/#{banner.id}", headers:, params:)
          end.to not_change { banner.reload.provider }
            .and not_change { banner.business }
            .and not_change(NavigationGeolocation, :count)
          expect(response).to have_http_status(:not_found)
          expect(json_parse_response_body["error"]).to eq("Registro(s) não encontrado(s).")
        end

        it "expected return forbiden when project does not allow provider" do
          project.update(telemedicine: true)
          banner = create(:banner, :telemedicine, business: project)
          project.update(telemedicine: false)
          params = {
            project_id: project.id,
            image:,
            provider: "telemedicine",
            destination: "/telemedicine",
            georeferenced: false
          }

          put("/admin/v1/navigations/banners/#{banner.id}", headers:, params:)
          expect(response).to have_http_status(:unprocessable_entity)
          expect(json_parse_response_body["errors"]).to eq("Produto telemedicine não é uma configuração ativa para o business")
        end
      end
    end

    context "when account is admin_client" do
      let(:project) { create(:business) }
      let!(:admin) { create(:client_employee, businesses: [project]) }
      let!(:headers) { Devise::JWT::TestHelpers.auth_headers({}, admin) }

      let(:image) do
        tempfile = Tempfile.new(["product_image", ".png"], "tmp")
        Rack::Test::UploadedFile.new(tempfile)
      end

      it "expected to return not_found when the parameters are from another business" do
        another_project = create(:business)
        another_project.update(telemedicine: true)
        banner = create(:banner, :telemedicine, business: another_project)
        params = {
          project_id: another_project.id,
          image:,
          provider: "telemedicine",
          destination: "/telemedicine",
          georeferenced: false
        }

        expect do
          put("/admin/v1/navigations/banners/#{banner.id}", headers:, params:)
        end.to not_change(Navigation.kind_banner, :count)
          .and not_change(NavigationGeolocation, :count)
        expect(response).to have_http_status(:not_found)
        expect(json_parse_response_body["error"]).to eq("Registro(s) não encontrado(s).")
      end
    end

    context "when is not authenticated" do
      it "must be unauthorized" do
        put "/admin/v1/navigations/banners/1"

        expect(response).to be_unauthorized
      end
    end
  end
end
