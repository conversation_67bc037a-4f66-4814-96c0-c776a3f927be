# frozen_string_literal: true

require "rails_helper"
require "sidekiq/testing"

RSpec.describe FundRedemption, type: :model do
  describe "#business_id" do
    subject { fund_redemption.business }

    let(:main_business) { create(:business) }
    let(:business) { create(:business, main_business:) }
    let(:user) { create(:user, business:) }
    let(:fund_redemption) { build_stubbed(:fund_redemption, :points, user:) }

    before { fund_redemption.validate }

    it { is_expected.to eq main_business }
  end

  describe "#fair_value" do
    subject { fund_redemption.fair_value }

    context "with service" do
      let(:business) { create(:business) }
      let(:user) { create(:user, business:) }
      let!(:fund_redemption_config) do
        create(:fund_redemption_config, :points, :bank_transfer, business:, fair_value: 0.0125, min_amount: 1000, multiple: 100)
      end
      let(:fund_redemption) { build_stubbed(:fund_redemption, :points, :bank_transfer, user:, amount: 1000) }

      before { fund_redemption.validate }

      it { is_expected.to eq(fund_redemption_config.fair_value) }
    end

    context "without service" do
      let(:fund_redemption) { build_stubbed(:fund_redemption, :BRL) }

      before { fund_redemption.validate }

      it { is_expected.to be_nil }
    end
  end

  describe "amount multiple validation" do
    subject { fund_redemption.errors.messages }

    let(:business) { create(:business) }
    let(:user) { create(:user, business:) }
    let(:service) { described_class.services[:azul] }
    let!(:fund_redemption_config) do
      create(:fund_redemption_config, :points, business:, service:, fair_value: 0.01, min_amount: 1000, multiple:)
    end

    let(:fund_redemption) { build_stubbed(:fund_redemption, :points, service:, user:, amount:) }

    before { fund_redemption.validate }

    context "when business configured multiple" do
      let(:multiple) { 100 }

      context "when amount is multiple" do
        let(:amount) { 1000 }

        it { is_expected.not_to include amount: ["deve ser múltiplo de 100"] }
      end

      context "when amount is not multiple" do
        let(:amount) { 1010 }

        it { is_expected.to include amount: ["deve ser múltiplo de 100"] }
      end
    end

    context "when business did not configure multiple" do
      let(:multiple) { nil }
      let(:service) { described_class.services[:giftcard] }

      context "when amount is multiple" do
        let(:amount) { 1000 }

        it { is_expected.not_to include amount: ["deve ser múltiplo de 100"] }
      end

      context "when amount is not multiple" do
        let(:amount) { 1010 }

        it { is_expected.not_to include amount: ["deve ser múltiplo de 100"] }
      end
    end
  end

  describe "service handler" do
    let!(:business) { create(:business, fair_value: 0.01, currency: "points") }
    let!(:user) { create(:user, business:) }
    let!(:fund_redemption_config) do
      create(:fund_redemption_config, :points, service:, business:, fair_value: 0.03, min_amount: 1000, multiple: 100)
    end
    let(:fund_redemption) { build(:fund_redemption, :points, service:, user:, business:, amount: 1200) }

    describe "bank_transfer" do
      let(:service) { FundRedemption.services[:bank_transfer] }

      it "requests bank transfer fund redemption" do
        expect { fund_redemption.save! }
          .to change(Payout.where(user:, total_amount: 36), :count)
      end
    end

    describe "azul" do
      let(:service) { FundRedemption.services[:azul] }
      let(:tudo_azul_payload_stub) { instance_double(TudoAzul::Payload) }
      let(:tudo_azul_client_stub) { instance_double(TudoAzul::Client) }
      let(:tudo_azul_response_stub) do
        TudoAzul::Response.new(
          transaction_id: "13.TRNa792aa126",
          points: 36,
          raw_data: {
            "notifications" => [],
            "data" => {
              "transaction" => {
                "transactionId" => "13.TRNa792aa126",
                "points" => 36
              }
            }
          }
        )
      end

      before do
        allow(TudoAzul::Payload).to receive(:new).and_return(tudo_azul_payload_stub)
        allow(TudoAzul::Client).to receive(:new).and_return(tudo_azul_client_stub)
        allow(tudo_azul_client_stub).to receive(:register_points).and_return(tudo_azul_response_stub)
      end

      it "requests azul fund redemption and enqueues processing" do
        Sidekiq::Testing.inline! do
          expect { fund_redemption.save! }
            .to change(TudoAzulAdHocTransaction.alloyal.where(taxpayer_id: user.cpf, points: 36), :count).by(1)

          expect(TudoAzul::Payload).to have_received(:new)
          expect(TudoAzul::Client).to have_received(:new)

          azul_transaction = TudoAzulAdHocTransaction.take!
          expect(azul_transaction.taxpayer_id).to eq user.cpf
          expect(azul_transaction.points).to eq 36
          expect(azul_transaction.response_data).to eq tudo_azul_response_stub.raw_data
          expect(azul_transaction.tudo_azul_transaction_id).to eq "13.TRNa792aa126"
        end
      end
    end
  end
end
