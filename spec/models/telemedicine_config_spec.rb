require "rails_helper"
require "sidekiq/testing"

RSpec.describe TelemedicineConfig, type: :model do
  describe "validations" do
    let(:business) { create(:business) }
    let(:telemedicine_config) { business.telemedicine_config }

    context "when contracted beneficiaries is not a pre-defined limit for plan" do
      it "must be invalid" do
        telemedicine_config.plan = Telemedicine::Plans::INTEGRAL_PLUS
        telemedicine_config.contracted_beneficiaries = 2250

        expect(telemedicine_config).to be_invalid
        expect(telemedicine_config.errors.first.full_message).to eq("Número de vidas contratadas não está em um limite permitido para o plano")
      end
    end

    context "when contracted beneficiaries is above the maximum limit for plan" do
      it "must be invalid" do
        telemedicine_config.plan = Telemedicine::Plans::INTEGRAL_PLUS
        telemedicine_config.contracted_beneficiaries = 120000

        expect(telemedicine_config).to be_invalid
        expect(telemedicine_config.errors.first.full_message).to eq("Número de vidas contratadas está acima do limite máximo permitido para o plano")
      end
    end

    context "when contracted beneficiaries is below the minimum limit for plan" do
      it "must be invalid" do
        telemedicine_config.plan = Telemedicine::Plans::COPART
        telemedicine_config.contracted_beneficiaries = 100

        expect(telemedicine_config).to be_invalid
        expect(telemedicine_config.errors.first.full_message).to eq("Número de vidas contratadas está abaixo do limite mínimo permitido para o plano")
      end
    end

    describe "#sync_telemedicine callback" do
      context "when changing telemedicine plan and/or contracted beneficiaries limit" do
        let!(:business) { create(:business, telemedicine: true) }
        let!(:auth_user_one) { create(:authorized_user, business:, telemedicine: true, telemedicine_external_id: 1) }
        let!(:auth_user_two) { create(:authorized_user, business:, telemedicine: true, telemedicine_external_id: 2) }
        let!(:auth_user_three) { create(:authorized_user, business:, telemedicine: false, telemedicine_external_id: 3) }
        let!(:auth_user_four) { create(:authorized_user, business:, telemedicine: false, telemedicine_external_id: 4) }

        it "must clean all project beneficiaries' external ids" do
          expect do
            business.telemedicine_config.update!(plan: Telemedicine::Plans::ULTRA, contracted_beneficiaries: 1000)
          end.to change(AuthorizedUser.where(telemedicine_external_id: nil), :count).from(0).to(4)
        end

        it "must set as elegible all authorized users based on the limit" do
          expect do
            business.telemedicine_config.update_columns(contracted_beneficiaries: 3, plan: Telemedicine::Plans::ULTRA)
            business.telemedicine_config.sync_telemedicine
          end.to change(business.authorized_users.where(telemedicine: true), :count).from(2).to(3)
        end

        it "must schedule an upsert of elegible authorized users on third party" do
          auth_user_one.update(telemedicine_external_id: nil)
          auth_user_two.update(telemedicine_external_id: nil)

          expect do
            Sidekiq::Testing.inline! do
              business.telemedicine_config.update!(plan: Telemedicine::Plans::ULTRA, contracted_beneficiaries: 1000)
              auth_user_one.reload
              auth_user_two.reload
            end
          end.to change(AuthorizedUser.where.not(telemedicine_external_id: nil), :count).from(2).to(4)
            .and change(auth_user_one, :telemedicine_external_id)
            .and change(auth_user_two, :telemedicine_external_id)
        end
      end
    end
  end
end
