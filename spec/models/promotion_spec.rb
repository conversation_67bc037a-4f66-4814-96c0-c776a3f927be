require "rails_helper"

RSpec.describe Promotion, type: :model do
  describe "quantity validation" do
    let(:valid_promotion) { create(:promotion) }
    let(:invalid_promotion) { create(:promotion) }

    context "when promotion is infinite" do
      before do
        valid_promotion.update(quantity: nil, infinity: true)
      end

      it "must not require quantity" do
        expect(valid_promotion).to be_valid
      end
    end

    context "when promotion is not infinite" do
      before do
        valid_promotion.update(quantity: 10, infinity: false)
      end

      it "must require quantity as integer and bigger than 0" do
        expect(valid_promotion).to be_valid

        invalid_promotion.infinity = false

        invalid_promotion.quantity = 0
        expect(invalid_promotion.invalid?).to eq(true)
      end
    end
  end

  describe "redeemed_count validation" do
    let(:valid_promotion) { create(:promotion) }
    let(:invalid_promotion) { create(:promotion) }

    context "when promotion is infinite" do
      before do
        valid_promotion.update(redeemed_count: 10, quantity: nil, infinity: true)
      end

      it "must not validate redeemed_count less than quantity" do
        expect(valid_promotion).to be_valid
      end
    end

    context "when promotion is not infinite" do
      before do
        valid_promotion.update(redeemed_count: 5, quantity: 10, infinity: false)
        invalid_promotion.update(quantity: 10, infinity: false)
      end

      it "must require redeemed_count to be less than quantity" do
        expect(valid_promotion).to be_valid

        invalid_promotion.redeemed_count = invalid_promotion.quantity + 1
        expect(invalid_promotion.invalid?).to eq(true)
      end
    end
  end

  describe "url strip and validation" do
    let(:promotion_with_valid_url) { build(:promotion, :online, url: "https://example.com") }
    let(:promotion_with_url_with_whitespaces) { build(:promotion, :online, url: "  https://example.com ") }
    let(:promotion_with_invalid_url) { build(:promotion, :online, url: "not url") }

    it { expect(promotion_with_valid_url).to be_valid }

    it { expect(promotion_with_invalid_url).to be_invalid }

    it "strips and validates url with whitespaces" do
      expect(promotion_with_url_with_whitespaces).to be_valid
      expect(promotion_with_url_with_whitespaces.url).to eq("https://example.com")
    end
  end

  describe "normalize rules" do
    let(:promotion) { build(:promotion, rules: " Regras  teste 😊🧡  teste   ") }

    subject { promotion.rules }

    before { promotion.validate }

    it { is_expected.to eq("Regras teste teste") }
  end

  describe "code normalization" do
    it "must not be blank string" do
      promotion = create(:promotion, code: "")
      expect(promotion.code).to be_nil
    end
  end

  describe "#increment_redeemed_count" do
    let!(:promotion) { create(:promotion, redeemed_count: 0) }
    let!(:coupons) { create_list(:cupon, 2, promotion:, redeemed_count: 0) }

    it "increments promotion redeemed count and its coupons redeemed count" do
      expect { promotion.increment_redeemed_count }
        .to change(promotion, :redeemed_count).from(0).to(1)
        .and change { promotion.cupons.pluck(:redeemed_count) }.from(all(eq(0))).to(all(eq(1)))
    end
  end

  describe "#decrement_redeemed_count" do
    let!(:promotion) { create(:promotion, redeemed_count: 1) }
    let!(:coupons) { create_list(:cupon, 2, promotion:, redeemed_count: 1) }

    it "decrements promotion redeemed count and its coupons redeemed count" do
      expect { promotion.decrement_redeemed_count }
        .to change(promotion, :redeemed_count).from(1).to(0)
        .and change { promotion.cupons.pluck(:redeemed_count) }.from(all(eq(1))).to(all(eq(0)))
    end
  end

  describe "integration partner normalization" do
    it "normalizes integration partner" do
      promotion = build(:promotion, integration_partner: "")
      expect(promotion).to be_valid
      expect(promotion.integration_partner).to eq(nil)
    end
  end

  describe "cashback rules" do
    it "does nothing when cashback rules is present" do
      promotion = build(:promotion, cashback_rules: FFaker::LoremBR.phrase, cashback_value: 1)

      expect { promotion.validate }.not_to change(promotion, :cashback_rules)
    end

    it "does nothing when cashback value is zero" do
      promotion = build(:promotion, cashback_rules: "", cashback_value: 0)

      expect { promotion.validate }.not_to change(promotion, :cashback_rules)
    end

    it "fills cashback rules when it is blank and cashback value is greater than zero" do
      promotion = build(:promotion, cashback_rules: "", cashback_value: 1)

      expect { promotion.validate }.to change(promotion, :cashback_rules).from("").to(be_present)
    end
  end

  describe "#refresh_provider" do
    it "refreshes promotion provider when it changed" do
      promotion = create(:promotion, provider_type: "qrcode", provider: nil)

      allow(Promotion::Provider::Convert).to receive(:to_provider).with(promotion).and_return("qrcode")

      expect { promotion.refresh_provider }
        .to change(promotion, :provider).from(nil).to("qrcode")
    end

    it "does not refresh promotion provider when it did not change" do
      promotion = create(:promotion, provider_type: "qrcode", provider: "qrcode")

      allow(Promotion::Provider::Convert).to receive(:to_provider).with(promotion).and_return("qrcode")

      expect { promotion.refresh_provider }
        .to not_change(promotion, :provider)
    end
  end

  describe "#refresh_status" do
    it "calls refresh status" do
      promotion = create(:promotion)

      allow(Promotion::Status::Refresh).to receive(:call).with(promotion)

      expect(promotion.refresh_status).to eq(promotion)
      expect(Promotion::Status::Refresh).to have_received(:call).with(promotion)
    end
  end

  describe "#propagate_changes" do
    it "expect call propagate_changes" do
      promotion = create(:promotion)
      propagate_changes_mock = instance_double(Promotion::PropagateChanges)
      allow(Promotion::PropagateChanges).to receive(:new).and_return(propagate_changes_mock)
      allow(propagate_changes_mock).to receive(:call)

      promotion.propagate_changes

      expect(propagate_changes_mock).to have_received(:call)
    end
  end

  describe "#refresh_coupons" do
    it "refreshes coupons based on promotion" do
      old_business = create(:business)
      business = create(:business)
      promotion = create(:promotion,
        :percent_discount,
        business:,
        provider: "coupon_code",
        title: "title",
        description: "description",
        rules: "rules",
        cashback_rules: "cashback rules",
        code: "code",
        url: "https://google.com",
        discount_value: 10,
        contract_custom_text: "contract custom text",
        start_date: 1.day.ago,
        end_date: 1.year.from_now,
        start_hour: "08:00",
        end_hour: "18:00",
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        quantity: 10,
        redeemed_count: 8,
        not_cumulative: true,
        only_physical: true,
        one_for_cpf: true,
        one_per_day: true,
        infinity: false,
        frequency_in_days: 7,
        redeems_per_cpf: 10,
        price: 20,
        average_ticket: 30,
        integration_code: "123",
        integration_partner: "awin")
      coupons = create_list(:cupon,
        2,
        promotion:,
        business: old_business,
        c_type: "CLASSIC",
        description: nil,
        rules: nil,
        cashback_rules: nil,
        code: nil,
        url: nil,
        contract_custom_text: nil,
        start_date: nil,
        end_date: nil,
        start_hour: nil,
        end_hour: nil,
        monday: nil,
        tuesday: nil,
        wednesday: nil,
        thursday: nil,
        friday: nil,
        saturday: nil,
        sunday: nil,
        quantity: nil,
        redeemed_count: 0,
        not_cumulative: nil,
        only_physical: nil,
        one_for_cpf: nil,
        one_per_day: nil,
        infinity: true,
        frequency_in_days: 0,
        redeems_per_cpf: nil,
        price: nil,
        average_ticket: nil,
        integration_code: nil,
        integration_partner: nil)

      expect { promotion.refresh_coupons }
        .to change { coupons.each(&:reload).map(&:business) }.to(all(eq(promotion.business)))
        .and change { coupons.map(&:c_type) }.to(all(eq("FIXED")))
        .and change { coupons.map(&:description) }.to(all(eq(promotion.description)))
        .and change { coupons.map(&:rules) }.to(all(eq(promotion.rules)))
        .and change { coupons.map(&:cashback_rules) }.to(all(eq(promotion.cashback_rules)))
        .and change { coupons.map(&:code) }.to(all(eq(promotion.code)))
        .and change { coupons.map(&:url) }.to(all(eq(promotion.url)))
        .and change { coupons.map(&:discount_value) }.to(all(eq(promotion.discount_value)))
        .and change { coupons.map(&:discount_type) }.to(all(eq(promotion.discount_type)))
        .and change { coupons.map(&:contract_custom_text) }.to(all(eq(promotion.contract_custom_text)))
        .and change { coupons.map(&:start_date) }.to(all(eq(promotion.start_date)))
        .and change { coupons.map(&:end_date) }.to(all(eq(promotion.end_date)))
        .and change { coupons.map(&:start_hour) }.to(all(eq(promotion.start_hour)))
        .and change { coupons.map(&:end_hour) }.to(all(eq(promotion.end_hour)))
        .and change { coupons.map(&:monday) }.to(all(eq(promotion.monday)))
        .and change { coupons.map(&:tuesday) }.to(all(eq(promotion.tuesday)))
        .and change { coupons.map(&:wednesday) }.to(all(eq(promotion.wednesday)))
        .and change { coupons.map(&:thursday) }.to(all(eq(promotion.thursday)))
        .and change { coupons.map(&:friday) }.to(all(eq(promotion.friday)))
        .and change { coupons.map(&:saturday) }.to(all(eq(promotion.saturday)))
        .and change { coupons.map(&:sunday) }.to(all(eq(promotion.sunday)))
        .and change { coupons.map(&:quantity) }.to(all(eq(promotion.quantity)))
        .and change { coupons.map(&:redeemed_count) }.to(all(eq(promotion.redeemed_count)))
        .and change { coupons.map(&:not_cumulative) }.to(all(eq(promotion.not_cumulative)))
        .and change { coupons.map(&:only_physical) }.to(all(eq(promotion.only_physical)))
        .and change { coupons.map(&:one_for_cpf) }.to(all(eq(promotion.one_for_cpf)))
        .and change { coupons.map(&:one_per_day) }.to(all(eq(promotion.one_per_day)))
        .and change { coupons.map(&:infinity) }.to(all(eq(promotion.infinity)))
        .and change { coupons.map(&:frequency_in_days) }.to(all(eq(promotion.frequency_in_days)))
        .and change { coupons.map(&:redeems_per_cpf) }.to(all(eq(promotion.redeems_per_cpf)))
        .and change { coupons.map(&:price) }.to(all(eq(promotion.price)))
        .and change { coupons.map(&:average_ticket) }.to(all(eq(promotion.average_ticket)))
        .and change { coupons.map(&:integration_code) }.to(all(eq(promotion.integration_code)))
        .and change { coupons.map(&:integration_partner) }.to(all(eq(promotion.integration_partner)))
    end
  end

  describe "#update_voucher_type" do
    it "updates voucher type to none" do
      bucket = create(:voucher_bucket)
      promotion = create(:promotion, voucher_type: nil, dynamic_voucher: true, code: "ABC123", voucher_bucket: bucket)
      create_list(:voucher, 2, bucket:)

      expect { promotion.update_voucher_type(voucher_type: "none") }
        .to change(promotion, :voucher_type).to("none")
        .and change(promotion, :dynamic_voucher).to(false)
        .and change(promotion, :code).to(nil)
        .and change(promotion, :voucher_bucket_id).to(nil)
    end

    it "updates voucher type to dynamic" do
      bucket = create(:voucher_bucket)
      promotion = create(:promotion, voucher_type: nil, dynamic_voucher: false, code: "ABC123", voucher_bucket: bucket)
      create_list(:voucher, 2, bucket:)

      expect { promotion.update_voucher_type(voucher_type: "dynamic") }
        .to change(promotion, :voucher_type).to("dynamic")
        .and change(promotion, :dynamic_voucher).to(true)
        .and change(promotion, :code).to(nil)
        .and not_change(promotion.voucher_bucket.vouchers.available, :count)
    end

    it "updates voucher type to fixed" do
      bucket = create(:voucher_bucket)
      promotion = create(:promotion, voucher_type: nil, dynamic_voucher: true, code: nil, voucher_bucket: bucket)
      create_list(:voucher, 2, bucket:)

      expect { promotion.update_voucher_type(voucher_type: "fixed", fixed_code: "ABC123") }
        .to change(promotion, :voucher_type).to("fixed")
        .and change(promotion, :dynamic_voucher).to(false)
        .and change(promotion, :code).to("ABC123")
        .and not_change(promotion.voucher_bucket.vouchers.available, :count)
    end

    it "updates voucher type to list" do
      bucket = create(:voucher_bucket)
      promotion = create(:promotion, voucher_type: nil, dynamic_voucher: true, code: "ABC123", voucher_bucket: bucket)
      create_list(:voucher, 2, bucket:)

      expect { promotion.update_voucher_type(voucher_type: "list") }
        .to change(promotion, :voucher_type).to("list")
        .and change(promotion, :dynamic_voucher).to(false)
        .and change(promotion, :code).to(nil)
        .and not_change(promotion.voucher_bucket.vouchers.available, :count)
    end
  end

  describe ".non_exclusive" do
    let!(:promotions_exclusive) { create_list(:promotion, 3, :exclusive) }
    let!(:promotions_non_exclusive) { create_list(:promotion, 2, :non_exclusive) }

    it { expect(described_class.non_exclusive).to match_array(promotions_non_exclusive) }
  end

  describe "#discount_value" do
    it "expected validate discount_value size" do
      promotion = build(:promotion, :fixed_discount, discount_value: 1_000_000_000.0)

      expect(promotion).to be_invalid
      expect(promotion.errors.as_json).to eq({
        discount_value: ["deve ser menor ou igual a 10000000.0"]
      })
    end
  end

  describe "taggable" do
    it "must act as taggable on 'tags' array attribute" do
      Tag.create!(name: "Celebração", slug: "celebracao")

      expect do
        promotion = create(:promotion, tags: ["Tag 1", "Celebraçao."])

        expect(promotion.tags).to match_array(["Tag 1", "Celebração"])
      end.to change(Tag, :count).from(1).to(2)
        .and change(Tagging, :count).to(2)
    end
  end
end
