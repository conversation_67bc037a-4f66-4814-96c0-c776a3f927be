# frozen_string_literal: true

require "rails_helper"

RSpec.describe Business::CreateWallets do
  describe ".call" do
    let(:business) { create(:business, cashback_wallet_destination: :cashback) }
    let(:unrelated_business) { create(:business, cashback_wallet_destination: :cashback) }
    let(:unrelated_user) { create(:user, business: unrelated_business) }

    context "when user is from main business" do
      let(:user) { create(:user, business:) }

      context "when wallets do not exist" do
        context "when business currency is BRL" do
          let(:business) { create(:business, cashback_wallet_destination: :cashback, currency: "BRL") }

          it "creates wallets" do
            expect { described_class.call(business) }
              .to change(business.wallets.cashback.where(currency: "BRL"), :count).by(1)
              .and change(user.wallets.cashback.where(currency: "BRL"), :count).by(1)
              .and not_change(unrelated_business.wallets, :count)
              .and not_change(unrelated_user.wallets, :count)
          end
        end

        context "when business currency is points" do
          let(:business) { create(:business, cashback_wallet_destination: :cashback, currency: :points, fair_value: 0.03) }

          it "creates wallets" do
            expect { described_class.call(business) }
              .to change(business.wallets.where(currency: "points"), :count).by(1)
              .and change(user.wallets.where(currency: "points"), :count).by(1)
              .and not_change(unrelated_business.wallets, :count)
              .and not_change(unrelated_user.wallets, :count)
          end
        end
      end

      context "when wallets exist" do
        context "when business currency is BRL" do
          let!(:user_wallet) { create(:wallet, :cashback, user:) }
          let!(:business_wallet) { create(:wallet, :cashback, business:) }
          let(:business) { create(:business, cashback_wallet_destination: :cashback, currency: "BRL") }

          it "does not create wallets" do
            expect { described_class.call(business) }.to not_change(Wallet, :count)
          end
        end

        context "when business currency is points" do
          let!(:user_wallet) { create(:wallet, user:, kind: :cashback, currency: :points) }
          let!(:business_wallet) { create(:wallet, business:, kind: :cashback, currency: :points) }
          let(:business) { create(:business, cashback_wallet_destination: :cashback, currency: :points, fair_value: 0.03) }

          it "does not create wallets" do
            expect { described_class.call(business) }.to not_change(Wallet, :count)
          end
        end
      end
    end

    context "when user is from sub business" do
      let(:sub_business) { create(:business, main_business: business) }
      let(:user) { create(:user, business: sub_business) }

      context "when wallets do not exist" do
        it "creates wallets" do
          expect { described_class.call(business) }
            .to change(business.wallets.cashback, :count).by(1)
            .and change(user.wallets.cashback, :count).by(1)
            .and not_change(unrelated_business.wallets, :count)
            .and not_change(unrelated_user.wallets, :count)
        end
      end

      context "when wallets exist" do
        let!(:user_wallet) { create(:wallet, :cashback, user:) }
        let!(:business_wallet) { create(:wallet, :cashback, business:) }
        let!(:sub_business_wallet) { create(:wallet, :cashback, business: sub_business) }

        it "does not create wallets" do
          expect { described_class.call(business) }.to not_change(Wallet, :count)
        end
      end
    end
  end
end
