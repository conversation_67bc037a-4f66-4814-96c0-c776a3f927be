require "rails_helper"
require "sidekiq/testing"

RSpec.describe Business do
  describe "#deactivate_telemedicine_users", :vcr do
    subject(:deactivate_telemedicine) do
      Sidekiq::Testing.inline! do
        business.update(telemedicine: false)
      end
    end

    let!(:business) { create(:business, telemedicine: true) }

    let!(:active_telemedicine_user) do
      create(:authorized_user,
        telemedicine: true,
        telemedicine_external_id: "1",
        telemedicine_enabled: true,
        business:)
    end
    let!(:inactive_telemedicine_user) do
      create(:authorized_user,
        telemedicine: true,
        telemedicine_external_id: "2",
        telemedicine_enabled: false,
        business:)
    end
    let!(:elegible_telemedicine_user) { create(:authorized_user, telemedicine: true, business:) }

    it "must disable telemedicine for all project users" do
      expect(Conexa::Client).to receive(:block_user).with(active_telemedicine_user.telemedicine_external_id, business.telemedicine_config.plan)

      expect { deactivate_telemedicine }.to change { active_telemedicine_user.reload.telemedicine_enabled }.from(true).to(false)

      inactive_telemedicine_user.reload
      elegible_telemedicine_user.reload

      expect(inactive_telemedicine_user.telemedicine).to eq(false)
      expect(elegible_telemedicine_user.telemedicine).to eq(false)

      expect(active_telemedicine_user.telemedicine_activities.where(entry_type: :deactivated).count).to eq(1)
      expect(inactive_telemedicine_user.telemedicine_activities.where(entry_type: :deactivated).count).to eq(0)
      expect(elegible_telemedicine_user.telemedicine_activities.where(entry_type: :deactivated).count).to eq(0)

      expect(business.authorized_users.with_telemedicine_disabled.count).to eq(2)
      expect(business.authorized_users.with_telemedicine_enabled.count).to eq(0)
    end
  end
end
