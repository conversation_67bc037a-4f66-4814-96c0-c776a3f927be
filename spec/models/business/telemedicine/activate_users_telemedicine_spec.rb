require "rails_helper"
require "sidekiq/testing"

RSpec.describe Business do
  describe "#activate_telemedicine_users", :vcr do
    subject(:activate_telemedicine) do
      Sidekiq::Testing.inline! do
        business.update!(telemedicine: true)
        business.telemedicine_config.update!(contracted_beneficiaries: 2)
      end
    end

    let!(:business) { create(:business, telemedicine: true) }
    let(:plan) { business.telemedicine_config.plan }

    let!(:inactive_user) { create(:authorized_user, telemedicine: false, business:, telemedicine_external_id: 1) }
    let!(:eligible_user) { create(:authorized_user, telemedicine: true, business:) }
    let!(:non_eligible_user) { create(:authorized_user, telemedicine: false, business:) }

    let!(:eligible_user_external_id) { "104" }

    before do
      allow(Telemedicine::Plans).to receive(:contracted_beneficiaries_limits_for_plan).and_return([2])
    end

    it "activates telemedicine for users within the contracted limit" do
      expect(Conexa::Client).to receive(:unblock_user)
        .with(inactive_user.telemedicine_external_id, plan)

      expect(Conexa::Client).to receive(:create_or_update_user)
        .once
        .with(hash_including(cpf: eligible_user.cpf), plan)
        .and_return({"id" => eligible_user_external_id})

      expect { activate_telemedicine }.to change(AuthorizedUser.subscribed_to_telemedicine, :count).by(1)

      # Verify user states
      expect(inactive_user.reload).to have_attributes(
        telemedicine_enabled: true,
        telemedicine_external_id: "1"
      )

      expect(eligible_user.reload).to have_attributes(
        telemedicine: true,
        telemedicine_external_id: eligible_user_external_id
      )

      expect(non_eligible_user.reload).to have_attributes(
        telemedicine: false,
        telemedicine_beneficiary: nil
      )

      # Verify activity tracking
      expect(inactive_user.telemedicine_activities.where(entry_type: :reactivated).count).to eq(1)
      expect(eligible_user.telemedicine_activities.where(entry_type: :subscribed).count).to eq(1)
      expect(non_eligible_user.telemedicine_activities.where(entry_type: [:subscribed, :reactivated])).to be_empty
    end
  end
end
