# frozen_string_literal: true

require "rails_helper"

RSpec.describe Business::Setup::Menu do
  let!(:business) { create(:business, :oab) }
  let!(:project_config) { create(:project_config, business:, term_of_use_url: "https://example.com/terms_of_use") }

  it "creates the default menus for the business" do
    described_class.call(business:)

    # menu profile_icon
    expect(business.menus.where(disposition: "profile_icon", fixed: true).count).to eq(0)
    expect(business.menus.where(disposition: "profile_icon", fixed: false).count).to eq(9)
    # menu header
    expect(business.menus.where(disposition: "header", fixed: false).count).to eq(1)
    expect(business.menus.where(disposition: "header", fixed: true).count).to eq(0)
    # menu footer
    expect(business.menus.where(disposition: "footer", fixed: true).count).to eq(0)
    expect(business.menus.where(disposition: "footer", fixed: false).count).to eq(3)
    # menu drawer
    expect(business.menus.where(disposition: "drawer", fixed: false).count).to eq(12)
    expect(business.menus.where(disposition: "drawer", fixed: true).count).to eq(0)
    # menu navigation
    expect(business.menus.where(disposition: "navigation", fixed: false).count).to eq(7)
    expect(business.menus.where(disposition: "navigation", fixed: true).count).to eq(0)

    expect(Navigation.kind_menu.where("destination LIKE ?", "http%"))
      .to all(be_external_link_provider)
      .and all(be_browser_mobile_navigation_option)

    expect(business.menus.count).to eq(32)
    expect(business.menus.where(web_navigation_option: nil).count).to eq(0)
    expect(business.menus.order(:disposition, :position).pluck(:disposition, :position))
      .to eq([
        ["drawer", 1],
        ["drawer", 2],
        ["drawer", 3],
        ["drawer", 4],
        ["drawer", 5],
        ["drawer", 6],
        ["drawer", 7],
        ["drawer", 8],
        ["drawer", 9],
        ["drawer", 10],
        ["drawer", 11],
        ["drawer", 12],
        ["footer", 1],
        ["footer", 2],
        ["footer", 3],
        ["header", 1],
        ["navigation", 1],
        ["navigation", 2],
        ["navigation", 3],
        ["navigation", 4],
        ["navigation", 5],
        ["navigation", 6],
        ["navigation", 7],
        ["profile_icon", 1],
        ["profile_icon", 2],
        ["profile_icon", 3],
        ["profile_icon", 4],
        ["profile_icon", 5],
        ["profile_icon", 6],
        ["profile_icon", 7],
        ["profile_icon", 8],
        ["profile_icon", 9]
      ])
  end
end
