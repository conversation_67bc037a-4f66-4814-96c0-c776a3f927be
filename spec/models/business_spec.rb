# frozen_string_literal: true

require "rails_helper"

RSpec.describe Business, type: :model do
  include ActiveSupport::Testing::TimeHelpers

  describe "#message_by_type" do
    let!(:business) { create(:business, :oab) }
    let(:message_type) { Enums::CustomTextType::CONTACT_MANAGER }

    let(:message) { business.reload.message_by_type(message_type) }

    context "when business has the message for the type" do
      let(:custom_message) { "Custom business message" }
      let!(:custom_text) do
        create(:custom_text,
          business:,
          message_type:,
          message: custom_message)
      end

      it { expect(message).to eq(custom_message) }
    end

    context "when business does not have the message for the type" do
      it { expect(message).to eq Enums::CustomTextType.translate(message_type)[:value] }
    end
  end

  describe "#current_payout" do
    before do
      travel_to Time.zone.parse("2022-04-08 00:00:01")
    end

    let!(:business) { create(:business, :oab, :with_cashback) }
    let!(:previous_payout) do
      create(:payout,
        business:,
        receiver_taxpayer_number: business.cnpj,
        status: Payout::Status::OPEN,
        due_on: "2022-04-02")
    end
    let!(:current_payout) do
      create(:payout,
        business:,
        receiver_taxpayer_number: business.cnpj,
        status: Payout::Status::OPEN,
        due_on: "2022-04-09")
    end

    it { expect(business.current_payout).to eq(current_payout) }
  end

  describe ".main" do
    let!(:main_business) { create(:business) }
    let!(:child_business) { create(:business, main_business:) }

    subject { described_class.main }

    it { is_expected.to eq([main_business]) }
  end

  describe "main business validation" do
    let(:main_business) { build(:business) }
    let(:child_business) { build(:business, main_business:) }
    let(:business_with_main_business_that_is_main) do
      build(
        :business,
        name: "Sample Business Premium",
        main_business:
      )
    end
    let(:business_with_main_business_that_is_child) do
      build(
        :business,
        name: "Sample Business Platinum",
        main_business: child_business
      )
    end

    it { expect(business_with_main_business_that_is_main).to be_valid }
    it { expect(business_with_main_business_that_is_child).to be_invalid }
  end

  describe "#accept_cashback?" do
    context "when main business cashback is true" do
      let(:main_business) { create(:business, :with_cashback) }

      context "when this business' cashback is also true" do
        let(:business) { create(:business, :with_cashback, main_business:) }

        it "must return true" do
          expect(business.accept_cashback?).to eq(true)
        end
      end

      context "when this business' cashback is false" do
        let(:business) { create(:business, cashback: false, main_business:) }

        it "must return false" do
          expect(business.accept_cashback?).to eq(false)
        end
      end
    end

    context "when main business cashback is false" do
      let(:main_business) { create(:business, cashback: false) }

      context "when this business' cashback is also false" do
        let(:business) { create(:business, cashback: false, main_business:) }

        it "must return false" do
          expect(business.accept_cashback?).to eq(false)
        end
      end

      context "when this business' cashback is true" do
        let(:business) { create(:business, :with_cashback, main_business:) }

        it "must return false" do
          expect(business.accept_cashback?).to eq(false)
        end
      end
    end
  end

  describe "#accept_giftcard?" do
    context "when main business giftcard is true" do
      let(:main_business) { create(:business, giftcard: true) }

      context "when this business' giftcard is also true" do
        let(:business) { create(:business, giftcard: true, main_business:) }

        it "must return true" do
          expect(business.accept_giftcard?).to eq(true)
        end
      end

      context "when this business' giftcard is false" do
        let(:business) { create(:business, giftcard: false, main_business:) }

        it "must return false" do
          expect(business.accept_giftcard?).to eq(false)
        end
      end
    end

    context "when main business giftcard is false" do
      let(:main_business) { create(:business, giftcard: false) }

      context "when this business' giftcard is also false" do
        let(:business) { create(:business, giftcard: false, main_business:) }

        it "must return false" do
          expect(business.accept_giftcard?).to eq(false)
        end
      end

      context "when this business' giftcard is true" do
        let(:business) { create(:business, giftcard: true, main_business:) }

        it "must return false" do
          expect(business.accept_giftcard?).to eq(false)
        end
      end
    end
  end

  describe "#integration_type validation" do
    let(:business) { create(:business) }

    it "must be present" do
      business.integration_type = nil
      expect(business).to be_invalid
    end

    it "must included on IntegrationType enum" do
      expect do
        business.integration_type = "any"
      end.to raise_error(ArgumentError, /is not a valid integration_type/)
    end
  end

  describe ".by_identifier" do
    let!(:business_1) { create(:business, cnpj: "**************") }
    let!(:business_2) { create(:business, cnpj: "98168474000141") }
    let!(:business_3) { create(:business, cnpj: "18987910000175") }

    it { expect(described_class.by_identifier(business_1.cnpj)).to match_array([business_1]) }
    it { expect(described_class.by_identifier(business_2.id)).to match_array([business_2]) }
    it { expect(described_class.by_identifier(business_3.id.to_s)).to match_array([business_3]) }
    it { expect(described_class.by_identifier("")).to match_array([business_1, business_2, business_3]) }
  end

  describe "sub business wallet destination validation" do
    let(:business) { create(:business, cashback_wallet_destination: Wallet::Kind::CASHBACK) }
    let(:sub_business) { build(:business, main_business: business, cashback_wallet_destination:) }

    context "when equal to main business" do
      let(:cashback_wallet_destination) { business.cashback_wallet_destination }

      it { expect(sub_business).to be_valid }
    end

    context "when different from main business" do
      let(:cashback_wallet_destination) { (Wallet::Kind.all - [business.cashback_wallet_destination]).sample }

      it do
        expect(sub_business).to be_invalid
        expect(sub_business.errors).to include :cashback_wallet_destination
      end
    end
  end

  describe ".current" do
    subject { described_class.current(headers) }

    let!(:project_config) { create(:project_config, web_domain: "app.example.com") }
    let!(:business) do
      create(:business, create_project_config: false, create_web_application: false, project_config:, api_secret: "123")
    end

    let!(:another_project_config) { create(:project_config, web_domain: "app.another.com") }
    let!(:another_business) do
      create(:business, create_project_config: false, create_web_application: false, project_config: another_project_config, api_secret: "456")
    end

    describe "by Web-Domain from project config" do
      let(:headers) { {"Web-Domain" => project_config.web_domain} }

      it { is_expected.to eq business }
    end

    describe "by Web-Domain from web application distribution url" do
      let!(:web_application) do
        create(:web_application, business:, cloudfront_distribution_url: "abcxyz.cloudfront.net")
      end
      let!(:another_web_application) do
        create(:web_application, business: another_business, cloudfront_distribution_url: "qwerty.cloudfront.net")
      end
      let(:headers) { {"Web-Domain" => web_application.cloudfront_distribution_url} }

      it { is_expected.to eq business }
    end

    describe "by Api-Secret" do
      let(:headers) { {"Api-Secret" => business.api_secret} }

      it { is_expected.to eq business }
    end

    describe "by both" do
      let(:headers) { {"Api-Secret" => another_business.api_secret, "Web-Domain" => project_config.web_domain} }

      it { is_expected.to eq business }
    end

    describe "by wrong credential" do
      let(:headers) { {"Web-Domain" => "any"} }

      it { is_expected.to be_nil }
    end
  end

  describe ".current!" do
    subject(:current!) { described_class.current!(headers) }

    let!(:project_config) { create(:project_config, web_domain: "app.example.com") }
    let!(:business) { create(:business, create_project_config: false, project_config:, api_secret: "123") }

    let!(:another_project_config) { create(:project_config, web_domain: "app.another.com") }
    let!(:another_business) do
      create(:business, create_project_config: false, project_config: another_project_config, api_secret: "456")
    end

    describe "by Web-Domain from project config" do
      let(:headers) { {"Web-Domain" => project_config.web_domain} }

      it { is_expected.to eq business }
    end

    describe "by Web-Domain from web application distribution url" do
      let!(:web_application) do
        create(:web_application, business:, cloudfront_distribution_url: "abcxyz.cloudfront.net")
      end
      let!(:another_web_application) do
        create(:web_application, business: another_business, cloudfront_distribution_url: "qwerty.cloudfront.net")
      end
      let(:headers) { {"Web-Domain" => web_application.cloudfront_distribution_url} }

      it { is_expected.to eq business }
    end

    describe "by Api-Secret" do
      let(:headers) { {"Api-Secret" => business.api_secret} }

      it { is_expected.to eq business }
    end

    describe "by both" do
      let(:headers) { {"Api-Secret" => another_business.api_secret, "Web-Domain" => project_config.web_domain} }

      it { is_expected.to eq business }
    end

    describe "by wrong credential" do
      let(:headers) { {"Web-Domain" => "any"} }

      it { expect { current! }.to raise_error ActiveRecord::RecordNotFound }
    end
  end

  describe "#update_policy_url" do
    context "when create business" do
      it "save in project config a policy_url" do
        business = create(:business, name: "Business XPTO Çá")

        expect(business.project_config.reload.policy_url).to eq("businessxptoca.clubedefidelidade.com")
      end
    end

    context "when update business" do
      it "update policy_url project_config when name is changed" do
        business = create(:business)

        expect { business.update(name: "Business XYZ Çá") }
          .to change { business.project_config.policy_url }.to("businessxyzca.clubedefidelidade.com")
      end

      it "does not update policy_url project_config when name is not changed" do
        business = create(:business)

        expect { business.update(cnpj: CNPJ.generate) }
          .to not_change { business.project_config.policy_url }
          .and change { business.cnpj }
      end
    end
  end

  describe "Cards" do
    let(:business) { create(:business) }

    it "return nil when not card association" do
      expect(business.cards).to be_empty
    end

    it "return card when association exists" do
      card = create(:card, business:)
      expect(business.cards).to eq([card])
    end
  end

  describe "delegates" do
    let!(:business) { create(:business) }
    let!(:card_support) { create(:card, :support_view_type, business:) }
    let!(:card_faq) { create(:card, :faq_view_type, business:) }

    it "delegates the supports method to cards" do
      expect(business.support).to eq([card_support])
    end

    it "delegates the faqs method to cards" do
      expect(business.faq).to eq([card_faq])
    end
  end

  describe "Subscription Config" do
    let(:business) { create(:business) }

    it "return nil when not business subscription_config association" do
      expect(business.subscription_config).to be_nil
    end

    it "return business subscription when association exists" do
      subscription_config = create(:subscription_config, business:)
      expect(business.subscription_config).to eq(subscription_config)
    end
  end
end
