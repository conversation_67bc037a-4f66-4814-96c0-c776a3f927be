# frozen_string_literal: true

require "rails_helper"

RSpec.describe Wallet, type: :model do
  describe "#formatted_balance" do
    subject { wallet.formatted_balance }

    let(:wallet) { build_stubbed(:wallet, currency:, balance: 15326) }

    context "when currency is BRL" do
      let(:currency) { "BRL" }

      it { is_expected.to eq 153.26 }
    end

    context "when currency is points" do
      let(:currency) { "points" }

      it { is_expected.to eq 15326 }
    end
  end

  describe "kind presence validation" do
    subject { wallet.validate }

    let(:user) { build_stubbed(:user) }
    let(:wallet) { build_stubbed(:wallet, user:, currency:, kind:) }

    context "when currency is BRL" do
      let(:currency) { "BRL" }

      context "and kind is present" do
        let(:kind) { Wallet::Kind::CASHBACK }

        it { is_expected.to be true }
      end

      context "and kind is blank" do
        let(:kind) { nil }

        it { is_expected.to be false }
      end
    end

    context "when currency is not BRL" do
      let(:currency) { "points" }
      let(:kind) { nil }

      it { is_expected.to be true }
    end
  end

  describe "#cash_in!" do
    let(:business) { create(:business) }
    let(:user) { create(:user, business:) }

    let(:inflow_wallet) { create(:wallet, :points, :inflow) }
    let(:business_wallet) { create(:wallet, :points, business:) }
    let(:user_wallet) { create(:wallet, :points, user:) }

    let(:amount) { 2000 }
    let(:kind) { Wallet::Entry::Kind::POINT_ACCRUED }

    it "cashes in user wallet" do
      expect do
        user_wallet.cash_in!(amount:, operation_kind: kind)

        [inflow_wallet, business_wallet, user_wallet].each(&:reload)
      end.to not_change(inflow_wallet, :balance)
        .and not_change(business_wallet, :balance)
        .and change(user_wallet, :balance).by(amount)
        .and change(inflow_wallet.entries.credit.where(amount:, kind:), :count).by(1)
        .and change(inflow_wallet.entries.debit.where(amount:, kind:), :count).by(1)
        .and change(business_wallet.entries.credit.where(amount:, kind:), :count).by(1)
        .and change(business_wallet.entries.debit.where(amount:, kind:), :count).by(1)
        .and change(user_wallet.entries.credit.where(amount:, kind:), :count).by(1)
    end
  end

  describe "#cash_out!" do
    let(:business) { create(:business) }
    let(:user) { create(:user, business:) }

    let(:user_wallet) { create(:wallet, :points, user:, balance: 3000) }
    let(:outflow_wallet) { create(:wallet, :points, :outflow) }
    let(:business_wallet) { create(:wallet, :points, business:) }

    let(:kind) { Wallet::Entry::Kind::POINT_EXPIRED }

    context "with balance" do
      let(:amount) { user_wallet.balance }

      it "cashes out user wallet" do
        expect do
          user_wallet.cash_out!(amount:, operation_kind: kind)

          [user_wallet, outflow_wallet, business_wallet].each(&:reload)
        end.to change(user_wallet, :balance).by(-amount)
          .and change(outflow_wallet, :balance).by(amount)
          .and not_change(business_wallet, :balance)
          .and change(user_wallet.entries.debit.where(amount:, kind:), :count).by(1)
          .and change(outflow_wallet.entries.credit.where(amount:, kind:), :count).by(1)
          .and not_change(business_wallet.entries, :count)
      end
    end

    context "without balance" do
      let(:amount) { user_wallet.balance + 1 }

      it "raises error and does not cash out user wallet" do
        expect do
          user_wallet.cash_out!(amount:, operation_kind: kind)

          [user_wallet, outflow_wallet, business_wallet].each(&:reload)
        end.to raise_error(Wallet::NoBalanceError, I18n.t("wallet.errors.no_balance"))
          .and not_change(user_wallet, :balance)
          .and not_change(outflow_wallet, :balance)
          .and not_change(business_wallet, :balance)
          .and not_change(WalletEntry, :count)
      end
    end
  end

  describe "#reverse!" do
    let(:business) { create(:business, :with_cashback, currency:) }
    let(:user) { create(:user, business:) }
    let(:user_wallet) { create(:wallet, :cashback, currency:, user:, balance: 4956) }
    let(:business_wallet) { create(:wallet, :cashback, currency:, business:, balance: 0) }
    let(:outflow_wallet) { create(:wallet, :outflow, :cashback, currency:, balance: 644592) }

    let(:amount) { 15627 }

    context "with cashback wallet" do
      let(:currency) { "BRL" }

      it "reverses payout" do
        expect { user_wallet.reverse!(amount:) }
          .to change { user_wallet.reload.balance }.by(amount)
          .and not_change { business_wallet.reload.balance }
          .and change { outflow_wallet.reload.balance }.by(-amount)
      end
    end
  end

  describe "#withdraw!" do
    context "when currency is points" do
      let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.03) }
      let(:user) { create(:user, business:) }
      let(:user_id) { user.id }
      let!(:non_credited_fund) do
        create(:fund, :points, :approved, user:, credited: false, amount: 1000, expires_at: 1.day.from_now)
      end
      let!(:expired_fund) do
        create(:fund, :points, :available, user:, credited: true, amount: 1000, expires_at: 1.day.ago)
      end
      let!(:fund_another_user) do
        another_user = create(:user, business:)
        create(:fund, :points, :available, user: another_user, credited: true, amount: 1500, expires_at: 1.day.from_now)
      end
      let!(:funds) do
        [
          create(:fund, :points, :available, user:, credited: true, amount: 1500, expires_at: 1.hour.from_now),
          create(:fund, :points, :available, user:, credited: true, amount: 1000, expires_at: 1.day.from_now),
          create(:fund, :points, :available, user:, credited: true, amount: 2000, expires_at: 2.days.from_now)
        ]
      end
      let(:user_wallet) { create(:wallet, :points, user:, balance: funds.sum(&:amount)) }
      let(:outflow_wallet) { create(:wallet, :points, :outflow) }
      let(:business_wallet) { create(:wallet, :points, business:) }

      describe "amount 1000" do
        let(:amount) { 1000 }

        it "redeems points" do
          expect { user_wallet.withdraw!(amount:) }
            .to change { funds[0].reload.remaining_amount }.to(500)
            .and not_change { funds[1].reload.remaining_amount }
            .and not_change { funds[2].reload.remaining_amount }
            .and not_change { non_credited_fund.reload.remaining_amount }
            .and not_change { expired_fund.reload.remaining_amount }
            .and not_change { fund_another_user.reload.remaining_amount }
            .and change { user_wallet.reload.balance }.by(-amount)
            .and change { outflow_wallet.reload.balance }.by(amount)
            .and not_change { business_wallet.reload.balance }
        end
      end

      describe "amount 1400" do
        let(:amount) { 1400 }

        it "redeems points" do
          expect { user_wallet.withdraw!(amount:) }
            .to change { funds[0].reload.remaining_amount }.to(100)
            .and not_change { funds[1].reload.remaining_amount }
            .and not_change { funds[2].reload.remaining_amount }
            .and not_change { non_credited_fund.reload.remaining_amount }
            .and not_change { expired_fund.reload.remaining_amount }
            .and not_change { fund_another_user.reload.remaining_amount }
            .and change { user_wallet.reload.balance }.by(-amount)
            .and change { outflow_wallet.reload.balance }.by(amount)
            .and not_change { business_wallet.reload.balance }
        end
      end

      describe "amount 1500" do
        let(:amount) { 1500 }

        it "redeems points" do
          expect { user_wallet.withdraw!(amount:) }
            .to change { funds[0].reload.remaining_amount }.to(0)
            .and not_change { funds[1].reload.remaining_amount }
            .and not_change { funds[2].reload.remaining_amount }
            .and not_change { non_credited_fund.reload.remaining_amount }
            .and not_change { expired_fund.reload.remaining_amount }
            .and not_change { fund_another_user.reload.remaining_amount }
            .and change { user_wallet.reload.balance }.by(-amount)
            .and change { outflow_wallet.reload.balance }.by(amount)
            .and not_change { business_wallet.reload.balance }
        end
      end

      describe "amount 1600" do
        let(:amount) { 1600 }

        it "redeems points" do
          expect { user_wallet.withdraw!(amount:) }
            .to change { funds[0].reload.remaining_amount }.to(0)
            .and change { funds[1].reload.remaining_amount }.to(900)
            .and not_change { funds[2].reload.remaining_amount }
            .and not_change { non_credited_fund.reload.remaining_amount }
            .and not_change { expired_fund.reload.remaining_amount }
            .and not_change { fund_another_user.reload.remaining_amount }
            .and change { user_wallet.reload.balance }.by(-amount)
            .and change { outflow_wallet.reload.balance }.by(amount)
            .and not_change { business_wallet.reload.balance }
        end
      end

      describe "amount 2400" do
        let(:amount) { 2400 }

        it "redeems points" do
          expect { user_wallet.withdraw!(amount:) }
            .to change { funds[0].reload.remaining_amount }.to(0)
            .and change { funds[1].reload.remaining_amount }.to(100)
            .and not_change { funds[2].reload.remaining_amount }
            .and not_change { non_credited_fund.reload.remaining_amount }
            .and not_change { expired_fund.reload.remaining_amount }
            .and not_change { fund_another_user.reload.remaining_amount }
            .and change { user_wallet.reload.balance }.by(-amount)
            .and change { outflow_wallet.reload.balance }.by(amount)
            .and not_change { business_wallet.reload.balance }
        end
      end

      describe "amount 2500" do
        let(:amount) { 2500 }

        it "redeems points" do
          expect { user_wallet.withdraw!(amount:) }
            .to change { funds[0].reload.remaining_amount }.to(0)
            .and change { funds[1].reload.remaining_amount }.to(0)
            .and not_change { funds[2].reload.remaining_amount }
            .and not_change { non_credited_fund.reload.remaining_amount }
            .and not_change { expired_fund.reload.remaining_amount }
            .and not_change { fund_another_user.reload.remaining_amount }
            .and change { user_wallet.reload.balance }.by(-amount)
            .and change { outflow_wallet.reload.balance }.by(amount)
            .and not_change { business_wallet.reload.balance }
        end
      end

      describe "amount 2600" do
        let(:amount) { 2600 }

        it "redeems points" do
          expect { user_wallet.withdraw!(amount:) }
            .to change { funds[0].reload.remaining_amount }.to(0)
            .and change { funds[1].reload.remaining_amount }.to(0)
            .and change { funds[2].reload.remaining_amount }.to(1900)
            .and not_change { non_credited_fund.reload.remaining_amount }
            .and not_change { expired_fund.reload.remaining_amount }
            .and not_change { fund_another_user.reload.remaining_amount }
            .and change { user_wallet.reload.balance }.by(-amount)
            .and change { outflow_wallet.reload.balance }.by(amount)
            .and not_change { business_wallet.reload.balance }
        end
      end

      describe "amount 4500" do
        let(:amount) { 4500 }

        it "redeems points" do
          expect { user_wallet.withdraw!(amount:) }
            .to change { funds[0].reload.remaining_amount }.to(0)
            .and change { funds[1].reload.remaining_amount }.to(0)
            .and change { funds[2].reload.remaining_amount }.to(0)
            .and not_change { non_credited_fund.reload.remaining_amount }
            .and not_change { expired_fund.reload.remaining_amount }
            .and not_change { fund_another_user.reload.remaining_amount }
            .and change { user_wallet.reload.balance }.by(-amount)
            .and change { outflow_wallet.reload.balance }.by(amount)
            .and not_change { business_wallet.reload.balance }
        end
      end

      describe "no balance" do
        let(:amount) { 4600 }

        it "does not redeem points" do
          expect { user_wallet.withdraw!(amount:) }
            .to raise_error(Wallet::NoBalanceError, "Saldo insuficiente")
            .and not_change { funds[0].reload.remaining_amount }
            .and not_change { funds[1].reload.remaining_amount }
            .and not_change { funds[2].reload.remaining_amount }
            .and not_change { non_credited_fund.reload.remaining_amount }
            .and not_change { expired_fund.reload.remaining_amount }
            .and not_change { fund_another_user.reload.remaining_amount }
            .and not_change { user_wallet.reload.balance }
            .and not_change { outflow_wallet.reload.balance }
            .and not_change { business_wallet.reload.balance }
        end
      end
    end
  end
end
