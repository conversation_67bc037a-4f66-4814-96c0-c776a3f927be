# frozen_string_literal: true

require "rails_helper"

RSpec.describe User, type: :model do
  let(:business) { create(:business) }

  describe "#register_token" do
    let!(:user) { create(:user) }
    let(:token) { "ABCD1234" }
    let(:client) { "some-client" }
    let(:type) { 1 }

    context "when the user does not have the device registered" do
      it "registers the device for the user" do
        user.register_token(client, token, type)

        expect(user.device_tokens["registereds"]).to eq(1)
        expect(user.device_tokens["authorizeds"]).to eq(1)
        expect(user.device_tokens["tokens"].size).to eq(1)
        expect(user.device_tokens["tokens"].first).to include({
          "token" => token,
          "device_type" => type,
          "client" => client
        })
      end
    end

    context "when the user already has the device registered" do
      before do
        user.register_token(client, token, type)
      end

      it "updates the device for the user" do
        user.register_token(client, token, 0)

        expect(user.device_tokens["registereds"]).to eq(1)
        expect(user.device_tokens["authorizeds"]).to eq(1)
        expect(user.device_tokens["tokens"].size).to eq(1)
        expect(user.device_tokens["tokens"].first).to include({
          "token" => token,
          "device_type" => 0,
          "client" => client
        })
      end
    end

    context "when the user has another device registered" do
      before do
        user.register_token(client, "XYZ9876", type)
      end

      it "registers the device for the user" do
        user.register_token(client, token, type)

        expect(user.device_tokens["registereds"]).to eq(2)
        expect(user.device_tokens["authorizeds"]).to eq(2)
        expect(user.device_tokens["tokens"].size).to eq(2)
        expect(user.device_tokens["tokens"].first).to include({
          "token" => "XYZ9876",
          "device_type" => type,
          "client" => client
        })
        expect(user.device_tokens["tokens"].second).to include({
          "token" => token,
          "device_type" => type,
          "client" => client
        })
      end

      context "when user has 5 devices registered" do
        before do
          user.register_token(client, "XYZ1111", type)
          user.register_token(client, "XYZ2222", type)
          user.register_token(client, "XYZ3333", type)
          user.register_token(client, "XYZ4444", type)
          user.register_token(client, "XYZ5555", type)
        end

        it "must insert new device and remove the oldest one" do
          user.register_token(client, token, type)

          expect(user.device_tokens["registereds"]).to eq(5)
          expect(user.device_tokens["authorizeds"]).to eq(5)
          expect(user.device_tokens["tokens"].size).to eq(5)

          expect(user.device_tokens["tokens"].find { _1["token"] == "XYZ1111" }).to be_blank
          expect(user.device_tokens["tokens"].first).to include({
            "token" => "XYZ2222",
            "device_type" => type,
            "client" => client
          })
          expect(user.device_tokens["tokens"].fifth).to include({
            "token" => token,
            "device_type" => type,
            "client" => client
          })
        end
      end
    end
  end

  describe "normalize cellphone" do
    let(:user) { build(:user, cellphone: "(53) 9 91234567") }

    subject { user.cellphone }

    before { user.validate }

    it { is_expected.to eq("***********") }
  end

  describe "#create_new_auth_token" do
    let!(:business) { create(:business, :lecupon) }
    let!(:user) { create(:user, business:) }

    it "creates a new user auth token" do
      auth_token = user.create_new_auth_token
      expect(auth_token.keys).to match_array(%w[access-token token-type client expiry uid])
    end
  end

  describe "#affiliated?" do
    let!(:oab) { create(:business, :oab) }
    let!(:lecupon) { create(:business, :lecupon) }

    let!(:user_lecupon) { create(:user, business: lecupon) }
    let!(:user_oab) { create(:user, business: oab) }

    it { expect(user_lecupon.affiliated?(oab.id)).to be false }
    it { expect(user_lecupon.affiliated?(lecupon.id)).to be true }
    it { expect(user_oab.affiliated?(oab.id)).to be true }
    it { expect(user_oab.affiliated?(lecupon.id)).to be false }
  end

  describe "#create user with deleted user" do
    let!(:business) { create(:business) }
    let!(:user) { create(:user, business:, authorized_user:) }

    let!(:another_business) { create(:business) }
    let!(:authorized_user) { create(:authorized_user, business:) }

    context "with deleted user" do
      before do
        user.update!(deleted_at: DateTime.current, deleted: true)
      end

      it "must be able to create new user with same email and cpf" do
        new_user = build(:user, business:, email: user.email, cpf: user.cpf, password: "123456", authorized_user:)
        expect(new_user.save!).to be_truthy
        expect(User.count).to eq(2)
      end

      it "create new user with email and cpf in another subdomain" do
        new_user = build(:user, business: another_business, email: user.email, cpf: user.cpf, password: "123456")
        expect(new_user.save!).to be_truthy
        expect(User.count).to eq(2)
      end

      it "create new user with cpf" do
        new_user = build(:user, business:, email: "<EMAIL>", cpf: user.cpf, password: "123456")
        expect(new_user.save!).to be_truthy
        expect(User.count).to eq(2)
      end

      it "create new user with cpf in another subdomain" do
        new_user = build(:user, business: another_business, email: "<EMAIL>", cpf: user.cpf, password: "123456")
        expect(new_user.save!).to be_truthy
        expect(User.count).to eq(2)
      end
    end

    context "with active user" do
      it "create new user with email and cpf" do
        new_user = build(:user, business:, email: user.email, cpf: user.cpf, password: "123456")
        expect { new_user.save! }.to raise_error(ActiveRecord::RecordInvalid)
        expect(new_user.errors.any? { |error| error.full_message == "Cpf já está em uso" }).to eq(true)
        expect(User.count).to eq(1)
      end

      it "create new user with cpf" do
        new_user = build(:user, business:, email: "<EMAIL>", cpf: user.cpf, password: "123456")
        expect { new_user.save! }.to raise_error(ActiveRecord::RecordInvalid)
        expect(new_user.errors.any? { |error| error.full_message == "Cpf já está em uso" }).to eq(true)
        expect(User.count).to eq(1)
      end
    end
  end

  describe "#projects_tree" do
    let!(:care) { create(:business) }
    let!(:care_plus) { create(:business, main_business: care) }
    let!(:care_user) { create(:user, business: care) }
    let!(:care_plus_user) { create(:user, business: care_plus) }
    let!(:inactive_care_user) { create(:user, business: care, active: false) }

    it { expect(care_user.projects_tree).to eq([care]) }
    it { expect(care_plus_user.projects_tree).to eq([care_plus, care]) }
    it { expect(inactive_care_user.projects_tree).to be_empty }
  end

  describe ".active" do
    let!(:active_user) { create(:user, active: true) }
    let!(:inactive_user) { create(:user, active: false) }

    subject { described_class.active }

    it { is_expected.to eq([active_user]) }
  end

  describe "remove emoji from name" do
    let(:user_with_emoji) { build(:user, name: "Nome com emoji 😊🧡 teste", business:) }
    let(:user_without_emoji) { build(:user, name: "Nome com pontuações áàâãä", business:) }

    before do
      user_with_emoji.validate
      user_without_emoji.validate
    end

    it { expect(user_with_emoji.name).to eq("Nome Com Emoji Teste") }
    it { expect(user_without_emoji.name).to eq("Nome Com Pontuações Áàâãä") }
  end

  describe "transliterate email" do
    let(:user_with_unaccepted_email) { build(:user, email: "gonç**************", business:) }

    before do
      user_with_unaccepted_email.validate
    end

    it { expect(user_with_unaccepted_email.email).to eq("<EMAIL>") }
  end

  describe "#mailable?" do
    let(:mailable_user) { build(:user, email_status: Enums::User::EmailStatus::NEW) }
    let(:unmailable_user) { build(:user, email_status: Enums::User::EmailStatus::HARD_BOUNCE) }

    it { expect(mailable_user.mailable?).to eq(true) }
    it { expect(unmailable_user.mailable?).to eq(false) }
  end

  describe "#cpf validation" do
    let(:user) { create(:user) }
    context "when cpf is not stripped" do
      it "must strip cpf" do
        user.cpf = "561.200.040-50"
        user.save!
        expect(user.reload.cpf).to eq("***********")
      end
    end

    context "when cpf is not valid" do
      it "must nullify cpf and invalidate instance" do
        user.cpf = "***********"
        expect(user).to be_invalid
        expect([user.errors.first.attribute, user.errors.first.type]).to eq([:"authorized_user.cpf", "inválido"])
      end
    end
  end

  describe "#email validation" do
    let(:business) { create(:business) }
    context "when email has special char" do
      let(:user) { build(:user, business:, email: "invá*************") }
      let(:user_two) { build(:user, business:, email: "user🙂@mail.com") }

      context "when can transliterate validate it back" do
        it "must be valid" do
          expect(user.valid?).to eq(true)
        end
      end

      context "when transliterate can not validate it back" do
        it "must be invalid" do
          expect(user_two.valid?).to eq(false)
        end
      end
    end

    context "when email does not have only one @" do
      let(:user) { build(:user, business:, email: "user_onemail.com") }
      let(:user_two) { build(:user, business:, email: "user_two@@mail.com") }
      it "must be invalid" do
        expect(user.valid?).to eq(false)
        expect(user_two.valid?).to eq(false)
      end
    end

    context "when email does not have top-level domain" do
      let(:user) { build(:user, business:, email: "user_one@mail") }
      let(:user_two) { build(:user, business:, email: "user_two@mailcom") }
      it "must be invalid" do
        expect(user.valid?).to eq(false)
        expect(user_two.valid?).to eq(false)
      end
    end
  end

  describe "#activation validation" do
    context "when trying to activate cpf that is banned" do
      let!(:user) { create(:user) }

      before do
        BannedCpf.create(cpf: user.cpf)
      end

      it "must be invalid" do
        user.reload
        expect(user).not_to be_active
        user.update(active: true)
        expect(user).to be_invalid
        expect(user.errors.first.message).to eq(I18n.t("user.sign_up.banned_cpf"))
      end
    end
  end

  describe "#password_validation" do
    context "when password is shorter than 6 chars" do
      let(:user) { create(:user) }

      it "must be invalid" do
        user.password = "1234"
        expect(user.valid?).to eq(false)
      end

      context "when business has no_signup integration" do
        before { user.business.project_config.update(user_manager: Enums::UserManager::NO_SIGNUP) }
        it "must be valid" do
          user.password = "1234"
          expect(user.valid?).to eq(true)
        end
      end
    end

    context "when password is longer than 128 chars" do
      let(:user) { create(:user) }
      it "must be invalid" do
        user.password = "n" * 129
        expect(user.valid?).to eq(false)
      end

      context "when business has no_signup integration" do
        before { user.business.project_config.update(user_manager: Enums::UserManager::NO_SIGNUP) }
        it "must be valid" do
          user.password = "n" * 129
          expect(user.valid?).to eq(true)
        end
      end
    end

    context "when password is between 6 and 128 chars" do
      let(:user) { create(:user) }

      it "must be valid" do
        user.password = "12345678"
        expect(user.valid?).to eq(true)
      end
    end
  end

  describe "#propagate_active" do
    context "when activating user" do
      let!(:authorized_user) { create(:authorized_user, active: false) }
      let!(:user) { create(:user, business: authorized_user.business, authorized_user:, active: false) }

      it "must activate authorized_user" do
        expect { user.update!(active: true) }.to change(authorized_user, :active).from(false).to(true)
      end
    end

    context "when deactivating user" do
      let!(:authorized_user) { create(:authorized_user) }
      let!(:user) { create(:user, business: authorized_user.business, authorized_user:) }

      it "must deactivate authorized_user" do
        expect { user.update!(active: false) }.to change(authorized_user, :active).from(true).to(false)
      end
    end
  end

  describe "#revoke_auth" do
    let!(:user) { create(:user) }
    before { user.create_new_auth_token }

    context "when changing default_auth_flow" do
      it "must revoke auth" do
        expect { user.update!(default_auth_flow: true) }
          .to change(user, :api_token).to(nil)
          .and change(user, :jti)
      end
    end
  end

  describe "#mark_authorized_user_as_registered" do
    it "must set authorized_user registered_at timestamp after create user" do
      user = create(:user)
      expect(user.authorized_user.registered_at).to be_present
    end
  end

  describe "#smart_token" do
    context "when scheduling smart_token expiration" do
      context "when smart_token is empty" do
        let(:user) { create(:user, smart_token: "") }

        it "must not schedule worker to expire token" do
          expect(User::SmartTokenExpireWorker).not_to receive(:perform_in)

          user.schedule_smart_token_expiration
        end
      end

      context "when smart_token is present" do
        let(:user) { create(:user, smart_token: "ABC123") }

        it "must schedule User::SmartTokenExpireWorker with current smart_token" do
          expect(User::SmartTokenExpireWorker).to receive(:perform_in).with(5.hours, user.smart_token)

          user.schedule_smart_token_expiration
        end
      end
    end

    context "when creating smart_token" do
      let(:user) { create(:user, smart_token: "") }

      before do
        allow(User::SmartTokenExpireWorker).to receive(:perform_in)
      end

      it "must add token to user and schedule to expire it" do
        expect { user.create_smart_token }.to change(user, :smart_token).from("")
        expect(User::SmartTokenExpireWorker)
          .to have_received(:perform_in)
          .with(User::HOURS_TO_EXPIRE_SMART_TOKEN, user.smart_token)
      end
    end

    context "when expiring smart_token" do
      let(:user) { create(:user, smart_token: "ABC1234") }

      it "update smart_token column in user to empty" do
        expect { user.expire_smart_token }.to change(user, :smart_token).from("ABC1234").to("")
      end
    end
  end

  describe "#set_cashback_and_giftcard" do
    let!(:business) { create(:business, cashback: false, giftcard: true) }

    it "must set cashback and giftcard as it is on business" do
      user = create(:user, business:)
      expect(user.cashback).to eq(business.cashback)
      expect(user.giftcard).to eq(business.giftcard)
    end
  end

  describe "authorization" do
    context "when creating a user" do
      let(:business) { create(:business) }
      let(:user) { build(:user, business:) }
      context "when authorized_user exists" do
        let!(:authorized_user) { create(:authorized_user, cpf: user.cpf, business: user.business) }

        it "must update its data to sync with user" do
          user.save!
          user.reload
          authorized_user.reload
          expect(user.authorized_user).to eq(authorized_user)
          expect(authorized_user.name).to eq(Utils::NameNormalizer.call(user.name))
        end
      end

      context "when authorized_user does not exists" do
        it "must create a new one" do
          expect { user.save! }.to change(AuthorizedUser, :count).by(1)

          expect(AuthorizedUser.first.cpf).to eq(user.cpf)
        end
      end
    end
  end

  describe "#points_to_expire" do
    let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.03) }
    let(:user) { create(:user, business:) }
    let!(:funds) do
      [
        create(:fund, :points, :available, user:, credited: true, amount: 1500, expires_at: 14.days.from_now),
        create(:fund, :points, :available, user:, credited: true, amount: 2800, expires_at: 1.hour.from_now)
      ]
    end
    let!(:fund_far_expiration) do
      create(:fund, :points, :available, user:, credited: true, amount: 1000, expires_at: 16.days.from_now)
    end
    let!(:fund_another_user) do
      another_user = create(:user, business:)
      create(:fund, :points, :available, user: another_user, credited: true, amount: 1500, expires_at: 14.days.from_now)
    end

    subject { user.points_to_expire }

    it { is_expected.to eq(4300) }
  end

  describe "associations" do
    it "has many subscriptions" do
      association = User.reflect_on_association(:subscriptions)
      expect(association.macro).to eq(:has_many)
    end
  end

  describe "normalize name" do
    it "normalizes names with particles" do
      user = create(:user, name: "PEDRO DE LARA")
      user.validate
      expect(user.name).to eq("Pedro de Lara")
    end
  end

  describe "#force_logout!" do
    let(:user) do
      create(:user,
        full_address: "123 Main St",
        lat: 40.7128,
        lng: -74.0060,
        tokens: {valid: true},
        api_token: "abc123",
        web_token: "def456",
        smart_token: "ghi789",
        jti: "old_jti")
    end

    it "resets all sensitive data and tracking without updating timestamps" do
      create_list(:allowlisted_jwt, 3, user: user)
      original_updated_at = user.updated_at
      user.force_logout!
      user.reload

      expect(user.updated_at.strftime("%Y-%m-%d %H:%M:%S.%3N")).to eq(
        original_updated_at.strftime("%Y-%m-%d %H:%M:%S.%3N")
      )
      expect(user.full_address).to be_nil
      expect(user.lat).to be_nil
      expect(user.lng).to be_nil
      expect(user.tokens).to be_nil
      expect(user.api_token).to be_nil
      expect(user.web_token).to be_nil
      expect(user.smart_token).to be_nil
      expect(user.jti).not_to eq("old_jti")
      expect(user.allowlisted_jwts.count).to eq(0)
    end
  end
end
