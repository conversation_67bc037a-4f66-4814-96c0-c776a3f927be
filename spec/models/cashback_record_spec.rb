# frozen_string_literal: true

require "rails_helper"
require "sidekiq/testing"

RSpec.describe CashbackRecord, type: :model do
  describe "#by_organization_id" do
    let!(:organization_one) { create(:organization) }
    let!(:organization_two) { create(:organization) }
    let!(:branch_one) { create(:branch, :online, organization: organization_one) }
    let!(:branch_online) { create(:branch, :online, organization: organization_two) }
    let!(:coupon_one) { create(:cupon, :online, branch: branch_online) }
    let!(:coupon_two) { create(:cupon, :online, branch: branch_one) }
    let!(:coupon_three) { create(:cupon, :online, branch: branch_online) }
    let!(:order_one) { create(:order, :online, cupon: coupon_one) }
    let!(:order_two) { create(:order, :online, cupon: coupon_two) }
    let!(:order_three) { create(:order, :online, cupon: coupon_three) }
    let!(:cashback_one) { create(:cashback_record, order: order_one) }
    let!(:cashback_two) { create(:cashback_record, order: order_three) }
    let(:by_organization_id) { CashbackRecord.by_organization_id(organization_two.id) }

    it { expect(by_organization_id).to contain_exactly(cashback_one, cashback_two) }
  end

  describe "#by_branch_id" do
    let!(:organization_one) { create(:organization) }
    let!(:organization_two) { create(:organization) }
    let!(:branch_one) { create(:branch, :online, organization: organization_one) }
    let!(:branch_online) { create(:branch, :online, organization: organization_two) }
    let!(:coupon_one) { create(:cupon, :online, branch: branch_online) }
    let!(:coupon_two) { create(:cupon, :online, branch: branch_one) }
    let!(:coupon_three) { create(:cupon, :online, branch: branch_online) }
    let!(:order_one) { create(:order, :online, cupon: coupon_one) }
    let!(:order_two) { create(:order, :online, cupon: coupon_two) }
    let!(:order_three) { create(:order, :online, cupon: coupon_three) }
    let!(:cashback_one) { create(:cashback_record, order: order_one) }
    let!(:cashback_two) { create(:cashback_record, order: order_three) }
    let(:by_branch_id) { CashbackRecord.by_branch_id(branch_online.id) }

    it { expect(by_branch_id).to contain_exactly(cashback_one, cashback_two) }
  end

  describe "#by_order_number" do
    let!(:order_one) { create(:order, :online, number: "LC123") }
    let!(:order_two) { create(:order, :online, number: "LC456") }
    let!(:order_three) { create(:order, :online, number: "LC012") }
    let!(:cashback_one) { create(:cashback_record, order: order_one) }
    let!(:cashback_two) { create(:cashback_record, order: order_two) }
    let!(:cashback_three) { create(:cashback_record, order: order_three) }
    let(:by_order_number) { CashbackRecord.by_order_number("12") }

    it { expect(by_order_number).to contain_exactly(cashback_one, cashback_three) }
  end

  describe "#by_cpf" do
    let!(:user_one) { create(:user, cpf: "63993911059") }
    let!(:user_two) { create(:user, cpf: "25012279050") }
    let!(:user_three) { create(:user, cpf: "31441855076") }
    let!(:cashback_one) { create(:cashback_record, user: user_one) }
    let!(:cashback_two) { create(:cashback_record, user: user_two) }
    let!(:cashback_three) { create(:cashback_record, user: user_three) }
    let(:by_cpf) { CashbackRecord.by_cpf("05") }

    it { expect(by_cpf).to contain_exactly(cashback_one, cashback_two) }
  end

  describe "after_save" do
    let!(:user) { create(:user, :with_cashback) }
    let!(:order) { create(:order, :online, user:) }
    let(:cashback) { build(:cashback_record, order:, user:, business: user.business) }

    it "creates history" do
      expect { cashback.save }.to change(CashbackRecordHistory, :count).by(1)
    end
  end

  describe "guard transferred on destroy" do
    let!(:cashback_one) { create(:cashback_record, transaction_status: Enums::CashbackRecordStatus::PENDING) }
    let!(:cashback_two) { create(:cashback_record, transaction_status: Enums::CashbackRecordStatus::APPROVED) }
    let!(:cashback_three) { create(:cashback_record, transaction_status: Enums::CashbackRecordStatus::TRANSFERRED) }
    let!(:cashback_four) { create(:cashback_record, transaction_status: Enums::CashbackRecordStatus::TRANSFERRED) }

    it { expect(cashback_one.destroy).to be_truthy }
    it { expect(cashback_two.destroy).to be_truthy }
    it { expect(cashback_three.destroy).to be_falsey }
    it { expect(cashback_four.destroy).to be_falsey }
  end

  describe "validate order update" do
    let!(:order) { create(:order, :online) }
    let!(:cashback) { create(:cashback_record) }

    before do
      cashback.order = order
      cashback.validate
    end

    it { expect(cashback.errors[:base]).not_to be_empty }
  end

  context "when giftcard cashback record is created and user is the cashback transfer receiver" do
    it "makes cashback record available" do
      business = create(:business, :with_cashback, cashback_wallet_destination: Wallet::Kind::CASHBACK)
      user = create(:user, business:)
      bucket = create(:voucher_bucket)
      giftcard = create(:giftcard, :with_vouchers, :with_branches, voucher_bucket: bucket)
      voucher = create(:voucher, bucket:)
      order = create(:order, :completed, giftcard:, user:, voucher:)

      Sidekiq::Testing.inline! do
        CashbackRecord.create!(
          order_amount: order.price,
          transaction_status: Enums::CashbackRecordStatus::APPROVED,
          order:,
          user: order.user
        )
      end
      expect(CashbackRecord.last).to be_available
    end
  end

  context "when giftcard cashback record is created and business is the cashback transfer receiver" do
    it "makes cashback record available_lecupon" do
      business = create(:business, :with_cashback, cashback_wallet_destination: Wallet::Kind::MEMBERSHIP)
      create(:cashback_transfer_frequency, business:)
      user = create(:user, business:)
      bucket = create(:voucher_bucket)
      giftcard = create(:giftcard, :with_vouchers, :with_branches, voucher_bucket: bucket)
      voucher = create(:voucher, bucket:)
      order = create(:order, :completed, giftcard:, user:, voucher:)

      Sidekiq::Testing.inline! do
        CashbackRecord.create!(
          order_amount: order.price,
          transaction_status: Enums::CashbackRecordStatus::APPROVED,
          order:,
          user: order.user
        )
      end
      expect(CashbackRecord.last).to be_available_lecupon
    end
  end

  describe "order + external_id uniqueness" do
    it "validates uniqueness" do
      order = create(:order, :online)
      user = order.user
      business = user.business
      create(:cashback_record, external_id: "123", order:, user:, business:)
      cashback_record_invalid = build(:cashback_record, external_id: "123", order:, user:, business:)
      cashback_record_valid = build(:cashback_record, external_id: "456", order:, user:, business:)
      expect(cashback_record_invalid).to be_invalid
      expect(cashback_record_valid).to be_valid
    end
  end

  describe "#transfer_to_wallet!" do
    let(:sub_business) do
      create(:business, main_business: business, cashback_wallet_destination: business.cashback_wallet_destination)
    end
    let(:user) { create(:user, business: sub_business) }
    let(:order) { create(:order, user:, cashback_type: "percent", cashback_value: 10) }
    let(:cashback) { create(:cashback_record, :available, order:, user:, order_amount: 301.02) }
    let(:amount) { 2709 }

    context "when cashback does not have payout associated" do
      let(:business) { create(:business, :with_cashback, cashback_wallet_destination:, spread_percent: 10) }
      let(:project_wallet) { create(:wallet, kind: cashback_wallet_destination, business:, balance: 100_000) }
      let(:user_wallet) { create(:wallet, kind: cashback_wallet_destination, user:, balance: 2000) }
      let(:inflow_wallet) { create(:wallet, :inflow, kind: cashback_wallet_destination, balance: 300_000) }

      context "when wallet destination is cashback" do
        let(:cashback_wallet_destination) { Wallet::Kind::CASHBACK }

        let(:inflow_membership_wallet) { create(:wallet, :inflow, :membership, balance: 300_000) }
        let(:project_membership_wallet) { create(:wallet, :membership, business:, balance: 100_000) }
        let(:user_membership_wallet) { create(:wallet, :membership, user:, balance: 2000) }

        it "transfers to cashback wallet" do
          expect { cashback.transfer_to_wallet! }
            .to change(inflow_wallet.entries.credit.where(amount:), :count).by(1)
            .and change(inflow_wallet.entries.debit.where(amount:), :count).by(1)
            .and change(project_wallet.entries.credit.where(amount:), :count).by(1)
            .and change(project_wallet.entries.debit.where(amount:), :count).by(1)
            .and change(user_wallet.entries.credit.where(amount:), :count).by(1)
            .and not_change(user_wallet.entries.debit, :count)
            .and not_change(WalletEntry.where.not(amount:), :count)
            .and not_change { inflow_wallet.reload.balance }
            .and not_change { project_wallet.reload.balance }
            .and change { user_wallet.reload.balance }.by(amount)
            .and not_change { inflow_membership_wallet.reload.balance }
            .and not_change { project_membership_wallet.reload.balance }
            .and not_change { user_membership_wallet.reload.balance }
            .and not_change(inflow_membership_wallet.entries, :count)
            .and not_change(project_membership_wallet.entries, :count)
            .and not_change(user_membership_wallet.entries, :count)
        end
      end

      context "when wallet destination is membership" do
        let(:cashback_wallet_destination) { Wallet::Kind::MEMBERSHIP }

        let(:inflow_cashback_wallet) { create(:wallet, :inflow, :cashback, balance: 300_000) }
        let(:project_cashback_wallet) { create(:wallet, :cashback, business:, balance: 100_000) }
        let(:user_cashback_wallet) { create(:wallet, :cashback, user:, balance: 2000) }

        it "transfers to membership wallet" do
          expect { cashback.transfer_to_wallet! }
            .to change(inflow_wallet.entries.credit.where(amount:), :count).by(1)
            .and change(inflow_wallet.entries.debit.where(amount:), :count).by(1)
            .and change(project_wallet.entries.credit.where(amount:), :count).by(1)
            .and change(project_wallet.entries.debit.where(amount:), :count).by(1)
            .and change(user_wallet.entries.credit.where(amount:), :count).by(1)
            .and not_change(user_wallet.entries.debit, :count)
            .and not_change(WalletEntry.where.not(amount:), :count)
            .and not_change { inflow_wallet.reload.balance }
            .and not_change { project_wallet.reload.balance }
            .and change { user_wallet.reload.balance }.by(amount)
            .and not_change { inflow_cashback_wallet.reload.balance }
            .and not_change { project_cashback_wallet.reload.balance }
            .and not_change { user_cashback_wallet.reload.balance }
            .and not_change(inflow_cashback_wallet.entries, :count)
            .and not_change(project_cashback_wallet.entries, :count)
            .and not_change(user_cashback_wallet.entries, :count)
        end
      end
    end
  end
end
