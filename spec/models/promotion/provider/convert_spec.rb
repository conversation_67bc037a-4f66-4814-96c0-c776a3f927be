require "rails_helper"

RSpec.describe Promotion::Provider::Convert do
  describe ".to_provider_type" do
    it "returns provider_type from provider" do
      expect(described_class.to_provider_type("coupon_code")).to eq("code")
      expect(described_class.to_provider_type("cpf")).to eq("cpf")
      expect(described_class.to_provider_type("dynamic")).to eq("code")
      expect(described_class.to_provider_type("dynamic_coupon_code")).to eq("online")
      expect(described_class.to_provider_type("fixed_code")).to eq("code")
      expect(described_class.to_provider_type("lbc_giftcard")).to eq("code")
      expect(described_class.to_provider_type("online")).to eq("online")
      expect(described_class.to_provider_type("qrcode")).to eq("qrcode")
      expect(described_class.to_provider_type("regionalized")).to eq("regionalized")
      expect(described_class.to_provider_type("multimed")).to eq("code")
      expect(described_class.to_provider_type("whatsapp")).to eq("online")
    end
  end

  describe ".to_provider" do
    context "when provider_type is code" do
      it "returns provider coupon_code when promotion is dynamic voucher" do
        promotion = build(:promotion, provider_type: "code", dynamic_voucher: true)

        expect(described_class.to_provider(promotion)).to eq("dynamic")
      end

      it "returns provider coupon_code when promotion has voucher" do
        bucket = create(:voucher_bucket)
        promotion = create(:promotion, provider_type: "code", voucher_bucket: bucket)
        create(:voucher, bucket:)

        expect(described_class.to_provider(promotion)).to eq("coupon_code")
      end

      it "returns provider fixed_code when promotion does not have voucher" do
        promotion = build(:promotion, provider_type: "code")

        expect(described_class.to_provider(promotion)).to eq("fixed_code")
      end
    end

    context "when provider_type is cpf" do
      it "returns provider cpf" do
        promotion = build(:promotion, provider_type: "cpf")

        expect(described_class.to_provider(promotion)).to eq("cpf")
      end
    end

    context "when provider_type is qrcode" do
      it "returns provider qrcode" do
        promotion = build(:promotion, provider_type: "qrcode")

        expect(described_class.to_provider(promotion)).to eq("qrcode")
      end
    end

    context "when provider_type is regionalized" do
      it "returns provider regionalized" do
        promotion = build(:promotion, provider_type: "regionalized")

        expect(described_class.to_provider(promotion)).to eq("regionalized")
      end
    end

    context "when provider_type is online" do
      it "returns provider dynamic_coupon_code when promotion is dynamic voucher" do
        promotion = build(:promotion, provider_type: "online", dynamic_voucher: true)

        expect(described_class.to_provider(promotion)).to eq("dynamic_coupon_code")
      end

      it "returns provider dynamic_coupon_code when promotion has voucher" do
        bucket = create(:voucher_bucket)
        promotion = create(:promotion, provider_type: "online", voucher_bucket: bucket)
        create(:voucher, bucket:)

        expect(described_class.to_provider(promotion)).to eq("dynamic_coupon_code")
      end

      it "returns provider online when promotion does not have voucher" do
        promotion = build(:promotion, provider_type: "online")

        expect(described_class.to_provider(promotion)).to eq("online")
      end
    end
  end
end
