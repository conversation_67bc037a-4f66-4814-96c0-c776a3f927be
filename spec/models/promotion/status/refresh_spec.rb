require "rails_helper"

RSpec.describe Promotion::Status::Refresh do
  describe ".call" do
    it "changes status to pending when required field is not filled" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      not_filled = [:title, :description, :monday, :tuesday, :wednesday, :thursday, :friday, :saturday, :sunday].sample
      attributes = {
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 2.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true
      }.except(not_filled)
      promotion = Promotion.create!(organization:, status: "available", **attributes)
      Cupon.create!(branch:, promotion:, active: true)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("available").to("pending")
    end

    it "changes status to redeemed_out when quantity is out" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = Promotion.create!(
        organization:,
        status: "available",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 2.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 20,
        quantity: 20
      )
      Cupon.create!(branch:, promotion:, active: true)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("available").to("redeemed_out")
    end

    it "changes status to expired when expired" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = Promotion.create!(
        organization:,
        status: "available",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 1.day.ago,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 19,
        quantity: 20
      )
      Cupon.create!(branch:, promotion:, active: true)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("available").to("expired")
    end

    it "changes status to not_started when not started" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = Promotion.create!(
        organization:,
        status: "available",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.from_now,
        end_date: 4.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 19,
        quantity: 20
      )
      Cupon.create!(branch:, promotion:, active: true)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("available").to("not_started")
    end

    it "changes status to no_branch when there is no branch associated" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = Promotion.create!(
        organization:,
        status: "available",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 2.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 19,
        quantity: 20
      )
      Cupon.create!(branch:, promotion:, active: false)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("available").to("no_branch")
    end

    it "changes status to no_voucher when it must have available voucher but it does not" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      bucket = create(:voucher_bucket)
      promotion = Promotion.create!(
        organization:,
        provider_type: "code",
        code: nil,
        dynamic_voucher: false,
        status: "available",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 2.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 19,
        quantity: 20,
        voucher_bucket: bucket
      )
      Cupon.create!(branch:, promotion:, active: true)
      create(:voucher, order: create(:order), bucket:)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("available").to("no_voucher")
    end

    it "changes status to no_voucher when it is online, has voucher_type but has no voucher" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      bucket = create(:voucher_bucket)
      promotion = Promotion.create!(
        organization:,
        provider_type: "online",
        voucher_type: (Promotion::VoucherType.all - ["none"]).sample,
        url: nil,
        code: nil,
        dynamic_voucher: false,
        status: "available",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 2.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 19,
        quantity: 20,
        voucher_bucket: bucket
      )
      Cupon.create!(branch:, promotion:, active: true)
      create(:voucher, order: create(:order), bucket:)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("available").to("no_voucher")
    end

    it "does not change status when inactive" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = Promotion.create!(
        organization:,
        status: "inactive",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 2.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 19,
        quantity: 20
      )
      Cupon.create!(branch:, promotion:, active: true)

      expect { described_class.call(promotion) }
        .not_to change(promotion, :status)
    end

    it "changes status to available when it has all required fields filled" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = Promotion.create!(
        organization:,
        status: "pending",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 2.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 19,
        quantity: 20
      )
      Cupon.create!(branch:, promotion:, active: true)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("pending").to("available")
    end

    it "changes status to available when it must have available voucher and it does" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      bucket = create(:voucher_bucket)
      promotion = Promotion.create!(
        organization:,
        provider_type: "code",
        code: nil,
        dynamic_voucher: false,
        status: "no_voucher",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 2.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 19,
        quantity: 20,
        voucher_bucket: bucket
      )
      Cupon.create!(branch:, promotion:, active: true)
      create(:voucher, order: nil, bucket:)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("no_voucher").to("available")
    end

    it "changes status to available when it is online and has url" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = Promotion.create!(
        organization:,
        provider_type: "online",
        voucher_type: "none",
        url: FFaker::Internet.http_url,
        code: nil,
        dynamic_voucher: false,
        status: "no_url",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 2.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 19,
        quantity: 20
      )
      Cupon.create!(branch:, promotion:, active: true)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("no_url").to("available")
    end

    it "changes status to available when it is online and is dynamic voucher" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = Promotion.create!(
        organization:,
        provider_type: "online",
        voucher_type: "dynamic",
        url: nil,
        code: nil,
        dynamic_voucher: true,
        status: "no_voucher",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 2.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 19,
        quantity: 20
      )
      Cupon.create!(branch:, promotion:, active: true)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("no_voucher").to("available")
    end

    it "changes status to available when it is online and has code" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      promotion = Promotion.create!(
        organization:,
        provider_type: "online",
        voucher_type: "fixed",
        url: nil,
        code: "ABC123",
        dynamic_voucher: true,
        status: "no_voucher",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 2.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 19,
        quantity: 20
      )
      Cupon.create!(branch:, promotion:, active: true)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("no_voucher").to("available")
    end

    it "changes status to available when it is online and has available voucher" do
      organization = create(:organization)
      branch = create(:branch, organization:)
      bucket = create(:voucher_bucket)
      promotion = Promotion.create!(
        organization:,
        provider_type: "online",
        voucher_type: "list",
        url: nil,
        code: nil,
        dynamic_voucher: false,
        status: "no_voucher",
        title: "title",
        description: "description",
        discount_value: 10,
        discount_type: Promotion::DiscountType::PERCENT,
        average_ticket: 20,
        start_date: 2.days.ago,
        end_date: 2.days.from_now,
        start_hour: "09:00",
        end_hour: "18:00",
        frequency_in_days: 1,
        redeems_per_cpf: 1,
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
        infinity: false,
        redeemed_count: 19,
        quantity: 20,
        voucher_bucket: bucket
      )
      Cupon.create!(branch:, promotion:, active: true)
      create(:voucher, order: nil, bucket:)

      expect { described_class.call(promotion) }
        .to change(promotion, :status).from("no_voucher").to("available")
    end
  end
end
