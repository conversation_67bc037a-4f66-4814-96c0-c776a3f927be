# frozen_string_literal: true

require "rails_helper"

RSpec.describe Plan, type: :model do
  describe "associations" do
    it "belongs to a business" do
      association = Plan.reflect_on_association(:business)
      expect(association.macro).to eq(:belongs_to)
    end

    it "has many subscriptions" do
      association = Plan.reflect_on_association(:subscriptions)
      expect(association.macro).to eq(:has_many)
    end
  end

  describe "validations" do
    context "presence" do
      it "is invalid without title" do
        plan = build(:plan, title: nil)
        plan.valid?
        expect(plan.errors[:title]).to include("não pode ficar em branco")
      end

      it "is invalid without background_color" do
        plan = build(:plan, background_color: nil)
        plan.valid?
        expect(plan.errors[:background_color]).to include("não pode ficar em branco")
      end

      it "is invalid without font_color" do
        plan = build(:plan, font_color: nil)
        plan.valid?
        expect(plan.errors[:font_color]).to include("não pode ficar em branco")
      end

      it "is invalid without price" do
        plan = build(:plan, price: nil)
        plan.valid?
        expect(plan.errors[:price]).to include("não pode ficar em branco")
      end

      it "is invalid without points" do
        plan = build(:plan, points: nil)
        plan.valid?
        expect(plan.errors[:points]).to include("não pode ficar em branco")
      end

      it "is invalid without recurrence" do
        plan = build(:plan, recurrence: nil)
        plan.valid?
        expect(plan.errors[:recurrence]).to include("não pode ficar em branco")
      end
    end

    context "numerical validations" do
      it "is invalid if price is not greater than 0" do
        plan = build(:plan, price: 0)
        plan.valid?
        expect(plan.errors[:price]).to include("deve ser maior que 0")
      end

      it "is invalid if points is less than 0" do
        plan = build(:plan, points: -1)
        plan.valid?
        expect(plan.errors[:points]).to include("deve ser maior ou igual a 0")
      end
    end

    context "recurrence" do
      it "is valid when recurrence is among the allowed ones" do
        plan = build(:plan)
        Plan.recurrences.each_value do |recurrence_value|
          plan.recurrence = recurrence_value
          expect(plan).to be_valid
        end
      end

      it "raises error when assigning an invalid recurrence" do
        plan = build(:plan)
        expect { plan.recurrence = "invalid_recurrence" }.to raise_error(ArgumentError)
      end
    end

    describe "destination business must be in project tree" do
      let!(:business) { create(:business) }
      let!(:sub_business_basic) { create(:business, main_business: business) }
      let!(:sub_business_premium) { create(:business, main_business: business) }

      let!(:subscription_config) { create(:subscription_config, business:) }

      let!(:business_main_other_tree) { create(:business) }
      let!(:sub_business_other_tree) { create(:business, main_business: business_main_other_tree) }

      let(:plan_main) do
        build(:plan, subscription_config:, business:, destination_business: business)
      end
      let(:plan_same_project_tree) do
        build(:plan, subscription_config:, business:, destination_business: sub_business_premium)
      end
      let(:plan_diff_project_tree) do
        build(:plan, subscription_config:, business:, destination_business: sub_business_other_tree)
      end

      it "validates presence of destination business in the same project tree as the business" do
        expect(plan_main).to be_valid
        expect(plan_same_project_tree).to be_valid

        expect(plan_diff_project_tree).to be_invalid
        expect(plan_diff_project_tree.errors.key?(:destination_business_id)).to be true
      end
    end
  end

  describe "scopes" do
    let(:plan_active) { create(:plan, active: true, recurrence: "monthly") }
    let(:plan_inactive) { create(:plan, active: false, recurrence: "yearly") }

    describe ".active" do
      it "returns only active plans" do
        result = Plan.active
        expect(result).to include(plan_active)
        expect(result).not_to include(plan_inactive)
      end
    end

    describe ".monthly" do
      it "returns only plans with monthly recurrence" do
        result = Plan.monthly
        expect(result).to include(plan_active)
        expect(result).not_to include(plan_inactive)
      end
    end

    describe ".yearly" do
      it "returns only plans with annual recurrence" do
        result = Plan.yearly
        expect(result).to include(plan_inactive)
        expect(result).not_to include(plan_active)
      end
    end
  end

  describe "instance methods" do
    it "#monthly? returns true when the plan is monthly" do
      plan = build(:plan, recurrence: "monthly")
      expect(plan.monthly?).to eq(true)
      expect(plan.yearly?).to eq(false)
    end

    it "#yearly? returns true when the plan is annual" do
      plan = build(:plan, :yearly)
      expect(plan.yearly?).to eq(true)
      expect(plan.monthly?).to eq(false)
    end
  end
end
