require "rails_helper"

RSpec.describe Wallet::CashIn do
  describe "#call" do
    let(:user) { create(:user, business:) }
    let(:business) { create(:business, cashback_wallet_destination: :membership, spread_percent: 0) }
    let(:inflow_wallet) { create(:wallet, :inflow, :membership, balance: 300000) }
    let(:project_wallet) { create(:wallet, :membership, business:, balance: 54806223) }
    let(:user_wallet) { create(:wallet, :membership, user:, balance: 16246) }
    let(:idempotency_key) { SecureRandom.uuid }

    shared_examples "unsuccessful cash in" do
      specify do
        expect { cash_in.call }
          .to not_change { inflow_wallet.reload.balance }
          .and not_change { project_wallet.reload.balance }
          .and not_change { user_wallet.reload.balance }
          .and not_change(WalletTransaction, :count)

        expect(cash_in.errors.full_messages.join(", ")).to eq(expected_message)
      end
    end

    context "with params correct" do
      let(:cash_in) do
        described_class.new(
          project: business,
          wallet: user_wallet,
          amount: FFaker::Number.number(digits: 4),
          currency: "BRL",
          description: FFaker::LoremBR.phrase,
          idempotency_key:
        )
      end

      it "cashes in user wallet" do
        expect { cash_in.call }
          .to change { user_wallet.reload.balance }.by(cash_in.amount)
          .and not_change { inflow_wallet.reload.balance }
          .and not_change { project_wallet.reload.balance }
          .and change(WalletTransaction, :count).by(1)

        wallet_transaction = WalletTransaction.order(:created_at).last
        expect(wallet_transaction.wallet).to eq(user_wallet)
        expect(wallet_transaction.idempotency_key).to eq(cash_in.idempotency_key)
        expect(wallet_transaction.amount).to eq(cash_in.amount)
        expect(wallet_transaction.currency).to eq(cash_in.currency)
        expect(wallet_transaction.description).to eq(cash_in.description)
      end
    end

    context "when project wallet destination is not membership" do
      let(:business) do
        create(:business, cashback_wallet_destination: :cashback, cashback: true, spread_percent: 0)
      end
      let(:cash_in) do
        described_class.new(
          project: business,
          wallet: user_wallet,
          amount: FFaker::Number.number(digits: 4),
          currency: "BRL",
          description: FFaker::LoremBR.phrase,
          idempotency_key:
        )
      end
      let(:expected_message) { "Tipo da carteira inválido" }

      it_behaves_like "unsuccessful cash in"
    end

    context "when currency is wrong" do
      let(:cash_in) do
        described_class.new(
          project: business,
          wallet: user_wallet,
          amount: FFaker::Number.number(digits: 4),
          currency: "anything",
          description: FFaker::LoremBR.phrase,
          idempotency_key:
        )
      end
      let(:expected_message) { "Moeda inválida" }

      it_behaves_like "unsuccessful cash in"
    end

    context "when amount is wrong" do
      let(:cash_in) do
        described_class.new(
          project: business,
          wallet: user_wallet,
          amount: "anything",
          currency: "BRL",
          description: FFaker::LoremBR.phrase,
          idempotency_key:
        )
      end
      let(:expected_message) { "Valor não é um número" }

      it_behaves_like "unsuccessful cash in"
    end
  end
end
