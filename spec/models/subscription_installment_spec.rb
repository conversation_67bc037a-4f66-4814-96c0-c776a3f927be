require "rails_helper"

RSpec.describe SubscriptionInstallment, type: :model do
  describe "associations" do
    it "belongs to a subscription" do
      association = described_class.reflect_on_association(:subscription)
      expect(association.macro).to eq(:belongs_to)
    end
  end

  describe "validations" do
    it "number is not valid" do
      subscription = create(:subscription)
      subscription_installment = build(:subscription_installment, subscription:)
      subscription_installment.number = nil
      subscription_installment.valid?
      expect(subscription_installment.errors[:number]).to include("não pode ficar em branco")
    end

    it "price is not valid" do
      subscription = create(:subscription)
      subscription_installment = build(:subscription_installment, subscription:)
      subscription_installment.price = nil
      subscription_installment.valid?
      expect(subscription_installment.errors[:price]).to include("não pode ficar em branco")
    end

    it "paid_at is not valid" do
      subscription = create(:subscription)
      subscription_installment = build(:subscription_installment, subscription:)
      subscription_installment.paid_at = nil
      subscription_installment.valid?
      expect(subscription_installment.errors[:paid_at]).to include("não pode ficar em branco")
    end

    it "due_at is not valid" do
      subscription = create(:subscription)
      subscription_installment = build(:subscription_installment, subscription:)
      subscription_installment.due_at = nil
      subscription_installment.valid?
      expect(subscription_installment.errors[:due_at]).to include("não pode ficar em branco")
    end
  end

  describe "enums" do
    context "allows setting status using string values" do
      it "paid" do
        installment = build(:subscription_installment, status: "paid")
        expect(installment.paid?).to be true
        expect(installment.status).to eq("paid")
      end

      it "failed" do
        installment = build(:subscription_installment, status: "failed")
        expect(installment.failed?).to be true
        expect(installment.status).to eq("failed")
      end

      it "panding" do
        installment = build(:subscription_installment, status: "pending")
        expect(installment.pending?).to be true
        expect(installment.status).to eq("pending")
      end
    end
  end
end
