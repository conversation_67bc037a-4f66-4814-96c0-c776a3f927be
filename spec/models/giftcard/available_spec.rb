require "rails_helper"

RSpec.describe Giftcard, type: :model do
  include ActiveSupport::Testing::TimeHelpers

  describe "#update_availability" do
    let(:voucher_bucket) { create(:voucher_bucket) }
    let!(:giftcard) { create(:giftcard, :with_branches, available: false, voucher_bucket:) }

    context "when giftcard is available" do
      let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
      let!(:voucher_unavailable) { create(:voucher, bucket: voucher_bucket) }
      let!(:user) { create(:user, :with_cashback) }
      let!(:order) do
        create(:order, :pending, :with_giftcard, user:, giftcard:, voucher: voucher_unavailable)
      end

      it "makes giftcard available" do
        giftcard.update_availability

        expect(giftcard.reload.available).to be true
      end
    end

    context "when giftcard is not available because it has no voucher available" do
      let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
      let!(:user) { create(:user, :with_cashback) }
      let!(:order) do
        create(:order, :pending, :with_giftcard, user:, giftcard:, voucher:)
      end

      it "makes giftcard unavailable" do
        giftcard.update_availability

        expect(giftcard.reload.available).to be false
      end
    end

    context "when giftcard is not available because it is no longer for sale" do
      let!(:giftcard) { create(:giftcard, voucher_bucket:, available: false, sale_ended_at: 1.day.ago) }
      let!(:voucher) { create(:voucher, bucket: voucher_bucket) }

      it "makes giftcard unavailable" do
        giftcard.update_availability

        expect(giftcard.reload.available).to be false
      end
    end
  end

  describe ".update_availability" do
    let(:voucher_bucket) { create(:voucher_bucket) }
    let(:giftcard_sale_expired) { create(:giftcard, available: false, voucher_bucket:, sale_ended_at: "2023-05-01 10:00:00") }

    let(:giftcard_sale_expired_old_flag) { create(:giftcard, available: true, voucher_bucket:, sale_ended_at: "2023-05-01 10:00:00") }

    let(:giftcard_for_sale_with_available_voucher) { create(:giftcard, available: true, voucher_bucket:, sale_ended_at: "2023-05-03 10:00:00") }

    let(:giftcard_for_sale_with_available_voucher_old_flag) { create(:giftcard, available: false, voucher_bucket:, sale_ended_at: "2023-05-03 10:00:00") }

    let(:voucher_bucket_for_sale) { create(:voucher_bucket) }
    let(:giftcard_for_sale_without_available_voucher) { create(:giftcard, available: false, voucher_bucket: voucher_bucket_for_sale, sale_ended_at: "2023-05-03 10:00:00") }

    let(:voucher_bucket_for_sale_old_flag) { create(:voucher_bucket) }
    let(:giftcard_for_sale_without_available_voucher_old_flag) { create(:giftcard, available: true, voucher_bucket: voucher_bucket_for_sale_old_flag, sale_ended_at: "2023-05-03 10:00:00") }

    before do
      travel_to Time.zone.parse("2023-05-02 10:00:00")

      create(:voucher, bucket: voucher_bucket)

      create(:voucher, bucket: voucher_bucket)
      voucher = create(:voucher, bucket: voucher_bucket)
      create(:order, :with_giftcard, user: create(:user, :with_cashback), voucher:)

      create(:voucher, bucket: voucher_bucket)
      voucher = create(:voucher, bucket: voucher_bucket)
      create(:order, :with_giftcard, user: create(:user, :with_cashback), voucher:)

      voucher = create(:voucher, bucket: voucher_bucket)
      create(:order, :with_giftcard, user: create(:user, :with_cashback), voucher:)

      voucher = create(:voucher, bucket: voucher_bucket)
      create(:order, :with_giftcard, user: create(:user, :with_cashback), voucher:)

      voucher = create(:voucher, bucket: voucher_bucket_for_sale_old_flag)
      create(:order, :with_giftcard, user: create(:user, :with_cashback), voucher:)
    end

    it "updates availability for all giftcards" do
      expect { described_class.update_availability }
        .to change { giftcard_sale_expired_old_flag.reload.available }.from(true).to(false)
        .and not_change { giftcard_sale_expired.reload.available }
        .and not_change { giftcard_for_sale_with_available_voucher.reload.available }
        .and change { giftcard_for_sale_with_available_voucher_old_flag.reload.available }.from(false).to(true)
        .and not_change { giftcard_for_sale_without_available_voucher.reload.available }
        .and change { giftcard_for_sale_without_available_voucher_old_flag.reload.available }.from(true).to(false)
    end
  end
end
