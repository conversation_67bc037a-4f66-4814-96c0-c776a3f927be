require "rails_helper"

RSpec.describe Giftcard::Order do
  let!(:user) { create(:user) }

  describe ".unreverse" do
    let(:voucher_bucket) { create(:voucher_bucket) }
    let!(:giftcard) { create(:giftcard, :with_vouchers, :with_branches, available: false, voucher_bucket:) }
    let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
    let!(:order) { create(:order, :pending, :with_giftcard, giftcard:, user:, voucher:) }

    it "unreserve order" do
      expect do
        Giftcard::Order.unreserve(order:)
        [giftcard, voucher].each(&:reload)
      end.to change(Order, :count).by(-1)
        .and change(giftcard, :available).from(false).to(true)
        .and change(voucher, :order_id).from(be_present).to(nil)
    end
  end

  describe ".reserve" do
    let!(:branch_previously_reserved) { create(:branch) }
    let(:voucher_bucket) { create(:voucher_bucket) }
    let!(:giftcard_previously_reserved) { create(:giftcard, available: false, branches: [branch_previously_reserved], voucher_bucket:) }
    let!(:voucher_previously_reserved) { create(:voucher, bucket: voucher_bucket) }
    let!(:order_previously_reserved) { create(:order, :pending, :with_giftcard, user:, voucher: voucher_previously_reserved) }
    let!(:branch) { create(:branch) }
    let!(:giftcard) { create(:giftcard, branches: [branch], voucher_bucket:) }
    let!(:voucher) { create(:voucher, bucket: voucher_bucket) }

    it "unreserves all reserved orders and reserves order" do
      order = Giftcard::Order.reserve(user:, giftcard:, voucher:)

      expect(Order.status_pending.with_gift_card_reserved_to(user:).find_by(giftcard:)).to eq(order)
      expect(Order.where(id: order_previously_reserved.id)).not_to exist
    end
  end
end
