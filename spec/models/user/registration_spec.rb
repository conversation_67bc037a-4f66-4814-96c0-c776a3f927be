# frozen_string_literal: true

require "rails_helper"

RSpec.describe User::Registration do
  let(:business) { create(:business) }

  describe ".authorization" do
    context "when authorized_user has default_auth_flow true" do
      let(:cpf) { CPF.new(FFaker::IdentificationBR.cpf).stripped }
      let!(:authorized_user) { create(:authorized_user, default_auth_flow: true, cpf:, business:) }

      before { business.project_config.update(user_manager: Enums::UserManager::DEFAULT_WITH_EXTERNAL_VALIDATION) }

      it "must instantiate Default strategy independent of UserManager option" do
        expect(User::RegistrationStrategy::Default).to receive(:new)
        User::Registration.authorization(business:, params: {cpf:})
      end
    end

    context "when user manager is default" do
      it "must instantiate Default strategy" do
        expect(User::RegistrationStrategy::Default).to receive(:new)
        User::Registration.authorization(business:, params: {})
      end
    end

    context "when user manager is open signup" do
      before { business.project_config.update(user_manager: Enums::UserManager::OPEN_SIGNUP) }
      it "must instantiate OpenSignup strategy" do
        expect(User::RegistrationStrategy::OpenSignup).to receive(:new)
        User::Registration.authorization(business:, params: {})
      end
    end

    context "when user manager is default_with_external_validation" do
      before { business.project_config.update(user_manager: Enums::UserManager::DEFAULT_WITH_EXTERNAL_VALIDATION) }
      it "must instantiate DefaultWithExternalValidation strategy" do
        expect(User::RegistrationStrategy::DefaultWithExternalValidation).to receive(:new)
        User::Registration.authorization(business:, params: {})
      end
    end

    context "when user manager is external" do
      before { business.project_config.update(user_manager: Enums::UserManager::EXTERNAL) }

      it "must instantiate Default strategy" do
        expect(User::RegistrationStrategy::Default).to receive(:new)
        User::Registration.authorization(business:, params: {})
      end
    end

    context "when user manager is no_signup" do
      before { business.project_config.update(user_manager: Enums::UserManager::NO_SIGNUP) }

      it "must throw NoRegistrationError" do
        expect do
          User::Registration.authorization(business:, params: {})
        end.to raise_error(User::Registration::NoRegistrationError)
      end
    end
  end
end
