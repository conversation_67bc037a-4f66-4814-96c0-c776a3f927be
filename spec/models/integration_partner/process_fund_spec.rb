# frozen_string_literal: true

require "rails_helper"

RSpec.describe IntegrationPartner::ProcessFund do
  let!(:order) do
    promotion = create(:promotion, cashback_value: 3.24)
    cupon = create(:cupon, promotion:)
    create(:order, cupon:, business:)
  end

  context "when currency is points" do
    let!(:business) { create(:business, fair_value: 0.0125, currency: "points") }

    context "and point does not exist" do
      let(:params) do
        {
          order_amount: 129.98,
          status: "pending",
          external_id: SecureRandom.uuid,
          metadata: {any: "metadata"}
        }
      end

      it "creates fund of points" do
        expect { described_class.call(order:, params:) }.to change(Fund, :count).by(1)

        fund = Fund.order(:created_at).last
        expect(fund).to be_pending
        expect(fund.order_amount).to eq 12998
        expect(fund.idempotency_key).to eq(params[:external_id])
        expect(fund.metadata).to eq("any" => "metadata")
      end
    end

    context "and point exists" do
      let!(:point) do
        create(:fund, :points, status:, user: order.user, order:, order_amount: 12998, idempotency_key: SecureRandom.uuid)
      end
      let(:params) do
        {
          order_amount: 122.41,
          status: "approved",
          external_id: point.idempotency_key,
          metadata: {any: "changed metadata"}
        }
      end

      context "and is pending" do
        let(:status) { :pending }

        it "updates fund of points" do
          expect do
            described_class.call(order:, params:)
            point.reload
          end.to change(point, :status).to("approved")
            .and change(point, :metadata).to("any" => "changed metadata")
            .and change(point, :order_amount).to(12241)
            .and change(point, :amount).to(307)
            .and change(point, :original_amount).to(31729)
            .and change(point, :monetary_amount).to(397)
        end
      end

      context "and is available" do
        let(:status) { :available }

        it "updates only metadata of fund" do
          expect do
            described_class.call(order:, params:)
            point.reload
          end.to change(point, :metadata).to("any" => "changed metadata")
            .and not_change(point, :status)
            .and not_change(point, :order_amount)
            .and not_change(point, :amount)
            .and not_change(point, :original_amount)
            .and not_change(point, :monetary_amount)
        end
      end
    end
  end

  context "when currency is cashback" do
    let!(:business) { create(:business, currency: "BRL", spread_percent: 0) }

    context "and cashback does not exist" do
      let(:params) do
        {
          order_amount: 129.98,
          status: "pending",
          external_id: SecureRandom.uuid,
          metadata: {any: "metadata"}.to_json
        }
      end

      it "creates fund of cashback" do
        expect { described_class.call(order:, params:) }.to change(CashbackRecord, :count).by(1)

        fund = CashbackRecord.order(:created_at).last
        expect(fund).to be_pending
        expect(fund.order_amount).to eq 129.98
        expect(fund.external_id).to eq(params[:external_id])
        expect(JSON.parse(fund.transaction_json)).to eq("any" => "metadata")
      end
    end

    context "and cashback exists" do
      let!(:cashback) do
        create(:cashback_record, :pending, user: order.user, order:, order_amount: 129.98, external_id: SecureRandom.uuid)
      end
      let(:params) do
        {
          order_amount: 122.41,
          status: "approved",
          external_id: cashback.external_id,
          metadata: {any: "changed metadata"}.to_json
        }
      end

      it "updates fund of cashback" do
        expect do
          described_class.call(order:, params:)
          cashback.reload
        end.to change(cashback, :transaction_status).to("approved")
          .and change(cashback, :transaction_json).to({"any" => "changed metadata"}.to_json)
          .and change(cashback, :order_amount).to(122.41)
          .and change(cashback, :cashback_amount).to(3.97)
      end
    end

    context "and the order has a nil cashback_value" do
      let!(:order) do
        promotion = create(:promotion, cashback_value: nil)
        cupon = create(:cupon, promotion:)
        create(:order, cupon:, business:)
      end

      let(:params) do
        {
          order_amount: 129.98,
          status: "pending",
          external_id: SecureRandom.uuid,
          metadata: {any: "metadata"}.to_json
        }
      end

      it "does not create a cashback record" do
        expect { described_class.call(order:, params:) }.not_to change(CashbackRecord, :count)
      end
    end
  end
end
