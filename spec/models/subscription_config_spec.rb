# frozen_string_literal: true

require "rails_helper"

RSpec.describe SubscriptionConfig, type: :model do
  let!(:business) { create(:business) }
  let!(:subscription_config) { create(:subscription_config, business:) }

  describe "Validation" do
    context "when title is blank" do
      it "is invalid" do
        subscription_config.title = nil
        subscription_config.valid?
        expect(subscription_config.errors[:title]).to include("não pode ficar em branco")
      end
    end

    context "when description is blank" do
      it "is invalid" do
        subscription_config.description = ""
        subscription_config.valid?
        expect(subscription_config.errors[:description]).to include("não pode ficar em branco")
      end
    end

    context "when days_to_credit_points is blank" do
      it "is invalid" do
        subscription_config.days_to_credit_points = ""
        subscription_config.valid?
        expect(subscription_config.errors[:days_to_credit_points]).to include("não pode ficar em branco")
      end

      it "not value is zero" do
        subscription_config.days_to_credit_points = 0
        subscription_config.valid?
        expect(subscription_config.errors[:days_to_credit_points]).to include("deve ser maior que 0")
      end
    end

    describe "cancel destination business must be in project tree" do
      let!(:business) { create(:business) }
      let!(:sub_business_basic) { create(:business, main_business: business) }
      let!(:sub_business_premium) { create(:business, main_business: business) }

      let!(:business_main_other_tree) { create(:business) }
      let!(:sub_business_other_tree) { create(:business, main_business: business_main_other_tree) }

      let(:subscription_config_main) do
        build(:subscription_config, business:, cancel_destination_business: business)
      end
      let(:subscription_config_same_project_tree) do
        build(:subscription_config, business:, cancel_destination_business: sub_business_premium)
      end
      let(:subscription_config_diff_project_tree) do
        build(:subscription_config, business:, cancel_destination_business: sub_business_other_tree)
      end

      it "validates presence of cancel destination business in the same project tree as the business" do
        expect(subscription_config_main).to be_valid
        expect(subscription_config_same_project_tree).to be_valid

        expect(subscription_config_diff_project_tree).to be_invalid
        expect(subscription_config_diff_project_tree.errors.key?(:cancel_destination_business_id)).to be true
      end
    end
  end

  describe "scopes" do
    describe ".active" do
      it "returns only active subscriptions" do
        active_subscription = create(:subscription_config, :active, business:)
        inactive_subscription = create(:subscription_config, :inactive, business:)

        result = SubscriptionConfig.active

        expect(result).to include(active_subscription)
        expect(result).not_to include(inactive_subscription)
      end
    end

    describe ".inactive" do
      it "returns only inactive subscriptions" do
        active_subscription = create(:subscription_config, :active, business:)
        inactive_subscription = create(:subscription_config, :inactive, business:)

        result = SubscriptionConfig.inactive

        expect(result).to include(inactive_subscription)
        expect(result).not_to include(active_subscription)
      end
    end
  end

  describe "associations" do
    it "belongs to a business" do
      association = described_class.reflect_on_association(:business)
      expect(association.macro).to eq(:belongs_to)
    end

    it 'has many groups with class_name "SubscriptionGroup"' do
      association = described_class.reflect_on_association(:groups)
      expect(association.macro).to eq(:has_many)
      expect(association.options[:class_name]).to eq("SubscriptionGroup")
    end

    it "has many subscriptions" do
      association = SubscriptionConfig.reflect_on_association(:subscriptions)
      expect(association.macro).to eq(:has_many)
    end
  end
end
