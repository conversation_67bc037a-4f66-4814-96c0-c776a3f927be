require "rails_helper"

RSpec.describe Order::UsageExpiration do
  describe ".update" do
    let!(:bucket) { create(:voucher_bucket) }
    let!(:giftcard_not_expired) { create(:giftcard, :with_vouchers, :with_branches, quantity: 2, sale_ended_at: Time.current, usage_ended_at: 1.day.from_now, voucher_bucket: bucket) }
    let!(:order_not_expired) { create(:order, :with_giftcard, giftcard: giftcard_not_expired, usage_expired: false, voucher: giftcard_not_expired.vouchers.first) }
    let!(:giftcard_expired) { create(:giftcard, :with_vouchers, :with_branches, quantity: 2, sale_ended_at: 2.days.ago, usage_ended_at: 1.day.ago, voucher_bucket: bucket) }
    let!(:order_expired) { create(:order, :with_giftcard, giftcard: giftcard_expired, usage_expired: false, voucher: giftcard_expired.vouchers.first) }

    it "updates usage expired for all orders" do
      described_class.update
      [order_not_expired, order_expired].each(&:reload)
      expect(order_not_expired.usage_expired).to eq(false)
      expect(order_expired.usage_expired).to eq(true)
    end
  end
end
