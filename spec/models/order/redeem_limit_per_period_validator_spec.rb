require "rails_helper"

RSpec.describe Order, type: :model do
  let!(:user) { create(:user) }

  context "with redeem rules defined for giftcard" do
    let!(:voucher_bucket) { create(:voucher_bucket) }
    let!(:giftcard) { create(:giftcard, :with_vouchers, :with_branches, quantity: 2, voucher_bucket:) }
    let!(:redeem_limit) { create(:redeem_rule, :redeem_limit, giftcard:, value: 2) }
    let!(:period_interval) { create(:redeem_rule, :period_interval, giftcard:, value: 1) }
    let!(:period_type) { create(:redeem_rule, :period_type, giftcard:, value: RedeemRule::PeriodTypes::MONTH) }

    context "with no order" do
      specify do
        order = build(:order, :pending, :with_giftcard, giftcard:, user:, voucher: giftcard.vouchers[0])

        expect(order).to be_valid
      end
    end

    context "with order within redeem limit" do
      let!(:voucher_bucket) { create(:voucher_bucket) }
      let!(:giftcard) { create(:giftcard, :with_vouchers, :with_branches, quantity: 3, voucher_bucket:) }
      let!(:order_one) { create(:order, :with_giftcard, user:, giftcard:, redeemed_at: 20.days.ago, voucher: giftcard.vouchers[0]) }
      let!(:order_two) { create(:order, :with_giftcard, user:, giftcard:, redeemed_at: (1.month + 1.day).ago, voucher: giftcard.vouchers[1]) }
      let!(:unrelated_giftcard) { create(:giftcard, :with_vouchers, :with_branches, quantity: 2) }
      let!(:order_unrelated_giftcard) do
        create(:order, :with_giftcard, user:, giftcard: unrelated_giftcard, redeemed_at: 20.days.ago, voucher: unrelated_giftcard.vouchers[0])
      end

      specify do
        order = build(:order, :pending, :with_giftcard, giftcard:, user:, voucher: giftcard.vouchers[2])
        expect(order).to be_valid
      end
    end

    context "with order out of redeem limit" do
      let!(:voucher_bucket) { create(:voucher_bucket) }
      let!(:giftcard) { create(:giftcard, :with_vouchers, :with_branches, quantity: 3, voucher_bucket:) }
      let!(:order_one) { create(:order, :with_giftcard, user:, giftcard:, redeemed_at: 20.days.ago, voucher: giftcard.vouchers[0]) }
      let!(:order_two) { create(:order, :with_giftcard, user:, giftcard:, redeemed_at: (1.month - 1.day).ago, voucher: giftcard.vouchers[1]) }

      specify do
        order = build(:order, :pending, :with_giftcard, giftcard:, user:, voucher: giftcard.vouchers[2])

        expect(order).to be_invalid
      end
    end

    context "with canceled order" do
      let!(:voucher_bucket) { create(:voucher_bucket) }
      let!(:giftcard) { create(:giftcard, :with_vouchers, :with_branches, voucher_bucket:) }
      let!(:order_one) { create(:order, :with_giftcard, user:, giftcard:, redeemed_at: 20.days.ago, voucher: giftcard.vouchers[0]) }
      let!(:order_two) { create(:order, :with_giftcard, user:, giftcard:, redeemed_at: (1.month - 2.days).ago, status: Order::Status::CANCELED, voucher: giftcard.vouchers[1]) }
      let!(:order_three) { create(:order, :with_giftcard, user:, giftcard:, redeemed_at: (1.month - 1.day).ago, status: Order::Status::CANCELED, voucher: giftcard.vouchers[2]) }

      it "ignores the canceled order and be valid" do
        order = build(:order, :pending, :with_giftcard, giftcard:, user:, voucher: giftcard.vouchers[3])

        expect(order).to be_valid
      end
    end
  end

  context "with any of redeem rules not defined for giftcard" do
    let!(:voucher_bucket) { create(:voucher_bucket) }
    let!(:giftcard) { create(:giftcard, :with_vouchers, :with_branches, quantity: 2, voucher_bucket:) }

    specify do
      order = build(:order, :pending, :with_giftcard, giftcard:, user:, voucher: giftcard.vouchers[0])

      expect(order).to be_valid
    end
  end

  context "with business redeem limit" do
    let!(:voucher_bucket) { create(:voucher_bucket) }
    let!(:giftcard) { create(:giftcard, :with_vouchers, :with_branches, quantity: 2, voucher_bucket:) }

    before do
      giftcard_config = user.business.giftcard_config
      giftcard_config.update(limit_by_user: 2)
    end

    context "with no order" do
      let!(:voucher_bucket) { create(:voucher_bucket) }
      let!(:giftcard) { create(:giftcard, :with_vouchers, :with_branches, quantity: 2, voucher_bucket:) }

      specify do
        order = build(:order, :pending, :with_giftcard, giftcard:, user:, voucher: giftcard.vouchers[0])

        expect(order).to be_valid
      end
    end

    context "with order within redeem limit" do
      let!(:voucher_bucket) { create(:voucher_bucket) }
      let!(:giftcard) { create(:giftcard, :with_vouchers, :with_branches, voucher_bucket:) }
      let!(:order_one) { create(:order, :with_giftcard, user:, giftcard:, redeemed_at: 20.days.ago, voucher: giftcard.vouchers[0]) }
      let!(:order_two) { create(:order, :with_giftcard, user:, giftcard:, redeemed_at: (1.month + 1.day).ago, voucher: giftcard.vouchers[1]) }

      specify do
        order = build(:order, :pending, :with_giftcard, giftcard:, user:, voucher: giftcard.vouchers[2])
        expect(order).to be_valid
      end
    end

    context "with order out of redeem limit" do
      let!(:voucher_bucket) { create(:voucher_bucket) }
      let!(:giftcard) { create(:giftcard, :with_vouchers, :with_branches, quantity: 3, voucher_bucket:) }
      let!(:order_one) { create(:order, :with_giftcard, user:, giftcard:, redeemed_at: 20.days.ago, voucher: giftcard.vouchers[0]) }
      let!(:order_two) { create(:order, :with_giftcard, user:, giftcard:, redeemed_at: (1.month + 1.day).ago, voucher: giftcard.vouchers[1]) }
      let!(:another_giftcard) { create(:giftcard, :with_vouchers, :with_branches) }
      let!(:order_another_giftcard) do
        create(:order, :with_giftcard, user:, giftcard: another_giftcard, redeemed_at: 20.days.ago, voucher: another_giftcard.vouchers.first)
      end

      specify do
        order = build(:order, :pending, :with_giftcard, giftcard:, user:, voucher: giftcard.vouchers[2])

        expect(order).to be_invalid
      end
    end
  end
end
