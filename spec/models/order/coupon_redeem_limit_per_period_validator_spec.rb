require "rails_helper"

RSpec.describe Order::CouponRedeemLimitPerPeriodValidator, type: :model do
  include ActiveSupport::Testing::TimeHelpers

  let(:user) { create(:user) }

  subject { Order.new(user:, cupon: coupon) }

  context "when frequency in days is zero / not defined" do
    let!(:promotion) { create(:promotion, :cpf, frequency_in_days: 0, redeems_per_cpf: 2, closed_interval: false) }
    let!(:coupon) { create(:cupon, :cpf, promotion:, frequency_in_days: 0, redeems_per_cpf: 2) }

    context "when redeem limit did not exceed" do
      before do
        create(:order, user:, cupon: coupon)
      end

      it { is_expected.to be_valid }
    end

    context "when redeem limit exceeded" do
      it do
        create(:order, user:, cupon: coupon)
        create(:order, user:, cupon: coupon)
        order = build(:order, user:, cupon: coupon)

        expect(order).to be_invalid
        expect(order.errors.as_json).to eq({
          base: ["Esta promoção só pode ser ativada novamente a cada 0 dia(s)."]
        })
      end
    end
  end

  describe "open interval with frequency defined" do
    let(:promotion) { create(:promotion, :cpf, frequency_in_days: 10, redeems_per_cpf: 1, closed_interval: false) }
    let(:coupon) { create(:cupon, :cpf, promotion:, frequency_in_days: 10, redeems_per_cpf: 1) }

    before do
      travel_to(Time.zone.parse("2023-08-30 08:35"))
    end

    context "when redeem limit did not exceed" do
      before do
        create(:order, user:, cupon: coupon, created_at: "2023-08-20 08:34")
      end

      it { is_expected.to be_valid }
    end

    context "when redeem limit exceeded" do
      it do
        create(:order, user:, cupon: coupon, created_at: "2023-08-20 08:35")
        order = build(:order, user:, cupon: coupon)

        expect(order).to be_invalid
        expect(order.errors.as_json).to eq({
          base: ["Esta promoção só pode ser ativada novamente a cada 10 dia(s)."]
        })
      end
    end
  end

  describe "closed interval with frequency defined" do
    let(:promotion) do
      create(:promotion,
        :cpf,
        frequency_in_days: 10,
        redeems_per_cpf: 1,
        closed_interval: true,
        start_date: Time.zone.parse("2023-08-30 08:34"),
        start_hour: "08:34")
    end
    let(:coupon) { create(:cupon, :cpf, promotion:, frequency_in_days: 10, redeems_per_cpf: 1) }

    before do
      travel_to(Time.zone.parse("2023-08-30 08:35"))
    end

    context "when redeem limit did not exceed" do
      before do
        create(:order, user:, cupon: coupon, created_at: "2023-08-30 08:33")
      end

      it { is_expected.to be_valid }
    end

    context "when redeem limit exceeded" do
      before do
        create(:order, user:, cupon: coupon, created_at: "2023-08-30 08:34")
      end

      it { is_expected.not_to be_valid }
    end
  end

  describe "web_postos" do
    context "when there are more than one web_postos promotions" do
      let!(:promotion_one) { create(:promotion, :cpf, frequency_in_days: 0, redeems_per_cpf: 2, closed_interval: false, web_postos: true) }
      let!(:coupon_one) { create(:cupon, :cpf, promotion: promotion_one, frequency_in_days: 0, redeems_per_cpf: 2) }
      let!(:promotion_two) { create(:promotion, :cpf, frequency_in_days: 1, redeems_per_cpf: 1, closed_interval: false, web_postos: true) }
      let!(:coupon_two) { create(:cupon, :cpf, promotion: promotion_two, frequency_in_days: 1, redeems_per_cpf: 1) }

      it do
        create(:order, user:, cupon: coupon_one)
        create(:order, user:, cupon: coupon_one)
        order = build(:order, user:, cupon: coupon_two)

        expect(order).to be_invalid
        expect(order.errors.as_json).to eq({
          base: ["Esta promoção só pode ser ativada novamente a cada 1 dia(s)."]
        })
      end
    end
  end
end
