require "rails_helper"

RSpec.describe Order, type: :model do
  describe ".set_deep_link" do
    let(:user) { create(:user) }

    context "when is default strategy" do
      let(:cupon) { create(:cupon, :online, :online, url: "www.foo.com.br") }
      let!(:order) { build(:order, :online, cupon:, user:) }

      it "fill deep_link field" do
        expect { order.save! }.to change { order.deep_link }.from(nil).to(cupon.url)
      end
    end
    context "when is shopee strategy" do
      let!(:cupon) do
        create(:cupon, :online, url: "https://shopee.com.br/Pets-cat.11059991", integration_partner: IntegrationPartner::SHOPEE)
      end
      let!(:order) { build(:order, :online, cupon:, user:) }

      it "fill deep_link field", :vcr do
        expect { order.save! }.to change { order.deep_link }
          .from(nil).to(include("shp.ee"))
      end
    end
    context "when is awin strategy" do
      let(:cupon) { create(:cupon, :online, url: "https://www.awin1.com/test", integration_partner: IntegrationPartner::AWIN) }
      let!(:order) { build(:order, :online, cupon:, user:) }

      it "fill deep_link field" do
        expect { order.save! }.to change { order.deep_link }
          .from(nil).to("#{order.cupon.url}&clickref=#{order.number}")
      end
    end
    context "when is city_ads strategy" do
      let(:cupon) { create(:cupon, :online, url: "http://cityads.com/api/offers/test", integration_partner: IntegrationPartner::CITY_ADS) }
      let!(:order) { build(:order, :online, cupon:, user:) }

      it "fill deep_link field" do
        expect { order.save! }.to change { order.deep_link }
          .from(nil).to("#{order.cupon.url}&sa=#{order.number}")
      end
    end

    context "when is admitad strategy" do
      let!(:organization) { create(:organization, :with_admitad_integration) }
      let!(:branch) { create(:branch, :online, organization:) }
      let!(:cupon) do
        create(:cupon, :online, branch:,
          url: "https://ad.admitad.com/g/wgojabs850c9938dsa2fe12268cba/?i=3",
          integration_partner: IntegrationPartner::ADMITAD)
      end
      let!(:order) { build(:order, :online, cupon:, organization:, user:) }

      it "fill deep_link field" do
        expect { order.save! }.to change { order.deep_link }
          .from(nil).to(include("https://xyowz.com/g/"))
      end
    end

    context "when is rakuten strategy" do
      let!(:organization) { create(:organization, :with_rakuten_integration) }
      let!(:branch) { create(:branch, :online, organization:) }
      let!(:cupon) do
        create(:cupon, :online, branch:,
          url: "https://www.rakuten.com.br/random-promotion",
          integration_partner: IntegrationPartner::RAKUTEN)
      end
      let!(:order) { build(:order, :online, cupon:, organization:, user:) }

      it "fill deep_link field" do
        expect { order.save! }.to change { order.deep_link }
          .from(nil).to(include("#{cupon.url}&u1=#{order.number}"))
      end
    end

    context "with tracking placeholder in url" do
      it "fill deep_link field" do
        bucket = create(:voucher_bucket)
        promotion = create(:promotion, :available, :dynamic_coupon_code, dynamic_voucher: true, voucher_bucket: bucket)
        coupon = create(
          :cupon,
          :dynamic_online,
          promotion:,
          url: "https://example.com/product/123?code={{tracking_info}}",
          code: "XYZ098"
        )
        voucher = create(:voucher, code: "ABC123", bucket:)
        order = build(:order, :online, cupon: coupon, voucher:, user:)

        expect { order.save! }.to change { order.deep_link }
          .from(nil).to("https://example.com/product/123?code=ABC123")
      end
    end
  end
end
