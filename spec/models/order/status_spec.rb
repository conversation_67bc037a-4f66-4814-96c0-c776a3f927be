require "rails_helper"

RSpec.describe Order::Status do
  describe ".all" do
    it do
      expect(described_class.all).to match_array([
        "canceled",
        "completed",
        "pending"
      ])
    end
  end

  describe ".enum" do
    specify do
      expect(described_class.enum)
        .to eq(
          canceled: "canceled",
          completed: "completed",
          pending: "pending"
        )
    end
  end

  describe ".translate" do
    it do
      expect(described_class.translate("canceled")).to eq("Cancelado")
      expect(described_class.translate("completed")).to eq("Finalizado")
      expect(described_class.translate("pending")).to eq("Pendente")
    end
  end
end
