require "rails_helper"

RSpec.describe OrganizationProfile, type: :model do
  describe "#refresh_highest_cashback" do
    let(:organization) { create(:organization) }
    let(:business) { create(:business) }

    context "when highest cashback value is percent" do
      let(:organization_profile) do
        create(:organization_profile, organization:, business:, highest_cashback_type: :fixed, highest_cashback_value: 40)
      end

      before do
        create(:promotion, :available, organization:, business: nil, cashback_type: :percent, cashback_value: 30)
        create(:promotion, :available, organization:, business: nil, cashback_type: :percent, cashback_value: 40)
        create(:promotion, :available, organization:, business:, cashback_type: :percent, cashback_value: 20)
        create(:promotion, status: :pending, organization:, business:, cashback_type: :percent, cashback_value: 30)
        create(:promotion, :available, organization:, business:, cashback_type: :percent, cashback_value: 10)
      end

      it "refreshes highest cashback value and type" do
        expect { organization_profile.refresh_highest_cashback }
          .to change(organization_profile, :highest_cashback_type).from("fixed").to("percent")
          .and change(organization_profile, :highest_cashback_value).from(40).to(20)
      end
    end

    context "when highest cashback value is fixed" do
      let(:organization_profile) do
        create(:organization_profile, organization:, business:, highest_cashback_type: :percent, highest_cashback_value: 40)
      end

      before do
        create(:promotion, :available, organization:, business: nil, cashback_type: :fixed, cashback_value: 30)
        create(:promotion, :available, organization:, business: nil, cashback_type: :fixed, cashback_value: 40)
        create(:promotion, :available, organization:, business:, cashback_type: :fixed, cashback_value: 20)
        create(:promotion, status: :pending, organization:, business:, cashback_type: :fixed, cashback_value: 30)
        create(:promotion, :available, organization:, business:, cashback_type: :fixed, cashback_value: 10)
      end

      it "refreshes highest cashback value and type" do
        expect { organization_profile.refresh_highest_cashback }
          .to change(organization_profile, :highest_cashback_type).from("percent").to("fixed")
          .and change(organization_profile, :highest_cashback_value).from(40).to(20)
      end
    end
  end

  describe "#refresh_highest_discount" do
    let!(:organization) { create(:organization, :percent_highest_discount, highest_discount_value: 20, active: true) }
    let!(:business_one) { create(:business) }
    let!(:business_two) { create(:business) }

    it "when start with percent: expected to update the highest discount in profile to the percentage even if the fixed is higher" do
      profile_one = create(:organization_profile, :percent_highest_discount, organization:, business: business_one)
      profile_two = create(:organization_profile, :percent_highest_discount, organization:, business: business_two)
      create(:promotion, :available, :percent_discount, organization:, business: nil, discount_value: 5)
      create(:promotion, :available, :percent_discount, organization:, business: nil, discount_value: 4)
      create(:promotion, :available, :fixed_discount, organization:, business: business_one, discount_value: 30)
      create(:promotion, :available, :percent_discount, organization:, business: business_one, discount_value: 10)
      create(:promotion, :inactive, :percent_discount, organization:, business: business_one, discount_value: 15)
      create(:promotion, :available, :percent_discount, organization:, business: business_two, discount_value: 12)

      profile_one.refresh_highest_discount
      profile_two.refresh_highest_discount

      expect(organization.highest_discount_value).to eq(20)
      expect(organization.highest_discount_type).to eq("percent")
      expect(profile_one.highest_discount_value).to eq(10)
      expect(profile_one.highest_discount_type).to eq("percent")
      expect(profile_two.highest_discount_value).to eq(12)
      expect(profile_two.highest_discount_type).to eq("percent")
    end

    it "when start with fixed: expected to update the highest discount in profile to the percentage even if the fixed is higher" do
      profile_one = create(:organization_profile, :fixed_highest_discount, organization:, business: business_one)
      profile_two = create(:organization_profile, :fixed_highest_discount, organization:, business: business_two)
      create(:promotion, :available, :fixed_discount, organization:, business: nil, discount_value: 5)
      create(:promotion, :available, :percent_discount, organization:, business: nil, discount_value: 4)
      create(:promotion, :available, :percent_discount, organization:, business: business_one, discount_value: 3)
      create(:promotion, :available, :fixed_discount, organization:, business: business_one, discount_value: 10)
      create(:promotion, :available, :fixed_discount, organization:, business: business_one, discount_value: 10)
      create(:promotion, :available, :percent_discount, organization:, business: business_two, discount_value: 2)
      create(:promotion, :inactive, :fixed_discount, organization:, business: business_one, discount_value: 15)
      create(:promotion, :available, :fixed_discount, organization:, business: business_two, discount_value: 12)

      profile_one.refresh_highest_discount
      profile_two.refresh_highest_discount

      expect(organization.highest_discount_value).to eq(20.0)
      expect(organization.highest_discount_type).to eq("percent")
      expect(profile_one.highest_discount_value).to eq(3.0)
      expect(profile_one.highest_discount_type).to eq("percent")
      expect(profile_two.highest_discount_value).to eq(2.0)
      expect(profile_one.highest_discount_type).to eq("percent")
    end

    it "expected to update the highest discount in profile to fixed when there is no percentage discount" do
      profile_one = create(:organization_profile, :percent_highest_discount, organization:, business: business_one)
      profile_two = create(:organization_profile, :percent_highest_discount, organization:, business: business_two)
      create(:promotion, :available, :fixed_discount, organization:, business: nil, discount_value: 5)
      create(:promotion, :available, :fixed_discount, organization:, business: business_one, discount_value: 10)
      create(:promotion, :available, :fixed_discount, organization:, business: business_one, discount_value: 12)
      create(:promotion, :inactive, :fixed_discount, organization:, business: business_one, discount_value: 15)
      create(:promotion, :available, :fixed_discount, organization:, business: business_two, discount_value: 12)

      profile_one.refresh_highest_discount
      profile_two.refresh_highest_discount

      expect(organization.highest_discount_value).to eq(20.0)
      expect(organization.highest_discount_type).to eq("percent")
      expect(profile_one.highest_discount_value).to eq(12.0)
      expect(profile_one.highest_discount_type).to eq("fixed")
      expect(profile_two.highest_discount_value).to eq(12.0)
      expect(profile_one.highest_discount_type).to eq("fixed")
    end

    context "when there are no promotions" do
      it "expected update profile highest_discount" do
        profile_one = create(:organization_profile, :percent_highest_discount, organization:, business: business_one)
        profile_two = create(:organization_profile, :percent_highest_discount, organization:, business: business_two)

        profile_one.refresh_highest_discount
        profile_two.refresh_highest_discount

        expect(organization.highest_discount_value).to eq(20)
        expect(organization.highest_discount_type).to eq("percent")
        expect(profile_one.highest_discount_value).to eq(nil)
        expect(profile_one.highest_discount_type).to eq(nil)
        expect(profile_two.highest_discount_value).to eq(nil)
        expect(profile_one.highest_discount_type).to eq(nil)
      end
    end
  end
end
