require "rails_helper"

RSpec.describe Order, type: :model do
  let!(:user) { create(:user) }

  describe "returns reserved order" do
    let!(:bucket) { create(:voucher_bucket) }
    let!(:giftcard) { create(:giftcard, voucher_bucket: bucket) }
    let!(:voucher_one) { create(:voucher, bucket:) }
    let!(:voucher_two) { create(:voucher, bucket:) }
    let!(:voucher_three) { create(:voucher, bucket:) }
    let!(:order_reserved) { create(:order, :pending, :with_giftcard, user:, voucher: voucher_one) }
    let!(:order_not_reserved) { create(:order, :canceled, :with_giftcard, user:, voucher: voucher_two) }
    let!(:another_user) { create(:user) }
    let!(:order_reserved_another_user) { create(:order, :pending, :with_giftcard, user: another_user, voucher: voucher_three) }

    specify do
      result = Order
        .status_pending
        .with_gift_card_reserved_to(user:)
        .to_a

      expect(result).to eq([order_reserved])
    end
  end

  describe "price" do
    let!(:user) { create(:user, business:) }
    let!(:organization) { create(:organization) }
    let!(:branch) { create(:branch, :online, organization:) }
    let!(:bucket) { create(:voucher_bucket) }
    let!(:giftcard) { create(:giftcard, branches: [branch], price: 59.99, voucher_bucket: bucket) }
    let!(:voucher_one) { create(:voucher, bucket:) }
    let!(:voucher_two) { create(:voucher, bucket:) }

    context "when config exists" do
      let!(:business) { create(:business, cashback: true, currency: "points", fair_value: 0.0125) }
      let!(:fund_redemption_config) do
        create(:fund_redemption_config, :points, :giftcard, business:, fair_value: 0.03)
      end

      it "fills column" do
        order = build(:order, cupon: nil, giftcard:, user:, voucher: voucher_one)

        order.validate

        expect(order.redemption_fair_value).to eq 0.03
        expect(order.price).to eq 2000
      end
    end

    context "when config does not exist" do
      let!(:business) { create(:business, cashback: true, currency: "BRL") }

      it "does not fill column" do
        order = build(:order, cupon: nil, giftcard:, user:, voucher: voucher_two)

        order.validate

        expect(order.redemption_fair_value).to be_nil
        expect(order.price).to eq 59.99
      end
    end
  end
end
