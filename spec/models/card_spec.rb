# frozen_string_literal: true

require "rails_helper"

RSpec.describe Card, type: :model do
  let!(:business) { create(:business) }

  describe "association" do
    it "belongs to a business" do
      association = described_class.reflect_on_association(:business)
      expect(association.macro).to eq(:belongs_to)
    end
  end

  describe "validations" do
    context "presence" do
      it "is invalid without title" do
        card = build(:card, title: nil)
        card.valid?
        expect(card.errors[:title]).to include("não pode ficar em branco")
      end

      it "is invalid without description" do
        card = build(:card, description: nil)
        card.valid?
        expect(card.errors[:description]).to include("não pode ficar em branco")
      end

      it "is invalid without view_type" do
        card = build(:card, view_type: nil)
        card.valid?
        expect(card.errors[:view_type]).to include("não está incluído na lista")
      end
    end

    context "inclusion of view_type" do
      it "is valid when the type is included in the allowed ones" do
        card = build(:card)
        Card.view_types.each_value do |type|
          card.view_type = type
          expect(card).to be_valid
        end
      end

      it "is invalid when the type is not allowed" do
        card = build(:card)
        card.view_type = "invalid_type"
        expect(card).to be_invalid
      end
    end

    describe "scopes" do
      let!(:business) { create(:business) }
      let!(:card_support) { create(:card, :support_view_type, business:) }
      let!(:card_faq) { create(:card, :faq_view_type, business:) }

      describe ".supports" do
        it "returns only card of type support" do
          result = Card.support
          expect(result).to include(card_support)
          expect(result).not_to include(card_faq)
        end
      end

      describe ".faqs" do
        it "returns only card of type faq" do
          result = Card.faq
          expect(result).to include(card_faq)
          expect(result).not_to include(card_support)
        end
      end
    end
  end
end
