# frozen_string_literal: true

require "rails_helper"

RSpec.describe SubscriptionGroupPlan, type: :model do
  describe "associations" do
    it "belongs to a plan" do
      association = described_class.reflect_on_association(:plan)
      expect(association.macro).to eq(:belongs_to)
    end

    it "belongs to a subscription_group" do
      association = described_class.reflect_on_association(:subscription_group)
      expect(association.macro).to eq(:belongs_to)
    end
  end

  describe "validations" do
    let(:business) { create(:business) }
    let(:subscription_config) { create(:subscription_config, business:) }
    let(:subscription_group) { create(:subscription_group, subscription_config:) }
    let(:plan) { create(:plan, business:) }

    it "is valid with valid attributes" do
      subscription_group_plan = build(:subscription_group_plan, plan:, subscription_group:)
      expect(subscription_group_plan).to be_valid
    end

    it "is invalid without a plan" do
      subscription_group_plan = build(:subscription_group_plan, plan: nil, subscription_group:)
      subscription_group_plan.valid?
      expect(subscription_group_plan.errors[:plan]).to include("é obrigatório(a)").or include("can't be blank")
    end

    it "is invalid without a subscription_group" do
      subscription_group_plan = build(:subscription_group_plan, plan:, subscription_group: nil)
      subscription_group_plan.valid?
      expect(subscription_group_plan.errors[:subscription_group]).to include("é obrigatório(a)").or include("can't be blank")
    end
  end
end
