# frozen_string_literal: true

require "rails_helper"

RSpec.describe MailerConfig, type: :model do
  describe "#validations" do
    describe "email validation" do
      let(:mailer_config_with_valid_email) { build(:mailer_config, email_sender: "<EMAIL>") }
      let(:mailer_config_with_invalid_email) { build(:mailer_config, email_sender: "not_email") }

      it { expect(mailer_config_with_valid_email).to be_valid }
      it { expect(mailer_config_with_invalid_email).to be_invalid }
    end
  end

  describe "downcase email sender" do
    let(:mailer_config) { build(:mailer_config, email_sender: "<EMAIL>") }

    before { mailer_config.validate }

    it { expect(mailer_config.email_sender).to eq("<EMAIL>") }
  end

  describe "#refresh" do
    let(:email_domain) { "lecupon.com" }
    let(:email_domain_two) { "anydomain.com" }
    let!(:mailer_config) { create(:mailer_config, email_sender: "first@#{email_domain}") }
    let!(:unconfigured_mailer_config) { create(:mailer_config, email_sender: "second@#{email_domain_two}") }

    let(:aws_ses_mocked_api) { double("ses_mocked_api") }
    let(:domain_verify_mock) do
      double(:domain_verify_mock, dkim_tokens: ["ddpmitgd63igukzrhg7cuyqarju45crd", "zmmfcvbnsloxau7ohryhob7ugsmxursu", "wsq2vnhjb6gxkmoblna5toegerkep7t2"])
    end

    before do
      allow(Aws::SES::Client).to receive(:new).and_return(aws_ses_mocked_api)
      allow(aws_ses_mocked_api).to receive(:verify_domain_dkim).and_return(domain_verify_mock)
      allow(aws_ses_mocked_api).to receive(:get_identity_dkim_attributes).with({identities: [email_domain]}).and_return(mocked_dkim_attributes_for(email_domain))
      allow(aws_ses_mocked_api).to receive(:get_identity_dkim_attributes).with({identities: [email_domain_two]}).and_return(mocked_dkim_attributes_for(email_domain_two))
    end

    context "given a present email_sender" do
      context "when it has valid domain" do
        it "must validate this mailer_config dkim" do
          mailer_config.refresh!
          expect(aws_ses_mocked_api).to have_received(:verify_domain_dkim).with(domain: email_domain)

          expect(aws_ses_mocked_api).to have_received(:get_identity_dkim_attributes).with({identities: [email_domain]})

          expect(mailer_config.domain_configured).to eq(true)
          expect(mailer_config.dns_configured).to eq(true)

          expect(mailer_config.dns_records).to eq([
            {"name" => "ddpmitgd63igukzrhg7cuyqarju45crd._domainkey.lecupon.com", "type" => "CNAME", "value" => "ddpmitgd63igukzrhg7cuyqarju45crd.dkim.amazonses.com"},
            {"name" => "zmmfcvbnsloxau7ohryhob7ugsmxursu._domainkey.lecupon.com", "type" => "CNAME", "value" => "zmmfcvbnsloxau7ohryhob7ugsmxursu.dkim.amazonses.com"},
            {"name" => "wsq2vnhjb6gxkmoblna5toegerkep7t2._domainkey.lecupon.com", "type" => "CNAME", "value" => "wsq2vnhjb6gxkmoblna5toegerkep7t2.dkim.amazonses.com"}
          ])
        end
      end
    end

    context "when a mailer_config does not have email sender" do
      before do
        unconfigured_mailer_config.update(dns_configured: false, email_sender: nil)
      end

      it "must reset configs" do
        unconfigured_mailer_config.refresh!
        expect(aws_ses_mocked_api).not_to have_received(:verify_domain_dkim).with(domain: email_domain_two)
        expect(aws_ses_mocked_api).not_to have_received(:get_identity_dkim_attributes).with({identities: [email_domain_two]})

        expect(unconfigured_mailer_config.domain_configured).to eq(false)
        expect(unconfigured_mailer_config.dns_configured).to eq(false)
        expect(unconfigured_mailer_config.dns_records).to eq([])
      end
    end
  end

  describe "#refresh_all" do
    let!(:mailer_config_one) { create(:mailer_config, email_sender: "<EMAIL>", dns_configured: false, domain_configured: false) }
    let!(:mailer_config_two) { create(:mailer_config, email_sender: "<EMAIL>", dns_configured: true, domain_configured: true) }
    let!(:mailer_config_three) { create(:mailer_config, email_sender: nil) }

    it "must schedule MailerConfig::RefreshWorker for all unconfigured mailer_configs" do
      expect(MailerConfig::RefreshWorker).to receive(:perform_async).with(mailer_config_one.id)
      expect(MailerConfig::RefreshWorker).not_to receive(:perform_async).with(mailer_config_two.id)
      expect(MailerConfig::RefreshWorker).not_to receive(:perform_async).with(mailer_config_three.id)
      MailerConfig.refresh_all
    end
  end

  describe "send_transactional_email behavious" do
    let!(:user) { create(:user) }
    let(:mailer_config) { user.business.mailer_config }

    context "when is true" do
      it "must sent emails to user normally" do
        TelemedicineMailer.with(business: user.business, user:).sign_up.deliver_now
        expect(ActionMailer::Base.deliveries.count).to eq(1)
      end
    end

    context "when is false" do
      it "must intercept Mailer and not send any emails to user" do
        mailer_config.update(send_transactional_emails: false)
        TelemedicineMailer.with(business: user.business, user:).sign_up.deliver_now
        expect(ActionMailer::Base.deliveries.count).to eq(0)
      end
    end
  end

  def mocked_dkim_attributes_for(domain)
    double(:api_mock,
      dkim_attributes: {
        domain => double(
          :domain,
          dkim_enabled: true,
          dkim_verification_status: "Success",
          dkim_tokens: ["ddpmitgd63igukzrhg7cuyqarju45crd", "zmmfcvbnsloxau7ohryhob7ugsmxursu", "wsq2vnhjb6gxkmoblna5toegerkep7t2"]
        )
      })
  end
end
