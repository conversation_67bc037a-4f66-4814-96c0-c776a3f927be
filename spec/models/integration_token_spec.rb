require "rails_helper"

RSpec.describe IntegrationToken, type: :model do
  describe "validations" do
    let(:integration_token) { build(:integration_token) }

    context "username and password validations" do
      it "requires password when username is present" do
        integration_token.username = "user123"
        integration_token.password = nil

        expect(integration_token).not_to be_valid
        expect(integration_token.errors[:password_digest]).to include("não pode ficar em branco")
      end

      it "requires username when password is present" do
        integration_token.password = "pass123"
        integration_token.username = nil

        expect(integration_token).not_to be_valid
        expect(integration_token.errors[:username]).to include("não pode ficar em branco")
      end

      it "is valid when both username and password are present" do
        integration_token.username = "user123"
        integration_token.password = "pass123"

        expect(integration_token).to be_valid
      end

      it "is valid when both username and password are nil" do
        integration_token.username = nil
        integration_token.password = nil

        expect(integration_token).to be_valid
      end
    end

    it "allows creating multiple tokens with nil username" do
      first_token = IntegrationToken.create!(
        service: Enums::IntegrationTokenService::FEAT_BANK,
        description: "Primeiro token",
        username: nil
      )

      second_token = IntegrationToken.create!(
        service: Enums::IntegrationTokenService::EXTRAFARMA,
        description: "Segundo token",
        username: nil
      )

      expect(first_token).to be_persisted
      expect(second_token).to be_persisted
    end
  end

  describe "password encryption" do
    let(:integration_token) { create(:integration_token, :with_credentials) }

    it "encrypts the password" do
      expect(integration_token.password_digest).not_to eq("pass123")
    end

    it "can authenticate with correct password" do
      expect(integration_token.authenticate("pass123")).to eq(integration_token)
    end

    it "cannot authenticate with incorrect password" do
      expect(integration_token.authenticate("wrong_password")).to be_falsey
    end
  end
end
