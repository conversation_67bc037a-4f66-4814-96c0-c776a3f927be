require "rails_helper"

RSpec.describe ImportFile::ProcessWorker do
  it "processes the import file" do
    business = create(:business, cashback: true, currency: "points", fair_value: 0.03)
    user = create(:user, business:)
    order_1 = create(:order, :online, user:, number: "LC987")
    order_2 = create(:order, :online, user:, number: "LC134")
    order_3 = create(:order, :online, user:, number: "LC456")
    order_4 = create(:order, :online, user:, number: "LC524")
    create(:order, :online, user:, number: "LC812")

    promotion_no_cashback = create(:promotion, :online, cashback_type: nil, cashback_value: 0.0)
    coupon_no_cashback = create(:cupon, :online, promotion: promotion_no_cashback)
    create(:order, :online, user:, cupon: coupon_no_cashback, number: "LC598")

    import_file = create_import_file(rows: [
      [" 123", "LC987", "pending ", 212.88],
      [nil, "  LC134", " approved ", 100.0],
      [" ", "LC456", " available_lecupon  ", 100.0],
      ["321 ", "LC524 ", " approved ", 50.0],
      [" 805", " LC524  ", " approved  ", 70.0],
      [nil, " LC182  ", " approved ", 90.0],
      [nil, "LC598", "pending", 120.0],
      [nil, "LC812", "pending", 0]
    ])

    expect { process(import_file) }.to change(Fund.points, :count).by(5)

    expect(Fund.points.pluck(:order_id, :status, :idempotency_key)).to match_array([
      [order_1.id, "pending", "123"],
      [order_2.id, "approved", nil],
      [order_3.id, "approved", nil],
      [order_4.id, "approved", "321"],
      [order_4.id, "approved", "805"]
    ])
    expect(Fund.points.approved.where.not(credits_at: nil).pluck(:credits_at).map(&:to_date)).to all(eq(Date.tomorrow))

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(8)
    expect(import_file.amount_rows_processed_successfully).to eq(5)
    expect(import_file.amount_rows_with_errors).to eq(3)
    errors = import_file.import_file_errors.map { [_1.line, _1.details] }
    expect(errors).to match_array([
      [7, "Order Order não encontrada"],
      [8, "Order cashback value O valor de cashback da order precisa ser maior do que zero para criar cashback"],
      [9, "Order amount deve ser maior que 0"]
    ])
  end

  it "processes with existing point and updates it, skipping unprocessable points" do
    business = create(:business, cashback: true, currency: "points", fair_value: 0.03)
    user = create(:user, :with_cashback, business:)
    business_no_cashback = create(:business, cashback: false, currency: "points", fair_value: 0.03)
    user_no_cashback = create(:user, business: business_no_cashback)
    order_1 = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    fund = create(:fund, :points, :approved, order: order_1, user:, order_amount: 24133)
    order_2 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10")
    order_3 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20")
    order_4 = create(:order, :online, user:, number: "LC237", redeem_code: "LECUPON30")
    order_5 = create(:order, :online, user:, number: "LC652", redeem_code: "LECUPON40")
    order_6 = create(:order, :online, user: user_no_cashback, number: "LC785", redeem_code: "LECUPON20")
    fund_credited = create(:fund, :points, :available, credited: true, order: order_4, user:, order_amount: 1000)
    fund_canceled = create(:fund, :points, :canceled, order: order_5, user:, order_amount: 1200)
    fund_not_computable = create(:fund, :points, :approved, order: order_6, user: user_no_cashback, order_amount: 2000)

    import_file = create_import_file(rows: [
      ["123", "LC987", "available_lecupon", 241.33],
      [nil, "LC134", "approved", 100.0],
      ["", "LC456", "available_lecupon", 100.0],
      ["321", "LC524", "approved", 50.0],
      ["321", "LC524", "approved", 70.0],
      [nil, "LC182", "approved", 90.0],
      [nil, "LC237", "available_lecupon", 1000.0],
      [nil, "LC652", "canceled", 1200.0],
      [nil, "LC785", "available_lecupon", 2000.0]
    ])

    expect { process(import_file) }.to change(Fund.points, :count).by(2)
      .and not_change(order_1.funds, :count)
      .and change { fund.reload.credits_at }
      .and not_change { fund_credited.reload.attributes }
      .and not_change { fund_canceled.reload.attributes }
      .and not_change { fund_not_computable.reload.credits_at }

    expect(Fund.points.pluck(:order_id, :status, :idempotency_key)).to match_array([
      [order_1.id, "approved", "123"],
      [order_2.id, "approved", nil],
      [order_3.id, "approved", nil],
      [order_4.id, "available", nil],
      [order_5.id, "canceled", nil],
      [order_6.id, "approved", nil]
    ])
    expect(Fund.points.approved.joins(:order).where(orders: {id: [order_1.id, order_3.id]}).pluck(:credits_at).map(&:to_date)).to all(eq(Date.tomorrow))

    fund.reload
    expect(fund).to be_approved
    expect(fund.order_amount).to eq(24133)
    expect(fund.idempotency_key).to eq("123")

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(9)
    expect(import_file.amount_rows_processed_successfully).to eq(6)
    expect(import_file.amount_rows_with_errors).to eq(3)
    errors = import_file.import_file_errors.map { [_1.line, _1.details] }
    expect(errors).to match_array([
      [5, "Order Order não encontrada"],
      [6, "Order Order não encontrada"],
      [7, "Order Order não encontrada"]
    ])
  end

  it "processes with existing available point, ignores it and imports the others" do
    business = create(:business, cashback: true, currency: "points", fair_value: 0.03)
    user = create(:user, :with_cashback, business:)
    order_1 = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    fund = create(:fund, :points, :available, order: order_1, user:, order_amount: 24133, credits_at: 1.day.from_now)
    order_2 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10")
    order_3 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20")

    import_file = create_import_file(rows: [
      ["123", "LC987", "approved", 212.88],
      [nil, "LC134", "approved", 100.0],
      ["", "LC456", "available_lecupon", 100.0],
      ["321", "LC524", "approved", 50.0],
      ["321", "LC524", "approved", 70.0],
      [nil, "LC182", "approved", 90.0]
    ])

    expect { process(import_file) }.to change(Fund.points, :count).by(2)
      .and not_change(order_1.funds, :count)

    expect(Fund.points.pluck(:order_id, :status, :idempotency_key)).to match_array([
      [order_1.id, "available", nil],
      [order_2.id, "approved", nil],
      [order_3.id, "approved", nil]
    ])
    expect(Fund.points.approved.where.not(credits_at: nil).pluck(:credits_at).map(&:to_date)).to all(eq(Date.tomorrow))

    fund.reload
    expect(fund).to be_available
    expect(fund.order_amount).to eq(24133)
    expect(fund.idempotency_key).to eq(nil)

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(6)
    expect(import_file.amount_rows_processed_successfully).to eq(3)
    expect(import_file.amount_rows_with_errors).to eq(3)
    errors = import_file.import_file_errors.map { [_1.line, _1.details] }
    expect(errors).to match_array([
      [5, "Order Order não encontrada"],
      [6, "Order Order não encontrada"],
      [7, "Order Order não encontrada"]
    ])
  end

  it "processes with existing point with different external_id, ignores it and creating new one" do
    business = create(:business, cashback: true, currency: "points", fair_value: 0.03)
    user = create(:user, :with_cashback, business:)
    order_1 = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    fund = create(:fund, :points, :pending, order: order_1, user:, order_amount: 21288, idempotency_key: 234)
    order_2 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10")
    order_3 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20")

    import_file = create_import_file(rows: [
      ["123", "LC987", "pending", 212.88],
      [nil, "LC134", "approved", 100.0],
      ["", "LC456", "available_lecupon", 100.0],
      ["321", "LC524", "approved", 50.0],
      ["321", "LC524", "approved", 70.0],
      [nil, "LC182", "approved", 90.0]
    ])

    expect { process(import_file) }.to change(Fund.points, :count).by(3)
      .and change(order_1.funds, :count).by(1)
      .and not_change(fund.reload, :attributes)

    expect(Fund.points.pluck(:order_id, :status, :idempotency_key)).to match_array([
      [order_1.id, "pending", "123"],
      [order_1.id, "pending", "234"],
      [order_2.id, "approved", nil],
      [order_3.id, "approved", nil]
    ])

    fund = order_1.funds.order(:created_at).last
    expect(fund).to be_pending
    expect(fund.order_amount).to eq(21288)
    expect(fund.idempotency_key).to eq("123")

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(6)
    expect(import_file.amount_rows_processed_successfully).to eq(3)
    expect(import_file.amount_rows_with_errors).to eq(3)
    errors = import_file.import_file_errors.map { [_1.line, _1.details] }
    expect(errors).to match_array([
      [5, "Order Order não encontrada"],
      [6, "Order Order não encontrada"],
      [7, "Order Order não encontrada"]
    ])
  end

  it "processes with points to be ignored" do
    business = create(:business, cashback: true, currency: "points", fair_value: 0.03)
    user = create(:user, :with_cashback, business:)
    order_1 = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    order_2 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10")
    order_3 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20")

    fund_1 = create(:fund, :points, :pending, order: order_1, user:, order_amount: 21288)
    fund_2 = create(:fund, :points, :available, order: order_3, user:, order_amount: 10000)

    import_file = create_import_file(rows: [
      ["123", "LC987", "approved", 212.88],
      ["", "LC134", "available_lecupon", 100.0]
    ])

    expect { process(import_file) }.to change(Fund.points, :count).by(1)
    expect(Fund.points.pluck(:order_id, :status, :idempotency_key)).to match_array([
      [order_1.id, "approved", "123"],
      [order_2.id, "approved", nil],
      [order_3.id, "available", nil]
    ])

    [fund_1, fund_2].each(&:reload)
    expect(fund_1).to be_approved
    expect(fund_1.idempotency_key).to eq("123")
    expect(fund_2).to be_available

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(2)
    expect(import_file.amount_rows_processed_successfully).to eq(2)
    expect(import_file.amount_rows_with_errors).to eq(0)
    expect(import_file.import_file_errors).to be_empty
  end

  it "processes when updating point without external_id in file and order has one point" do
    business = create(:business, cashback: true, currency: "points", fair_value: 0.03)
    user = create(:user, :with_cashback, business:)
    order = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    create(:fund, :points, :pending, order:, user:, idempotency_key: 987, order_amount: 48511)

    import_file = create_import_file(rows: [
      [nil, "LC987", "approved", 212.88]
    ])

    expect { process(import_file) }.to change(Fund.points, :count).by(1)
    expect(Fund.points.pluck(:order_id, :status, :idempotency_key)).to match_array([
      [order.id, "pending", "987"],
      [order.id, "approved", nil]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(1)
    expect(import_file.amount_rows_processed_successfully).to eq(1)
    expect(import_file.amount_rows_with_errors).to eq(0)
    expect(import_file.import_file_errors).to be_empty
  end

  it "processes when updating point without external_id in file and order has more than one point without external_id in file" do
    business = create(:business, cashback: true, currency: "points", fair_value: 0.03)
    user = create(:user, :with_cashback, business:)
    order = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    create(:fund, :points, :pending, order:, user:, idempotency_key: 987, order_amount: 15493)
    create(:fund, :points, :pending, order:, user:, idempotency_key: 654, order_amount: 52634)

    import_file = create_import_file(rows: [
      [nil, "LC987", "approved", 212.88]
    ])

    expect { process(import_file) }.to change(Fund.points, :count).by(0)
    expect(Fund.points.pluck(:order_id, :status, :idempotency_key)).to match_array([
      [order.id, "pending", "987"],
      [order.id, "pending", "654"]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(1)
    expect(import_file.amount_rows_processed_successfully).to eq(0)
    expect(import_file.amount_rows_with_errors).to eq(1)
    errors = import_file.import_file_errors.map { [_1.line, _1.details] }
    expect(errors).to match_array([
      [2, "External O external_id não foi informado e a order possui mais de 1 cashback"]
    ])
  end

  it "processes when updating point with external_id in file and order has more than one point with external_id in file" do
    business = create(:business, cashback: true, currency: "points", fair_value: 0.03)
    user = create(:user, :with_cashback, business:)
    order = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    create(:fund, :points, :pending, order:, user:, idempotency_key: 543, order_amount: 65420)
    create(:fund, :points, :pending, order:, user:, idempotency_key: 321, order_amount: 48152)

    import_file = create_import_file(rows: [
      ["123", "LC987", "approved", 212.88]
    ])

    expect { process(import_file) }.to change(Fund.points, :count).by(1)
    expect(Fund.points.pluck(:order_id, :status, :idempotency_key)).to match_array([
      [order.id, "pending", "543"],
      [order.id, "pending", "321"],
      [order.id, "approved", "123"]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(1)
    expect(import_file.amount_rows_processed_successfully).to eq(1)
    expect(import_file.amount_rows_with_errors).to eq(0)
  end

  it "processes generating only header error when headers are invalid" do
    file = Tempfile.new(["test", ".csv"], "tmp")
    CSV.open(file, "wb", headers: true, col_sep: ";") do |csv|
      csv << %w[external_id order_number status wrong_header]
      csv << ["123", "LC987", "pending", 212.88]
    end

    expect { create(:import_file, file:, kind: "cashback") }
      .to raise_error(ActiveRecord::RecordInvalid, /Cabeçalho inválido/)

    file.close
  end

  it "processes generating only malformed csv error when csv is malformed" do
    file = Tempfile.new(["test", ".csv"], "tmp")
    file.write('"external_id;order_number;status;order_amount')

    expect { create(:import_file, file:, kind: "cashback") }
      .to raise_error(CSV::MalformedCSVError)
  end

  it "processes the import file and does not update credit date when it did not turn available" do
    business = create(:business, cashback: true, currency: "points", fair_value: 0.03)
    user = create(:user, business:)
    order_1 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10", cashback_type: :percent, cashback_value: 10)
    order_2 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20", cashback_type: :percent, cashback_value: 10)
    order_3 = create(:order, :online, user:, number: "LC152", redeem_code: "LECUPON15", cashback_type: :percent, cashback_value: 10)

    fund = create(:fund, :points, :pending, order: order_3, user:, idempotency_key: "123", order_amount: 53259)

    import_file = create_import_file(rows: [
      [nil, "LC134", "approved", 100.0],
      ["", "LC456", "available_lecupon", 100.0],
      ["123", "LC152", "approved", 532.59]
    ])

    expect { process(import_file) }
      .to change(Fund.points, :count).by(2)
      .and not_change { fund.reload.credits_at }

    expect(Fund.points.pluck(:order_id, :status, :idempotency_key)).to match_array([
      [order_1.id, "approved", nil],
      [order_2.id, "approved", nil],
      [order_3.id, "approved", "123"]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(3)
    expect(import_file.amount_rows_processed_successfully).to eq(3)
    expect(import_file.amount_rows_with_errors).to eq(0)
  end

  def create_import_file(rows:)
    file = Tempfile.new(["test", ".csv"], "tmp")
    CSV.open(file, "wb", headers: true, col_sep: ";") do |csv|
      csv << %w[external_id order_number transaction_status order_amount]
      rows.each { |row| csv << row }
    end
    import_file = create(:import_file, file:, kind: "cashback")
    file.close
    import_file
  end

  def process(import_file)
    ImportFile::ProcessWorker.new.perform(
      FFaker::Internet.email,
      DateTime.current.to_s,
      import_file.id
    )
  end
end
