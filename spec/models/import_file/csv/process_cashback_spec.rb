require "rails_helper"

RSpec.describe ImportFile::ProcessWorker do
  let(:notifier_stub) { instance_double(CashbackRecord::History::NotificationService, call: nil) }

  it "processes the import file" do
    allow(CashbackRecord::History::NotificationService).to receive(:new).and_return(notifier_stub)

    business = create(:business, :with_cashback, spread_percent: 0)
    user = create(:user, business:)
    inflow_wallet = create(:wallet, :inflow, kind: business.cashback_wallet_destination)
    business_wallet = create(:wallet, business:, kind: business.cashback_wallet_destination)
    user_wallet = create(:wallet, user:, kind: business.cashback_wallet_destination)
    order_1 = create(:order, :online, user:, number: "LC987")
    order_2 = create(:order, :online, user:, number: "LC134")
    order_3 = create(:order, :online, user:, number: "LC456")
    order_4 = create(:order, :online, user:, number: "LC524")
    create(:order, :online, user:, number: "LC812")

    promotion_no_cashback = create(:promotion, :online, cashback_type: nil, cashback_value: 0.0)
    coupon_no_cashback = create(:cupon, :online, promotion: promotion_no_cashback)
    create(:order, :online, user:, cupon: coupon_no_cashback, number: "LC598")

    import_file = create_import_file(rows: [
      [" 123", "LC987", "pending ", 212.88],
      [nil, "  LC134", " approved ", 100.0],
      [" ", "LC456", " available_lecupon  ", 100.0],
      ["321 ", "LC524 ", " approved ", 50.0],
      [" 805", " LC524  ", " approved  ", 70.0],
      [nil, " LC182  ", " approved ", 90.0],
      [nil, "LC598", "pending", 120.0],
      [nil, "LC812", "pending", 0]
    ])

    expect { process(import_file) }
      .to change(CashbackRecord, :count).by(5)
      .and change { user_wallet.reload.balance }.by(1000)
      .and not_change { inflow_wallet.reload.balance }
      .and not_change { business_wallet.reload.balance }

    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order_1.id, "pending", "123"],
      [order_2.id, "approved", nil],
      [order_3.id, "available", nil],
      [order_4.id, "approved", "321"],
      [order_4.id, "approved", "805"]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(8)
    expect(import_file.amount_rows_processed_successfully).to eq(5)
    expect(import_file.amount_rows_with_errors).to eq(3)
    errors = import_file.import_file_errors.map { [_1.line, _1.details] }
    expect(errors).to match_array([
      [7, "Order Order não encontrada"],
      [8, "Order cashback value O valor de cashback da order precisa ser maior do que zero para criar cashback"],
      [9, "Order amount deve ser maior que 0"]
    ])

    expect(notifier_stub).to have_received(:call).exactly(5).times
  end

  it "processes when business has cashback as wallet destination" do
    business = create(:business, :with_cashback, cashback_wallet_destination: :cashback, spread_percent: 0)
    user = create(:user, :with_cashback, business:)
    inflow_wallet = create(:wallet, :inflow, kind: business.cashback_wallet_destination)
    business_wallet = create(:wallet, business:, kind: business.cashback_wallet_destination)
    user_wallet = create(:wallet, user:, kind: business.cashback_wallet_destination)
    order = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20")

    import_file = create_import_file(rows: [
      [nil, "LC456", "available_lecupon", 100.0]
    ])

    expect { process(import_file) }
      .to change(CashbackRecord, :count).by(1)
      .and change { user_wallet.reload.balance }.by(1000)
      .and not_change { inflow_wallet.reload.balance }
      .and not_change { business_wallet.reload.balance }

    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order.id, "available", nil]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(1)
    expect(import_file.amount_rows_processed_successfully).to eq(1)
    expect(import_file.amount_rows_with_errors).to eq(0)
    expect(import_file.import_file_errors).to be_empty
  end

  it "processes when business has membership as wallet destination" do
    business = create(:business, :with_cashback, cashback_wallet_destination: :membership, spread_percent: 0)
    create(:cashback_transfer_frequency, business:)
    user = create(:user, :with_cashback, business:)
    inflow_wallet = create(:wallet, :inflow, kind: business.cashback_wallet_destination)
    business_wallet = create(:wallet, business:, kind: business.cashback_wallet_destination)
    user_wallet = create(:wallet, user:, kind: business.cashback_wallet_destination)
    order = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20")

    import_file = create_import_file(rows: [
      ["", "LC456", "available", "100.0"]
    ])

    expect { process(import_file) }
      .to change(CashbackRecord, :count).by(1)
      .and not_change { user_wallet.reload.balance }
      .and not_change { inflow_wallet.reload.balance }
      .and not_change { business_wallet.reload.balance }

    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order.id, "available_lecupon", nil]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(1)
    expect(import_file.amount_rows_processed_successfully).to eq(1)
    expect(import_file.amount_rows_with_errors).to eq(0)
    expect(import_file.import_file_errors).to be_empty
  end

  it "processes with existing cashback and updates it correctly regardless of how many times the job is called concurrently" do
    business = create(:business, cashback: true, cashback_wallet_destination: :cashback, spread_percent: 0)
    user = create(:user, :with_cashback, business:)
    inflow_wallet = create(:wallet, :inflow, kind: business.cashback_wallet_destination)
    business_wallet = create(:wallet, business:, kind: business.cashback_wallet_destination)
    user_wallet = create(:wallet, user:, kind: business.cashback_wallet_destination)
    order_1 = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    cashback = create(:cashback_record, :pending, order: order_1, user:)
    order_2 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10")
    order_3 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20")

    allow(CashbackRecord::History::NotificationService).to receive(:new).and_return(notifier_stub)

    import_file = create_import_file(rows: [
      ["123", "LC987", "available_lecupon", 212.88],
      [nil, "LC134", "approved", 100.0],
      ["", "LC456", "available_lecupon", 100.0],
      ["321", "LC524", "approved", 50.0],
      ["321", "LC524", "approved", 70.0],
      [nil, "LC182", "approved", 90.0]
    ])

    Array.new(10) do
      Thread.new do
        ActiveRecord::Base.connection_pool.with_connection do
          ImportFile::ProcessWorker.new.perform(FFaker::Internet.email, DateTime.current.to_s, import_file.id)
        end
      end
    end.each(&:join)

    expect(CashbackRecord.count).to eq 3
    expect(user_wallet.reload.balance).to eq 3129
    expect(inflow_wallet.reload.balance).to eq 0
    expect(business_wallet.reload.balance).to eq 0
    expect(notifier_stub).to have_received(:call).exactly(3).times

    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order_1.id, "available", "123"],
      [order_2.id, "approved", nil],
      [order_3.id, "available", nil]
    ])

    cashback.reload
    expect(cashback).to be_available
    expect(cashback.order_amount).to eq(212.88)
    expect(cashback.total_commission_amount).to eq(21.29)
    expect(cashback.external_id).to eq("123")

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(6)
    expect(import_file.amount_rows_processed_successfully).to eq(3)
    expect(import_file.amount_rows_with_errors).to eq(3)
    errors = import_file.import_file_errors.map { [_1.line, _1.details] }
    expect(errors).to match_array([
      [5, "Order Order não encontrada"],
      [6, "Order Order não encontrada"],
      [7, "Order Order não encontrada"]
    ])
  end

  it "processes with existing transferred cashback, ignores it and imports the others" do
    user = create(:user, :with_cashback)
    create(:wallet, :inflow, kind: user.business.cashback_wallet_destination)
    create(:wallet, business: user.business, kind: user.business.cashback_wallet_destination)
    create(:wallet, user:, kind: user.business.cashback_wallet_destination)
    order_1 = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    cashback = create(:cashback_record, :transferred, order: order_1, user:)
    order_2 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10")
    order_3 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20")

    import_file = create_import_file(rows: [
      ["123", "LC987", "pending", 212.88],
      [nil, "LC134", "approved", 100.0],
      ["", "LC456", "available_lecupon", 100.0],
      ["321", "LC524", "approved", 50.0],
      ["321", "LC524", "approved", 70.0],
      [nil, "LC182", "approved", 90.0]
    ])

    expect { process(import_file) }.to change(CashbackRecord, :count).by(2)
      .and not_change(order_1.cashback_records, :count)

    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order_1.id, "transferred", nil],
      [order_2.id, "approved", nil],
      [order_3.id, "available", nil]
    ])

    cashback.reload
    expect(cashback).to be_transferred
    expect(cashback.order_amount).not_to eq(212.88)
    expect(cashback.total_commission_amount).not_to eq(21.29)
    expect(cashback.external_id).to eq(nil)

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(6)
    expect(import_file.amount_rows_processed_successfully).to eq(3)
    expect(import_file.amount_rows_with_errors).to eq(3)
    errors = import_file.import_file_errors.map { [_1.line, _1.details] }
    expect(errors).to match_array([
      [5, "Order Order não encontrada"],
      [6, "Order Order não encontrada"],
      [7, "Order Order não encontrada"]
    ])
  end

  it "processes with existing cashback with different external_id, ignores it and creating new one" do
    user = create(:user, :with_cashback)
    create(:wallet, :inflow, kind: user.business.cashback_wallet_destination)
    create(:wallet, business: user.business, kind: user.business.cashback_wallet_destination)
    create(:wallet, user:, kind: user.business.cashback_wallet_destination)
    order_1 = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    cashback = create(:cashback_record, :pending, order: order_1, external_id: 234, user:)
    order_2 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10")
    order_3 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20")

    import_file = create_import_file(rows: [
      ["123", "LC987", "pending", 212.88],
      [nil, "LC134", "approved", 100.0],
      ["", "LC456", "available_lecupon", 100.0],
      ["321", "LC524", "approved", 50.0],
      ["321", "LC524", "approved", 70.0],
      [nil, "LC182", "approved", 90.0]
    ])

    expect { process(import_file) }.to change(CashbackRecord, :count).by(3)
      .and change(order_1.cashback_records, :count).by(1)
      .and not_change(cashback.reload, :attributes)

    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order_1.id, "pending", "123"],
      [order_1.id, "pending", "234"],
      [order_2.id, "approved", nil],
      [order_3.id, "available", nil]
    ])

    cashback = order_1.cashback_records.order(:created_at).last
    expect(cashback).to be_pending
    expect(cashback.order_amount).to eq(212.88)
    expect(cashback.total_commission_amount).to eq(21.29)
    expect(cashback.external_id).to eq("123")

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(6)
    expect(import_file.amount_rows_processed_successfully).to eq(3)
    expect(import_file.amount_rows_with_errors).to eq(3)
    errors = import_file.import_file_errors.map { [_1.line, _1.details] }
    expect(errors).to match_array([
      [5, "Order Order não encontrada"],
      [6, "Order Order não encontrada"],
      [7, "Order Order não encontrada"]
    ])
  end

  it "processes with business cashback disabled" do
    user = create(:user, :with_cashback)
    create(:wallet, :inflow, kind: user.business.cashback_wallet_destination)
    create(:wallet, business: user.business, kind: user.business.cashback_wallet_destination)
    create(:wallet, user:, kind: user.business.cashback_wallet_destination)
    user_without_cashback = create(:user, :without_cashback)
    order_1 = create(:order, :online, user: user_without_cashback, number: "LC987", redeem_code: "PROMO10")
    order_2 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10")
    order_3 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20")

    import_file = create_import_file(rows: [
      ["123", "LC987", "pending", 212.88],
      [nil, "LC134", "approved", 100.0],
      ["", "LC456", "available_lecupon", 100.0],
      ["321", "LC524", "approved", 50.0],
      ["321", "LC524", "approved", 70.0],
      [nil, "LC182", "approved", 90.0]
    ])

    expect { process(import_file) }.to change(CashbackRecord, :count).by(3)
    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order_1.id, "pending", "123"],
      [order_2.id, "approved", nil],
      [order_3.id, "available", nil]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(6)
    expect(import_file.amount_rows_processed_successfully).to eq(3)
    expect(import_file.amount_rows_with_errors).to eq(3)
    errors = import_file.import_file_errors.map { [_1.line, _1.details] }
    expect(errors).to match_array([
      [5, "Order Order não encontrada"],
      [6, "Order Order não encontrada"],
      [7, "Order Order não encontrada"]
    ])
  end

  it "processes with cashbacks to be ignored" do
    user = create(:user, :with_cashback)
    create(:wallet, :inflow, kind: user.business.cashback_wallet_destination)
    create(:wallet, business: user.business, kind: user.business.cashback_wallet_destination)
    create(:wallet, user:, kind: user.business.cashback_wallet_destination)
    order_1 = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    order_2 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10")
    order_3 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20")

    cashback_1 =
      create(:cashback_record,
        :pending,
        order: order_1,
        user:,
        order_amount: 212.88,
        total_commission_amount: 21.29,
        business_spread_amount: 2.13,
        cashback_amount: 19.16)
    cashback_2 =
      create(:cashback_record,
        :in_transfer_lecupon,
        order: order_3,
        user:,
        order_amount: 100,
        total_commission_amount: 10,
        business_spread_amount: 1,
        cashback_amount: 9)

    import_file = create_import_file(rows: [
      ["123", "LC987", "approved", 212.88],
      ["", "LC134", "available_lecupon", 100.0]
    ])

    expect { process(import_file) }.to change(CashbackRecord, :count).by(1)
    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order_1.id, "approved", "123"],
      [order_2.id, "available", nil],
      [order_3.id, "in_transfer_lecupon", nil]
    ])

    [cashback_1, cashback_2].each(&:reload)
    expect(cashback_1.transaction_status).to eq(Enums::CashbackRecordStatus::APPROVED)
    expect(cashback_1.external_id).to eq("123")
    expect(cashback_2.transaction_status).to eq(Enums::CashbackRecordStatus::IN_TRANSFER_LECUPON)

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(2)
    expect(import_file.amount_rows_processed_successfully).to eq(2)
    expect(import_file.amount_rows_with_errors).to eq(0)
    expect(import_file.import_file_errors).to be_empty
  end

  it "processes when updating cashback without external_id in file and order has one cashback" do
    user = create(:user, :with_cashback)
    create(:wallet, :inflow, kind: user.business.cashback_wallet_destination)
    create(:wallet, business: user.business, kind: user.business.cashback_wallet_destination)
    create(:wallet, user:, kind: user.business.cashback_wallet_destination)
    order = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    create(:cashback_record, :pending, order:, user:, external_id: 987)

    import_file = create_import_file(rows: [
      [nil, "LC987", "approved", 212.88]
    ])

    expect { process(import_file) }.to change(CashbackRecord, :count).by(1)
    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order.id, "pending", "987"],
      [order.id, "approved", nil]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(1)
    expect(import_file.amount_rows_processed_successfully).to eq(1)
    expect(import_file.amount_rows_with_errors).to eq(0)
    expect(import_file.import_file_errors).to be_empty
  end

  it "processes when updating cashback without external_id in file and order has more than one cashback without external_id in file" do
    user = create(:user, :with_cashback)
    create(:wallet, :inflow, kind: user.business.cashback_wallet_destination)
    create(:wallet, business: user.business, kind: user.business.cashback_wallet_destination)
    create(:wallet, user:, kind: user.business.cashback_wallet_destination)
    order = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    create(:cashback_record, :pending, order:, user:, external_id: 987)
    create(:cashback_record, :pending, order:, user:, external_id: 654)

    import_file = create_import_file(rows: [
      [nil, "LC987", "approved", 212.88]
    ])

    expect { process(import_file) }.to change(CashbackRecord, :count).by(0)
    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order.id, "pending", "987"],
      [order.id, "pending", "654"]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(1)
    expect(import_file.amount_rows_processed_successfully).to eq(0)
    expect(import_file.amount_rows_with_errors).to eq(1)
    errors = import_file.import_file_errors.map { [_1.line, _1.details] }
    expect(errors).to match_array([
      [2, "External O external_id não foi informado e a order possui mais de 1 cashback"]
    ])
  end

  it "processes when updating cashback with external_id in file and order has more than one cashback with external_id in file" do
    user = create(:user, :with_cashback)
    create(:wallet, :inflow, kind: user.business.cashback_wallet_destination)
    create(:wallet, business: user.business, kind: user.business.cashback_wallet_destination)
    create(:wallet, user:, kind: user.business.cashback_wallet_destination)
    order = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    create(:cashback_record, :pending, order:, user:, external_id: 543)
    create(:cashback_record, :pending, order:, user:, external_id: 321)

    import_file = create_import_file(rows: [
      ["123", "LC987", "approved", 212.88]
    ])

    expect { process(import_file) }.to change(CashbackRecord, :count).by(1)
    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order.id, "pending", "543"],
      [order.id, "pending", "321"],
      [order.id, "approved", "123"]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(1)
    expect(import_file.amount_rows_processed_successfully).to eq(1)
    expect(import_file.amount_rows_with_errors).to eq(0)
  end

  it "processes generating only header error when headers are invalid" do
    file = Tempfile.new(["test", ".csv"], "tmp")
    CSV.open(file, "wb", headers: true, col_sep: ";") do |csv|
      csv << %w[external_id order_number transaction_status wrong_header]
      csv << ["123", "LC987", "pending", 212.88]
    end

    expect { create(:import_file, file:, kind: "cashback") }
      .to raise_error(ActiveRecord::RecordInvalid, /Cabeçalho inválido/)

    file.close
  end

  it "processes generating only malformed csv error when csv is malformed" do
    file = Tempfile.new(["test", ".csv"], "tmp")
    file.write('"external_id;order_number;transaction_status;order_amount')

    expect { create(:import_file, file:, kind: "cashback") }
      .to raise_error(CSV::MalformedCSVError)
  end

  it "processes the import file and does not send notification when business has cashback disabled" do
    allow(CashbackRecord::History::NotificationService).to receive(:new).and_return(notifier_stub)
    business = create(:business, cashback: false, cashback_wallet_destination: :cashback)
    user = create(:user, :with_cashback, business:)
    order_1 = create(:order, :online, user:, number: "LC987", redeem_code: "PROMO10")
    order_2 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10")
    order_3 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20")

    import_file = create_import_file(rows: [
      ["123", "LC987", "pending", 212.88],
      [nil, "LC134", "approved", 100.0],
      ["", "LC456", "available_lecupon", 100.0],
      ["321", "LC524", "approved", 50.0],
      ["321", "LC524", "approved", 70.0],
      [nil, "LC182", "approved", 90.0]
    ])

    expect { process(import_file) }.to change(CashbackRecord, :count).by(3)
    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order_1.id, "pending", "123"],
      [order_2.id, "approved", nil],
      [order_3.id, "available", nil]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(6)
    expect(import_file.amount_rows_processed_successfully).to eq(3)
    expect(import_file.amount_rows_with_errors).to eq(3)
    errors = import_file.import_file_errors.map { [_1.line, _1.details] }
    expect(errors).to match_array([
      [5, "Order Order não encontrada"],
      [6, "Order Order não encontrada"],
      [7, "Order Order não encontrada"]
    ])

    expect(notifier_stub).not_to have_received(:call)
  end

  it "processes the import file and transfers available cashbacks to wallet" do
    allow(CashbackRecord::History::NotificationService).to receive(:new).and_return(notifier_stub)

    inflow_wallet = create(:wallet, :cashback, :inflow)

    project = create(
      :business,
      cashback: true,
      cashback_wallet_destination: :cashback,
      spread_percent: 0
    )
    project_wallet = create(:wallet, :cashback, business: project)
    user = create(:user, :with_cashback, business: project)
    user_wallet = create(:wallet, :cashback, user:)

    project_2 = create(
      :business,
      cashback: true,
      cashback_wallet_destination: :cashback,
      spread_percent: 0
    )
    project_wallet_2 = create(:wallet, :cashback, business: project_2)
    user_2 = create(:user, :with_cashback, business: project_2)
    user_wallet_2 = create(:wallet, :cashback, user: user_2)

    project_external = create(
      :business,
      cashback: true,
      cashback_wallet_destination: :membership,
      spread_percent: 0
    )
    project_wallet_external = create(:wallet, :cashback, business: project_external)
    user_external = create(:user, :with_cashback, business: project_external)
    user_wallet_external = create(:wallet, :cashback, user: user_external)

    order_1 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10", cashback_type: :percent, cashback_value: 10)
    order_2 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20", cashback_type: :percent, cashback_value: 10)
    order_3 = create(:order, :online, user: user_2, number: "LC152", redeem_code: "LECUPON15", cashback_type: :percent, cashback_value: 10)
    order_external = create(:order, :online, user: user_external, number: "LC987", redeem_code: "LECUPON5", cashback_type: :percent, cashback_value: 10)

    cashback = create(:cashback_record, :approved, order: order_3, user: user_2, external_id: "123")

    import_file = create_import_file(rows: [
      [nil, "LC134", "approved", 100.0],
      ["", "LC456", "available_lecupon", 100.0],
      ["123", "LC152", "available", 532.59],
      ["456", "LC987", "available", 100.0]
    ])

    expect { process(import_file) }.to change(CashbackRecord, :count).by(3)
      .and change { cashback.reload.transaction_status }.to(Enums::CashbackRecordStatus::AVAILABLE)
      .and change { user_wallet.reload.balance }.from(0).to(1000)
      .and not_change { inflow_wallet.reload.balance }
      .and not_change { project_wallet.reload.balance }
      .and change { user_wallet_2.reload.balance }.from(0).to(5326)
      .and not_change { project_wallet_2.reload.balance }
      .and not_change { user_wallet_external.reload.balance }
      .and not_change { project_wallet_external.reload.balance }

    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order_1.id, "approved", nil],
      [order_2.id, "available", nil],
      [order_3.id, "available", "123"],
      [order_external.id, "available_lecupon", "456"]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(4)
    expect(import_file.amount_rows_processed_successfully).to eq(4)
    expect(import_file.amount_rows_with_errors).to eq(0)
  end

  it "processes the import file and does not transfer cashback to wallet when it did not turn available" do
    allow(CashbackRecord::History::NotificationService).to receive(:new).and_return(notifier_stub)

    inflow_wallet = create(:wallet, :cashback, :inflow)

    project = create(
      :business,
      cashback: true,
      cashback_wallet_destination: :cashback,
      spread_percent: 0
    )
    project_wallet = create(:wallet, :cashback, business: project)
    user = create(:user, :with_cashback, business: project)
    user_wallet = create(:wallet, :cashback, user:)

    project_2 = create(
      :business,
      cashback: true,
      cashback_wallet_destination: :cashback,
      spread_percent: 0
    )
    project_wallet_2 = create(:wallet, :cashback, business: project_2)
    user_2 = create(:user, :with_cashback, business: project_2)
    user_wallet_2 = create(:wallet, :cashback, user: user_2)

    order_1 = create(:order, :online, user:, number: "LC134", redeem_code: "LECUPON10", cashback_type: :percent, cashback_value: 10)
    order_2 = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20", cashback_type: :percent, cashback_value: 10)
    order_3 = create(:order, :online, user: user_2, number: "LC152", redeem_code: "LECUPON15", cashback_type: :percent, cashback_value: 10)

    cashback = create(:cashback_record, :available, order: order_3, user: user_2, external_id: "123")

    import_file = create_import_file(rows: [
      [nil, "LC134", "approved", 100.0],
      ["", "LC456", "available_lecupon", 100.0],
      ["123", "LC152", "available", 532.59]
    ])

    expect { process(import_file) }.to change(CashbackRecord, :count).by(2)
      .and not_change { cashback.reload.transaction_status }
      .and change { user_wallet.reload.balance }.from(0).to(1000)
      .and not_change { inflow_wallet.reload.balance }
      .and not_change { project_wallet.reload.balance }
      .and not_change { user_wallet_2.reload.balance }
      .and not_change { project_wallet_2.reload.balance }

    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order_1.id, "approved", nil],
      [order_2.id, "available", nil],
      [order_3.id, "available", "123"]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(3)
    expect(import_file.amount_rows_processed_successfully).to eq(3)
    expect(import_file.amount_rows_with_errors).to eq(0)
  end

  it "processes the import file and does not transfer cashback to wallet when business has cashback disabled" do
    allow(CashbackRecord::History::NotificationService).to receive(:new).and_return(notifier_stub)

    inflow_wallet = create(:wallet, :cashback, :inflow)

    project = create(
      :business,
      cashback: false,
      cashback_wallet_destination: :cashback,
      spread_percent: 0
    )
    project_wallet = create(:wallet, :cashback, business: project)
    user = create(:user, :with_cashback, business: project)
    user_wallet = create(:wallet, :cashback, user:)

    order = create(:order, :online, user:, number: "LC456", redeem_code: "LECUPON20", cashback_type: :percent, cashback_value: 10)

    import_file = create_import_file(rows: [
      ["", "LC456", "available_lecupon", 100.0]
    ])

    expect { process(import_file) }.to change(CashbackRecord, :count).by(1)
      .and not_change { user_wallet.reload.balance }
      .and not_change { inflow_wallet.reload.balance }
      .and not_change { project_wallet.reload.balance }

    expect(CashbackRecord.pluck(:order_id, :transaction_status, :external_id)).to match_array([
      [order.id, "available", nil]
    ])

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_data_rows).to eq(1)
    expect(import_file.amount_rows_processed_successfully).to eq(1)
    expect(import_file.amount_rows_with_errors).to eq(0)
  end

  def create_import_file(rows:)
    file = Tempfile.new(["test", ".csv"], "tmp")
    CSV.open(file, "wb", headers: true, col_sep: ";") do |csv|
      csv << %w[external_id order_number transaction_status order_amount]
      rows.each { |row| csv << row }
    end
    import_file = create(:import_file, file:, kind: "cashback")
    file.close
    import_file
  end

  def process(import_file)
    ImportFile::ProcessWorker.new.perform(
      FFaker::Internet.email,
      DateTime.current.to_s,
      import_file.id
    )
  end
end
