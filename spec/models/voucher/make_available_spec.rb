# frozen_string_literal: true

require "rails_helper"

RSpec.describe Voucher do
  let(:user) { create(:user) }
  let!(:coupon) { create(:cupon, :with_branch, :coupon_code) }
  let!(:bucket) { create(:voucher_bucket) }
  let!(:redeemed_voucher) do
    voucher = create(:voucher, bucket:)
    create(:order, cupon: coupon, user:, voucher:)
    voucher
  end

  describe "#make_available" do
    it "makes the voucher available" do
      redeemed_voucher.make_available

      expect(redeemed_voucher.user_id).to be_nil
      expect(redeemed_voucher.cupon_id).to be_nil
      expect(redeemed_voucher.cpf).to be_nil
      expect(redeemed_voucher.redeemed_at).to be_nil
      expect(redeemed_voucher.used_at).to be_nil
      expect(redeemed_voucher.queried_at).to be_nil
      expect(redeemed_voucher.expired_at).to eq(redeemed_voucher.expired_at)
    end

    context "when passing the expired at param" do
      let(:expired_at) { 15.days.from_now }

      it "makes the voucher available, setting the expired at" do
        redeemed_voucher.make_available(expired_at:)

        expect(redeemed_voucher.user_id).to be_nil
        expect(redeemed_voucher.cupon_id).to be_nil
        expect(redeemed_voucher.cpf).to be_nil
        expect(redeemed_voucher.redeemed_at).to be_nil
        expect(redeemed_voucher.used_at).to be_nil
        expect(redeemed_voucher.queried_at).to be_nil
        expect(redeemed_voucher.expired_at.to_s).to eq(expired_at.to_s)
      end
    end
  end
end
