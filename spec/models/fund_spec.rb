# frozen_string_literal: true

require "rails_helper"

RSpec.describe Fund, type: :model do
  include ActiveSupport::Testing::TimeHelpers

  context "with points coming from purchase" do
    let!(:business) { create(:business, fair_value: 0.0125, currency: "points") }
    let!(:user) { create(:user, business:) }

    describe "on fund creation" do
      describe "and cashback percent" do
        let!(:order) do
          promotion = create(:promotion, cashback_type: :percent, cashback_value: 3.24)
          cupon = create(:cupon, promotion:)
          create(:order, cupon:, user:)
        end

        before { business.update!(fair_value: 0.02) }

        it "creates fund" do
          expect { create(:fund, :points, :pending, user:, order:, order_amount: 12998) }
            .to change(Fund, :count).by(1)

          fund = Fund.order(:created_at).last
          expect(fund.order_amount).to eq 12998
          expect(fund.fair_value).to eq 0.0125
          expect(fund.original_amount).to eq 33691
          expect(fund.amount).to eq 325
          expect(fund.accrual_parity_value).to eq 2.5
          expect(fund.monetary_amount).to eq 421
        end
      end

      describe "and cashback fixed value" do
        let!(:order) do
          promotion = create(:promotion, cashback_type: :fixed, cashback_value: 4.21)
          cupon = create(:cupon, promotion:)
          create(:order, cupon:, user:)
        end

        before { business.update!(fair_value: 0.02) }

        it "creates fund" do
          expect { create(:fund, :points, :pending, user:, order:, order_amount: 12998) }
            .to change(Fund, :count).by(1)

          fund = Fund.order(:created_at).last
          expect(fund.order_amount).to eq 12998
          expect(fund.fair_value).to eq 0.0125
          expect(fund.original_amount).to eq 33680
          expect(fund.amount).to eq 337
          expect(fund.accrual_parity_value).to be_nil
          expect(fund.monetary_amount).to eq 421
        end
      end

      describe "and user is from sub business" do
        let!(:sub_business) { create(:business, main_business: business) }
        let!(:user) { create(:user, business: sub_business, main_business: business) }

        let!(:order) do
          promotion = create(:promotion, cashback_type: :percent, cashback_value: 3.24)
          cupon = create(:cupon, promotion:)
          create(:order, cupon:, user:)
        end

        it "creates fund with config from main business" do
          expect { create(:fund, :points, :pending, user:, order:, order_amount: 12998) }
            .to change(Fund, :count).by(1)

          fund = Fund.order(:created_at).last
          expect(fund.order_amount).to eq 12998
          expect(fund.fair_value).to eq 0.0125
          expect(fund.original_amount).to eq 33691
          expect(fund.amount).to eq 325
          expect(fund.accrual_parity_value).to eq 2.5
          expect(fund.monetary_amount).to eq 421
        end
      end
    end

    describe "on fund update" do
      let!(:order) do
        promotion = create(:promotion, cashback_type: :percent, cashback_value: 3.24)
        cupon = create(:cupon, promotion:)
        create(:order, cupon:, user:)
      end

      context "when updatable" do
        let!(:fund) { create(:fund, :points, :pending, user:, order:, order_amount: 12998) }
        let(:params) do
          {
            order_amount: 12241,
            status: "approved",
            metadata: {any: "changed metadata"}
          }
        end

        it "updates fund" do
          expect { fund.update!(params) }
            .to change(fund, :status).to("approved")
            .and change(fund, :metadata).to("any" => "changed metadata")
            .and change(fund, :order_amount).to(12241)
            .and change(fund, :amount).to(307)
            .and change(fund, :original_amount).to(31729)
            .and change(fund, :monetary_amount).to(397)
        end
      end

      context "when not updatable" do
        let!(:fund) { create(:fund, :points, :available, user:, order:, order_amount: 12998) }
        let(:params) do
          {
            order_amount: 12241
          }
        end

        it "does not update" do
          expect { fund.update!(params) }.to raise_error(ActiveRecord::RecordInvalid)

          fund.reload
          expect(fund.order_amount).to eq 12998
          expect(fund.fair_value).to eq 0.0125
          expect(fund.original_amount).to eq 33691
          expect(fund.amount).to eq 325
          expect(fund.accrual_parity_value).to eq 2.5
          expect(fund.monetary_amount).to eq 421
          expect(fund.errors).to be_present
        end
      end
    end
  end

  context "with points coming from other sources" do
    let!(:business) { create(:business, fair_value: 0.0125, currency: "points") }
    let!(:user) { create(:user, business:) }

    it "creates fund" do
      expect { create(:fund, :points, :pending, user:, amount: 1525) }
        .to change(Fund, :count).by(1)

      fund = Fund.order(:created_at).last
      expect(fund.fair_value).to eq 0.0125
      expect(fund.amount).to eq 1525
      expect(fund.original_amount).to eq 152500
      expect(fund.accrual_parity_value).to be_nil
      expect(fund.order_amount).to be_nil
      expect(fund.monetary_amount).to eq 1906
    end
  end

  context "with BRL coming from purchase" do
    let!(:business) { create(:business, spread_percent: 0) }
    let!(:user) { create(:user, business:) }

    describe "on fund creation" do
      describe "and cashback percent" do
        let!(:order) do
          promotion = create(:promotion, cashback_type: :percent, cashback_value: 3.24)
          cupon = create(:cupon, promotion:)
          create(:order, cupon:, user:)
        end

        it "creates fund" do
          expect { create(:fund, :BRL, :pending, user:, order:, order_amount: 12993) }
            .to change(Fund, :count).by(1)

          fund = Fund.order(:created_at).last
          expect(fund.order_amount).to eq 12993
          expect(fund.fair_value).to be_nil
          expect(fund.accrual_parity_value).to be_nil
          expect(fund.amount).to eq 421
          expect(fund.original_amount).to eq 421
          expect(fund.monetary_amount).to eq 421
        end
      end

      describe "and cashback fixed value" do
        let!(:order) do
          promotion = create(:promotion, cashback_type: :fixed, cashback_value: 5)
          cupon = create(:cupon, promotion:)
          create(:order, cupon:, user:)
        end

        it "creates fund" do
          expect { create(:fund, :BRL, :pending, user:, order:, order_amount: 12993) }
            .to change(Fund, :count).by(1)

          fund = Fund.order(:created_at).last
          expect(fund.order_amount).to eq 12993
          expect(fund.fair_value).to be_nil
          expect(fund.accrual_parity_value).to be_nil
          expect(fund.amount).to eq 500
          expect(fund.original_amount).to eq 500
          expect(fund.monetary_amount).to eq 500
        end
      end
    end

    describe "on fund update" do
      let!(:order) do
        promotion = create(:promotion, cashback_type: :percent, cashback_value: 3.24)
        cupon = create(:cupon, promotion:)
        create(:order, cupon:, user:)
      end
      let!(:fund) { create(:fund, :BRL, :pending, user:, order:, order_amount: 12993) }
      let(:params) do
        {
          order_amount: 12241,
          status: "approved",
          metadata: {any: "changed metadata"}
        }
      end

      it "updates fund" do
        expect { fund.update!(params) }
          .to change(fund, :status).to("approved")
          .and change(fund, :metadata).to("any" => "changed metadata")
          .and change(fund, :order_amount).to(12241)
          .and change(fund, :amount).to(397)
          .and change(fund, :original_amount).to(397)
          .and change(fund, :monetary_amount).to(397)
      end
    end
  end

  describe "status updated event" do
    let!(:business) { create(:business, fair_value: 0.01, currency: "points") }
    let!(:user) { create(:user, business:) }

    context "when status is updated" do
      let!(:fund) { create(:fund, :points, :pending, user:, amount: FFaker::Number.number(digits: 3)) }

      it "creates event" do
        expect { fund.approved! }.to change(fund.activities.status_updated.approved, :count).by(1)
      end
    end

    context "when status is not updated" do
      let!(:fund) { create(:fund, :points, :approved, user:, amount: FFaker::Number.number(digits: 3)) }

      it "does not create event" do
        expect { fund.touch }.not_to change(FundActivity, :count)
      end
    end
  end

  describe "status validation" do
    let(:business) { create(:business, fair_value: 0.01, currency: "points") }
    let(:user) { create(:user, business:) }
    let(:fund) { create(:fund, :points, user:, amount: FFaker::Number.number(digits: 3), status: current_status) }
    let(:all_statuses) { FundStatus.all }

    before { fund.status = new_status }

    context "from pending to canceled/approved/available" do
      let(:current_status) { "pending" }
      let(:new_status) { %w[canceled approved available].sample }

      it { expect(fund).to be_valid }
    end

    context "from pending to any status except canceled/approved/available" do
      let(:current_status) { "pending" }
      let(:new_status) { (all_statuses - %w[pending canceled approved available]).sample }

      it { expect(fund).to be_invalid }
    end

    context "from approved to canceled/pending/available" do
      let(:current_status) { "approved" }
      let(:new_status) { %w[canceled pending available].sample }

      it { expect(fund).to be_valid }
    end

    context "from approved to any status except canceled/pending/available" do
      let(:current_status) { "approved" }
      let(:new_status) { (all_statuses - %w[approved canceled pending available]).sample }

      it { expect(fund).to be_invalid }
    end

    context "from available to any status" do
      let(:current_status) { "available" }
      let(:new_status) { (all_statuses - %w[available]).sample }

      it { expect(fund).to be_invalid }
    end

    context "from canceled to any status" do
      let(:current_status) { "canceled" }
      let(:new_status) { (all_statuses - %w[canceled]).sample }

      it { expect(fund).to be_invalid }
    end
  end

  describe ".credit_due_points!" do
    let(:business) { create(:business, :with_cashback, fair_value: 0.0125, currency: "points") }
    let(:user) { create(:user, business:) }

    let(:inflow_wallet) { create(:wallet, :points, :inflow) }
    let(:business_wallet) { create(:wallet, :points, business:) }
    let(:user_wallet) { create(:wallet, :points, user:) }

    let(:fund_not_due_today_not_credited) do
      travel_to 2.days.ago do
        create(:fund, :points, :approved, user:, credited: false, credits_at: DateTime.current, amount: 1500)
      end
    end
    let(:fund_not_due_today_credited) do
      travel_to 2.days.ago do
        create(:fund, :points, :approved, user:, credited: true, credits_at: DateTime.current, amount: 2500)
      end
    end
    let(:fund_due_today_not_eligible) do
      travel_to 1.second.ago do
        create(:fund, :points, :available, user:, credited: false, credits_at: DateTime.current, amount: 4500)
      end
    end
    let(:fund_eligible_but_expired) do
      travel_to 1.second.ago do
        create(
          :fund,
          :points,
          :pending,
          user:,
          credited: false,
          credits_at: DateTime.current,
          amount: 3000,
          expires_at: 1.second.ago
        )
      end
    end
    let(:funds_due_today) do
      travel_to 1.second.ago do
        [
          create(:fund, :points, :pending, user:, credited: false, credits_at: DateTime.current, amount: 2000),
          create(
            :fund,
            :points,
            :approved,
            user:,
            credited: false,
            credits_at: DateTime.current,
            amount: 3500,
            expires_at: 1.day.from_now
          )
        ]
      end
    end

    it "credits user wallet of points in due date" do
      expect do
        described_class.credit_due_points!

        [
          inflow_wallet, business_wallet, user_wallet,
          fund_not_due_today_not_credited, fund_due_today_not_eligible, fund_not_due_today_credited,
          fund_eligible_but_expired,
          *funds_due_today
        ].each(&:reload)
      end.to not_change(inflow_wallet, :balance)
        .and not_change(business_wallet, :balance)
        .and change(user_wallet, :balance).by(7000)
        .and change { funds_due_today.map(&:credited) }.to(all(eq(true)))
        .and change { funds_due_today.map(&:status) }.to(all(eq(FundStatus::AVAILABLE)))
        .and change { funds_due_today.map { |fund| fund.expires_at&.to_date } }.to(all(eq(365.days.from_now.to_date)))
        .and change(fund_not_due_today_not_credited, :credited).to(true)
        .and change(fund_not_due_today_not_credited, :status).to(FundStatus::AVAILABLE)
        .and not_change(fund_due_today_not_eligible, :credited)
        .and not_change(fund_due_today_not_eligible, :status)
        .and not_change(fund_not_due_today_credited, :credited)
        .and not_change(fund_not_due_today_credited, :status)
        .and not_change(fund_eligible_but_expired, :credited)
        .and not_change(fund_eligible_but_expired, :status)
    end
  end

  describe "#credit_point!" do
    let(:business) { create(:business, :with_cashback, fair_value: 0.0125, currency: "points") }
    let(:user) { create(:user, business:) }

    let(:inflow_wallet) { create(:wallet, :points, :inflow) }
    let(:business_wallet) { create(:wallet, :points, business:) }
    let(:user_wallet) { create(:wallet, :points, user:) }

    let(:fund) do
      travel_to 1.second.ago do
        create(
          :fund,
          :points,
          :pending,
          user:,
          credited: false,
          credits_at: DateTime.current,
          amount: 2000,
          expires_at: 1.week.from_now
        )
      end
    end

    it "credits user wallet of points in due date" do
      expect do
        fund.credit_point!

        [inflow_wallet, business_wallet, user_wallet].each(&:reload)
      end.to not_change(inflow_wallet, :balance)
        .and not_change(business_wallet, :balance)
        .and change(user_wallet, :balance).by(2000)
        .and change(fund, :credited).to(true)
        .and change(fund, :status).to(FundStatus::AVAILABLE)
        .and change { fund.expires_at.to_date }.to(365.days.from_now.to_date)
    end
  end

  describe ".expire_points!" do
    let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.03) }
    let(:user) { create(:user, business:) }
    let(:fund) { create(:fund, :points, :available, user:, credited: true, amount: 1500, expires_at: 1.day.ago) }
    let(:fund_no_remaining_amount) do
      create(:fund, :points, :available, user:, credited: true, amount: 3000, expires_at: 1.day.ago)
    end
    let(:fund_not_expired) do
      create(:fund, :points, :available, user:, credited: true, amount: 2000, expires_at: 1.day.from_now)
    end
    let(:fund_not_credited) do
      create(:fund, :points, :available, user:, credited: false, amount: 2500, expires_at: 1.day.from_now)
    end

    let(:user_wallet) { create(:wallet, :points, user:, balance: 3500) }
    let(:outflow_wallet) { create(:wallet, :points, :outflow) }
    let(:business_wallet) { create(:wallet, :points, business:) }

    let(:amount) { 1000 }

    before do
      fund.update_columns(remaining_amount: 1000)
      fund_no_remaining_amount.update_columns(remaining_amount: 0)
    end

    it "expires points" do
      expect { described_class.expire_points! }
        .to change { fund.reload.remaining_amount }.to(0)
        .and not_change { fund_not_expired.reload.remaining_amount }
        .and not_change { fund_not_credited.reload.remaining_amount }
        .and change(FundDebit.expiration, :count).by(1)
        .and change { user_wallet.reload.balance }.by(-amount)
        .and change(user_wallet.entries.debit.where(amount:, kind: Wallet::Entry::Kind::POINT_EXPIRED), :count).by(1)
        .and change { outflow_wallet.reload.balance }.by(amount)
        .and change(outflow_wallet.entries.credit.where(amount:, kind: Wallet::Entry::Kind::POINT_EXPIRED), :count).by(1)
        .and not_change { business_wallet.reload.balance }
        .and not_change(business_wallet.entries, :count)
    end
  end

  describe "#deduct!" do
    let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.03) }
    let(:user) { create(:user, business:) }
    let(:fund) { create(:fund, :points, :available, user:, credited: true, amount: 1500, expires_at: 1.day.ago) }

    let(:amount_to_deduct) { 1500 }
    let(:debit_kind) { :expiration }

    it "deducts fund" do
      expect { fund.deduct!(amount_to_deduct:, debit_kind:) }
        .to change(fund, :remaining_amount).to(0)
        .and change(fund.debits.where(amount: amount_to_deduct, kind: debit_kind), :count).by(1)
    end
  end

  describe ".redeemable_points" do
    subject(:redeemable_points) { described_class.redeemable_points(user_id:, amount:).ids }

    let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.03) }
    let(:user) { create(:user, business:) }
    let(:user_id) { user.id }
    let!(:non_redeemable_fund) do
      create(:fund, :points, :approved, user:, credited: false, amount: 1000, expires_at: 1.day.from_now)
    end
    let!(:fund_another_user) do
      another_user = create(:user, business:)
      create(:fund, :points, :available, user: another_user, credited: true, amount: 1500, expires_at: 1.day.from_now)
    end
    let!(:funds) do
      expires_at = 2.days.from_now
      [
        create(:fund, :points, :available, user:, credited: true, amount: 1500, expires_at: 1.hour.from_now),
        create(:fund, :points, :available, user:, credited: true, amount: 1000, expires_at: 1.day.from_now),
        create(:fund, :points, :available, user:, credited: true, amount: 1000, expires_at:),
        create(:fund, :points, :available, user:, credited: true, amount: 1000, expires_at:),
        create(:fund, :points, :available, user:, credited: true, amount: 2500, expires_at: 1.day.ago),
        create(:fund, :points, :pending, user:, credited: false, amount: 3000, expires_at: 1.day.from_now)
      ]
    end

    describe "amount 999" do
      let(:amount) { 999 }

      it { is_expected.to eq([funds[0].id]) }
    end

    describe "amount 1000" do
      let(:amount) { 1000 }

      it { is_expected.to eq([funds[0].id]) }
    end

    describe "amount 1499" do
      let(:amount) { 1499 }

      it { is_expected.to eq([funds[0].id]) }
    end

    describe "amount 1500" do
      let(:amount) { 1500 }

      it { is_expected.to eq([funds[0].id]) }
    end

    describe "amount 1501" do
      let(:amount) { 1501 }

      it { is_expected.to eq([funds[0].id, funds[1].id]) }
    end

    describe "amount 2499" do
      let(:amount) { 2499 }

      it { is_expected.to eq([funds[0].id, funds[1].id]) }
    end

    describe "amount 2500" do
      let(:amount) { 2500 }

      it { is_expected.to eq([funds[0].id, funds[1].id]) }
    end

    describe "amount 2501" do
      let(:amount) { 2501 }

      it { is_expected.to eq([funds[0].id, funds[1].id, funds[2].id]) }
    end

    describe "amount 4500" do
      let(:amount) { 4500 }

      it { is_expected.to eq([funds[0].id, funds[1].id, funds[2].id, funds[3].id]) }
    end

    describe "amount 4501" do
      let(:amount) { 4501 }

      it { expect { redeemable_points }.to raise_error(Fund::NoRedeemablePoint) }
    end
  end

  describe "#processable?" do
    let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.03) }
    let(:user) { create(:user, business:) }

    let(:fund_pending) { create(:fund, :points, :pending, user:, credited: false, amount: 1000) }
    let(:fund_approved) { create(:fund, :points, :approved, user:, credited: false, amount: 1000) }
    let(:fund_credited) { create(:fund, :points, :available, user:, credited: true, amount: 1000) }

    it { expect(fund_pending).to be_processable }
    it { expect(fund_approved).to be_processable }
    it { expect(fund_credited).not_to be_processable }
  end
end
