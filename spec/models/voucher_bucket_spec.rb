# frozen_string_literal: true

require "rails_helper"

RSpec.describe VoucherBucket do
  describe "#issue" do
    let(:user) { create(:user) }

    context "givem a promotion available to provide a voucher" do
      let(:voucher_bucket) { create(:voucher_bucket) }
      let(:promotion) { create(:promotion, :coupon_code, :available, voucher_bucket:, dynamic_voucher: false) }
      let(:coupon) { create(:cupon, :fixed, promotion:) }
      let(:create_redeemed_voucher) do
        voucher = create(:voucher, bucket: voucher_bucket)
        create(:order, cupon: coupon, user:, voucher:)
      end
      let(:available_voucher) do
        create(:voucher, bucket: voucher_bucket)
      end

      it "returns available voucher" do
        create_redeemed_voucher
        voucher = available_voucher

        expect do
          available_voucher = voucher_bucket.issue(resource: promotion)

          expect(available_voucher).to eq(voucher)
        end.to not_change(Voucher, :count)
          .and not_change(Order, :count)
      end
    end

    context "givem a promotion with a dynamic_coupon_code voucher" do
      let(:voucher_bucket) { create(:voucher_bucket) }
      let(:create_dynamic_promotion) { create(:promotion, :dynamic_coupon_code, :available, dynamic_voucher: true, voucher_bucket:) }

      it "create andreturns available vocuher" do
        promotion = create_dynamic_promotion

        expect do
          voucher_bucket.issue(resource: promotion)
        end.to change(Voucher, :count).by(1)
          .and change(voucher_bucket.vouchers, :count).from(0).to(1)
      end
    end

    context "givem a gift card with a available voucher" do
      let(:voucher_bucket) { create(:voucher_bucket) }
      let(:gift_card) { create(:giftcard, :with_branches, available: true, voucher_bucket:) }
      let(:create_redeemed_voucher) do
        voucher = create(:voucher, bucket: voucher_bucket)
        create(:order, giftcard: gift_card, user:, voucher:)
      end
      let(:available_voucher) do
        create(:voucher, bucket: voucher_bucket)
      end

      it "returns available voucher" do
        create_redeemed_voucher
        voucher = available_voucher

        expect do
          available_voucher = voucher_bucket.issue(resource: gift_card)

          expect(available_voucher).to eq(voucher)
        end.to not_change(Voucher, :count)
          .and not_change(Order, :count)
      end
    end

    context "when no available voucher exists" do
      let(:voucher_bucket) { create(:voucher_bucket) }
      let(:promotion) { create(:promotion, :coupon_code, :available, voucher_bucket:, dynamic_voucher: true) }

      it "raises NotAvailableError" do
        expect do
          voucher_bucket.issue(resource: promotion)
        end.to raise_error(Voucher::NotAvailableError, "Voucher não disponível para resgate")
      end
    end
  end
end
