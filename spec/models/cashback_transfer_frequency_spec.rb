# frozen_string_literal: true

require "rails_helper"

RSpec.describe CashbackTransferFrequency, type: :model do
  describe "#next_date" do
    describe "every week on thursday" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::WEEKLY,
          interval: 1,
          day_of_week: Date::DAYNAMES.index("Thursday"))
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 4, 7)) }
    end

    describe "every week on friday" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::WEEKLY,
          interval: 1,
          day_of_week: Date::DAYNAMES.index("Friday"))
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 4, 8)) }
    end

    describe "every week on saturday" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::WEEKLY,
          interval: 1,
          day_of_week: Date::DAYNAMES.index("Saturday"))
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 4, 2)) }
    end

    describe "every month on 1st" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::MONTHLY,
          interval: 1,
          day_of_month: 1)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 5, 1)) }
    end

    describe "every month on 2nd" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::MONTHLY,
          interval: 1,
          day_of_month: 2)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 4, 2)) }
    end

    describe "every month on 30th, when base month is January and year is leap" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::MONTHLY,
          interval: 1,
          day_of_month: 30)
      end

      it { expect(frequency.next_date(base_date: Date.new(2024, 1, 30))).to eq(Date.new(2024, 2, 29)) }
    end

    describe "every month on 30th, when base month is January and year is not leap" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::MONTHLY,
          interval: 1,
          day_of_month: 30)
      end

      it { expect(frequency.next_date(base_date: Date.new(2023, 1, 30))).to eq(Date.new(2023, 2, 28)) }
    end

    describe "every month on 30th, when base month is February and year is leap" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::MONTHLY,
          interval: 1,
          day_of_month: 30)
      end

      it { expect(frequency.next_date(base_date: Date.new(2024, 2, 1))).to eq(Date.new(2024, 2, 29)) }
    end

    describe "every month on 30th, when base month is February and year is not leap" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::MONTHLY,
          interval: 1,
          day_of_month: 30)
      end

      it { expect(frequency.next_date(base_date: Date.new(2023, 2, 1))).to eq(Date.new(2023, 2, 28)) }
    end

    describe "every month on 31st" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::MONTHLY,
          interval: 1,
          day_of_month: 31)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 4, 30)) }
    end

    describe "every year on February 31st" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::YEARLY,
          interval: 1,
          day_of_month: 31,
          month_of_year: 2)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2023, 2, 28)) }
    end

    describe "every year on April 2nd" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::YEARLY,
          interval: 1,
          day_of_month: 2,
          month_of_year: 4)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 4, 2)) }
    end

    describe "every year on December 1st" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::YEARLY,
          interval: 1,
          day_of_month: 1,
          month_of_year: 12)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 12, 1)) }
    end

    describe "every year on January 1st" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::YEARLY,
          interval: 1,
          day_of_month: 1,
          month_of_year: 1)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 1, 1))).to eq(Date.new(2023, 1, 1)) }

      it { expect(frequency.next_date(base_date: Date.new(2022, 1, 2))).to eq(Date.new(2023, 1, 1)) }

      it { expect(frequency.next_date(base_date: Date.new(2022, 12, 31))).to eq(Date.new(2023, 1, 1)) }
    end

    describe "every year on February 1st" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::YEARLY,
          interval: 1,
          day_of_month: 1,
          month_of_year: 2)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 2, 1))).to eq(Date.new(2023, 2, 1)) }

      it { expect(frequency.next_date(base_date: Date.new(2022, 2, 2))).to eq(Date.new(2023, 2, 1)) }

      it { expect(frequency.next_date(base_date: Date.new(2022, 1, 31))).to eq(Date.new(2022, 2, 1)) }
    end

    describe "every year on March 1st" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::YEARLY,
          interval: 1,
          day_of_month: 1,
          month_of_year: 3)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 3, 1))).to eq(Date.new(2023, 3, 1)) }

      it { expect(frequency.next_date(base_date: Date.new(2022, 3, 2))).to eq(Date.new(2023, 3, 1)) }

      it { expect(frequency.next_date(base_date: Date.new(2022, 2, 28))).to eq(Date.new(2022, 3, 1)) }
    end

    describe "every 2 weeks on thursday" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::WEEKLY,
          interval: 2,
          day_of_week: Date::DAYNAMES.index("Thursday"))
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 4, 14)) }
    end

    describe "every 2 weeks on friday" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::WEEKLY,
          interval: 2,
          day_of_week: Date::DAYNAMES.index("Friday"))
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 4, 15)) }
    end

    describe "every 2 weeks on saturday" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::WEEKLY,
          interval: 2,
          day_of_week: Date::DAYNAMES.index("Saturday"))
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 4, 9)) }
    end

    describe "every 2 months on 1st" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::MONTHLY,
          interval: 2,
          day_of_month: 1)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 6, 1)) }
    end

    describe "every 2 months on 2nd" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::MONTHLY,
          interval: 2,
          day_of_month: 2)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 5, 2)) }
    end

    describe "every 2 months on 31st" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::MONTHLY,
          interval: 2,
          day_of_month: 31)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2022, 5, 31)) }
    end

    describe "every 2 years on February 31st" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::YEARLY,
          interval: 2,
          day_of_month: 31,
          month_of_year: 2)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2024, 2, 29)) }
    end

    describe "every 2 years on April 2nd" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::YEARLY,
          interval: 2,
          day_of_month: 2,
          month_of_year: 4)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2023, 4, 2)) }
    end

    describe "every 2 years on December 1st" do
      let(:frequency) do
        build(:cashback_transfer_frequency,
          recurring_type: Enums::CashbackTransferFrequency::YEARLY,
          interval: 2,
          day_of_month: 1,
          month_of_year: 12)
      end

      it { expect(frequency.next_date(base_date: Date.new(2022, 4, 1))).to eq(Date.new(2023, 12, 1)) }
    end
  end

  describe "#on_date?" do
    it "returns whether is on date when every 1 week" do
      frequency = build(
        :cashback_transfer_frequency,
        :weekly,
        start_date: Date.new(2022, 4, 7),
        interval: 1,
        day_of_week: 4
      )

      expect(frequency.on_date?(Date.new(2022, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2022, 4, 13))).to be false
      expect(frequency.on_date?(Date.new(2022, 4, 14))).to be true
      expect(frequency.on_date?(Date.new(2031, 1, 2))).to be true
      expect(frequency.on_date?(Date.new(2031, 1, 3))).to be false
    end

    it "returns whether is on date when every 2 weeks" do
      frequency = build(
        :cashback_transfer_frequency,
        :weekly,
        start_date: Date.new(2022, 4, 7),
        interval: 2,
        day_of_week: 4
      )

      expect(frequency.on_date?(Date.new(2022, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2022, 4, 14))).to be false
      expect(frequency.on_date?(Date.new(2022, 4, 21))).to be true
      expect(frequency.on_date?(Date.new(2022, 4, 28))).to be false
      expect(frequency.on_date?(Date.new(2022, 5, 5))).to be true
      expect(frequency.on_date?(Date.new(2022, 5, 14))).to be false
      expect(frequency.on_date?(Date.new(2022, 5, 19))).to be true
      expect(frequency.on_date?(Date.new(2031, 1, 2))).to be true
      expect(frequency.on_date?(Date.new(2031, 1, 3))).to be false
    end

    it "returns whether is on date when every 3 weeks" do
      frequency = build(
        :cashback_transfer_frequency,
        :weekly,
        start_date: Date.new(2022, 4, 7),
        interval: 3,
        day_of_week: 4
      )

      expect(frequency.on_date?(Date.new(2022, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2022, 4, 14))).to be false
      expect(frequency.on_date?(Date.new(2022, 4, 21))).to be false
      expect(frequency.on_date?(Date.new(2022, 4, 28))).to be true
      expect(frequency.on_date?(Date.new(2022, 5, 5))).to be false
      expect(frequency.on_date?(Date.new(2022, 5, 14))).to be false
      expect(frequency.on_date?(Date.new(2022, 5, 19))).to be true
      expect(frequency.on_date?(Date.new(2031, 1, 2))).to be true
      expect(frequency.on_date?(Date.new(2031, 1, 3))).to be false
    end

    it "returns whether is on date when every 1 month" do
      frequency = build(
        :cashback_transfer_frequency,
        :monthly,
        start_date: Date.new(2022, 4, 7),
        interval: 1,
        day_of_month: 7
      )

      expect(frequency.on_date?(Date.new(2022, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2022, 4, 8))).to be false
      expect(frequency.on_date?(Date.new(2022, 5, 6))).to be false
      expect(frequency.on_date?(Date.new(2022, 5, 7))).to be true
      expect(frequency.on_date?(Date.new(2022, 5, 8))).to be false
      expect(frequency.on_date?(Date.new(2022, 6, 6))).to be false
      expect(frequency.on_date?(Date.new(2022, 6, 7))).to be true
      expect(frequency.on_date?(Date.new(2022, 6, 8))).to be false
      expect(frequency.on_date?(Date.new(2031, 11, 7))).to be true
      expect(frequency.on_date?(Date.new(2031, 11, 8))).to be false
    end

    it "returns whether is on date when every 2 months" do
      frequency = build(
        :cashback_transfer_frequency,
        :monthly,
        start_date: Date.new(2022, 4, 7),
        interval: 2,
        day_of_month: 7
      )

      expect(frequency.on_date?(Date.new(2022, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2022, 5, 7))).to be false
      expect(frequency.on_date?(Date.new(2022, 6, 7))).to be true
      expect(frequency.on_date?(Date.new(2022, 7, 7))).to be false
      expect(frequency.on_date?(Date.new(2022, 8, 7))).to be true
      expect(frequency.on_date?(Date.new(2031, 2, 7))).to be true
      expect(frequency.on_date?(Date.new(2031, 2, 8))).to be false
    end

    it "returns whether is on date when every 3 months" do
      frequency = build(
        :cashback_transfer_frequency,
        :monthly,
        start_date: Date.new(2022, 4, 7),
        interval: 3,
        day_of_month: 7
      )

      expect(frequency.on_date?(Date.new(2022, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2022, 5, 7))).to be false
      expect(frequency.on_date?(Date.new(2022, 6, 7))).to be false
      expect(frequency.on_date?(Date.new(2022, 7, 7))).to be true
      expect(frequency.on_date?(Date.new(2022, 8, 7))).to be false
      expect(frequency.on_date?(Date.new(2022, 9, 7))).to be false
      expect(frequency.on_date?(Date.new(2022, 10, 7))).to be true
      expect(frequency.on_date?(Date.new(2022, 11, 7))).to be false
      expect(frequency.on_date?(Date.new(2022, 12, 7))).to be false
      expect(frequency.on_date?(Date.new(2023, 1, 7))).to be true
      expect(frequency.on_date?(Date.new(2031, 1, 7))).to be true
      expect(frequency.on_date?(Date.new(2031, 1, 8))).to be false
    end

    it "returns whether is on date when every 1 year" do
      frequency = build(
        :cashback_transfer_frequency,
        :yearly,
        start_date: Date.new(2022, 4, 7),
        interval: 1,
        day_of_month: 7,
        month_of_year: 4
      )

      expect(frequency.on_date?(Date.new(2022, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2023, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2023, 4, 8))).to be false
      expect(frequency.on_date?(Date.new(2024, 4, 6))).to be false
      expect(frequency.on_date?(Date.new(2024, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2024, 4, 8))).to be false
      expect(frequency.on_date?(Date.new(2031, 4, 6))).to be false
      expect(frequency.on_date?(Date.new(2031, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2031, 4, 8))).to be false
    end

    it "returns whether is on date when every 2 years" do
      frequency = build(
        :cashback_transfer_frequency,
        :yearly,
        start_date: Date.new(2022, 4, 7),
        interval: 2,
        day_of_month: 7,
        month_of_year: 4
      )

      expect(frequency.on_date?(Date.new(2022, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2023, 4, 7))).to be false
      expect(frequency.on_date?(Date.new(2023, 4, 8))).to be false
      expect(frequency.on_date?(Date.new(2024, 4, 6))).to be false
      expect(frequency.on_date?(Date.new(2024, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2024, 4, 8))).to be false
      expect(frequency.on_date?(Date.new(2032, 4, 6))).to be false
      expect(frequency.on_date?(Date.new(2032, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2032, 4, 8))).to be false
    end

    it "returns whether is on date when every 3 years" do
      frequency = build(
        :cashback_transfer_frequency,
        :yearly,
        start_date: Date.new(2022, 4, 7),
        interval: 3,
        day_of_month: 7,
        month_of_year: 4
      )

      expect(frequency.on_date?(Date.new(2022, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2023, 4, 7))).to be false
      expect(frequency.on_date?(Date.new(2023, 4, 8))).to be false
      expect(frequency.on_date?(Date.new(2024, 4, 6))).to be false
      expect(frequency.on_date?(Date.new(2024, 4, 7))).to be false
      expect(frequency.on_date?(Date.new(2024, 4, 8))).to be false
      expect(frequency.on_date?(Date.new(2025, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2025, 4, 8))).to be false
      expect(frequency.on_date?(Date.new(2031, 4, 6))).to be false
      expect(frequency.on_date?(Date.new(2031, 4, 7))).to be true
      expect(frequency.on_date?(Date.new(2031, 4, 8))).to be false
    end
  end
end
