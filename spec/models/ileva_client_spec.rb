require "rails_helper"

RSpec.describe IlevaClient, :vcr do
  let!(:business) { create(:business) }
  let!(:authorized_user_group) { create(:authorized_user_group, business:) }
  let!(:ileva_client) { create(:ileva_client, business:, authorized_user_group:) }

  describe "#import_users" do
    context "given an existing cpf" do
      context "when is active on ileva" do
        let(:existing_cpf) { "***********" }
        let(:mocked_name) { "Any Name 1" }
        let!(:authorized_user) { create(:authorized_user, business:, active: false, cpf: existing_cpf) }

        it "must update and activate user" do
          ileva_client.import_users
          authorized_user.reload

          expect(authorized_user.active).to eq(true)
          expect(authorized_user.name).to eq(Utils::NameNormalizer.call(mocked_name))
          expect(authorized_user.authorized_user_group).to eq(authorized_user_group)
        end
      end

      context "when is inactive on ileva" do
        let(:existing_cpf) { "***********" }
        let(:mocked_name) { "Any Name 2" }
        let!(:authorized_user) { create(:authorized_user, business:, active: true, cpf: existing_cpf) }

        it "must update and inactivate user" do
          ileva_client.import_users
          authorized_user.reload

          expect(authorized_user.active).to eq(false)
          expect(authorized_user.name).to eq(Utils::NameNormalizer.call(mocked_name))
          expect(authorized_user.authorized_user_group).to eq(authorized_user_group)
        end
      end
    end

    context "given a new cpf" do
      context "when is active on ileva" do
        let(:active_cpf) { "***********" }
        it "must create user as active" do
          ileva_client.import_users
          authorized_user = business.authorized_users.find_by_cpf(active_cpf)

          expect(authorized_user).to be_present
          expect(authorized_user.active).to eq(true)
          expect(authorized_user.authorized_user_group).to eq(authorized_user_group)
        end
      end

      context "when is inactive on ileva" do
        let(:inactive_cpf) { "***********" }
        it "must create user as inactive" do
          ileva_client.import_users
          authorized_user = business.authorized_users.find_by_cpf(inactive_cpf)

          expect(authorized_user).to be_present
          expect(authorized_user.active).to eq(false)
          expect(authorized_user.authorized_user_group).to eq(authorized_user_group)
        end
      end
    end
  end
end
