require "rails_helper"
require "sidekiq/testing"

RSpec.describe AuthorizedUser, type: :model do
  describe "#validations" do
    context "when changing authorized_user_group" do
      context "when group belongs to the same business" do
        let!(:authorized_user) { create(:authorized_user, :with_user, :with_authorized_user_group) }
        let!(:another_authorized_user_group) { create(:authorized_user_group, business: another_business) }
        let(:another_business) { create(:business, main_business: authorized_user.business) }

        it "must be invalid" do
          authorized_user.authorized_user_group = another_authorized_user_group
          authorized_user.business = another_business
          authorized_user.active = false
          expect { authorized_user.save! }.not_to raise_error(ActiveRecord::RecordInvalid, /Tag indefinida no projeto/)
        end
      end

      context "when group does not belong to business" do
        let!(:authorized_user) { create(:authorized_user) }
        let!(:authorized_user_group) { create(:authorized_user_group, business: another_business) }
        let(:another_business) { create(:business, main_business: authorized_user.business) }

        it "must be invalid" do
          authorized_user.authorized_user_group = authorized_user_group
          expect(authorized_user).to be_invalid
          expect(authorized_user.errors.first.type).to eq(I18n.t("authorized_user.unrelated_authorized_user_group"))
        end
      end
    end

    context "when trying to activate cpf that is banned" do
      let!(:authorized_user) { create(:authorized_user) }

      before do
        BannedCpf.create(cpf: authorized_user.cpf)
      end

      it "must be invalid" do
        authorized_user.reload
        expect(authorized_user).not_to be_active
        authorized_user.update(active: true)
        expect(authorized_user).to be_invalid
        expect(authorized_user.errors.first.message).to eq(I18n.t("user.sign_up.banned_cpf"))
      end
    end
  end

  describe "#callbacks" do
    describe "#sync_user" do
      context "when transfering authorized_user to another business" do
        let!(:authorized_user) { create(:authorized_user) }
        let!(:user) { create(:user, business: authorized_user.business, authorized_user:) }
        let(:sub_business) { create(:business, main_business: authorized_user.business) }

        it "must transfer user to the same business" do
          expect { authorized_user.update!(business: sub_business) }.to change(user, :business).to(sub_business)
        end
      end
      context "when activating authorized_user" do
        let!(:authorized_user) { create(:authorized_user, active: false) }
        let!(:user) { create(:user, business: authorized_user.business, authorized_user:, active: false) }

        it "must activate user" do
          expect { authorized_user.update!(active: true) }.to change(user, :active).from(false).to(true)
        end
      end

      context "when deactivating authorized_user" do
        let!(:authorized_user) { create(:authorized_user) }
        let!(:user) { create(:user, business: authorized_user.business, authorized_user:) }

        it "must deactivate user" do
          expect { authorized_user.update!(active: false) }.to change(user, :active).from(true).to(false)
        end
      end
    end

    describe "#set_main_business" do
      context "when changing authorized user business" do
        context "when new business is main business" do
          let!(:new_business) { create(:business) }
          let(:authorized_user) { build(:authorized_user, business: new_business, main_business: nil) }

          it "must set main_business to new business" do
            authorized_user.save!
            expect(authorized_user.reload.main_business).to eq(new_business)
          end
        end

        context "when business is sub business" do
          let!(:new_business) { create(:business) }
          let!(:sub_business) { create(:business, main_business: new_business) }
          let(:authorized_user) { build(:authorized_user, business: sub_business, main_business: nil) }

          it "must set main_business to new business' main business" do
            authorized_user.save!
            expect(authorized_user.reload.main_business).to eq(new_business)
          end
        end
      end
    end

    describe "normalize phone" do
      let(:authorized_user) { build(:authorized_user, phone: "(53) 9 91234567") }

      subject { authorized_user.phone }

      before { authorized_user.validate }

      it { is_expected.to eq("***********") }
    end

    describe "email normalization" do
      subject { authorized_user.email }

      before { authorized_user.validate }

      context "when email is present" do
        context "and is valid" do
          let(:authorized_user) { build(:authorized_user, email: " fulano.açaí@gmail.com  ") }

          it { is_expected.to eq("<EMAIL>") }
        end

        context "and is not valid" do
          let(:authorized_user) { build(:authorized_user, email: "<EMAIL>;<EMAIL>") }

          it { is_expected.to be_nil }
        end
      end

      context "when email is blank" do
        let(:authorized_user) { build(:authorized_user, email: "") }

        it { is_expected.to be_nil }
      end
    end

    describe "normalize name" do
      let(:authorized_user) { build(:authorized_user, name: " Nome  teste 😊🧡  teste   ") }

      subject { authorized_user.name }

      before { authorized_user.validate }

      it { is_expected.to eq("Nome Teste Teste") }
    end

    describe "disable telemedicine subscription" do
      let(:business) { create(:business, telemedicine: true) }
      let(:telemedicine_config) { authorized_user.business.telemedicine_config }

      context "when setting telemedicine flag as false" do
        let!(:authorized_user) do
          create(:authorized_user,
            telemedicine: true,
            telemedicine_external_id: "1",
            telemedicine_enabled: true,
            business:)
        end

        it "must disable telemedicine subscription", :vcr do
          expect(Conexa::Client).to receive(:block_user).with(authorized_user.telemedicine_external_id, telemedicine_config.plan)

          expect do
            authorized_user.update!(telemedicine: false)
            TelemedicineBeneficiary::ThirdPartyDeactivateWorker.drain
            authorized_user.reload
          end.to change(authorized_user, :telemedicine_enabled).to(false)
            .and change(authorized_user.telemedicine_activities.where(entry_type: :deactivated), :count).to(1)
        end
      end
    end

    describe "enable telemedicine subscription" do
      let(:business) { create(:business, telemedicine: true) }
      let(:telemedicine_config) { authorized_user.business.telemedicine_config }

      context "when set telemedicine flag as true" do
        context "when user does not have telemedicine external id" do
          let!(:authorized_user) { create(:authorized_user, telemedicine: false, business:) }

          it "must create third party beneficiary" do
            expect do
              authorized_user.update!(telemedicine: true)
              TelemedicineBeneficiary::ThirdPartyUpsertWorker.drain
              authorized_user.reload
            end.to change(authorized_user, :telemedicine_external_id).from(nil)
              .and change(authorized_user, :telemedicine_enabled).from(false).to(true)
          end
        end

        context "when current telemedicine beneficiary is disabled" do
          let!(:authorized_user) do
            create(:authorized_user,
              telemedicine: false,
              telemedicine_enabled: false,
              telemedicine_external_id: "1",
              business:)
          end

          it "must enable telemedicine subscription" do
            expect(Conexa::Client).to receive(:unblock_user).with(authorized_user.telemedicine_external_id, telemedicine_config.plan)

            expect do
              authorized_user.update!(telemedicine: true)
              TelemedicineBeneficiary::ThirdPartyReactivateWorker.drain
              authorized_user.reload
            end.to change(authorized_user, :telemedicine_enabled).from(false).to(true)
              .and change(authorized_user.telemedicine_activities.where(entry_type: :reactivated), :count).to(1)
          end
        end

        context "when current user is active to telemedicine" do
          let!(:authorized_user) { create(:authorized_user, :active_to_telemedicine, telemedicine: false) }

          it "must do nothing" do
            expect do
              authorized_user.update!(telemedicine: true)
            end.to not_change { authorized_user.reload }
              .and not_change(authorized_user.telemedicine_activities, :count)
          end
        end
      end
    end
  end

  describe ".by_term" do
    let!(:authorized_user_one) { create(:authorized_user, cpf: "671.503.710-86", name: "Another Name", email: "<EMAIL>") }
    let!(:authorized_user_two) { create(:authorized_user, cpf: "386.916.230-98", name: "Any Name", email: "<EMAIL>") }
    let!(:authorized_user_three) { create(:authorized_user, cpf: "173.385.860-12", name: "Another Name", email: "<EMAIL>") }
    let!(:authorized_user_four) { create(:authorized_user, cpf: "508.267.330-00", name: "Another name") }

    let(:term_one) { "any" }
    let(:term_two) { "671" }

    it "must filter by cpf, email or name that matches the term" do
      result_one = AuthorizedUser.by_term(term_one)
      expect(result_one.pluck(:id)).to match_array([authorized_user_two.id, authorized_user_three.id])

      result_two = AuthorizedUser.by_term(term_two)
      expect(result_two.pluck(:id)).to match_array([authorized_user_one.id])
    end
  end

  context "query with regex" do
    specify do
      create(:authorized_user, email: "<EMAIL>")

      query = AuthorizedUser.where("LOWER(:email) ~ :rgx", email: "<EMAIL>", rgx: "^[^@[:space:]]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}$")
      expect(query.count).to eq(1)
    end
  end

  context "when try upsert with duplicated values" do
    specify do
      authorized_user = create(:authorized_user, email: "<EMAIL>")
      authorized_users_params = [
        {id: authorized_user.id, email: "<EMAIL>"},
        {id: authorized_user.id, email: "<EMAIL>"}
      ]

      expect do
        AuthorizedUser.upsert_all(authorized_users_params)
      end.to raise_error(ActiveRecord::StatementInvalid, /PG::CardinalityViolation: ERROR:  ON CONFLICT DO UPDATE command cannot affect row a second time/)
    end

    specify do
      authorized_user = create(:authorized_user, email: "<EMAIL>")
      authorized_users_params = [
        {id: authorized_user.id, email: "<EMAIL>"},
        {id: authorized_user.id, email: "<EMAIL>"}
      ]
      authorized_users_params = authorized_users_params.uniq { |k| k[:id] }

      expect do
        AuthorizedUser.upsert_all(authorized_users_params)
      end.to change { authorized_user.reload.email }.from("<EMAIL>").to("<EMAIL>")
    end
  end

  describe "taggable" do
    context "when defining a user_tags attribute on save" do
      let(:business) { create(:business) }
      let(:authorized_user) { create(:authorized_user, business:) }

      context "when authorized_user_group is found" do
        let!(:authorized_user_group) { create(:authorized_user_group, business:) }

        it "must set authorized_user's group to be the one found" do
          authorized_user.update!(user_tags: authorized_user_group.slug)
          expect(authorized_user.authorized_user_group).to eq(authorized_user_group)
        end
      end

      context "when authorized_user_group is not found and will be created" do
        context "when user_tags is a array of slugs" do
          it "must create authorized_user_group with first slug from the array" do
            authorized_user.update!(user_tags: ["first_slug", "second_slug"])
            expect(authorized_user.authorized_user_group.slug).to eq("first_slug")
            expect(authorized_user.authorized_user_group.name).to eq("first_slug")
            expect(AuthorizedUserGroup.count).to eq(1)
          end
        end

        context "when user_tags is a single slug" do
          it "must create authorized_user_group" do
            authorized_user.update!(user_tags: "slug")
            expect(authorized_user.authorized_user_group.slug).to eq("slug")
            expect(authorized_user.authorized_user_group.name).to eq("slug")
            expect(AuthorizedUserGroup.count).to eq(1)
          end
        end
      end

      describe "taggable" do
        context "when updating tags attribute" do
          it "must set auth user tags list" do
            authorized_user.update(tags: ["Tag Teste 1", "Tag Teste 2"])
            expect(Tag.count).to eq(2)
            expect(Tagging.count).to eq(2)
            tagging_one = Tagging.first
            tagging_two = Tagging.second
            expect(tagging_one.tag.slug).to eq("tag-teste-1")
            expect(tagging_two.tag.slug).to eq("tag-teste-2")
            expect(tagging_one.business_id).to eq(authorized_user.business_id)
            expect(tagging_two.business_id).to eq(authorized_user.business_id)
            expect(authorized_user.reload.tags).to match_array(["Tag Teste 1", "Tag Teste 2"])
          end
        end

        context "when trying to set too many tags" do
          it "must take first tags based on limit" do
            authorized_user.update(tags: Array.new(12) { "Tag #{_1}" })
            expect(Tag.count).to eq(10)
            expect(Tagging.count).to eq(10)
            expect(authorized_user.reload.tags).to match_array(Array.new(10) { "Tag #{_1}" })
          end
        end
      end
    end
  end
end
