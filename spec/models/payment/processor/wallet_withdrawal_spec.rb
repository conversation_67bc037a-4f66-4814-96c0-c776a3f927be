# frozen_string_literal: true

require "rails_helper"

RSpec.describe Payment::Processor do
  describe "#call" do
    let(:currency) { "points" }
    let(:kind) { Wallet::Kind::CASHBACK }

    let(:business) { create(:business, cashback: true, currency:, fair_value: 0.0125) }
    let(:user) { create(:user, business:) }

    let(:business_wallet) { create(:wallet, currency:, kind:, business:, balance: 97520) }
    let(:outflow_wallet) { create(:wallet, :outflow, currency:, kind:, balance: 850951) }

    let!(:branch) { create(:branch, :online) }
    let!(:voucher_bucket) { create(:voucher_bucket) }
    let!(:giftcard) { create(:giftcard, branches: [branch], price: 59.99, voucher_bucket:) }
    let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
    let(:order) { create(:order, :with_giftcard, user:, giftcard:, voucher:) }

    let!(:fund_redemption_config) { create(:fund_redemption_config, :points, :giftcard, business:, fair_value: 0.03) }

    let(:idempotency_key) { SecureRandom.uuid }
    let(:payment_method) { Payment::PaymentMethod::WALLET_WITHDRAWAL }
    let(:processor) { described_class.new(order:, idempotency_key:, payment_method:, params: nil) }

    context "when user has balance" do
      let(:funds) do
        [
          create(:fund, :points, :available, user:, credited: true, amount: 1800, expires_at: 1.week.from_now),
          create(:fund, :points, :available, user:, credited: true, amount: 300, expires_at: 1.day.from_now)
        ]
      end
      let(:user_wallet) { create(:wallet, currency:, kind:, user:, balance: 15326) }
      let(:expected_amount) { 2000 }

      it "processes payment" do
        expect { processor.call }
          .to change { user_wallet.reload.balance }.by(-expected_amount)
          .and change { outflow_wallet.reload.balance }.by(expected_amount)
          .and not_change { business_wallet.reload.balance }
          .and change { order.reload.status }.to(Order::Status::COMPLETED)
          .and change(Payment.captured.where(order:, value: expected_amount), :count).by(1)
          .and not_change(CashbackRecord, :count)
          .and change { funds[1].reload.remaining_amount }.to(0)
          .and change { funds[0].reload.remaining_amount }.to(100)
      end
    end

    context "when user does not have balance" do
      let(:funds) do
        [
          create(:fund, :points, :available, user:, credited: true, amount: 1698, expires_at: 1.week.from_now),
          create(:fund, :points, :available, user:, credited: true, amount: 300, expires_at: 1.day.from_now)
        ]
      end
      let(:user_wallet) { create(:wallet, currency:, kind:, user:, balance: 1998) }

      it "leaves payment and order pending" do
        expect { processor.call }
          .to not_change { user_wallet.reload.balance }
          .and not_change { outflow_wallet.reload.balance }
          .and not_change { business_wallet.reload.balance }
          .and not_change { order.reload.status }
          .and change(Payment.denied.where(order:, value: 2000, reason_denied: "Saldo insuficiente"), :count).by(1)
          .and not_change { funds[1].reload.remaining_amount }
          .and not_change { funds[0].reload.remaining_amount }
      end
    end

    context "when payment method is invalid" do
      let(:funds) do
        [
          create(:fund, :points, :available, user:, credited: true, amount: 1800, expires_at: 1.week.from_now),
          create(:fund, :points, :available, user:, credited: true, amount: 300, expires_at: 1.day.from_now)
        ]
      end
      let(:user_wallet) { create(:wallet, currency:, kind:, user:, balance: 15326) }
      let(:payment_method) { "invalid" }

      it "does not process payment" do
        expect { processor.call }
          .to raise_error(ArgumentError, "'invalid' is not a valid payment_method")
          .and not_change { user_wallet.reload.balance }
          .and not_change { outflow_wallet.reload.balance }
          .and not_change { business_wallet.reload.balance }
          .and not_change { order.reload.status }
          .and not_change(Payment, :count)
          .and not_change { funds[1].reload.remaining_amount }
          .and not_change { funds[0].reload.remaining_amount }
      end
    end
  end
end
