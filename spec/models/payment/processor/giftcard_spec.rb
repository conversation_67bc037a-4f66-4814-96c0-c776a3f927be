# frozen_string_literal: true

require "rails_helper"
require "global_pay/api"

RSpec.describe Payment::Processor do
  describe "#call" do
    let(:business) { create(:business, :with_cashback) }
    let(:user) do
      create(
        :user,
        business:,
        name: "ADRIANO PESSOA SOUZA",
        cpf: "***********",
        email: "<EMAIL>",
        cellphone: "***********"
      )
    end
    let!(:branch) { create(:branch, :online) }
    let(:voucher_bucket) { create(:voucher_bucket) }
    let!(:giftcard) { create(:giftcard, branches: [branch], price: 50.21, voucher_bucket:) }
    let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
    let(:order) { create(:order, :with_giftcard, user:, giftcard:, payment_gateway: :global_pay, voucher:) }
    let(:params) do
      Api::V2::PaymentParams.new(
        user:,
        ip: "*************",
        payment_method: Payment::PaymentMethod::CREDIT_CARD,
        payment_details: {
          product_name: "giftcard",
          user: {
            name: user.name,
            document: user.cpf,
            email: user.email,
            phone: user.cellphone
          },
          address: {
            postal_code: "45810000",
            street: "Rua <PERSON> Lacerda",
            number: "55",
            complement: "APT 104 BL 02",
            neighborhood: "Trevo",
            city: "Belo Horizonte",
            state: "MG"
          },
          card: {
            security_code: "321",
            exp_month: 12,
            exp_year: 25,
            holder: "ADRIANOP SOUZA",
            number: "****************"
          },
          anti_fraud: {
            session_id: "fc10b881-d9a0-4ab1-a6fd-a102db188f49"
          },
          store_card: true
        }
      ).to_hash
    end
    let(:idempotency_key) { SecureRandom.uuid }
    let(:payment_method) { Payment::PaymentMethod::CREDIT_CARD }
    let(:processor) { described_class.new(order:, idempotency_key:, payment_method:, params:) }
    let(:gateway) { "global_pay" }
    let(:gateway_response) do
      GlobalPay::PaymentResponse.new(
        success: true,
        order_code: "561cb534a5304",
        payment_id: "020091487401111734280002361881640000000000",
        authorization_code: "955726",
        status_text: "PAID",
        amount: "50.21",
        json: {
          "status" => 201,
          "message" => "Pay Credit Card",
          "data" => {
            "orderCode" => "561cb534a5304",
            "paymentId" => "020091487401111734280002361881640000000000",
            "authorizationCode" => "955726",
            "statusText" => "PAID",
            "amount" => "50.21",
            "gateway" => {
              "paymentAuthorization" => {
                "returnCode" => "0",
                "description" => "Sucesso",
                "paymentId" => "020091487401111734280002361881640000000000",
                "authorizationCode" => "955726",
                "orderNumber" => "561cb534a5304",
                "amount" => 5021,
                "releaseAt" => "2025-01-11T14:34:28.2605125-03:00"
              }
            }
          },
          "id" => "4195317",
          "companyName" => "LECUPON S.A.",
          "documentNumber" => "26989697000169"
        }
      )
    end
    let(:store_card_response) do
      GlobalPay::StoreCardResponse.new(
        success: true,
        json: {"data" => {"storeCardId" => "a6c112b4-b9d2-4b44-9c51-d6fb276706b4"}},
        store_card_id: "a6c112b4-b9d2-4b44-9c51-d6fb276706b4"
      )
    end
    let(:gateway_mock) { instance_double(GlobalPay::Client, payment: gateway_response, store_card: store_card_response) }

    before do
      allow(GlobalPay::Client).to receive(:new).and_return(gateway_mock)
    end

    it "processes payment" do
      expect { processor.call }
        .to change(Payment.captured.where(order:, value: 50.21, json: gateway_response.json), :count).by(1)
        .and change { order.reload.status }.to(Order::Status::COMPLETED)
        .and change(order.cashback_records.where(user:, order_amount: 50.21), :count).by(1)
        .and change(user.credit_cards, :count).by(1)

      expect(processor.payment_data).to be_a CreditCard
    end

    context "when gateway raises error" do
      let(:error_message) { FFaker::LoremBR.sentence }
      let(:gateway_response) { GlobalPay::PaymentResponse.new(success: false, json: {}, error_message:) }

      it "leaves payment and order pending" do
        expect { processor.call }
          .to change(Payment.denied.where(order:, value: 50.21, reason_denied: error_message), :count).by(1)
          .and not_change { order.reload.status }
          .and not_change(CashbackRecord, :count)
      end
    end

    context "when payment method is invalid" do
      let(:payment_method) { "invalid" }

      it "does not process payment" do
        expect { processor.call }
          .to raise_error(ArgumentError, "'invalid' is not a valid payment_method")
          .and not_change(Payment, :count)
          .and not_change { order.reload.status }
          .and not_change(CashbackRecord, :count)
      end
    end
  end
end
