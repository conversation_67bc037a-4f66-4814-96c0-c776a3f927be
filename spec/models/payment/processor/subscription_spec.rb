# frozen_string_literal: true

require "rails_helper"
require "global_pay/api"

RSpec.describe Payment::Processor do
  describe "#call" do
    let(:business) { create(:business, global_pay_external_id: "4195319", fair_value: 0.0125, currency: :points) }
    let(:user) do
      create(
        :user,
        business:,
        name: "ADRIANO PESSOA SOUZA",
        cpf: "***********",
        email: "<EMAIL>",
        cellphone: "***********"
      )
    end
    let!(:subscription_config) { create(:subscription_config, business:) }
    let!(:plan) { create(:plan, subscription_config:, business:, price: 59.99) }
    let!(:credit_card) { create(:credit_card, user:) }
    let!(:subscription) { create(:subscription, user:, plan:) }
    let(:params) do
      Api::V2::PaymentParams.new(
        user:,
        ip: "*************",
        payment_method: Payment::PaymentMethod::CREDIT_CARD,
        payment_details: {
          card: {
            id: credit_card.id
          },
          anti_fraud: {
            session_id: "fc10b881-d9a0-4ab1-a6fd-a102db188f49"
          }
        }
      ).to_hash
    end
    let(:idempotency_key) { SecureRandom.uuid }
    let(:payment_method) { Payment::PaymentMethod::CREDIT_CARD }
    let(:processor) { described_class.new(order: subscription, idempotency_key:, payment_method:, params:) }
    let(:gateway_response) do
      GlobalPay::PaymentResponse.new(
        success: true,
        order_code: "561cb534a5304",
        payment_id: "020091487401111734280002361881640000000000",
        authorization_code: "955726",
        status_text: "PAID",
        amount: "59.99",
        json: {
          "status" => 201,
          "message" => "Pay Credit Card",
          "data" => {
            "orderCode" => "561cb534a5304",
            "paymentId" => "020091487401111734280002361881640000000000",
            "authorizationCode" => "955726",
            "statusText" => "PAID",
            "amount" => "59.99",
            "gateway" => {
              "paymentAuthorization" => {
                "returnCode" => "0",
                "description" => "Sucesso",
                "paymentId" => "020091487401111734280002361881640000000000",
                "authorizationCode" => "955726",
                "orderNumber" => "561cb534a5304",
                "amount" => 5021,
                "releaseAt" => "2025-01-11T14:34:28.2605125-03:00"
              }
            }
          },
          "id" => "4195317",
          "companyName" => "LECUPON S.A.",
          "documentNumber" => "26989697000169"
        }
      )
    end
    let(:gateway_api_mock) { instance_double(GlobalPay::Api) }
    let(:gateway_mock) { instance_double(GlobalPay::Client, payment: gateway_response) }

    before do
      allow(GlobalPay::Api).to receive(:new).and_return(gateway_api_mock)
      allow(GlobalPay::Client).to receive(:new).and_return(gateway_mock)
    end

    it "processes payment" do
      expect { processor.call }
        .to change(Payment.captured.where(subscription:, value: 59.99, json: gateway_response.json), :count).by(1)
        .and change { subscription.reload.active }.to(true)

      expect(gateway_mock).to have_received(:payment).with(instance_of(GlobalPay::PaymentRequest))
    end

    context "when gateway returns error" do
      let(:error_message) { FFaker::LoremBR.sentence }
      let(:gateway_response) { GlobalPay::PaymentResponse.new(success: false, json: {}, error_message:) }

      it "processes payment with status denied" do
        expect { processor.call }
          .to change(Payment.denied.where(subscription:, value: 59.99, reason_denied: error_message), :count).by(1)
      end
    end

    context "when payment method is invalid" do
      let(:payment_method) { "invalid" }

      it "does not process payment" do
        expect { processor.call }
          .to raise_error(ArgumentError, "'invalid' is not a valid payment_method")
          .and not_change(Payment, :count)
      end
    end
  end
end
