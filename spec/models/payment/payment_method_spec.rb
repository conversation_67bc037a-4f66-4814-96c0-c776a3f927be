# frozen_string_literal: true

require "rails_helper"

RSpec.describe Payment::PaymentMethod do
  describe ".all" do
    it do
      expect(described_class.all).to match_array([
        "credit_card",
        "wallet_withdrawal"
      ])
    end
  end

  describe ".enum" do
    it do
      expect(described_class.enum).to eq({
        "credit_card" => "credit_card",
        "wallet_withdrawal" => "wallet_withdrawal"
      })
    end
  end
end
