# frozen_string_literal: true

require "rails_helper"

RSpec.describe Payment::Gateway::GlobalPay::FeeCalculator do
  let(:plan_price) { 59.9 }

  describe "#fee_percentage" do
    subject { described_class.new(plan_price).fee_percentage }

    it { is_expected.to eq 3.42 }
  end

  describe "#fee_amount" do
    subject { described_class.new(plan_price).fee_amount }

    it { is_expected.to eq 2.05 }
  end
end
