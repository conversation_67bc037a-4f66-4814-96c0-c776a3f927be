# frozen_string_literal: true

require "rails_helper"

RSpec.describe FundCalculation do
  describe ".for" do
    subject { described_class.for(currency) }

    context "when currency is BRL" do
      let(:currency) { "BRL" }

      it { is_expected.to eq FundCalculation::Cashback }
    end

    context "when currency is points" do
      let(:currency) { "points" }

      it { is_expected.to eq FundCalculation::Point }
    end
  end

  describe ".redemption_amount" do
    subject { described_class.redemption_amount(currency:, service:, amount:, fair_value:) }

    describe "points" do
      let(:currency) { "points" }

      context "when service is bank_transfer" do
        let(:service) { FundRedemptionConfig.services[:bank_transfer] }
        let(:amount) { 2500 }
        let(:fair_value) { 0.0125 }

        it { is_expected.to eq 31.25 }
      end

      context "when service is azul" do
        let(:service) { FundRedemptionConfig.services[:azul] }
        let(:amount) { 1000 }
        let(:fair_value) { 8 }

        it { is_expected.to eq 8000 }
      end
    end

    describe "BRL" do
      let(:currency) { "BRL" }

      context "when service is azul" do
        let(:service) { FundRedemptionConfig.services[:azul] }
        let(:amount) { 3098 }
        let(:fair_value) { 0.03 }

        it { is_expected.to eq 1032 }
      end
    end
  end

  describe ".redemption_amount_formatted" do
    subject { described_class.redemption_amount_formatted(currency:, service:, amount:, fair_value:) }

    describe "points" do
      let(:currency) { "points" }

      context "when service is bank_transfer" do
        let(:service) { FundRedemptionConfig.services[:bank_transfer] }
        let(:amount) { 2500 }
        let(:fair_value) { 0.0125 }

        it { is_expected.to eq "R$ 31,25" }
      end

      context "when service is azul" do
        let(:service) { FundRedemptionConfig.services[:azul] }
        let(:amount) { 1000 }
        let(:fair_value) { 8 }

        it { is_expected.to eq "8000" }
      end
    end
  end
end
