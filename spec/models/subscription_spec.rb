# frozen_string_literal: true

require "rails_helper"

RSpec.describe Subscription, type: :model do
  include ActiveSupport::Testing::TimeHelpers

  let!(:business) { create(:business) }
  let!(:user) { create(:user, business:) }
  let!(:subscription_config) { create(:subscription_config, business:) }
  let!(:plan) { create(:plan, active: true, recurrence: "monthly", business:) }

  describe "associations" do
    it "belongs to a user" do
      association = described_class.reflect_on_association(:user)
      expect(association.macro).to eq(:belongs_to)
    end

    it "belongs to a subscription_config" do
      association = described_class.reflect_on_association(:subscription_config)
      expect(association.macro).to eq(:belongs_to)
    end

    it "belongs to a plan" do
      association = described_class.reflect_on_association(:plan)
      expect(association.macro).to eq(:belongs_to)
    end

    it "belongs to a credit_card" do
      association = described_class.reflect_on_association(:credit_card)
      expect(association.macro).to eq(:belongs_to)
    end
  end

  describe "validations" do
    it "is valid with valid attributes" do
      subscription = build(:subscription, user:, subscription_config:, plan:)
      subscription.valid?
      expect(subscription).to be_valid
    end

    it "is invalid without a user" do
      subscription = build(:subscription, user: nil, subscription_config:, plan:)
      subscription.valid?
      expect(subscription).not_to be_valid
      expect(subscription.errors[:user]).to include("é obrigatório(a)")
    end

    it "is invalid without a subscription_config" do
      subscription = build(:subscription, user:, subscription_config: nil, plan:)
      subscription.valid?
      expect(subscription).not_to be_valid
      expect(subscription.errors[:subscription_config]).to include("é obrigatório(a)")
    end

    it "is invalid without a plan" do
      subscription = build(:subscription, user:, subscription_config:, plan: nil)
      subscription.valid?
      expect(subscription).not_to be_valid
      expect(subscription.errors[:plan]).to include("é obrigatório(a)")
    end
  end

  describe "#cancel" do
    let!(:plan) { create(:plan, subscription_config: subscription_config, business: business) }
    let!(:subscription) { create(:subscription, user: user, subscription_config: subscription_config, plan: plan, active: true) }

    it "sets the status to false and sends email" do
      expect { subscription.cancel! }
        .to change(subscription, :active).to(false)
        .and change(SubscriptionMailer.deliveries, :count).by(1)
    end

    context "without notifying" do
      it "sets the status to false and does not send email" do
        expect { subscription.cancel!(notify: false) }
          .to change(subscription, :active).to(false)
          .and not_change(SubscriptionMailer.deliveries, :count)
      end
    end
  end

  describe "scopes" do
    let!(:active_subscription) { create(:subscription, user:, subscription_config:, plan:, active: true) }
    let!(:inactive_subscription) { create(:subscription, user:, subscription_config:, plan:, active: false) }

    describe ".inactive" do
      it "returns only inactive subscriptions" do
        expect(described_class.inactive).to contain_exactly(inactive_subscription)
      end
    end

    describe ".active" do
      it "returns only active subscriptions" do
        expect(described_class.active).to contain_exactly(active_subscription)
      end
    end
  end

  describe "#update_credit_card!" do
    let(:user) { create(:user) }
    let!(:credit_card_main) { create(:credit_card, brand: :visa, user:, main: true) }
    let!(:credit_card_not_main) { create(:credit_card, brand: :mastercard, user:, main: false) }
    let!(:subscription) { create(:subscription, user:, credit_card: credit_card_main) }

    it "updates subscription credit card" do
      expect { subscription.update_credit_card!(credit_card_not_main) }
        .to change { subscription.reload.credit_card_id }.to(credit_card_not_main.id)
        .and change { credit_card_not_main.reload.main }.to(true)
        .and change { credit_card_main.reload.main }.to(false)
    end
  end

  describe ".charge_due_installments", :vcr do
    before { travel_to DateTime.new(2025, 5, 15, 10) }

    let!(:business) do
      create(
        :business,
        global_pay_external_id: "4195317",
        payment_split: false,
        cashback: true,
        fair_value: 0.03,
        currency: :points
      )
    end
    let!(:credit_card) do
      create(
        :credit_card,
        user:,
        brand: :visa,
        token: nil,
        seller_id: "4195317",
        number: "****************",
        holder: "ADRIANO P SOUZA",
        security_code: "321",
        exp_month: "02",
        exp_year: "26",
        billing_name: "ADRIANO PESSOA SOUZA",
        billing_document: "***********",
        billing_email: "<EMAIL>",
        billing_phone: "***********",
        billing_postal_code: "45810000",
        billing_street: "Rua Jose Lacerda",
        billing_number: "55",
        billing_complement: "APT 104 BL 02",
        billing_neighborhood: "Trevo",
        billing_city: "Belo Horizonte",
        billing_state: "MG"
      )
    end
    let!(:subscription_due) { create(:subscription, user:, credit_card:, active: true) }
    let!(:installment_sub_due) do
      create(
        :subscription_installment,
        subscription: subscription_due,
        price: subscription_due.price,
        status: :paid,
        paid_at: 31.days.ago,
        due_at: 31.days.ago
      )
    end

    let!(:other_user) { create(:user, business:) }
    let!(:subscription_not_due) { create(:subscription, user: other_user, active: true) }
    let!(:installment_sub_not_due) do
      create(
        :subscription_installment,
        subscription: subscription_not_due,
        price: subscription_not_due.price,
        status: :paid,
        paid_at: 27.days.ago,
        due_at: 27.days.ago
      )
    end

    let!(:user_retry) { create(:user, business:) }
    let!(:credit_card_retry) do
      cc = credit_card.dup
      cc.update!(user: user_retry)
      cc
    end
    let!(:subscription_last_retry) do
      create(:subscription, credit_card: credit_card_retry, user: user_retry, active: true, retries: 2)
    end
    let!(:installment_sub_last_retry) do
      create(
        :subscription_installment,
        subscription: subscription_last_retry,
        price: subscription_last_retry.price,
        status: :paid,
        paid_at: 31.days.ago,
        due_at: 31.days.ago
      )
    end

    let!(:user_retry_limit) { create(:user, business:) }
    let!(:subscription_retry_limit) { create(:subscription, user: user_retry_limit, active: true, retries: 3) }
    let!(:installment_sub_retry_limit) do
      create(
        :subscription_installment,
        subscription: subscription_retry_limit,
        price: subscription_retry_limit.price,
        status: :paid,
        paid_at: 31.days.ago,
        due_at: 31.days.ago
      )
    end

    it "charges due installments" do
      expect { described_class.charge_due_installments }
        .to change(subscription_due.payments, :count).by(1)
        .and change(subscription_due.subscription_installments, :count).by(1)
        .and change(subscription_due.user.funds.points.approved, :count).by(1)
        .and not_change { subscription_due.reload.retries }
        .and not_change(subscription_not_due.payments, :count)
        .and not_change(subscription_not_due.subscription_installments, :count)
        .and not_change { subscription_not_due.reload.retries }
        .and change(subscription_last_retry.payments, :count)
        .and not_change(subscription_last_retry.user.funds, :count)
        .and change { subscription_last_retry.reload.retries }.to(3)
        .and change { subscription_last_retry.reload.active }.to(false)
    end
  end

  describe "#payment_next_date" do
    subject { subscription.payment_next_date }

    let!(:subscription) { create(:subscription) }

    describe "subscribed January 30th or 31st" do
      describe "1 installment" do
        let!(:installment) { create_installment(subscription:, due_at: DateTime.new(2025, 1, [30, 31].sample, 10)) }

        it { is_expected.to eq Date.new(2025, 2, 28) }
      end

      describe "2 installments" do
        let!(:installments) do
          [
            create_installment(subscription:, due_at: DateTime.new(2025, 1, [30, 31].sample, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 2, 28, 10))
          ]
        end

        it { is_expected.to eq Date.new(2025, 3, 30) }
      end

      describe "3 installments" do
        let!(:installments) do
          [
            create_installment(subscription:, due_at: DateTime.new(2025, 1, [30, 31].sample, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 2, 28, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 3, 30, 10))
          ]
        end

        it { is_expected.to eq Date.new(2025, 4, 30) }
      end

      describe "13 installments" do
        let!(:installments) do
          [
            create_installment(subscription:, due_at: DateTime.new(2025, 1, [30, 31].sample, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 2, 28, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 3, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 4, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 5, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 6, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 7, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 8, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 9, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 10, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 11, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 12, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2026, 1, 30, 10))
          ]
        end

        it { is_expected.to eq Date.new(2026, 2, 28) }
      end

      describe "14 installments" do
        let!(:installments) do
          [
            create_installment(subscription:, due_at: DateTime.new(2025, 1, [30, 31].sample, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 2, 28, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 3, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 4, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 5, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 6, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 7, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 8, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 9, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 10, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 11, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 12, 30, 10)),
            create_installment(subscription:, due_at: DateTime.new(2026, 2, 28, 10))
          ]
        end

        it { is_expected.to eq Date.new(2026, 3, 30) }
      end
    end

    describe "subscribed February 28th" do
      describe "1 installment" do
        let!(:installment) { create_installment(subscription:, due_at: DateTime.new(2025, 2, 28, 10)) }

        it { is_expected.to eq Date.new(2025, 3, 28) }
      end

      describe "2 installments" do
        let!(:installments) do
          [
            create_installment(subscription:, due_at: DateTime.new(2025, 2, 28, 10)),
            create_installment(subscription:, due_at: DateTime.new(2025, 3, 28, 10))
          ]
        end

        it { is_expected.to eq Date.new(2025, 4, 28) }
      end
    end
  end

  def create_installment(subscription:, due_at:, status: :paid)
    create(:subscription_installment, subscription:, status:, due_at:, paid_at: due_at)
  end
end
