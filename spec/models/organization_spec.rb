require "rails_helper"

RSpec.describe Organization, type: :model do
  describe "#confirm_cnpj" do
    let!(:organization) { create(:organization) }
    let!(:branch_one) { create(:branch, organization:, cnpj: "32.839.587/0001-13") }
    let!(:branch_two) { create(:branch, organization:, cnpj: "70.270.404/0001-80") }
    let!(:coupon_one) { create(:cupon, :classic, branch: branch_one) }
    let!(:coupon_two) { create(:cupon, :classic, branch: branch_two) }

    it { expect(organization.confirm_cnpj("32.839587000113", coupon_one)).to eq(true) }
    it { expect(organization.confirm_cnpj("32839587000113", coupon_two)).to eq(false) }
    it { expect(organization.confirm_cnpj("70.270404000180", coupon_two)).to eq(true) }
    it { expect(organization.confirm_cnpj("70270404000180", coupon_one)).to eq(false) }
  end

  describe "normalize description" do
    let(:organization) { build(:organization, description: "Descrição  teste 😊🧡 teste") }

    subject { organization.description }

    before { organization.validate }

    it { is_expected.to eq("Descrição teste teste") }
  end

  describe "#refresh_branch_flags" do
    let(:organization) { create(:organization) }

    context "with active physical branch" do
      let!(:branch) { create(:branch, :physical, organization:, promotion_redeemable: true) }
      let!(:inactive_branch) { create(:branch, :online, active: false, organization:, promotion_redeemable: true) }

      specify do
        organization.refresh_branch_flags
        expect(organization.physical_branches).to eq(true)
        expect(organization.online_branches).to eq(false)
      end
    end

    context "with active online branch" do
      let!(:branch) { create(:branch, :online, organization:, promotion_redeemable: true) }
      let!(:inactive_branch) { create(:branch, :physical, active: false, organization:, promotion_redeemable: true) }

      specify do
        organization.refresh_branch_flags
        expect(organization.physical_branches).to eq(false)
        expect(organization.online_branches).to eq(true)
      end
    end

    context "with inactive all branches" do
      let!(:branch) do
        create(:branch, active: false, organization:, branch_type: [:online, :physical].sample, promotion_redeemable: true)
      end

      specify do
        organization.refresh_branch_flags
        expect(organization.physical_branches).to eq(false)
        expect(organization.online_branches).to eq(false)
      end
    end
  end

  describe "cashback validations" do
    let(:organization_no_cashback_type) { build(:organization, cashback_type: nil, cashback_value: 10) }
    let(:organization_no_cashback_value) { build(:organization, cashback_type: "percent", cashback_value: 0) }
    let(:organization_no_cashback) { build(:organization, cashback_type: nil, cashback_value: 0) }
    let(:organization_with_cashback) { build(:organization, cashback_type: "percent", cashback_value: 10) }

    it { expect(organization_no_cashback_type).to be_invalid }
    it { expect(organization_no_cashback_value).to be_valid }
    it { expect(organization_no_cashback).to be_valid }
    it { expect(organization_with_cashback).to be_valid }
  end

  describe "cnpj normalization and validation" do
    it "normalizes and validates cnpj" do
      organization_no_cnpj = build(:organization, cnpj: nil)
      organization_cnpj_valid = build(:organization, cnpj: " 2.792.317/0001-33  ")
      organization_cnpj_wrong = build(:organization, cnpj: " 2.792.317/0001-34  ")
      organization_cnpj_too_long = build(:organization, cnpj: " 002.792.317/0001-33  ")

      expect(organization_no_cnpj).to be_valid
      expect(organization_no_cnpj.cnpj).to eq(nil)
      expect(organization_cnpj_valid).to be_valid
      expect(organization_cnpj_valid.cnpj).to eq("02792317000133")
      expect(organization_cnpj_wrong).to be_invalid
      expect(organization_cnpj_wrong.errors.as_json).to eq(cnpj: ["inválido"])
      expect(organization_cnpj_too_long).to be_invalid
      expect(organization_cnpj_too_long.errors.as_json).to eq(
        cnpj: ["não possui o tamanho esperado (14 caracteres)", "inválido"]
      )
    end
  end

  describe "#refresh_promotion_redeemable" do
    it "refreshes promotion redeemable" do
      org_with_promo_redeemable = create(:organization, promotion_redeemable: true)
      create(:branch, promotion_redeemable: true, organization: org_with_promo_redeemable)
      org_with_promo_redeemable_outdated_flag = create(:organization, promotion_redeemable: false)
      create(:branch, promotion_redeemable: true, organization: org_with_promo_redeemable_outdated_flag)
      org_without_promo_redeemable = create(:organization, promotion_redeemable: false)
      create(:branch, promotion_redeemable: false, organization: org_without_promo_redeemable)
      org_without_promo_redeemable_outdated_flag = create(:organization, promotion_redeemable: true)
      create(:branch, promotion_redeemable: false, organization: org_without_promo_redeemable_outdated_flag)
      org_without_branch_outdated_flag = create(:organization, promotion_redeemable: true)

      org_with_promo_redeemable.refresh_promotion_redeemable
      expect(org_with_promo_redeemable.reload.promotion_redeemable).to eq(true)
      org_with_promo_redeemable_outdated_flag.refresh_promotion_redeemable
      expect(org_with_promo_redeemable_outdated_flag.reload.promotion_redeemable).to eq(true)
      org_without_promo_redeemable.refresh_promotion_redeemable
      expect(org_without_promo_redeemable.reload.promotion_redeemable).to eq(false)
      org_without_promo_redeemable_outdated_flag.refresh_promotion_redeemable
      expect(org_without_promo_redeemable_outdated_flag.reload.promotion_redeemable).to eq(false)
      org_without_branch_outdated_flag.refresh_promotion_redeemable
      expect(org_without_branch_outdated_flag.reload.promotion_redeemable).to eq(false)
    end
  end

  describe ".refresh_promotion_redeemable" do
    let!(:promotion_available) { create(:promotion, :cpf, :available) }
    let!(:promotion_unavailable) { create(:promotion, :cpf, :inactive, end_date: 1.day.ago) }
    let!(:org_with_promo_redeemable) { create(:organization, promotion_redeemable: true) }
    let!(:branch_with_promo_redeemable) do
      create(:branch, promotion_redeemable: true, organization: org_with_promo_redeemable)
    end
    let!(:org_with_promo_redeemable_outdated_flag) { create(:organization, promotion_redeemable: false) }
    let!(:branch_with_promo_redeemable_outdated_flag) do
      create(:branch, promotion_redeemable: false, organization: org_with_promo_redeemable_outdated_flag)
    end
    let!(:org_without_promo_redeemable) { create(:organization, promotion_redeemable: false) }
    let!(:branch_without_promo_redeemable) do
      create(:branch, promotion_redeemable: false, organization: org_without_promo_redeemable)
    end
    let!(:org_without_promo_redeemable_outdated_flag) { create(:organization, promotion_redeemable: true) }
    let!(:branch_without_promo_redeemable_outdated_flag) do
      create(:branch, promotion_redeemable: true, organization: org_without_promo_redeemable_outdated_flag)
    end
    let!(:org_without_branch_outdated_flag) { create(:organization, promotion_redeemable: true) }

    before do
      Promotion::Coupons::CreateService.call(
        promotion: promotion_available,
        branch_ids: [branch_with_promo_redeemable.id, branch_with_promo_redeemable_outdated_flag.id]
      )
      Promotion::Coupons::CreateService.call(
        promotion: promotion_unavailable,
        branch_ids: [branch_without_promo_redeemable.id, branch_without_promo_redeemable_outdated_flag.id]
      )
    end

    specify do
      described_class.refresh_promotion_redeemable
      expect(org_with_promo_redeemable.reload.promotion_redeemable).to eq(true)
      expect(org_with_promo_redeemable_outdated_flag.reload.promotion_redeemable).to eq(true)
      expect(org_without_promo_redeemable.reload.promotion_redeemable).to eq(false)
      expect(org_without_promo_redeemable_outdated_flag.reload.promotion_redeemable).to eq(false)
      expect(org_without_branch_outdated_flag.reload.promotion_redeemable).to eq(false)
    end
  end

  describe ".only_with_coupons" do
    it "returns all organizations including without coupons when flag is false" do
      organizations_without_coupons = create(:organization, promotion_redeemable: false)
      organizations_with_coupons = create(:organization, promotion_redeemable: true)

      expect(described_class.only_with_coupons(false))
        .to match_array([organizations_without_coupons, organizations_with_coupons])
    end

    it "returns only organizations with coupons when flag is true" do
      organizations_without_coupons = create(:organization, promotion_redeemable: false)
      organizations_with_coupons = create(:organization, promotion_redeemable: true)

      expect(described_class.only_with_coupons(true))
        .to include(organizations_with_coupons)
        .and not_include(organizations_without_coupons)
    end
  end

  describe ".not_blocked" do
    it "returns allowed organizations for business" do
      business = create(:business)

      org_blocklisted = create(:organization, all_projects: true)
      blocklisted_orgs_query_mock = double(
        :blocklisted_orgs_query_mock,
        ids: [org_blocklisted.id]
      )
      allow(Organization::Blocklisted)
        .to receive(:new)
        .with(business_ids: [business.id])
        .and_return(blocklisted_orgs_query_mock)

      org_all_projects = create(:organization, all_projects: true)

      org_exclusive_to_business = create(:organization, all_projects: false)
      create(:organization_profile, organization: org_exclusive_to_business, business:)

      org_exclusive_to_other_business = create(:organization, all_projects: false)
      create(:organization_profile, organization: org_exclusive_to_other_business, business: create(:business))
      blocklisted_organization_ids = Organization::Blocklisted.new(business_ids: [business.id]).ids

      organizations = described_class
        .not_blocked(blocklisted_organization_ids)
        .and(
            Organization.where(all_projects: true)
              .or(Organization.active.where(id: OrganizationProfile.where(business:  [business.id]).pluck(:organization_id)))
          )
      expect(organizations).to match_array([org_all_projects, org_exclusive_to_business])
    end

    it "returns empty when passing empty argument" do
      empty_argument = [nil, []].sample
      organizations = described_class.not_blocked(empty_argument)

      expect(organizations).to be_empty
    end
  end

  context "with_profile" do
    it "returns organizations for which business has profile" do
      business = create(:business)

      org_exclusive_to_business = create(:organization)
      create(:organization_profile, organization: org_exclusive_to_business, business:)

      org_exclusive_to_other_business = create(:organization)
      create(:organization_profile, organization: org_exclusive_to_other_business, business: create(:business))

      organizations = described_class.active.where(id: OrganizationProfile.where(business:  [business.id]).pluck(:organization_id))

      expect(organizations).to match_array([org_exclusive_to_business])
    end

    it "returns empty when passing empty argument" do
      empty_argument = [nil, []].sample

      organizations = described_class.where(id: OrganizationProfile.where(business:  empty_argument).pluck(:organization_id))

      expect(organizations).to be_empty
    end
  end

  describe "#refresh_highest_discount" do
    let(:business_one) { create(:business) }
    let(:business_two) { create(:business) }

    it "when start with percent: expected to update the highest discount to the percentage even if the fixed is higher" do
      organization = create(:organization, :percent_highest_discount, highest_discount_value: 20, active: true)
      create(:promotion, :available, :fixed_discount, organization:, business: nil, discount_value: 5)
      create(:promotion, :available, :percent_discount, organization:, business: nil, discount_value: 4)
      create(:promotion, :available, :percent_discount, organization:, business: business_one, discount_value: 10)
      create(:promotion, :inactive, :percent_discount, organization:, business: business_one, discount_value: 15)
      create(:promotion, :available, :percent_discount, organization:, business: business_two, discount_value: 12)

      organization.refresh_highest_discount

      expect(organization.highest_discount_value).to eq(4)
      expect(organization.highest_discount_type).to eq("percent")
    end

    it "when start with fixed: expected to update the highest discount to the percentage even if the fixed is higher" do
      organization = create(:organization, :fixed_highest_discount, highest_discount_value: 20, active: true)
      create(:promotion, :available, :percent_discount, organization:, business: nil, discount_value: 3)
      create(:promotion, :available, :fixed_discount, organization:, business: nil, discount_value: 4)
      create(:promotion, :available, :fixed_discount, organization:, business: business_one, discount_value: 10)
      create(:promotion, :inactive, :fixed_discount, organization:, business: business_one, discount_value: 15)

      organization.refresh_highest_discount

      expect(organization.highest_discount_value).to eq(3)
      expect(organization.highest_discount_type).to eq("percent")
    end

    it "expected to update the highest discount to fixed when there is no percentage discount" do
      organization = create(:organization, :percent_highest_discount, highest_discount_value: 20, active: true)
      create(:promotion, :available, :fixed_discount, organization:, business: nil, discount_value: 4)
      create(:promotion, :available, :fixed_discount, organization:, business: business_one, discount_value: 10)
      create(:promotion, :inactive, :fixed_discount, organization:, business: business_one, discount_value: 15)

      organization.refresh_highest_discount

      expect(organization.highest_discount_value).to eq(4)
      expect(organization.highest_discount_type).to eq("fixed")
    end

    context "when there are no promotions " do
      it "expected update profile highest_discount" do
        organization = create(:organization, :percent_highest_discount, highest_discount_value: 20, active: true)

        organization.refresh_highest_discount

        expect(organization.highest_discount_value).to eq(nil)
        expect(organization.highest_discount_type).to eq(nil)
      end
    end
  end

  describe "#refresh_highest_cashback" do
    context "when organization promotion has highest cashback" do
      let!(:organization) { create(:organization, highest_cashback_type: :fixed, highest_cashback_value: 40) }

      before do
        create(:promotion, :available, :non_exclusive, organization:, cashback_type: :percent, cashback_value: 10)
        create(:promotion, :available, :non_exclusive, organization:, cashback_type: :percent, cashback_value: 20)
        create(:promotion, :available, :exclusive, organization:, cashback_type: :percent, cashback_value: 80)
      end

      it "refreshes highest cashback value" do
        expect { organization.refresh_highest_cashback }
          .to change(organization, :highest_cashback_value).from(40).to(20)
          .and change(organization, :highest_cashback_type).from("fixed").to("percent")
      end
    end

    context "when organization itself has highest cashback" do
      let!(:organization) do
        create(
          :organization,
          cashback_type: :percent,
          cashback_value: 10,
          highest_cashback_type: :fixed,
          highest_cashback_value: 5
        )
      end

      before do
        create(:promotion, :available, :exclusive, organization:, cashback_type: :percent, cashback_value: 80)
      end

      it "refreshes highest cashback value" do
        expect { organization.refresh_highest_cashback }
          .to change(organization, :highest_cashback_value).from(5).to(10)
          .and change(organization, :highest_cashback_type).from("fixed").to("percent")
      end
    end
  end

  describe "#profile_for" do
    context "when business does not have profile" do
      let(:organization) { create(:organization) }
      let(:business) { create(:business, organization_manage: true) }

      subject { organization.profile_for(business) }

      it { is_expected.to eq organization }
    end

    context "when business has profile and manages organization" do
      let(:organization) { create(:organization) }
      let(:business) { create(:business, organization_manage: true) }
      let!(:organization_profile) { create(:organization_profile, organization:, business:) }

      subject { organization.profile_for(business) }

      it { is_expected.to eq organization_profile }
    end

    context "when business has profile and does not manage organization" do
      let(:organization) { create(:organization) }
      let(:business) { create(:business, organization_manage: false) }
      let!(:organization_profile) { create(:organization_profile, organization:, business:) }

      subject { organization.profile_for(business) }

      it { is_expected.to eq organization }
    end
  end

  describe "#refresh_giftcard_min_price" do
    let!(:organization) { create(:organization, giftcard_min_price: nil) }
    let!(:branch) { create(:branch, organization:) }

    let!(:unrelated_organization) { create(:organization) }
    let!(:unrelated_branch) { create(:branch, organization: unrelated_organization) }

    before do
      create(:giftcard, branches: [branch], available: true, price: 15)
      create(:giftcard, branches: [branch], available: true, price: 20)
      create(:giftcard, branches: [branch], available: false, price: 10)
      create(:giftcard, branches: [unrelated_branch], available: true, price: 10)
    end

    it "refreshes giftcard min price" do
      expect { organization.refresh_giftcard_min_price }.to change(organization, :giftcard_min_price).to(15)
    end
  end
end
