require "rails_helper"

RSpec.describe Navigation::Icon do
  describe ".all" do
    specify do
      expect(described_class.all)
        .to eq([
          "account_balance_wallet_outlined",
          "account_box_outlined",
          "card_giftcard",
          "cart_outline",
          "control_point_duplicate",
          "description_outlined",
          "discount_outlined",
          "exit_to_app_outlined",
          "favorite_border_outlined",
          "help_outlined",
          "history",
          "home",
          "list",
          "login",
          "logout_outlined",
          "loyalty_outlined",
          "medical_services",
          "member_referral",
          "menu",
          "person_outline",
          "privacy_tip",
          "quiz_outlined",
          "rewarded_ads",
          "search",
          "star_border_outlined"
        ])
    end
  end

  describe ".enum" do
    specify do
      expect(described_class.enum)
        .to eq(
          account_balance_wallet_outlined: "account_balance_wallet_outlined",
          account_box_outlined: "account_box_outlined",
          card_giftcard: "card_giftcard",
          cart_outline: "cart_outline",
          control_point_duplicate: "control_point_duplicate",
          description_outlined: "description_outlined",
          discount_outlined: "discount_outlined",
          exit_to_app_outlined: "exit_to_app_outlined",
          favorite_border_outlined: "favorite_border_outlined",
          help_outlined: "help_outlined",
          history: "history",
          home: "home",
          list: "list",
          login: "login",
          logout_outlined: "logout_outlined",
          loyalty_outlined: "loyalty_outlined",
          medical_services: "medical_services",
          member_referral: "member_referral",
          menu: "menu",
          person_outline: "person_outline",
          privacy_tip: "privacy_tip",
          quiz_outlined: "quiz_outlined",
          rewarded_ads: "rewarded_ads",
          search: "search",
          star_border_outlined: "star_border_outlined"
        )
    end
  end
end
