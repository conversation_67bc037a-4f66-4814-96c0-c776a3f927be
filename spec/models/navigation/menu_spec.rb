# frozen_string_literal: true

require "rails_helper"

RSpec.describe Navigation, type: :model do
  describe "validates the destination format" do
    let(:business) { create(:business) }
    let(:valid_strings) { %w[https://www.foo.com.br http://www.foo.com.br] }
    let(:invalid_strings) { %w[www.foo.com.br path/to/foo] }

    let(:menu) do
      build(:menu, :external_link, {
        exclusive_business: business,
        disposition: Navigation::Disposition::PROFILE_ICON,
        business:
      })
    end

    context "when the url is invalid" do
      it "must be invalid" do
        menu.destination = invalid_strings.sample
        expect(menu).to be_invalid
        expect(menu.errors.first.message).to eq("Formato fornecido no destination é inválido")
      end
    end

    context "when the url is valid" do
      it "must be valid" do
        menu.destination = valid_strings.sample
        expect(menu).to be_valid
      end
    end
  end

  describe "when destination is an url but its not a external menu" do
    let(:business) { create(:business) }
    let!(:menu) {
      build(:menu,
        disposition: Navigation::Disposition::PROFILE_ICON,
        business:,
        icon: "list",
        destination: "https://google.com")
    }

    it "must be invalid" do
      expect(menu).to be_invalid
      expect(menu.errors.first.message).to eq(I18n.t("navigation.errors.destination.external_url_on_internal_menu"))
    end
  end

  describe "validate exclusive business" do
    let(:business) { create(:business) }

    context "when try to create menu with exclusive business out menu business tree" do
      let!(:unrelated_business) { create(:business) }
      let!(:menu) {
        build(:menu,
          exclusive_business: unrelated_business,
          disposition: Navigation::Disposition::PROFILE_ICON,
          business:)
      }
      it { expect(menu).to be_invalid }
    end

    context "when try to create menu with exclusive business in menu business tree" do
      let!(:related_business) { create(:business, main_business: business) }
      let!(:menu) {
        build(:menu,
          exclusive_business: related_business,
          disposition: Navigation::Disposition::PROFILE_ICON,
          business:,
          icon: "list")
      }
      it { expect(menu).to be_valid }
    end
  end

  describe "#apply_feature_flag_rule" do
    it "activates giftcard menu item when business has giftcard" do
      business = create(:business, giftcard: false)
      menu =
        create(
          :menu,
          disposition: Navigation::Disposition::PROFILE_ICON,
          business:,
          active: true,
          feature_flag_rule: "gift_card"
        )

      expect { menu.apply_feature_flag_rule }.to change(menu, :active).from(true).to(false)
    end

    it "deactivates giftcard menu item when business does not have giftcard" do
      business = create(:business, giftcard: true)
      menu = create(
        :menu,
        disposition: Navigation::Disposition::PROFILE_ICON,
        business:,
        active: false,
        feature_flag_rule: "gift_card"
      )

      expect { menu.apply_feature_flag_rule }.to change(menu, :active).from(false).to(true)
    end

    it "activates prize draw menu item when business has prize draw" do
      business = create(:business, prize_draw: false)
      menu = create(
        :menu,
        disposition: Navigation::Disposition::PROFILE_ICON,
        business:,
        active: true,
        feature_flag_rule: "prize_draw"
      )

      expect { menu.apply_feature_flag_rule }.to change(menu, :active).from(true).to(false)
    end

    it "deactivates prize draw menu item when business does not have prize draw" do
      business = create(:business, prize_draw: true)
      menu = create(
        :menu,
        disposition: Navigation::Disposition::PROFILE_ICON,
        business:,
        active: false,
        feature_flag_rule: "prize_draw"
      )

      expect { menu.apply_feature_flag_rule }.to change(menu, :active).from(false).to(true)
    end

    it "activates device_auth menu item when business has biometry" do
      business = create(:business, biometry: false)
      menu = create(:menu, active: true, feature_flag_rule: "biometry", business:)

      expect { menu.apply_feature_flag_rule }.to change(menu, :active).from(true).to(false)
    end

    it "deactivates biometry menu item when business does not have biometry" do
      business = create(:business, biometry: true)
      menu = create(:menu, active: false, feature_flag_rule: "biometry", business:)

      expect { menu.apply_feature_flag_rule }.to change(menu, :active).from(false).to(true)
    end

    it "activates cashback menu item when business has cashback" do
      business = create(:business, cashback: false)
      menu = create(
        :menu,
        disposition: Navigation::Disposition::FOOTER,
        business:,
        active: true,
        feature_flag_rule: "cashback"
      )

      expect { menu.apply_feature_flag_rule }.to change(menu, :active).from(true).to(false)
    end

    it "deactivates cashback menu item when business does not have cashback" do
      business = create(:business, cashback: true, cashback_wallet_destination: "cashback")
      menu = create(
        :menu,
        disposition: Navigation::Disposition::FOOTER,
        business:,
        active: false,
        feature_flag_rule: "cashback"
      )

      expect { menu.apply_feature_flag_rule }.to change(menu, :active).from(false).to(true)
    end

    context "applying internal_user_manager feature flag rule" do
      it "activates internal_user_manager menu item when business has internal_user_manager" do
        business = create(:business)
        menu = create(
          :menu,
          disposition: Navigation::Disposition::FOOTER,
          business:,
          active: false,
          feature_flag_rule: "internal_user_manager"
        )

        expect { menu.apply_feature_flag_rule }.to change(menu, :active).from(false).to(true)
      end

      it "deactivates internal_user_manager menu item when business does not have internal_user_manager" do
        business = create(:business, :avanza_ti)
        menu = create(
          :menu,
          disposition: Navigation::Disposition::FOOTER,
          business:,
          active: true,
          feature_flag_rule: "internal_user_manager"
        )

        expect { menu.apply_feature_flag_rule }.to change(menu, :active).from(true).to(false)
      end
    end

    context "when applying points feature flag rule" do
      context "when currency is points" do
        let(:business) { create(:business, :with_cashback, currency: "points", fair_value: 0.03) }
        let!(:menu) do
          create(
            :menu,
            disposition: Navigation::Disposition::PROFILE_ICON,
            business:,
            active: false,
            feature_flag_rule: Navigation::FeatureFlagRule::POINTS
          )
        end

        it { expect { menu.apply_feature_flag_rule }.to change(menu, :active).to(true) }
      end

      context "when currency is not points" do
        let(:business) { create(:business, :with_cashback, currency: "BRL") }
        let!(:menu) do
          create(
            :menu,
            disposition: Navigation::Disposition::PROFILE_ICON,
            business:,
            active: true,
            feature_flag_rule: Navigation::FeatureFlagRule::POINTS
          )
        end

        it { expect { menu.apply_feature_flag_rule }.to change(menu, :active).to(false) }
      end
    end

    it "does not apply feature flag rule to unmapped menu item" do
      business = create(:business, giftcard: true)
      menu = create(
        :menu,
        disposition: Navigation::Disposition::PROFILE_ICON,
        business:,
        active: false,
        feature_flag_rule: nil
      )

      expect { menu.apply_feature_flag_rule }.not_to change(menu, :active)
    end
  end

  describe "allow blank icon validation" do
    it "is valid when menu allows blank icon" do
      allow_blank_icon_dispositions = [Navigation::Disposition::HEADER, Navigation::Disposition::FOOTER]
      business = create(:business)
      menu = build(
        :menu,
        disposition: allow_blank_icon_dispositions.sample,
        business:,
        icon: ""
      )

      expect(menu).to be_valid
    end

    it "is invalid when menu does not allow blank icon" do
      allow_blank_icon_dispositions = [Navigation::Disposition::HEADER, Navigation::Disposition::FOOTER]
      does_not_allow_blank_icon_dispositions = Navigation::Disposition.all - allow_blank_icon_dispositions
      business = create(:business)
      menu = build(
        :menu,
        disposition: does_not_allow_blank_icon_dispositions.sample,
        business:,
        icon: ""
      )

      expect(menu).to be_invalid
      expect(menu.errors.as_json).to include(icon: ["não está incluído na lista"])
    end
  end

  describe ".by_products" do
    let(:business) { create(:business) }

    context "when authorize_user has telemedicine" do
      let(:authorized_user) { create(:authorized_user, telemedicine: true) }

      it "expect return all menus" do
        business = create(:business)
        create(:menu, business:, active: true, position: 1, feature_flag_rule: "telemedicine")
        create(:menu, business:, active: true, position: 2, feature_flag_rule: "gift_card")
        create(:menu, business:, active: true, position: 3, feature_flag_rule: "biometry")

        expect(Navigation.by_products(["telemedicine"]).count).to eq(3)
      end
    end

    context "when authorize_user has no telemedicine" do
      let(:authorized_user) { create(:authorized_user, telemedicine: false) }

      it "expect not returns telemedicine telemedicine" do
        business = create(:business)
        create(:menu, business:, active: true, position: 1, feature_flag_rule: "telemedicine")
        create(:menu, business:, active: true, position: 2, feature_flag_rule: "gift_card")

        expect(Navigation.by_products.count).to eq(1)
        expect(Navigation.by_products.first.feature_flag_rule).to eq("gift_card")
      end
    end
  end

  context "when already exists menu with position nil" do
    context "menu" do
      it "expect create another navigation with position nil" do
        business = create(:business)
        menu = create(:menu, business:, active: true, position: nil, feature_flag_rule: "telemedicine")
        menu_params = attributes_for(:menu).merge(disposition: menu.disposition, business:)

        expect(Navigation.new(menu_params)).to be_valid
      end

      it "expected must be valid when already exists banner with same position" do
        business = create(:business)
        category = build_stubbed(:category)
        create(:banner, :category, business:, active: true, position: 1, metadata: {category_id: category.id})
        menu_params =
          attributes_for(:menu)
            .merge(business:)
            .merge(position: 1)

        expect(Navigation.new(menu_params)).to be_valid
      end
    end
  end

  context "given a business with inactive giftcard" do
    let(:business) { build_stubbed(:business, giftcard: false) }

    it "expect be invalid if destination is /gift_card" do
      menu = build(:menu, business:, active: true, destination: "/gift_card", feature_flag_rule: "gift_card")

      expect(menu).to be_invalid
      expect(menu.errors.as_json)
        .to include(destination: ["/gift_card não é uma configuração ativa para o business"])
    end

    it "expect be valid if destination is /gift_card and inactive" do
      menu = build_stubbed(:menu, business:, active: false, destination: "/gift_card")

      expect(menu).to be_valid
    end
  end

  context "given a business with active giftcard" do
    let(:business) { build_stubbed(:business, giftcard: true) }

    it "expect be invalid if destination is /gift_card" do
      menu = build(:menu, business:, active: true, destination: "/gift_card", feature_flag_rule: "gift_card")

      expect(menu).to be_valid
    end
  end

  context "given a business with inactive cahshback" do
    let(:business) { build_stubbed(:business, prize_draw: false) }

    it "expect be invalid if destination is /prize_draw" do
      menu = build(:menu, business:, active: true, destination: "/prize_draw", feature_flag_rule: "prize_draw")

      expect(menu).to be_invalid
      expect(menu.errors.as_json)
        .to include(destination: ["/prize_draw não é uma configuração ativa para o business"])
    end

    it "expect be invalid if destination is /prize_draw and inactive" do
      menu = build_stubbed(:menu, business:, active: false, destination: "/prize_draw")

      expect(menu).to be_valid
    end
  end

  context "given a business with active prize_draw" do
    let(:business) { build_stubbed(:business, prize_draw: true) }

    it "expect be invalid if destination is /prize_draw" do
      menu = build(:menu, business:, active: true, destination: "/prize_draw", feature_flag_rule: "prize_draw")

      expect(menu).to be_valid
    end
  end

  context "given a business with inactive telemedicine" do
    let(:business) { build_stubbed(:business, telemedicine: false) }

    it "expect be invalid if destination is /telemedicine" do
      menu = build(:menu, business:, active: true, destination: "/telemedicine", feature_flag_rule: "telemedicine")

      expect(menu).to be_invalid
      expect(menu.errors.as_json)
        .to include(destination: ["/telemedicine não é uma configuração ativa para o business"])
    end

    it "expect be invalid if destination is /telemedicine and inactive" do
      menu = build(:menu, active: false)

      expect(menu).to be_valid
    end
  end

  context "given a business with active telemedicine" do
    let(:business) { build_stubbed(:business, telemedicine: true) }

    it "expect be invalid if destination is /telemedicine" do
      menu = build(:menu, business:, active: true, destination: "/telemedicine", feature_flag_rule: "telemedicine")

      expect(menu).to be_valid
    end
  end

  describe "#within_version" do
    context "when menu has min version" do
      let(:menu) { create(:menu, min_version: "3.10.2") }

      context "and app version is nil" do
        let(:app_version) { nil }

        it { expect(Navigation.within_version(app_version)).not_to include menu }
      end

      context "and app version is less than menu version" do
        let(:app_version) { "3.8.12" }

        it { expect(Navigation.within_version(app_version)).not_to include menu }
      end

      context "and app version is equal to menu version" do
        let(:app_version) { "3.10.2" }

        it { expect(Navigation.within_version(app_version)).to include menu }
      end

      context "and app version is greater than menu version" do
        let(:app_version) { "3.11.3" }

        it { expect(Navigation.within_version(app_version)).to include menu }
      end
    end

    context "when menu has max version" do
      let(:menu) { create(:menu, max_version: "4.0.2") }

      context "and app version is nil" do
        let(:app_version) { nil }

        it { expect(Navigation.within_version(app_version)).not_to include menu }
      end

      context "and app version is less than menu version" do
        let(:app_version) { "4.0.0" }

        it { expect(Navigation.within_version(app_version)).to include menu }
      end

      context "and app version is equal to menu version" do
        let(:app_version) { "4.0.2" }

        it { expect(Navigation.within_version(app_version)).to include menu }
      end

      context "and app version is greater than menu version" do
        let(:app_version) { "4.0.3" }

        it { expect(Navigation.within_version(app_version)).not_to include menu }
      end
    end

    context "when menu does not have min version" do
      let(:menu) { create(:menu, min_version: nil) }

      context "and app version is nil" do
        let(:app_version) { nil }

        it { expect(Navigation.within_version(app_version)).to include menu }
      end

      context "and app version is not nil" do
        let(:app_version) { "3.1.12" }

        it { expect(Navigation.within_version(app_version)).to include menu }
      end
    end
  end
end
