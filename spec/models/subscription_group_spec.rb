# frozen_string_literal: true

require "rails_helper"

RSpec.describe SubscriptionGroup, type: :model do
  let!(:business) { create(:business) }
  let!(:subscription_config) { create(:subscription_config, business:) }
  let(:plan) { create(:plan, business:) }

  describe "associations" do
    it "belongs to a subscription_config" do
      association = described_class.reflect_on_association(:subscription_config)
      expect(association.macro).to eq(:belongs_to)
    end

    it "has many plans through subscription_group_plans" do
      association = described_class.reflect_on_association(:plans)
      expect(association.macro).to eq(:has_many)
      expect(association.options[:through]).to eq(:subscription_group_plans)
    end

    it "has many subscription_group_plans" do
      association = described_class.reflect_on_association(:subscription_group_plans)
      expect(association.macro).to eq(:has_many)
    end
  end

  describe "validation of presence of associations" do
    it "name is valid" do
      subscription_group = build(:subscription_group, subscription_config:)
      subscription_group.name = nil
      subscription_group.validate
      expect(subscription_group.errors[:name]).to include("não pode ficar em branco")
    end

    it "is valid with subscription_config" do
      subscription_group = build(:subscription_group, subscription_config:)
      expect(subscription_group).to be_valid
    end

    it "is invalid without subscription_config" do
      subscription_group = build(:subscription_group, subscription_config:)
      subscription_group.subscription_config = nil
      subscription_group.valid?
      expect(subscription_group.errors[:subscription_config]).to include("é obrigatório(a)").or include("can't be blank")
    end
  end

  describe "plans association" do
    it "can have multiple plans" do
      subscription_group = create(:subscription_group, subscription_config:)
      plan1 = create(:plan, business: business)
      plan2 = create(:plan, business: business)
      create(:subscription_group_plan, subscription_group: subscription_group, plan: plan1)
      create(:subscription_group_plan, subscription_group: subscription_group, plan: plan2)

      expect(subscription_group.plans).to include(plan1, plan2)
    end
  end
end
