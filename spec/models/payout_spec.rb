require "rails_helper"

RSpec.describe Payout, type: :model do
  include ActiveSupport::Testing::TimeHelpers

  describe "taxpayer number validation" do
    let!(:user) { create(:user, cpf: "***********") }
    let!(:business) { create(:business, :oab, cnpj: "95655157000161") }

    let(:valid_batch_with_user) do
      build(:payout, user:, user_business: user.business, receiver_taxpayer_number: "***********")
    end
    let(:valid_batch_with_business) do
      build(:payout, business:, receiver_taxpayer_number: "95655157000161")
    end
    let(:invalid_batch) { build(:payout, business:, receiver_taxpayer_number: "***********") }

    it { expect(valid_batch_with_user).to be_valid }
    it { expect(valid_batch_with_business).to be_valid }
    it { expect(invalid_batch).not_to be_valid }
  end

  describe "total amount validation" do
    let!(:cashback_one) do
      create(:cashback_record,
        order_amount: 50,
        total_commission_amount: 5,
        business_spread_amount: 0.5,
        cashback_amount: 4.5)
    end
    let!(:cashback_two) do
      create(:cashback_record,
        order_amount: 500,
        total_commission_amount: 50,
        business_spread_amount: 5,
        cashback_amount: 45)
    end
    let!(:cashback_three) do
      create(:cashback_record,
        order_amount: 50,
        total_commission_amount: 5,
        business_spread_amount: 0.5,
        cashback_amount: 4.5)
    end
    let!(:cashback_four) do
      create(:cashback_record,
        order_amount: 500,
        total_commission_amount: 50,
        business_spread_amount: 5,
        cashback_amount: 45)
    end
    let(:valid_batch) { create(:payout, :with_user) }
    let(:valid_batch_two) { create(:payout, :with_user) }
    let(:valid_batch_three) { create(:payout, :with_user) }
    let(:invalid_batch) { create(:payout, :with_user) }

    before do
      valid_batch.cashback_records = [cashback_one, cashback_two]
      valid_batch.total_amount = 49.5

      invalid_batch.cashback_records = [cashback_three, cashback_four]
      invalid_batch.total_amount = 49.4

      valid_batch_two.status = Payout::Status::CANCELED
      valid_batch_two.reason = "sample reason"
      valid_batch_two.total_amount = 57.34

      valid_batch_three.total_amount = 62.85
    end

    it { expect(valid_batch).to be_valid }
    it { expect(valid_batch_two).to be_valid }
    it { expect(valid_batch_three).to be_valid }
    it { expect(invalid_batch).not_to be_valid }
  end

  describe "business_id + due_on uniqueness validation" do
    let!(:business_one) { create(:business, :oab) }
    let!(:business_two) { create(:business, :publicar) }
    let!(:valid_batch) do
      create(:payout,
        cashback_records: create_list(:transfer_requestable_cashback_record, 2),
        business: business_one,
        receiver_taxpayer_number: business_one.cnpj,
        total_amount: 216.0,
        due_on: "2022-04-05")
    end
    let!(:valid_batch_different_business) do
      build(:payout,
        cashback_records: create_list(:transfer_requestable_cashback_record, 2),
        business: business_two,
        receiver_taxpayer_number: business_two.cnpj,
        total_amount: 216.0,
        due_on: "2022-04-05")
    end
    let!(:valid_batch_different_due_on) do
      build(:payout,
        cashback_records: create_list(:transfer_requestable_cashback_record, 2),
        business: business_one,
        receiver_taxpayer_number: business_one.cnpj,
        total_amount: 216.0,
        due_on: "2022-04-04")
    end
    let!(:invalid_batch) do
      build(:payout,
        cashback_records: create_list(:transfer_requestable_cashback_record, 2),
        business: business_one,
        receiver_taxpayer_number: business_one.cnpj,
        total_amount: 216.0,
        due_on: "2022-04-05")
    end

    it { expect(valid_batch).to be_valid }
    it { expect(valid_batch_different_business).to be_valid }
    it { expect(valid_batch_different_due_on).to be_valid }
    it { expect(invalid_batch).not_to be_valid }
  end

  describe ".create" do
    before do
      travel_to Time.zone.parse("2022-04-01 00:00:01") # friday
    end

    it "creates the cashback batch for business" do
      business = create(:business, :with_cashback, cashback_wallet_destination: Wallet::Kind::MEMBERSHIP)
      frequency = create(:cashback_transfer_frequency, :weekly, business:, interval: 1, day_of_week: 4) # thursday
      cashback_record = create(
        :cashback_record,
        order: create(:order, :online, business:),
        order_amount: 50,
        total_commission_amount: 5,
        business_spread_amount: 0.5,
        cashback_amount: 4.5
      )
      due_on = frequency.next_date

      expect { described_class.create(business:, due_on:, total_amount: 4.5, cashback_records: [cashback_record]) }
        .to change(Payout, :count).by(1)
      batch = Payout.order(:created_at).last
      expect(batch).to be_open
      expect(batch.business).to eq(business)
      expect(batch.receiver_taxpayer_number).to eq(business.cnpj)
      expect(batch.due_on).to eq("2022-04-07".to_date)
      expect(batch.total_amount).to eq(4.5)
      expect(batch.cashback_records).to eq([cashback_record])
    end

    context "when business already has transferred batch for current period" do
      it "does not create cashback batch" do
        business = create(:business, :with_cashback, cashback_wallet_destination: Wallet::Kind::MEMBERSHIP)
        frequency = create(:cashback_transfer_frequency, :weekly, business:, interval: 1, day_of_week: 4) # thursday
        cashback_record = create(
          :cashback_record,
          order: create(:order, :online, business:),
          order_amount: 50,
          total_commission_amount: 5,
          business_spread_amount: 0.5,
          cashback_amount: 4.5
        )
        create(
          :payout,
          :transferred,
          business:,
          receiver_taxpayer_number: business.cnpj,
          total_amount: 0,
          due_on: "2022-04-02".to_date,
          created_at: "2022-03-29 17:01:09",
          locked_at: "2022-03-30 16:33:09",
          transferred_at: "2022-03-30 19:33:12"
        )
        create(
          :payout,
          :transferred,
          business:,
          receiver_taxpayer_number: business.cnpj,
          total_amount: 0,
          due_on: "2022-04-07".to_date,
          created_at: "2022-03-30 17:01:09",
          locked_at: "2022-03-31 16:33:09",
          transferred_at: "2022-03-31 19:33:12"
        )
        due_on = frequency.next_date

        expect { described_class.create(business:, due_on:, total_amount: 4.5, cashback_records: [cashback_record]) }
          .to change(Payout, :count).by(0)
      end
    end
  end

  context "when cashback batch is being canceled" do
    it "validates presence of reason" do
      valid_batch = build(:payout, :canceled, business: create(:business), reason: FFaker::LoremBR.phrase(3))
      invalid_batch = build(:payout, :canceled, business: create(:business), reason: nil)

      expect(valid_batch).to be_valid
      expect(invalid_batch).to be_invalid
      expect(invalid_batch.errors.as_json.keys).to eq([:reason])
    end

    it "validates that batch is cancelable" do
      business = create(:business)

      status_not_changed =
        create(:payout, :canceled, business:, reason: FFaker::LoremBR.phrase(3), due_on: 7.days.from_now)
      valid_old_status = create(:payout, :in_transfer, business:, due_on: 14.days.from_now)
      valid_old_status.status = Payout::Status::CANCELED
      valid_old_status.reason = FFaker::LoremBR.phrase(3)
      not_cancelable_status = [
        Payout::Status::LOCKED,
        Payout::Status::TRANSFERRED
      ].sample
      not_cancelable = create(:payout, business:, status: not_cancelable_status, due_on: 21.days.from_now)
      not_cancelable.status = Payout::Status::CANCELED
      not_cancelable.reason = FFaker::LoremBR.phrase(3)

      expect(status_not_changed).to be_valid
      expect(valid_old_status).to be_valid
      expect(not_cancelable).to be_invalid
    end
  end

  context "#reverse!" do
    let(:business) { create(:business, :with_cashback, currency:, fair_value: 0.03) }
    let(:user) { create(:user, business:) }
    let(:user_wallet) { create(:wallet, :cashback, currency:, user:, balance: 4956) }
    let(:business_wallet) { create(:wallet, :cashback, currency:, business:, balance: 0) }
    let(:outflow_wallet) { create(:wallet, :outflow, :cashback, currency:, balance: 644592) }
    let(:payout) do
      create(
        :payout,
        :failed,
        user:,
        user_business: business,
        reversible: true,
        total_amount: payout_amount,
        currency:
      )
    end

    context "with cashback wallet" do
      let(:currency) { "BRL" }
      let(:payout_amount) { 156.27 }

      it "reverses payout" do
        expect { payout.reverse! }
          .to change { user_wallet.reload.balance }.by(15627)
          .and not_change { business_wallet.reload.balance }
          .and change { outflow_wallet.reload.balance }.by(-15627)
      end

      context "when business does not accept cashback" do
        let(:business) { create(:business, cashback: false, currency:, fair_value: 0.03) }

        it "raises error" do
          expect { payout.reverse! }.to raise_error(Payout::IrreversibleError, I18n.t(".inactive_cashback"))
        end
      end
    end

    context "with point wallet" do
      let(:currency) { "points" }
      let!(:fund_redemption_config) do
        create(:fund_redemption_config, :points, :bank_transfer, business:, fair_value: 0.02, min_amount: 1000, multiple: 100)
      end
      let(:fund_redemption) do
        create(:fund_redemption, :points, :bank_transfer, user:, business:, amount: 1000)
      end
      let(:payout_amount) { 20 }

      it "reverses payout" do
        expect { payout.reverse! }
          .to change { user_wallet.reload.balance }.by(fund_redemption.amount)
          .and not_change { business_wallet.reload.balance }
          .and change { outflow_wallet.reload.balance }.by(-fund_redemption.amount)
      end
    end
  end

  describe "wallet destination filling" do
    let(:business) { create(:business, cashback_wallet_destination: :cashback) }

    shared_examples "wallet destination fillable" do
      it "fills wallet destination based on business info" do
        expect { payout.validate }
          .to change(payout, :wallet_destination).to(business.cashback_wallet_destination)
      end
    end

    context "with user payout" do
      let(:user) { build(:user, business:) }
      let(:payout) { build(:payout, user:, user_business: business, wallet_destination: nil) }

      it_behaves_like "wallet destination fillable"
    end

    context "with business payout" do
      let(:payout) { build(:payout, business:, wallet_destination: nil) }

      it_behaves_like "wallet destination fillable"
    end
  end
end
