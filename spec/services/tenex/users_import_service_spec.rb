require "rails_helper"

RSpec.describe Tenex::UsersImportService do
  let!(:users) do
    {
      "header" => {"limit" => 3, "offset" => 0, "count" => 3},
      "data" => [
        {"status" => 1, "nome" => "User 1",  "cpf" => CPF.new(FFaker::IdentificationBR.cpf).stripped},
        {"status" => 1, "nome" => "User 2",  "cpf" =>  CPF.new(FFaker::IdentificationBR.cpf).stripped},
        {"status" => 1, "nome" => "User 3",  "cpf" =>  CPF.new(FFaker::IdentificationBR.cpf).stripped}
      ]
    }
  end
  let!(:authorized_user_group) { create(:authorized_user_group, business:) }
  let!(:tenex_client) do
    create(:tenex_client,
      business:,
      authorized_user_group:)
  end
  let!(:business) { create(:business) }
  let(:service) { described_class.new(business:) }
  let(:template_mock) { double("template_mock", to_json: "{}") }
  let(:tenex_api_mock) { double("tenex_api_mock", fetch_users: users) }

  before do
    allow(Tenex::Api).to receive(:new).and_return(tenex_api_mock)
    allow(Slack::MessageTemplate::Simple).to receive(:new).and_return(template_mock)
    allow(Slack::Api).to receive(:send_message)
      .with(template_mock.to_json)
  end

  describe "#call" do
    context "when has users" do
      describe "#inactivate_users" do
        let!(:authorized_users_to_inactivate) do
          create_list(:authorized_user, 4, :with_user, authorized_user_group:, active: true, business:)
        end

        it "must inactivate AuthorizedUser on same group based on imported users" do
          expect { service.call }
            .to change(business.authorized_users.where(authorized_user_group:, active: false), :count).by(4)
            .and change(business.users.where(active: false), :count).by(4)
        end

        context "when authorized user is unsyced inactive" do
          before do
            AuthorizedUser.where(authorized_user_group:, active: true)
              .first.update!(active: false)
          end

          it "must ignore on inactivation" do
            expect { service.call }.to change(AuthorizedUser.where(authorized_user_group:, active: false), :count).by(3)
          end
        end
      end

      describe "#save_users" do
        context "with already created authorized users" do
          let!(:auth_user_one) do
            create(
              :authorized_user,
              business:,
              authorized_user_group:,
              cpf: users["data"][0]["cpf"],
              name: "old name 1"
            )
          end
          let!(:auth_user_two) do
            create(
              :authorized_user,
              business:,
              authorized_user_group:,
              cpf: users["data"][1]["cpf"],
              name: "old name 2"
            )
          end

          context "when existing authorized_user is on tenex group" do
            it "updates the existing authorized users" do
              expect do
                service.call

                auth_user_one.reload
                auth_user_two.reload
              end.to change(AuthorizedUser, :count).by(1)
                .and change(auth_user_one, :name).from("Old Name 1").to(Utils::NameNormalizer.call(users["data"][0]["nome"]))
                .and change(auth_user_two, :name).from("Old Name 2").to(Utils::NameNormalizer.call(users["data"][1]["nome"]))
            end
          end

          context "when existing authorized_user is not on tenex group" do
            let!(:auth_user_one) do
              create(
                :authorized_user,
                business:,
                authorized_user_group: nil,
                cpf: users["data"][0]["cpf"],
                name: "old name 2"
              )
            end

            it "must not update authorized user" do
              expect do
                service.call

                auth_user_one.reload
              end.not_to change { auth_user_one }
            end
          end
        end
      end

      describe "#activate_users" do
        let!(:authorized_user_to_reactivate) do
          create(:authorized_user,
            cpf: users["data"][0]["cpf"],
            name: users["data"][0]["nome"],
            business_id: business.id,
            authorized_user_group_id: authorized_user_group.id,
            active: false)
        end

        it "must reactivate AuthorizedUser for imported users" do
          expect do
            service.call
            authorized_user_to_reactivate.reload
          end.to change(authorized_user_to_reactivate, :active).to(true)
        end

        it "must ignore authorized users on another groups of same business if they're active" do
          authorized_user_to_reactivate.update!(active: true, authorized_user_group_id: nil)

          expect do
            service.call
            authorized_user_to_reactivate.reload
          end.not_to change { authorized_user_to_reactivate }
        end

        it "must update inactive authorized users of another related business or groups" do
          related_business = create(:business, main_business: business)
          authorized_user_to_reactivate.update!(active: false, authorized_user_group_id: nil, business_id: related_business.id)

          expect do
            service.call
            authorized_user_to_reactivate.reload
          end.to change(authorized_user_to_reactivate, :active).to(true)
            .and change(authorized_user_to_reactivate, :authorized_user_group).to(authorized_user_group)
        end
      end
    end

    context "when has not users" do
      let(:users) do
        {
          "header" => {"limit" => 3, "offset" => 0, "count" => 0},
          "data" => []
        }
      end

      it "notify in slack an integration error" do
        service.call
        expect(Slack::Api).to have_received(:send_message).once
      end
    end
  end
end
