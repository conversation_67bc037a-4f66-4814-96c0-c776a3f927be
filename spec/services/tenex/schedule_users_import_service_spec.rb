require "rails_helper"

RSpec.describe Tenex::ScheduleUsersImportService do
  let!(:business) { create(:business, :tenex_business) }
  let!(:business_two) { create(:business, :tenex_business) }
  let!(:business_not_tenex) { create(:business) }
  let!(:inactive_business) { create(:business, :tenex_business, status: Business::Status::SUSPENDED_BY_OVERDUE) }

  before do
    allow(Tenex::UsersImportWorker).to receive(:perform_async)
  end

  describe "#call" do
    it "calls users import worker for ixc businesses" do
      described_class.new.call

      expect(Tenex::UsersImportWorker).to have_received(:perform_async).twice
      expect(Tenex::UsersImportWorker).to have_received(:perform_async).with(business.id)
      expect(Tenex::UsersImportWorker).to have_received(:perform_async).with(business_two.id)
      expect(Tenex::UsersImportWorker).not_to have_received(:perform_async).with(business_not_tenex.id)
      expect(Tenex::UsersImportWorker).not_to have_received(:perform_async).with(inactive_business.id)
    end
  end
end
