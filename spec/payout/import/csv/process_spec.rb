# frozen_string_literal: true

require "rails_helper"

RSpec.describe ImportFile::ProcessWorker do
  let!(:business) do
    create(
      :business,
      :oab,
      cashback: true,
      cashback_wallet_destination: :membership,
      cnpj: "03814381000130",
      create_project_config: false
    )
  end
  let!(:project_config) { create(:project_config, business:) }
  let!(:client_employee) { create(:client_employee, businesses: [business]) }
  let!(:business_user) { create(:user, business:) }

  let!(:user_one) { create(:user, :with_cashback, business:, cpf: "***********") }
  let!(:payout_one) do
    business.update_column :cashback_wallet_destination, Wallet::Kind::CASHBACK

    payout = create(:payout,
      user: user_one,
      user_business: business,
      receiver_taxpayer_number: "***********",
      total_amount: 23.91,
      status: Payout::Status::LOCKED,
      created_at: "2022-04-01 16:33:09",
      locked_at: Time.zone.parse("2022-04-08 19:32:51"),
      lock_code: "IWG15B0XAYEFMZJQ")

    business.update_column :cashback_wallet_destination, Wallet::Kind::MEMBERSHIP

    payout
  end

  let!(:orders_two) { create_list(:order, 3, :online, user: business_user, cashback_type: :percent, cashback_value: 10) }
  let!(:cashback_record_two) do
    create(:cashback_record,
      order: orders_two[0],
      user: business_user,
      business:,
      transaction_status: Enums::CashbackRecordStatus::LOCKED,
      order_amount: 10000,
      total_commission_amount: 1000,
      business_spread_amount: 100,
      cashback_amount: 900,
      created_at: "2022-02-20 10:01:16")
  end
  let!(:cashback_record_three) do
    create(:cashback_record,
      order: orders_two[1],
      user: business_user,
      business:,
      transaction_status: Enums::CashbackRecordStatus::LOCKED,
      order_amount: 1176.33,
      total_commission_amount: 117.63,
      business_spread_amount: 11.76,
      cashback_amount: 105.87,
      created_at: "2022-02-26 18:54:31")
  end
  let!(:payout_two) do
    create(:payout,
      business:,
      receiver_taxpayer_number: "03814381000130",
      total_amount: 1005.87,
      cashback_records: [cashback_record_two, cashback_record_three],
      status: Payout::Status::LOCKED,
      created_at: "2022-04-02 17:04:12",
      due_on: "2022-04-01",
      locked_at: Time.zone.parse("2022-04-08 19:32:51"),
      lock_code: "IWG15B0XAYEFMZJQ")
  end

  let!(:user_three) { create(:user, business:, cpf: "***********") }
  let!(:payout_three) do
    business.update_column :cashback_wallet_destination, Wallet::Kind::CASHBACK

    payout = create(:payout,
      :locked,
      user: user_three,
      user_business: business,
      receiver_taxpayer_number: "***********",
      total_amount: 20.01,
      created_at: "2022-04-05 12:14:23",
      locked_at: Time.zone.parse("2022-04-08 19:32:51"),
      lock_code: "IWG15B0XAYEFMZJQ")

    business.update_column :cashback_wallet_destination, Wallet::Kind::MEMBERSHIP

    payout
  end

  let!(:user_four) { create(:user, business:, cpf: "***********") }
  let!(:payout_four) do
    business.update_column :cashback_wallet_destination, Wallet::Kind::CASHBACK

    payout = create(:payout,
      user: user_four,
      user_business: business,
      receiver_taxpayer_number: "***********",
      total_amount: 20.01,
      status: Payout::Status::IN_TRANSFER,
      created_at: "2022-04-05 12:14:23")

    business.update_column :cashback_wallet_destination, Wallet::Kind::MEMBERSHIP

    payout
  end

  let!(:business_to_fail) { create(:business, :publicar, :with_cashback, cnpj: "70592246000185") }
  let!(:client_employee_to_fail) { create(:client_employee, businesses: [business_to_fail]) }
  let!(:user_to_fail) { create(:user, business: business_to_fail) }
  let!(:order_to_fail) { create(:order, :online, user: user_to_fail, cashback_type: :percent, cashback_value: 10) }

  let!(:cashback_record_to_fail) do
    create(:cashback_record,
      order: order_to_fail,
      user: user_to_fail,
      transaction_status: Enums::CashbackRecordStatus::LOCKED,
      order_amount: 10000,
      total_commission_amount: 1000,
      business_spread_amount: 100,
      cashback_amount: 900)
  end
  let!(:payout_to_fail) do
    create(:payout,
      business: business_to_fail,
      receiver_taxpayer_number: "70592246000185",
      total_amount: 900,
      cashback_records: [cashback_record_to_fail],
      status: Payout::Status::LOCKED,
      created_at: "2022-04-02 17:04:12",
      due_on: "2022-04-01",
      locked_at: Time.zone.parse("2022-04-08 19:32:51"),
      lock_code: "IWG15B0XAYEFMZJQ")
  end

  let(:file) { Rails.root.join("spec/fixtures/payouts/process.csv").open }
  let(:import_file) do
    create(
      :import_file,
      file:,
      kind: ImportFile::Kind::PAYOUT,
      metadata: {lock_code: "IWG15B0XAYEFMZJQ"}
    )
  end
  let(:process) do
    ImportFile::ProcessWorker.new.perform(
      FFaker::Internet.email,
      DateTime.current.to_s,
      import_file.id
    )
  end

  it "processes the payouts and its records correctly regardless of how many times the job is called concurrently" do
    amount = payout_two.total_amount.in_cents

    inflow_membership_wallet = create(:wallet, :inflow, :membership, balance: 1985003)
    user_membership_wallet = create(:wallet, :membership, user: business_user, balance: 219)
    project_membership_wallet = create(:wallet, :membership, business:, balance: 46841)

    outflow_cashback_wallet = create(:wallet, :outflow, :cashback, kind: Wallet::Kind::CASHBACK, balance: 1854230)
    user_cashback_wallet = create(:wallet, :cashback, kind: Wallet::Kind::CASHBACK, user: user_three, balance: 1952)

    Array.new(10) do
      Thread.new do
        ActiveRecord::Base.connection_pool.with_connection do
          ImportFile::ProcessWorker.new.perform(FFaker::Internet.email, DateTime.current.to_s, import_file.id)
        end
      end
    end.each(&:join)

    expect(user_membership_wallet.reload.balance).to eq(219 + amount)
    expect(project_membership_wallet.reload.balance).to eq(46841)
    expect(inflow_membership_wallet.reload.balance).to eq(1985003)
    expect(outflow_cashback_wallet.reload.balance).to eq(1854230 - payout_three.total_amount.in_cents)
    expect(user_cashback_wallet.reload.balance).to eq(1952 + payout_three.total_amount.in_cents)

    import_file.reload
    expect(import_file).to be_done
    expect(import_file.amount_rows_processed_successfully).to eq(4)
    expect(import_file.amount_rows_with_errors).to eq(0)
    expect(import_file.import_file_errors).to be_empty

    [payout_one, payout_two, payout_three, payout_to_fail].each(&:reload)

    expect(payout_one).to be_transferred
    expect(payout_one.total_amount).to eq(23.91)
    expect(payout_one.receiver_taxpayer_number).to eq("***********")
    expect(payout_one.transferred_at.strftime("%Y-%m-%d %H:%M:%S")).to eq("2022-04-06 12:19:40")

    expect(payout_two).to be_transferred
    expect(payout_two.total_amount).to eq(1005.87)
    expect(payout_two.receiver_taxpayer_number).to eq("03814381000130")
    expect(payout_two.transferred_at.strftime("%Y-%m-%d %H:%M:%S")).to eq("2022-04-06 12:15:20")
    expect(payout_two.cashback_records).to all be_available

    expect(payout_three).to be_failed
    expect(payout_three.total_amount).to eq(20.01)
    expect(payout_three.receiver_taxpayer_number).to eq("***********")
    expect(payout_three.transferred_at.strftime("%Y-%m-%d %H:%M:%S")).to eq("2022-04-06 12:14:43")

    expect(payout_to_fail).to be_failed
    expect(payout_to_fail.total_amount).to eq(900)
    expect(payout_to_fail.receiver_taxpayer_number).to eq("70592246000185")
    expect(payout_to_fail.transferred_at).to be_nil
    expect(payout_to_fail.cashback_records).to all be_in_transfer_lecupon
  end

  context "with wrong headers" do
    let(:file) { Rails.root.join("spec/fixtures/payouts/process_without_headers.csv").open }

    it "does not process the payouts" do
      expect { process }.to raise_error(ActiveRecord::RecordInvalid, /Cabeçalho inválido/)

      expect(payout_one.reload).to be_locked
      expect(payout_two.reload).to be_locked
      expect(payout_three.reload).to be_locked
    end
  end

  context "with missing data in file" do
    let(:file) { Rails.root.join("spec/fixtures/payouts/process_missing_data.csv").open }

    it "does not process the payouts or its records" do
      outflow_cashback_wallet = create(:wallet, :outflow, :cashback, kind: Wallet::Kind::CASHBACK, balance: 1854230)
      user_cashback_wallet = create(:wallet, :cashback, kind: Wallet::Kind::CASHBACK, user: user_three, balance: 1952)

      expect { process }
        .to change { outflow_cashback_wallet.reload.balance }.by(-payout_three.total_amount.in_cents)
        .and change { user_cashback_wallet.reload.balance }.by(payout_three.total_amount.in_cents)

      import_file.reload
      expect(import_file).to be_done
      expect(import_file.amount_rows_processed_successfully).to eq(2)
      expect(import_file.amount_rows_with_errors).to eq(1)
      errors = import_file.import_file_errors.map { [_1.line, _1.details] }
      expect(errors).to match_array([
        [3, "CPF/CNPJ não pode ficar em branco, Remessa não pode ficar em branco"]
      ])

      [payout_one, payout_two, payout_three].each(&:reload)

      expect(payout_one).to be_transferred
      expect(payout_one.total_amount).to eq(23.91)
      expect(payout_one.receiver_taxpayer_number).to eq("***********")
      expect(payout_one.transferred_at.strftime("%Y-%m-%d %H:%M:%S")).to eq("2022-04-06 12:19:40")

      expect(payout_two).to be_locked
      expect(payout_two.total_amount).to eq(1005.87)
      expect(payout_two.receiver_taxpayer_number).to eq("03814381000130")
      expect(payout_two.transferred_at).to be_nil
      expect(payout_two.cashback_records).to all be_locked

      expect(payout_three).to be_failed
      expect(payout_three.total_amount).to eq(20.01)
      expect(payout_three.receiver_taxpayer_number).to eq("***********")
      expect(payout_three.transferred_at.strftime("%Y-%m-%d %H:%M:%S")).to eq("2022-04-06 12:14:43")
    end
  end

  context "with unexisting payouts" do
    let!(:payout_two) {}

    it "only processes the existing payouts" do
      outflow_cashback_wallet = create(:wallet, :outflow, :cashback, kind: Wallet::Kind::CASHBACK, balance: 1854230)
      user_cashback_wallet = create(:wallet, :cashback, kind: Wallet::Kind::CASHBACK, user: user_three, balance: 1952)

      expect { process }
        .to change { outflow_cashback_wallet.reload.balance }.by(-payout_three.total_amount.in_cents)
        .and change { user_cashback_wallet.reload.balance }.by(payout_three.total_amount.in_cents)

      import_file.reload
      expect(import_file).to be_done
      expect(import_file.amount_rows_processed_successfully).to eq(3)
      expect(import_file.amount_rows_with_errors).to eq(1)
      errors = import_file.import_file_errors.map { [_1.line, _1.details] }
      expect(errors).to match_array([
        [3, "Remessa não pode ficar em branco"]
      ])

      [payout_one, payout_three].each(&:reload)

      expect(payout_one).to be_transferred
      expect(payout_one.total_amount).to eq(23.91)
      expect(payout_one.receiver_taxpayer_number).to eq("***********")
      expect(payout_one.transferred_at.strftime("%Y-%m-%d %H:%M:%S")).to eq("2022-04-06 12:19:40")

      expect(payout_three).to be_failed
      expect(payout_three.total_amount).to eq(20.01)
      expect(payout_three.receiver_taxpayer_number).to eq("***********")
      expect(payout_three.transferred_at.strftime("%Y-%m-%d %H:%M:%S")).to eq("2022-04-06 12:14:43")
    end
  end

  context "with wrong status" do
    let(:file) do
      file = Tempfile.new(["transfer", ".csv"], "tmp")
      CSV.open(file, "wb", headers: true) do |csv|
        csv << %w[status chave valor data]
        csv << ["OUTRO STATUS", "***********", 23.91, "06/04/2022 12:19:40"]
      end
      file
    end

    after { file.close }

    it "does not process the payouts" do
      expect { process }
        .to not_change { payout_one.reload.status }
        .and not_change { payout_two.reload.status }
        .and not_change { payout_three.reload.status }

      import_file.reload
      expect(import_file).to be_done
      expect(import_file.amount_rows_processed_successfully).to eq(0)
      expect(import_file.amount_rows_with_errors).to eq(1)
      expect(import_file.import_file_errors.pluck(:line, :details)).to match_array([
        [2, "Status não está incluído na lista"]
      ])
    end
  end
end
