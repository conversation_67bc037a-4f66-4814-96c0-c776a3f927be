# frozen_string_literal: true

require "rails_helper"

RSpec.describe CustomAuth::IxcAuth do
  let!(:main_business) { create(:business, cnpj: "11113389000106") }

  let!(:first_subbusiness) { create(:business, main_business:) }
  let!(:second_subbusiness) { create(:business, main_business:) }
  let!(:third_subbusiness) { create(:business, main_business:) }

  let!(:first_group) { create(:authorized_user_group, business: first_subbusiness) }
  let!(:second_group) { create(:authorized_user_group, business: second_subbusiness) }
  let!(:third_group) { create(:authorized_user_group, business: third_subbusiness) }

  let!(:first_ixc_token) { create(:ixc_token, business: first_subbusiness, authorized_user_group: first_group) }
  let!(:second_ixc_token) { create(:ixc_token, business: second_subbusiness, authorized_user_group: second_group) }
  let!(:third_ixc_token) { create(:ixc_token, business: third_subbusiness, authorized_user_group: third_group) }

  let(:stubbed_ixc_sign_in) { double(Ixc::UserSignIn) }
  let(:stubbed_invalid_ixc_sign_in) { double(Ixc::UserSignIn) }

  let(:username) { "***********" }
  let(:valid_password) { "gwt@22#" }
  let(:invalid_password) { "invalid_password" }

  let(:ixc_sign_in_data) do
    {
      name: "John Doe",
      email: "<EMAIL>",
      cpf: username,
      password: valid_password
    }
  end

  let(:businesses) { [first_subbusiness, second_subbusiness, third_subbusiness] }

  before do
    main_business.project_config.update!(user_manager: Enums::UserManager::NO_SIGNUP)
    allow(Ixc::UserSignIn).to receive(:new)
      .with(cpf: anything, password: anything, ixc_token: first_ixc_token)
      .and_return(stubbed_invalid_ixc_sign_in)
    allow(Ixc::UserSignIn).to receive(:new)
      .with(cpf: anything, password: anything, ixc_token: second_ixc_token)
      .and_return(stubbed_invalid_ixc_sign_in)
    allow(Ixc::UserSignIn).to receive(:new)
      .with(cpf: anything, password: anything, ixc_token: third_ixc_token)
      .and_return(stubbed_ixc_sign_in)

    allow(stubbed_ixc_sign_in).to receive(:call).and_return(ixc_sign_in_data)
    allow(stubbed_invalid_ixc_sign_in).to receive(:call).and_return(nil)
  end

  describe "User does not exist" do
    context "and has valid contract in any of the subbusinesses" do
      it "invokes create user service on this subbusiness" do
        response = described_class.new(cpf: username, password: valid_password, businesses:).call

        expect(response).to be_a(CustomAuth::Response)
        expect(response.success).to be_truthy
        expect(response.message).to eq(I18n.t("devise.sessions.signed_in"))

        expect(stubbed_invalid_ixc_sign_in).to have_received(:call).twice
        expect(stubbed_ixc_sign_in).to have_received(:call).once

        expect(response.user_params[:name]).to eq(Utils::NameNormalizer.call(ixc_sign_in_data[:name]))
        expect(response.user_params[:email]).to eq(ixc_sign_in_data[:email])
        expect(response.user_params[:cpf]).to eq(ixc_sign_in_data[:cpf])
        expect(response.user_params[:password]).to eq(ixc_sign_in_data[:password])

        expect(response.user_params[:business_id]).to eq(third_subbusiness.id)
        expect(response.user_params[:default_auth_flow]).to eq(false)
      end

      it "must stop requesting to ixc api after first success" do
        allow(Ixc::UserSignIn).to receive(:new)
          .with(cpf: anything, password: anything, ixc_token: first_ixc_token)
          .and_return(stubbed_ixc_sign_in)

        described_class.new(cpf: username, password: valid_password, businesses:).call

        expect(stubbed_invalid_ixc_sign_in).not_to have_received(:call)
        expect(stubbed_ixc_sign_in).to have_received(:call).once
      end
    end

    context "and has invalid contract and password in all subbusinesses" do
      before do
        allow(Ixc::UserSignIn).to receive(:new)
          .with(cpf: anything, password: anything, ixc_token: third_ixc_token)
          .and_return(stubbed_invalid_ixc_sign_in)
      end

      it "must be unsuccessfull" do
        response = described_class.new(cpf: username, password: valid_password, businesses:).call

        expect(response).to be_a(CustomAuth::Response)
        expect(response.success).to be_falsey
        expect(response.message).to eq(I18n.t("devise.failure.not_found_in_database", authentication_keys: "CPF"))

        expect(User.count).to eq(0)
      end
    end
  end
end
