# frozen_string_literal: true

require "rails_helper"

RSpec.describe Ixc::ContractUsers, :vcr do
  let(:ixc_token) { create(:ixc_token) }
  let(:ixc_api) { Ixc::Api.new(token: ixc_token.token, base_url: ixc_token.base_url) }
  let(:allowed_contracts_status) { ixc_token.allowed_contracts_status.split(",") }

  let!(:contracts_page_1) { ixc_api.contracts(page: 1, allowed_contracts_status:) }
  let!(:contracts_page_2) { ixc_api.contracts(page: 2, allowed_contracts_status:) }

  let(:mocked_contracts_per_page) { 4 }

  before do
    allow(Ixc::Api).to receive(:new).and_return(ixc_api)
    allow(ixc_api).to receive(:contracts).with(page: 1, allowed_contracts_status: anything).and_return(contracts_page_1)
    allow(ixc_api).to receive(:contracts).with(page: 2, allowed_contracts_status: anything).and_return(contracts_page_2)
    allow(ixc_api).to receive(:contracts).with(page: 3, allowed_contracts_status: anything).and_return({})
  end

  describe "#list_all" do
    let(:all_contracts_hash) do
      contracts_page_1["registros"] +
        contracts_page_2["registros"]
    end

    it "must retrieve all users id with active contract" do
      contracts_users =
        described_class
          .new(ixc_token:)
          .list_all

      all_contracts_users_ids = all_contracts_hash
        .select { |contract| contract["status_internet"].in?(allowed_contracts_status) }
        .reduce({}) { |hash, value| hash.merge!(value["id_cliente"].to_s => true) }
      expect(contracts_users).to eq(all_contracts_users_ids)
      expect(contracts_users.count).to be == mocked_contracts_per_page * 2
    end

    context "when with allowed_contracts filter" do
      let!(:ixc_contracts) { create(:ixc_contract, ixc_token:, external_id: "70") }
      let(:users_with_allowed_contracts_count) do
        all_contracts_hash.count { |contract| contract["id_vd_contrato"] == "70" }
      end

      it "must return only contract users within allowed contracts" do
        contracts_users =
          described_class.new(
            ixc_token:
          ).list_all
        expect(contracts_users.count).to eq(users_with_allowed_contracts_count)
      end
    end

    context "when with product filter", :vcr do
      let(:ixc_token) { create(:ixc_token, product_id: 47, base_url: "https://ixc.slimnet.com.br/adm.php", token: "2:e6a2f057efd66553ea72994c67bf92a20b3671dd3516232cab6423fe732f1492") }

      it "must return only contract users within allowed contracts" do
        contracts_users =
          described_class.new(
            ixc_token:
          ).list_all

        expect(contracts_users.count).to eq(4)
      end

      context "when product contract list return empty" do
        it "must return no contract users" do
          contracts_users =
            described_class.new(
              ixc_token:
            ).list_all

          expect(contracts_users.count).to eq(0)
        end
      end
    end
  end
end
