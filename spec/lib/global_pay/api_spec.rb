require "rails_helper"

RSpec.describe GlobalPay::Api, :vcr do
  let(:api) { described_class.new }

  describe "#payment" do
    let(:uuid) { "fc10b881-d9a0-4ab1-a6fd-a102db188f49" }
    let(:address) do
      GlobalPay::Payload::Address.new(
        street: "Rua Jose Lacerda", number: 55, complement: "APT 104 BL 02",
        neighborhood: "Trevo", city: "Belo Horizonte", state: "MG",
        zip_code: "45810000", country: "BR"
      )
    end
    let(:customer) do
      GlobalPay::Payload::Customer.new(
        name: "ADRIANO PESSOA SOUZA", document_type: "PF", document_number: "90936034009",
        email: "<EMAIL>", phone_number: "31999855214", cell_phone_number: "31999855214",
        address: address
      )
    end
    let(:card) do
      GlobalPay::Payload::Card.new(
        seller_id: "4195317", number: "****************", holder_name: "ADRIANO P SOUZA",
        cvv: 321, expiration_month: 12, expiration_year: 25
      )
    end
    let(:items) do
      [GlobalPay::Payload::Item.new(unit_price: 50.21, product_name: "GIFT CARD", quantity: 1)]
    end
    let(:device_info) do
      GlobalPay::Payload::DeviceInfo.new(
        ip_address: "*************",
        http_accept_browser_value: "*/*",
        http_accept_content: "*/*",
        http_browser_language: "pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3",
        http_browser_java_enabled: "N",
        http_browser_javascript_enabled: "true",
        http_browser_color_depth: "24",
        http_browser_screen_height: "969",
        http_browser_screen_width: "1920",
        http_browser_time_difference: "240",
        user_agent_browser_value: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
      )
    end
    let(:payload) do
      GlobalPay::PaymentRequest.with_card(card: card).request(
        amount: 50.21,
        antifraud_code: uuid,
        customer: customer,
        items: items,
        device_info: device_info
      )
    end

    context "when the payment is successful" do
      it "returns the response body" do
        response = api.payment(payload)
        expect(response.status).to be(201)
        expect(response.json).to be_present
      end
    end

    context "when card is expired" do
      let(:card) do
        GlobalPay::Payload::Card.new(
          seller_id: "4195317", number: "****************", holder_name: "ADRIANO P SOUZA",
          cvv: 321, expiration_month: "09", expiration_year: "24"
        )
      end

      it "raises error with proper message" do
        response = api.payment(payload)
        expect(response.error_message).to eq("O cartão está vencido.")
      end
    end

    context "when card number is invalid and error is returned inside card.number attribute" do
      let(:card) do
        GlobalPay::Payload::Card.new(
          seller_id: "4195317", number: "************5500", holder_name: "ADRIANO P SOUZA",
          cvv: 321, expiration_month: "11", expiration_year: "25"
        )
      end

      it "raises error with proper message" do
        response = api.payment(payload)
        expect(response.error_message).to eq("Cartão inválido")
      end
    end

    context "when card number is invalid and error is returned inside number attribute" do
      let(:card) do
        GlobalPay::Payload::Card.new(
          seller_id: "4195317", number: "************5501", holder_name: "ADRIANO P SOUZA",
          cvv: 321, expiration_month: "11", expiration_year: "25"
        )
      end

      it "raises error with proper message" do
        response = api.payment(payload)
        expect(response.error_message).to eq("Cartão inválido")
      end
    end

    context "when mapped error occurs" do
      let(:card) do
        GlobalPay::Payload::Card.new(
          seller_id: "4195317", number: "************5500", holder_name: "ADRIANO P SOUZA",
          cvv: 321, expiration_month: "11", expiration_year: "25"
        )
      end

      it "raises error with proper message" do
        response = api.payment(payload)
        expect(response.error_message).to eq("Cartão suspenso")
      end
    end

    context "when unmapped error occurs" do
      let(:card) do
        GlobalPay::Payload::Card.new(
          seller_id: "4195317", number: "************5500", holder_name: "ADRIANO P SOUZA",
          cvv: 321, expiration_month: "11", expiration_year: "25"
        )
      end

      it "raises error with default message" do
        response = api.payment(payload)
        expect(response.error_message).to eq("Não foi possível completar o pagamento")
      end
    end
  end

  describe "#store_card" do
    context "when storing card is successful" do
      let(:store_card) do
        GlobalPay::Payload::Card.new(
          seller_id: "4195317",
          number: "****************",
          holder_name: "ADRIANO P SOUZA",
          cvv: "321",
          expiration_month: "12",
          expiration_year: "25",
          brand: "visa"
        )
      end

      it "stores a card" do
        response = api.store_card(store_card)
        expect(response.json.dig("data", "storeCardId")).to eq("a6c112b4-b9d2-4b44-9c51-d6fb276706b4")
      end
    end

    context "when card is expired" do
      let(:store_card) do
        GlobalPay::Payload::Card.new(
          seller_id: "4195317",
          number: "****************",
          holder_name: "ADRIANO P SOUZA",
          cvv: "321",
          expiration_month: "09",
          expiration_year: "24",
          brand: "mastercard"
        )
      end

      it "raises CardExpiredError" do
        response = api.store_card(store_card)
        expect(response.error_message).to eq("O cartão está vencido.")
      end
    end

    context "when card number is invalid" do
      let(:store_card) do
        GlobalPay::Payload::Card.new(
          seller_id: "4195317",
          number: "************5500",
          holder_name: "ADRIANO P SOUZA",
          cvv: "123",
          expiration_month: "11",
          expiration_year: "25",
          brand: "elo"
        )
      end

      it "raises InvalidCardNumberError" do
        response = api.store_card(store_card)
        expect(response.error_message).to eq("Número do cartão inválido.")
      end
    end

    context "when card security code is invalid" do
      let(:store_card) do
        GlobalPay::Payload::Card.new(
          seller_id: "4195317",
          number: "****************",
          holder_name: "ADRIANO P SOUZA",
          cvv: "12",
          expiration_month: "11",
          expiration_year: "25",
          brand: "elo"
        )
      end

      it "raises InvalidSecurityCodeError" do
        response = api.store_card(store_card)
        expect(response.error_message).to eq("CVV inválido.")
      end
    end

    context "when a generic bad request error occurs" do
      let(:store_card) do
        GlobalPay::Payload::Card.new(
          seller_id: "4195317",
          number: "****************",
          holder_name: "ADRIANO P SOUZA",
          cvv: "123",
          expiration_month: "11",
          expiration_year: "77",
          brand: "123"
        )
      end

      it "raises BadRequestError" do
        response = api.store_card(store_card)
        expect(response.error_message).to eq("Bandeira inexistente.")
      end
    end
  end
end
