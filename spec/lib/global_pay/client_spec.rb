require "rails_helper"

RSpec.describe GlobalPay::Client, :vcr do
  let(:mock_api) { instance_double(GlobalPay::Api) }
  let(:client) { described_class.new }
  let(:uuid) { "fc10b881-d9a0-4ab1-a6fd-a102db188f49" }
  let(:address) do
    GlobalPay::Payload::Address.new(
      street: "Rua Jose Lacerda", number: 55, complement: "APT 104 BL 02",
      neighborhood: "Trevo", city: "Belo Horizonte", state: "MG",
      zip_code: "45810000", country: "BR"
    )
  end
  let(:customer) do
    GlobalPay::Payload::Customer.new(
      name: "ADRIANO PESSOA SOUZA", document_type: "PF", document_number: "90936034009",
      email: "<EMAIL>", phone_number: "31999855214", cell_phone_number: "31999855214",
      address: address
    )
  end
  let(:card) do
    GlobalPay::Payload::Card.new(
      seller_id: "4195317", number: "****************", holder_name: "ADRIANO P SOUZA",
      cvv: 321, expiration_month: 12, expiration_year: 25
    )
  end
  let(:items) do
    [GlobalPay::Payload::Item.new(unit_price: 50.21, product_name: "GIFT CARD", quantity: 1)]
  end
  let(:device_info) do
    GlobalPay::Payload::DeviceInfo.new(
      ip_address: "*************",
      http_accept_browser_value: "*/*",
      http_accept_content: "*/*",
      http_browser_language: "pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3",
      http_browser_java_enabled: "N",
      http_browser_javascript_enabled: "true",
      http_browser_color_depth: "24",
      http_browser_screen_height: "969",
      http_browser_screen_width: "1920",
      http_browser_time_difference: "240",
      user_agent_browser_value: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    )
  end
  let(:payment_request) do
    GlobalPay::PaymentRequest.with_card(card: card).request(
      amount: 50.21,
      antifraud_code: uuid,
      customer: customer,
      items: items,
      device_info: device_info
    )
  end

  let(:api_response) do
    GlobalPay::ApiResponse.new(status: 201, json: {
      "status" => 201,
      "message" => "Pay Credit Card",
      "data" => {
        "orderCode" => "561cb534a5304",
        "paymentId" => "020091487401111734280002361881640000000000",
        "authorizationCode" => "955726",
        "statusText" => "PAID",
        "amount" => "50.21",
        "gateway" => {
          "paymentAuthorization" => {
            "returnCode" => "0",
            "description" => "Sucesso",
            "paymentId" => "020091487401111734280002361881640000000000",
            "authorizationCode" => "955726",
            "orderNumber" => "561cb534a5304",
            "amount" => 5021,
            "releaseAt" => "2025-01-11T14:34:28.2605125-03:00"
          }
        }
      },
      "id" => "4195317",
      "companyName" => "LECUPON S.A.",
      "documentNumber" => "26989697000169"
    })
  end

  describe "#payment" do
    before do
      allow(GlobalPay::Api).to receive(:new).and_return(mock_api)
      allow(mock_api).to receive(:payment).with(payment_request).and_return(api_response)
    end

    it "returns a PaymentResponse object with the correct attributes" do
      response = client.payment(payment_request)

      expect(response).to be_a(GlobalPay::PaymentResponse)
      expect(response.order_code).to eq("561cb534a5304")
    end
  end
end
