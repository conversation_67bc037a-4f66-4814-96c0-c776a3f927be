# frozen_string_literal: true

require "rails_helper"

RSpec.describe CustomSso::Aasp::Auth, :vcr do
  let!(:business) { create(:business, cnpj: "62500855000139") }
  let(:jwt) { "eyJhbGciOiJSUzI1NiIsImtpZCI6IjFFQ0FBODcwOUE2OTYzNEJCQUVCMzgxN0RGQTA0QjhGN0VGQzIwREEiLCJ0eXAiOiJKV1QiLCJ4NXQiOiJIc3FvY0pwcFkwdTY2emdYMzZCTGozNzhJTm8ifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XWcRISsF4-kdVKzaEh9gxlAu2I66pt6EAblIO1L8E7FNB0AgHJsbDHKMVRcigavcnuLOtvTOqHJ6lIKCW4uXHSuw09ybLO-pPEeX3HLXQVaryEz_e1CNNTvTPmy10m9ybYNLMIjyzSDOk-hlf92gstNd7kJ8UP4YRjfPBdTOswk3xXMmwsVMpEtClGejCbPTyYwhTDYVQU8CaRgttw5eNxDaDkXyejij_EMYusj7pCH0BTNOf3USZQaKc4jDmnXS0CMG4TtcH-wUL3WSpitKO1TKa1gbpn-FeYwdo7KvgR1P5qJB6stnqBJlUcOFOBmKz6h2NFtj_VKjOq5_WFj6VA" }
  let(:decoded_jwt) { CustomSso::Aasp::Decode.decode(access_token: jwt) }

  context "when jwt token is not valid" do
    let(:params) { {access_token: "invalid_jwt"} }

    it "must return unprocessable" do
      response = described_class.new(params).call
      expect(response.success).to eq(false)
      expect(response.user).to eq(nil)
      expect(response.message).to eq("JWT Invalido")
    end
  end

  context "when jwt token is valid" do
    let(:params) { {access_token: jwt} }

    context "when Associado is not valid" do
      before do
        allow(Base64).to receive(:urlsafe_decode64).and_wrap_original do |method, args|
          json = JSON.parse(method.call(args))
          json["Associado"] = "N"
          json["StatusAssociado"] = "1"
          json.to_json
        end
      end

      it "must return unauthorized" do
        response = described_class.new(params).call
        expect(response.success).to eq(false)
        expect(response.user).to eq(nil)
        expect(response.message).to eq("Nao autorizado")
      end
    end

    context "when StatusAssociado is not valid" do
      before do
        allow(Base64).to receive(:urlsafe_decode64).and_wrap_original do |method, args|
          json = JSON.parse(method.call(args))
          json["Associado"] = "S"
          json["StatusAssociado"] = "0"
          json.to_json
        end
      end

      it "must return unauthorized" do
        response = described_class.new(params).call
        expect(response.success).to eq(false)
        expect(response.user).to eq(nil)
        expect(response.message).to eq("Nao autorizado")
      end
    end

    context "when Associado status is true" do
      context "when user with external id exists on business" do
        let!(:user) { create(:user, business:, external_id: "5257822") }

        it "must return succesfull with existing user" do
          response = described_class.new(params).call
          expect(response.success).to eq(true)
          expect(response.user).to eq(user)
        end
      end

      context "when user with external id does not exist on business" do
        context "when params has all info to sign up" do
          let(:params) do
            {
              access_token: jwt,
              cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
              cellphone: "***********"
            }
          end

          it "must return succesfull and create user" do
            expect do
              response = described_class.new(params).call
              expect(response.success).to eq(true)
            end.to change(User, :count).by(1)

            user = User.last
            expect(user.external_id).to eq(decoded_jwt["user_id"])
            expect(user.email).to eq(decoded_jwt["Email"])
            expect(user.name).to eq(Utils::NameNormalizer.call(decoded_jwt["NomePessoaFisica"]))
            expect(user.cellphone).to eq(params[:cellphone])
            expect(user.cpf).to eq(params[:cpf])
          end
        end

        context "when params does not have all info to sign up" do
          let(:params) { {access_token: jwt} }

          it "must return error and user data with filled and missing content" do
            response = described_class.new(params).call
            expect(response.success).to eq(true)
            user = response.user
            expect(user.name).to eq(Utils::NameNormalizer.call(decoded_jwt["NomePessoaFisica"]))
            expect(user.email).to eq(decoded_jwt["Email"])
            expect(user.cpf).to eq(nil)
            expect(user).not_to be_persisted
          end
        end

        context "when params contains cpf with existing user on business" do
          let!(:user) { create(:user, business:, external_id: nil, cpf: params[:cpf]) }
          let(:params) do
            {
              access_token: jwt,
              cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
              cellphone: "***********"
            }
          end

          it "must return this user and update its external_id" do
            expect do
              response = described_class.new(params).call
              expect(response.success).to eq(true)
            end.to change(User, :count).by(0)

            user = User.last
            expect(user.external_id).to eq(decoded_jwt["user_id"])
            expect(user.email).to eq(decoded_jwt["Email"])
            expect(user.name).to eq(Utils::NameNormalizer.call(decoded_jwt["NomePessoaFisica"]))
            expect(user.cellphone).to eq(params[:cellphone])
            expect(user.cpf).to eq(params[:cpf])
          end
        end
      end
    end
  end
end
