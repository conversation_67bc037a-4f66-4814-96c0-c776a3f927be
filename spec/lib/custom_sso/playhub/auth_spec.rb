# frozen_string_literal: true

require "rails_helper"

RSpec.describe CustomSso::Playhub::Auth, :vcr do
  let!(:business) { create(:business) }
  let(:jwt) { "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" }
  let(:decoded_jwt) { CustomSso::Playhub::Decode.decode(access_token: jwt) }

  context "when jwt token is not valid" do
    let(:params) { {access_token: "invalid_jwt", business:} }

    it "must return unprocessable" do
      response = described_class.new(params).call
      expect(response.success).to eq(false)
      expect(response.user).to eq(nil)
      expect(response.message).to eq("JWT Invalido")
    end
  end

  context "when jwt token is valid" do
    let(:params) { {access_token: jwt, business:} }

    context "when user with external id exists on business" do
      let!(:user) { create(:user, business:, external_id: "7510") }

      it "must return succesfull with existing user" do
        response = described_class.new(params).call
        expect(response.success).to eq(true)
        expect(response.user).to eq(user)
      end
    end

    context "when user with external id does not exist on business" do
      context "when params has all info to sign up" do
        let(:params) do
          {
            access_token: jwt,
            business:,
            cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
            cellphone: "***********",
            name: "Test Name",
            email: "<EMAIL>"
          }
        end

        it "must return succesfull and create user" do
          expect do
            response = described_class.new(params).call
            expect(response.success).to eq(true)
          end.to change(User, :count).by(1)

          user = User.last

          expect(user.external_id).to eq(decoded_jwt["sub"])
          expect(user.email).to eq(params[:email])
          expect(user.name).to eq(Utils::NameNormalizer.call(params[:name]))
          expect(user.cellphone).to eq(params[:cellphone])
          expect(user.cpf).to eq(params[:cpf])
        end
      end

      context "when params does not have all info to sign up" do
        let(:params) { {access_token: jwt, business:, name: "Name"} }
        let(:business) { create(:business) }

        it "must return success with non persisted user data with filled and missing content" do
          response = described_class.new(params).call
          expect(response.success).to eq(true)
          user = response.user
          expect(user.name).to eq("Name")
          expect(user.email).to eq(nil)
          expect(user.cpf).to eq(nil)
          expect(user).not_to be_persisted
        end
      end

      context "when params contains cpf with existing user on business" do
        let!(:user) { create(:user, business:, external_id: nil, cpf: params[:cpf]) }
        let(:params) do
          {
            access_token: jwt,
            business:,
            cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
            cellphone: "***********"
          }
        end

        it "must return this user and update its external_id" do
          expect do
            response = described_class.new(params).call
            expect(response.success).to eq(true)
          end.to change(User, :count).by(0)

          user = User.last
          expect(user.external_id).to eq(decoded_jwt["sub"])
          expect(user.cellphone).to eq(params[:cellphone])
          expect(user.cpf).to eq(params[:cpf])
        end
      end
    end
  end
end
