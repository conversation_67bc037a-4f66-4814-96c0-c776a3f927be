# frozen_string_literal: true

require "rails_helper"

RSpec.describe CustomSso::AzureAd::Auth, :vcr do
  let!(:business) { create(:business) }
  let(:saml_response) { "PHNhbWxwOlJlc3BvbnNlIElEPSJfNGE4NmNkOWUtNTIyMi00NzhiLWJlMWItMGRjZGI4ZTYxMjhmIiBWZXJzaW9uPSIyLjAiIElzc3VlSW5zdGFudD0iMjAyMy0xMi0yN1QyMDowODozNi45NzJaIiBEZXN0aW5hdGlvbj0iaHR0cHM6Ly9zc28ubGVjdXBvbi5jb20vcHJvZC90aW0vbG9naW4iIEluUmVzcG9uc2VUbz0iX215X3VuaXF1ZV9zZXNzaW9uX2F3ZWhvIiB4bWxuczpzYW1scD0idXJuOm9hc2lzOm5hbWVzOnRjOlNBTUw6Mi4wOnByb3RvY29sIj48SXNzdWVyIHhtbG5zPSJ1cm46b2FzaXM6bmFtZXM6dGM6U0FNTDoyLjA6YXNzZXJ0aW9uIj5odHRwczovL3N0cy53aW5kb3dzLm5ldC81N2I4Yzk2ZS1hYzJmLTRkNzgtYTE0OS1mMWZjNjgxN2QzYzQvPC9Jc3N1ZXI%2BPHNhbWxwOlN0YXR1cz48c2FtbHA6U3RhdHVzQ29kZSBWYWx1ZT0idXJuOm9hc2lzOm5hbWVzOnRjOlNBTUw6Mi4wOnN0YXR1czpTdWNjZXNzIi8%2BPC9zYW1scDpTdGF0dXM%2BPEFzc2VydGlvbiBJRD0iXzA4ZWY5NjI4LTI4YzktNDNjYS05ZmIzLTA4ZTlkMzFkYWQwMSIgSXNzdWVJbnN0YW50PSIyMDIzLTEyLTI3VDIwOjA4OjM2Ljk2OVoiIFZlcnNpb249IjIuMCIgeG1sbnM9InVybjpvYXNpczpuYW1lczp0YzpTQU1MOjIuMDphc3NlcnRpb24iPjxJc3N1ZXI%2BaHR0cHM6Ly9zdHMud2luZG93cy5uZXQvNTdiOGM5NmUtYWMyZi00ZDc4LWExNDktZjFmYzY4MTdkM2M0LzwvSXNzdWVyPjxTaWduYXR1cmUgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvMDkveG1sZHNpZyMiPjxTaWduZWRJbmZvPjxDYW5vbmljYWxpemF0aW9uTWV0aG9kIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My5vcmcvMjAwMS8xMC94bWwtZXhjLWMxNG4jIi8%2BPFNpZ25hdHVyZU1ldGhvZCBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvMDQveG1sZHNpZy1tb3JlI3JzYS1zaGEyNTYiLz48UmVmZXJlbmNlIFVSST0iI18wOGVmOTYyOC0yOGM5LTQzY2EtOWZiMy0wOGU5ZDMxZGFkMDEiPjxUcmFuc2Zvcm1zPjxUcmFuc2Zvcm0gQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjZW52ZWxvcGVkLXNpZ25hdHVyZSIvPjxUcmFuc2Zvcm0gQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzEwL3htbC1leGMtYzE0biMiLz48L1RyYW5zZm9ybXM%2BPERpZ2VzdE1ldGhvZCBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvMDQveG1sZW5jI3NoYTI1NiIvPjxEaWdlc3RWYWx1ZT5CMjdlZmpROEdDUEtnaG5taEVlSzFBMW00MVNpQmdQUzVMa2huL0RnZGxnPTwvRGlnZXN0VmFsdWU%2BPC9SZWZlcmVuY2U%2BPC9TaWduZWRJbmZvPjxTaWduYXR1cmVWYWx1ZT54bXlyeW9HdWxYTUdOcGoxL2VYTk10Rk5qczlST3R4N05tQXhrRGczN1RrYldmZjkweWJ2STVlUmFGY3NudmdERDFXeXl1VDQxTE5wbTlINTVQOFphN0MyV3M0OURlN21BWnR5V2J5aEkrM2VTdFd1Wk11TXBkeDd3azF4T0J3WGxMMHlXSDI0dVZrVG9SaGZCdHV5WkNzLzZvSFgyY0x5dm8wOGgvK2RaNDZkOVpRT0cwOU1KbzZYQXUrWVg0bUdNUEJCV0xRWFlxRit4dEd0cW5HNFFsVHUrOHZnYXdxYyt5ejNVbXdpZUhnd2huMlFQNU5KSmdwMnRMS1k5dVBGRHgrUWoxUVVVa0lrRDJ3TmV0MjM4SmRzN1RNZnAweVNITTdPcHlLVXZzRTNvYjgxQkVxRHY5czk4Y2RTcFpOTHgyVnlJNVp4aFJBMHZUTUp4eC94N1E9PTwvU2lnbmF0dXJlVmFsdWU%2BPEtleUluZm8%2BPFg1MDlEYXRhPjxYNTA5Q2VydGlmaWNhdGU%2BTUlJQzhEQ0NBZGlnQXdJQkFnSVFaZ0Z6U2hMU0dMeElYZDhRTCtkRzJqQU5CZ2txaGtpRzl3MEJBUXNGQURBME1USXdNQVlEVlFRREV5bE5hV055YjNOdlpuUWdRWHAxY21VZ1JtVmtaWEpoZEdWa0lGTlRUeUJEWlhKMGFXWnBZMkYwWlRBZUZ3MHlNekV5TVRJeE9ERXdOREZhRncweU5qRXlNVEl4T0RFd05ERmFNRFF4TWpBd0JnTlZCQU1US1UxcFkzSnZjMjltZENCQmVuVnlaU0JHWldSbGNtRjBaV1FnVTFOUElFTmxjblJwWm1sallYUmxNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQTF1L2NndWE4TlFUSnZHZEI0eXVUSlo3SThQZjQwakd3RE5JTDlodkV3aUVsRWhsbUhyUEVJdExrUGNYTDAyREozd0tocW1PeGlnNXNOQ05iZEw4ZFlhV0RuWHNyNkR1WnJDR2JxTXVLWEtTeWxjaTJSNTh2VUhUYTdzaVErdlVraEZkbDNSbnZjRzM1aDFmRGxjekR1cGErRFR0RXZiMWdCNHBCbFJrMEl6RmR2QXNOaHZMMXVtbzREeWdGN0dzQVc4MHdCL0xhOVFUeFUvRUZjVHNYSGxUWnAzZUpQaStBWVZkZ0MvbHRPaEpsc2tyckIxNHptZE8yYXowcVdjNzh3Vk1GbkxMbm9JSUx3Zk80UjBTc0l1QjJLVGJLeDJlQ1pESUZ5TDI3aEZMcDh1alNvalM0VXBEL0k0aEhXYTVXSkY0MFRZeU8wbXQ4anUrUDFyQnRXUUlEQVFBQk1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQUxianZCN2JDeHQ1ZzF6V0UyS3VxWkNTRVVtWVlwZ2xqaGo3MVB4Z2pET3VVeXh3V3VCSGJKUkdBZlliakthMy9nSVJja1p2dDFVWmNmaUZ0RXFPNmxiek96b3RLamg5dEtLZ2RhR1BaWDBkWWNqSjVORmR1ZnhFOXdHdHhLeUdPK1pEWGlOUWhjU3hQUGVmczJVMUZldnYzUCthdVZKYTFQMkpQaGZuYW96MW40SzR0aTFOQlh4bDZYL3VBSWVOakp6ZVh5WU0rWUFtSFBzYzN5eklzWmdiU2lnbXFTTjRtbG4wNUxuSjJLbWJFMXR0R29YcUkvSjhIQUhGMGNBS2pLR2piaHBsTUZucVdQT3VocFIrZFluYWY0VmVpRnAvU3pkNlJpbGxIY2h6WVpURU5yazVNLy9qak1IVGFsWEVnNEkxWmFnb1llckZYd2k5RThWWmRpPC9YNTA5Q2VydGlmaWNhdGU%2BPC9YNTA5RGF0YT48L0tleUluZm8%2BPC9TaWduYXR1cmU%2BPFN1YmplY3Q%2BPE5hbWVJRCBGb3JtYXQ9InVybjpvYXNpczpuYW1lczp0YzpTQU1MOjEuMTpuYW1laWQtZm9ybWF0OmVtYWlsQWRkcmVzcyI%2BYmNvdXRvQHRpbWJyYXNpbC5jb20uYnI8L05hbWVJRD48U3ViamVjdENvbmZpcm1hdGlvbiBNZXRob2Q9InVybjpvYXNpczpuYW1lczp0YzpTQU1MOjIuMDpjbTpiZWFyZXIiPjxTdWJqZWN0Q29uZmlybWF0aW9uRGF0YSBJblJlc3BvbnNlVG89Il9teV91bmlxdWVfc2Vzc2lvbl9hd2VobyIgTm90T25PckFmdGVyPSIyMDIzLTEyLTI3VDIxOjA4OjM2LjgwM1oiIFJlY2lwaWVudD0iaHR0cHM6Ly9zc28ubGVjdXBvbi5jb20vcHJvZC90aW0vbG9naW4iLz48L1N1YmplY3RDb25maXJtYXRpb24%2BPC9TdWJqZWN0PjxDb25kaXRpb25zIE5vdEJlZm9yZT0iMjAyMy0xMi0yN1QyMDowMzozNi44MDNaIiBOb3RPbk9yQWZ0ZXI9IjIwMjMtMTItMjdUMjE6MDg6MzYuODAzWiI%2BPEF1ZGllbmNlUmVzdHJpY3Rpb24%2BPEF1ZGllbmNlPmh0dHBzOi8vdGltZmFjaWwudGltYnJhc2lsLmNvbS5icjwvQXVkaWVuY2U%2BPC9BdWRpZW5jZVJlc3RyaWN0aW9uPjwvQ29uZGl0aW9ucz48QXR0cmlidXRlU3RhdGVtZW50PjxBdHRyaWJ1dGUgTmFtZT0iaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS9pZGVudGl0eS9jbGFpbXMvdGVuYW50aWQiPjxBdHRyaWJ1dGVWYWx1ZT41N2I4Yzk2ZS1hYzJmLTRkNzgtYTE0OS1mMWZjNjgxN2QzYzQ8L0F0dHJpYnV0ZVZhbHVlPjwvQXR0cmlidXRlPjxBdHRyaWJ1dGUgTmFtZT0iaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS9pZGVudGl0eS9jbGFpbXMvb2JqZWN0aWRlbnRpZmllciI%2BPEF0dHJpYnV0ZVZhbHVlPjZhYTljYjA1LTNiZmItNDY1OC1hMmM3LWVlYWM3ZjRlOTU3ZDwvQXR0cmlidXRlVmFsdWU%2BPC9BdHRyaWJ1dGU%2BPEF0dHJpYnV0ZSBOYW1lPSJodHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL2lkZW50aXR5L2NsYWltcy9kaXNwbGF5bmFtZSI%2BPEF0dHJpYnV0ZVZhbHVlPkJydW5vIFJvY2hhIENvdXRvPC9BdHRyaWJ1dGVWYWx1ZT48L0F0dHJpYnV0ZT48QXR0cmlidXRlIE5hbWU9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vaWRlbnRpdHkvY2xhaW1zL2lkZW50aXR5cHJvdmlkZXIiPjxBdHRyaWJ1dGVWYWx1ZT5odHRwczovL3N0cy53aW5kb3dzLm5ldC81N2I4Yzk2ZS1hYzJmLTRkNzgtYTE0OS1mMWZjNjgxN2QzYzQvPC9BdHRyaWJ1dGVWYWx1ZT48L0F0dHJpYnV0ZT48QXR0cmlidXRlIE5hbWU9Imh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vY2xhaW1zL2F1dGhubWV0aG9kc3JlZmVyZW5jZXMiPjxBdHRyaWJ1dGVWYWx1ZT5odHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL3dzLzIwMDgvMDYvaWRlbnRpdHkvYXV0aGVudGljYXRpb25tZXRob2QvcGFzc3dvcmQ8L0F0dHJpYnV0ZVZhbHVlPjxBdHRyaWJ1dGVWYWx1ZT5odHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL3dzLzIwMDgvMDYvaWRlbnRpdHkvYXV0aGVudGljYXRpb25tZXRob2QveDUwOTwvQXR0cmlidXRlVmFsdWU%2BPEF0dHJpYnV0ZVZhbHVlPmh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vY2xhaW1zL211bHRpcGxlYXV0aG48L0F0dHJpYnV0ZVZhbHVlPjwvQXR0cmlidXRlPjxBdHRyaWJ1dGUgTmFtZT0iaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvZ2l2ZW5uYW1lIj48QXR0cmlidXRlVmFsdWU%2BQnJ1bm88L0F0dHJpYnV0ZVZhbHVlPjwvQXR0cmlidXRlPjxBdHRyaWJ1dGUgTmFtZT0iaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvc3VybmFtZSI%2BPEF0dHJpYnV0ZVZhbHVlPlJvY2hhIENvdXRvPC9BdHRyaWJ1dGVWYWx1ZT48L0F0dHJpYnV0ZT48QXR0cmlidXRlIE5hbWU9Imh0dHA6Ly9zY2hlbWFzLnhtbHNvYXAub3JnL3dzLzIwMDUvMDUvaWRlbnRpdHkvY2xhaW1zL2VtYWlsYWRkcmVzcyI%2BPEF0dHJpYnV0ZVZhbHVlPmJjb3V0b0B0aW1icmFzaWwuY29tLmJyPC9BdHRyaWJ1dGVWYWx1ZT48L0F0dHJpYnV0ZT48QXR0cmlidXRlIE5hbWU9Imh0dHA6Ly9zY2hlbWFzLnhtbHNvYXAub3JnL3dzLzIwMDUvMDUvaWRlbnRpdHkvY2xhaW1zL25hbWUiPjxBdHRyaWJ1dGVWYWx1ZT5iY291dG9AdGltYnJhc2lsLmNvbS5icjwvQXR0cmlidXRlVmFsdWU%2BPC9BdHRyaWJ1dGU%2BPEF0dHJpYnV0ZSBOYW1lPSJlbXBsb3llZUlEIj48QXR0cmlidXRlVmFsdWU%2BRjgwMzQ4MzA8L0F0dHJpYnV0ZVZhbHVlPjwvQXR0cmlidXRlPjwvQXR0cmlidXRlU3RhdGVtZW50PjxBdXRoblN0YXRlbWVudCBBdXRobkluc3RhbnQ9IjIwMjMtMTItMjdUMTc6MjA6NDguMzAzWiIgU2Vzc2lvbkluZGV4PSJfMDhlZjk2MjgtMjhjOS00M2NhLTlmYjMtMDhlOWQzMWRhZDAxIj48QXV0aG5Db250ZXh0PjxBdXRobkNvbnRleHRDbGFzc1JlZj51cm46b2FzaXM6bmFtZXM6dGM6U0FNTDoyLjA6YWM6Y2xhc3NlczpQYXNzd29yZDwvQXV0aG5Db250ZXh0Q2xhc3NSZWY%2BPC9BdXRobkNvbnRleHQ%2BPC9BdXRoblN0YXRlbWVudD48L0Fzc2VydGlvbj48L3NhbWxwOlJlc3BvbnNlPg%3D%3D" }
  let(:decoded_saml_response) { CustomSso::AzureAd::Decode.decode(access_token: saml_response) }

  context "when SAMLResponse is not valid" do
    let(:params) { {access_token: "invalid_saml_response", business:} }

    it "must return unprocessable" do
      response = described_class.new(params).call
      expect(response.success).to eq(false)
      expect(response.user).to eq(nil)
      expect(response.message).to eq("Autenticação mal-sucedida")
    end
  end

  context "when SAMLResponse is valid" do
    let(:params) { {access_token: saml_response, business:} }

    context "when user with external id exists on business" do
      let!(:user) { create(:user, business:, external_id: "F8034830") }

      it "must return succesfull with existing user" do
        response = described_class.new(params).call
        expect(response.success).to eq(true)
        expect(response.user).to eq(user)
      end
    end

    context "when user with external id does not exist on business" do
      context "when params has all info to sign up" do
        let(:params) do
          {
            access_token: saml_response,
            business:,
            cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
            cellphone: "***********",
            name: "Test Name",
            email: "<EMAIL>"
          }
        end

        it "must return succesfull and create user" do
          expect do
            response = described_class.new(params).call
            expect(response.success).to eq(true)
          end.to change(User, :count).by(1)

          user = User.last
          expect(user.external_id).to eq(decoded_saml_response[:external_id])
          expect(user.email).to eq(params[:email])
          expect(user.name).to eq(Utils::NameNormalizer.call(params[:name]))
          expect(user.cellphone).to eq(params[:cellphone])
          expect(user.cpf).to eq(params[:cpf])
        end
      end

      context "when params does not have all info to sign up" do
        let(:params) { {access_token: saml_response, business:} }
        let(:business) { create(:business) }

        it "must return success with non persisted user data with filled and missing content" do
          response = described_class.new(params).call
          expect(response.success).to eq(true)
          user = response.user
          expect(user.name).to eq(Utils::NameNormalizer.call(decoded_saml_response[:name]))
          expect(user.email).to eq(decoded_saml_response[:email])
          expect(user.cpf).to eq(nil)
          expect(user).not_to be_persisted
        end
      end

      context "when params contains cpf with existing user on business" do
        let!(:user) { create(:user, business:, external_id: nil, cpf: params[:cpf]) }
        let(:params) do
          {
            access_token: saml_response,
            business:,
            cpf: CPF.new(FFaker::IdentificationBR.cpf).stripped,
            cellphone: "***********"
          }
        end

        it "must return this user and update its external_id" do
          expect do
            response = described_class.new(params).call
            expect(response.success).to eq(true)
          end.to change(User, :count).by(0)

          user = User.last
          expect(user.external_id).to eq(decoded_saml_response[:external_id])
          expect(user.cellphone).to eq(params[:cellphone])
          expect(user.cpf).to eq(params[:cpf])
        end
      end
    end
  end
end
