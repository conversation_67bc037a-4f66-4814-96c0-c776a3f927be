require "rails_helper"

RSpec.describe Utils::<PERSON><PERSON>Hel<PERSON> do
  describe ".stringify_and_remove_nils" do
    # rubocop:disable Layout/SpaceInsideHashLiteralBraces
    it "converts all values to strings and removes nils from a flat hash" do
      input = {
        "key1" => 123,
        "key2" => nil,
        "key3" => true
      }
      expected = {
        "key1" => "123",
        "key3" => "true"
      }
      expect(described_class.stringify_and_remove_nils(input)).to eq(expected)
    end

    it "processes nested hashes and arrays correctly" do
      input = {
        "amount" => 1000,
        "details" => {
          "description" => "Payment",
          "optional" => nil
        },
        "items" => [
          { "name" => "Item1", "price" => 500 },
          { "name" => nil, "price" => 500 }
        ],
        "tags" => [nil, "urgent"]
      }
      expected = {
        "amount" => "1000",
        "details" => {
          "description" => "Payment"
        },
        "items" => [
          { "name" => "Item1", "price" => "500" },
          { "price" => "500" }
        ],
        "tags" => ["urgent"]
      }
      expect(described_class.stringify_and_remove_nils(input)).to eq(expected)
    end

    it "handles arrays with all nil elements" do
      input = [nil, nil, nil]
      expected = []
      expect(described_class.stringify_and_remove_nils(input)).to eq(expected)
    end
    # rubocop:enable Layout/SpaceInsideHashLiteralBraces

    it "converts non-hash and non-array objects to strings" do
      expect(described_class.stringify_and_remove_nils(42)).to eq("42")
      expect(described_class.stringify_and_remove_nils(true)).to eq("true")
      expect(described_class.stringify_and_remove_nils(nil)).to be_nil
    end
  end
end
