require "rails_helper"

RSpec.describe Webhook::Integration do
  describe ".all" do
    it do
      expect(described_class.all)
        .to eq([
          "asaas",
          "magazine_luiza",
          "woocomerce"
        ])
    end
  end

  describe ".enum" do
    it do
      expect(described_class.enum).to eq({
        asaas: "asaas",
        magazine_luiza: "magazine_luiza",
        woocomerce: "woocomerce"
      })
    end
  end
end
