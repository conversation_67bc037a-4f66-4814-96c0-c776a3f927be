require "rails_helper"

RSpec.describe Webhook::Asaas::Events::PaymentApprove, :vcr do
  let(:business) { create(:business) }
  let!(:webhook_client) { create(:webhook_client, business:, api_key: "$aact_YTU5YTE0M2M2N2I4MTliNzk0YTI5N2U5MzdjNWZmNDQ6OjAwMDAwMDAwMDAwMDAwODA2MjQ6OiRhYWNoX2E1ODhiYjlhLWE0OTEtNDE5MS1iMTBhLTYwYjMxYTJmZmYwMg==") }
  let(:mocked_customer) do
    {
      cpf: "***********",
      email: "<EMAIL>",
      phone: "***********",
      name: "Mocked Name"
    }
  end

  describe "#action" do
    context "when is payment event" do
      let(:params) do
        {
          "event" => Webhook::Asaas::Events::PaymentApprove::EVENTS.sample,
          "payment" => {
            "customer" => "cus_000006014696"
          }
        }
      end

      it "must call create_authorized_user Action for business" do
        allow(Webhook::Actions::CreateAuthorizedUser).to receive(:new).and_call_original
        described_class.new(business:, params:).action

        expect(Webhook::Actions::CreateAuthorizedUser).to have_received(:new).with(
          business:,
          cpf: mocked_customer[:cpf],
          name: mocked_customer[:name],
          email: mocked_customer[:email],
          phone: mocked_customer[:phone]
        )
      end
    end

    context "when is subscription event" do
      let(:params) do
        {
          "event" => "SUBSCRIPTION_CREATED",
          "subscription" => {
            "customer" => "cus_000006014696"
          }
        }
      end

      it "must call create_authorized_user Action for business" do
        allow(Webhook::Actions::CreateAuthorizedUser).to receive(:new).and_call_original
        described_class.new(business:, params:).action

        expect(Webhook::Actions::CreateAuthorizedUser).to have_received(:new).with(
          business:,
          cpf: mocked_customer[:cpf],
          name: mocked_customer[:name],
          email: mocked_customer[:email],
          phone: mocked_customer[:phone]
        )
      end
    end
  end
end
