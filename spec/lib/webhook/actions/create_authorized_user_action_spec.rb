require "rails_helper"

RSpec.describe Webhook::Actions::CreateAuthorizedUser do
  let(:business) { create(:business) }
  let(:name) { Utils::NameNormalizer.call(FFaker::Name.name) }
  let(:email) { FFaker::Internet.email }
  let(:cpf) { CPF.new(FFaker::IdentificationBR.cpf).stripped }
  let(:phone) { FFaker::PhoneNumberBR.phone_number.delete("^0-9") }
  let(:valid_action) { described_class.new(business:, cpf:, name:, email:, phone:) }

  let(:invalid_cpf) { "***********" }
  let(:invalid_action) { described_class.new(business:, name:, email:, phone:, cpf: invalid_cpf) }

  describe "#process" do
    context "when with valid params" do
      it "must create authorized_user" do
        expect { valid_action.process }
          .to change(business.authorized_users, :count).by(1)

        expected_attributes = {"business_id" => business.id, "cpf" => cpf, "email" => email, "name" => name, "phone" => phone}
        expect(AuthorizedUser.last.attributes.slice("business_id", "cpf", "email", "name", "phone")).to eq(expected_attributes)
      end
    end

    context "when with invalid params" do
      it "must fail to create authorized_user" do
        expect { invalid_action.process }
          .to change(AuthorizedUser, :count).by(0)
      end
    end
  end

  describe "#success?" do
    context "when authorized_user already exists" do
      let(:sub_business) { create(:business, main_business: business) }
      let!(:authorized_user) { create(:authorized_user, business:) }
      let(:valid_action) { described_class.new(business: sub_business, cpf: authorized_user.cpf, name: "New Name", email:, phone:) }

      it "must update authorized_user and return true" do
        valid_action.process
        authorized_user.reload
        expect(authorized_user.name).to eq("New Name")
        expect(authorized_user.business).to eq(sub_business)
        expect(valid_action.success?).to eq(true)
      end
    end
    context "when authorized_user was successfully created" do
      it "must return true" do
        expect(valid_action.success?).to eq(true)
        expect(AuthorizedUser.count).to eq(1)
      end
    end

    context "when authorized_user was not created" do
      it "must return false" do
        expect(invalid_action.success?).to eq(false)
        expect(AuthorizedUser.count).to eq(0)
      end
    end
  end

  describe "#message" do
    context "when authorized user was successfully created" do
      it "must return nil" do
        expect(valid_action.message).to eq(nil)
      end
    end

    context "when authorized user was not created" do
      it "must return user not created message" do
        expect(invalid_action.message).to include("Não foi possível sincronizar o usuário: Cpf inválido")
      end
    end
  end
end
