# frozen_string_literal: true

require "rails_helper"

RSpec.describe SearchUserIntegration::Panvel do
  subject { described_class.new(user_cpf:, organization_name:).call }

  let!(:business) { create(:business) }
  let!(:authorized_user) { create(:authorized_user) }
  let(:user_cpf) { authorized_user.cpf }

  let!(:organization) { create(:organization) }
  let(:organization_name) { organization.name }

  context "when organization is active" do
    context "taxpayer belongs to a registrated authorized_user" do
      it { is_expected.to eq({status: "Elegível", cpf: authorized_user.cpf, name: authorized_user.name}) }
    end
    context "taxpayer not belongs to a registrated authorized_user" do
      let(:user_cpf) { "123" }

      it { is_expected.to eq({status: "Não elegível"}) }
    end
  end

  context "when organization is not active" do
    before { organization.update!(active: false) }

    it { is_expected.to eq({status: "Não elegível"}) }
  end
end
