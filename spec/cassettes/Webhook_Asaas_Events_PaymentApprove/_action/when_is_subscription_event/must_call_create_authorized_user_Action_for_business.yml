---
http_interactions:
- request:
    method: get
    uri: https://sandbox.asaas.com/v3/customers/cus_000006014696
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v2.9.0
      Content-Type:
      - application/json; charset=utf-8
      Access-Token:
      - "$aact_YTU5YTE0M2M2N2I4MTliNzk0YTI5N2U5MzdjNWZmNDQ6OjAwMDAwMDAwMDAwMDAwODA2MjQ6OiRhYWNoX2E1ODhiYjlhLWE0OTEtNDE5MS1iMTBhLTYwYjMxYTJmZmYwMg=="
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: ''
    headers:
      Content-Type:
      - application/json;charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Date:
      - Mon, 20 May 2024 19:54:37 GMT
      Set-Cookie:
      - AWSALB=PaAIwaH5gcSb317q1043VQqyPiAaK34au3P79GpbblAVuDW4CyQU3sRy13/vEbb0vfmF7hi9SNIXXeH5IpVBHyV0vpjIXMMN1Y3hGHcmqejO9i7/Ljf9A1YAk4ru;
        Expires=Mon, 27 May 2024 19:54:37 GMT; Path=/
      - AWSALBCORS=PaAIwaH5gcSb317q1043VQqyPiAaK34au3P79GpbblAVuDW4CyQU3sRy13/vEbb0vfmF7hi9SNIXXeH5IpVBHyV0vpjIXMMN1Y3hGHcmqejO9i7/Ljf9A1YAk4ru;
        Expires=Mon, 27 May 2024 19:54:37 GMT; Path=/; SameSite=None; Secure
      - AWSALBTG=1CHrc+75db9ECmpR8GmLoyQb9gKcTify+dDBv9Ff+fcC/70h4xrGSutYU3pi4vcYAc/EKySliTfHR1VWP7Q+1/k09R6SnA5FcvaLetOsa9PIVwAgOvYJITxvPoaXVDGaqEDQ6Xhh6pQ9dKw3IFqmWKq56jzkHJbGapVKnDJk6r2n;
        Expires=Mon, 27 May 2024 19:54:37 GMT; Path=/
      - AWSALBTGCORS=1CHrc+75db9ECmpR8GmLoyQb9gKcTify+dDBv9Ff+fcC/70h4xrGSutYU3pi4vcYAc/EKySliTfHR1VWP7Q+1/k09R6SnA5FcvaLetOsa9PIVwAgOvYJITxvPoaXVDGaqEDQ6Xhh6pQ9dKw3IFqmWKq56jzkHJbGapVKnDJk6r2n;
        Expires=Mon, 27 May 2024 19:54:37 GMT; Path=/; SameSite=None; Secure
      Server:
      - nginx
      X-Cache:
      - Miss from cloudfront
      Via:
      - 1.1 e3a3c8a0ec7b3e46dacb56f83c6dc628.cloudfront.net (CloudFront)
      X-Amz-Cf-Pop:
      - GRU3-C2
      Alt-Svc:
      - h3=":443"; ma=86400
      X-Amz-Cf-Id:
      - Z-nsXX1Sfungg5SaYKrqNJsDdzgh5aZSbJ52VtqamieSNba5f3fqow==
    body:
      encoding: UTF-8
      string: '{"object":"customer","id":"cus_000006014696","dateCreated":"2024-05-15","name":"Mocked Name","email":"<EMAIL>","company":null,"phone":null,"mobilePhone":"55999123456","address":null,"addressNumber":null,"complement":null,"province":null,"postalCode":null,"cpfCnpj":"01594971080","personType":"FISICA","deleted":false,"additionalEmails":null,"externalReference":null,"notificationDisabled":false,"observations":null,"municipalInscription":null,"stateInscription":null,"canDelete":true,"cannotBeDeletedReason":null,"canEdit":true,"cannotEditReason":null,"city":null,"cityName":null,"state":null,"country":"Brasil"}'
  recorded_at: Mon, 20 May 2024 19:54:38 GMT
recorded_with: VCR 6.2.0
