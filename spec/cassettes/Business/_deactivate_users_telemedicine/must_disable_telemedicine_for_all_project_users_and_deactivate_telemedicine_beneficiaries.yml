---
http_interactions:
- request:
    method: post
    uri: https://hml-api.conexasaude.com.br/integration/enterprise/v2/patients/100/block
    body:
      encoding: UTF-8
      string: ''
    headers:
      Content-Type:
      - application/json
      Token:
      - 8f9aae94c0c93d12b4114a7dd91de750
      User-Agent:
      - Faraday v2.12.2
      Content-Length:
      - '0'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 17 Feb 2025 17:31:38 GMT
      Content-Type:
      - application/json;charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      X-Xss-Protection:
      - 1;mode=block
      Strict-Transport-Security:
      - max-age=63072000
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - DENY
      Content-Security-Policy:
      - script-src 'self' ; frame-src 'none'; object-src 'none'
      Referrer-Policy:
      - same-origin
      Cf-Cache-Status:
      - DYNAMIC
      Set-Cookie:
      - __cf_bm=4XM8eWuPYO8Y.toCNqSNQot5Ax70pKT5ENCGOx3lgNo-1739813498-*******-QmYyabKjrqzBHVBj3L0gg0YHMUWOsdFZuUvMY5fZ9eNFttdHU_CD0WczJBjOVXPFI_5nnbkhcA0Waac.TNn7zg;
        path=/; expires=Mon, 17-Feb-25 18:01:38 GMT; domain=.conexasaude.com.br; HttpOnly;
        Secure; SameSite=None
      Server:
      - cloudflare
      Cf-Ray:
      - 9137831a78aca454-GRU
    body:
      encoding: UTF-8
      string: |-
        {
          "status" : 200,
          "msg" : null,
          "object" : true,
          "timestamp" : 1739813498087
        }
  recorded_at: Mon, 17 Feb 2025 17:31:38 GMT
recorded_with: VCR 6.3.1
