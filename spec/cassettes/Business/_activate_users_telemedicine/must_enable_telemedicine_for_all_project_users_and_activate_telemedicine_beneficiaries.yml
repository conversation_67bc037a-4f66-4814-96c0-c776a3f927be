---
http_interactions:
- request:
    method: post
    uri: https://hml-api.conexasaude.com.br/integration/enterprise/v2/patients/100/unblock
    body:
      encoding: UTF-8
      string: ''
    headers:
      Content-Type:
      - application/json
      Authorization:
      - TOKEN
      User-Agent:
      - Faraday v2.12.2
      Content-Length:
      - '0'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: Not Found
    headers:
      Date:
      - Thu, 13 Feb 2025 04:04:54 GMT
      Content-Length:
      - '0'
      Connection:
      - keep-alive
      X-Xss-Protection:
      - 1;mode=block
      Strict-Transport-Security:
      - max-age=63072000
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - DENY
      Content-Security-Policy:
      - script-src 'self' ; frame-src 'none'; object-src 'none'
      Referrer-Policy:
      - same-origin
      Cf-Cache-Status:
      - DYNAMIC
      Set-Cookie:
      - __cf_bm=EAqayRwS67CdqVLXVAxCUFhu8RKZSVyWDYLLTArwt1Q-**********-*******-Zdn1EQczGGm4PDmv7CRPXPqmC1o4sB1vB0PMYo0a1kAJngW0aGjqew9yhK_NE.uovnR_x31D9Iz6ZsSZOjF4HA;
        path=/; expires=Thu, 13-Feb-25 04:34:54 GMT; domain=.conexasaude.com.br; HttpOnly;
        Secure; SameSite=None
      Server:
      - cloudflare
      Cf-Ray:
      - 9111efe26b7851f2-GRU
      Content-Type:
      - application/json;charset=UTF-8
    body:
      encoding: UTF-8
      string: |-
        {
          "status" : 200,
          "msg" : "Sucesso",
          "object" : true,
          "timestamp" : 1739416131423
        }
  recorded_at: Thu, 13 Feb 2025 03:57:23 GMT
recorded_with: VCR 6.3.1
