---
http_interactions:
  - request:
      method: post
      uri: https://apisandboxpay.globalpaysolucoes.com.br/api/transactions/v1/payments/card
      body:
        encoding: UTF-8
        string:
          '{"amount":"50.21","paymentCardType":"credit","installments":"1","antifraudCode":"fc10b881-d9a0-4ab1-a6fd-a102db188f49","card":{"sellerId":"4195317","number":"5067224275805500","holderName":"ADRIANO
          P SOUZA","securityCode":"321","expirationMonth":"11","expirationYear":"25"},"customer":{"name":"ADRIANO
          PESSOA SOUZA","documentType":"PF","documentNumber":"90936034009","email":"<EMAIL>","phoneNumber":"31999855214","cellPhoneNumber":"31999855214","address":{"street":"<PERSON><PERSON>","number":"55","complement":"APT 104 BL 02","neighborhood":"Trevo","city":"Belo
          Horizonte","state":"MG","zipCode":"45810000","country":"BR"}},"itens":[{"unitPrice":"50.21","productName":"GIFT
          CARD","quantity":"1"}],"deviceInfo":{"ipAddress":"*************","httpAcceptBrowserValue":"*/*","httpAcceptContent":"*/*","httpBrowserLanguage":"pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3","httpBrowserJavaEnabled":"N","httpBrowserJavaScriptEnabled":"true","httpBrowserColorDepth":"24","httpBrowserScreenHeight":"969","httpBrowserScreenWidth":"1920","httpBrowserTimeDifference":"240","userAgentBrowserValue":"Mozilla/5.0
          (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********
          Safari/537.36"}}'
      headers:
        User-Agent:
          - Faraday v2.12.2
        Authorization:
          - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTc0NDA0MjUxMywiZXhwIjoxNzQ0MDQyODEzfQ.-Y3wIsdM2AW-x8mqnsxZTqgtNkf12RN53bQUpA_uZ9M
        Content-Type:
          - application/json
        Accept-Encoding:
          - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
        Accept:
          - "*/*"
    response:
      status:
        code: 401
        message: Unauthorized
      headers:
        Server:
          - nginx/1.22.0 (Ubuntu)
        Date:
          - Mon, 07 Apr 2025 16:15:18 GMT
        Content-Type:
          - application/json; charset=utf-8
        Content-Length:
          - "170"
        Connection:
          - keep-alive
        X-Powered-By:
          - Express
        Vary:
          - Origin
        Etag:
          - W/"aa-xQDLu2dNGFeVfzj4q6OVXgkdz+s"
      body:
        encoding: UTF-8
        string: '{"status":401,"message":"Unauthorized"}'
    recorded_at: Mon, 07 Apr 2025 16:15:19 GMT
recorded_with: VCR 6.3.1
