---
http_interactions:
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/public/v1/vault/cards
    body:
      encoding: UTF-8
      string: '{"sellerId":"4195317","number":"****************","holderName":"ADRIANO P SOUZA","securityCode":"321","expirationMonth":"09","expirationYear":"24","brand":"mastercard"}'
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTczNjYxNjg3MywiZXhwIjoxNzM2NjE3MTczfQ.MW8pSycixuEFE04PJCBLePfwmF8ketXzaaQKYu2uTGM
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Sat, 11 Jan 2025 17:34:35 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '110'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Etag:
      - W/"6e-6iaun4Ty0BxDbVRwsZpLzA9SKOM"
    body:
      encoding: UTF-8
      string: '{"status":400,"message":"Bad Request","data":[{"tag":"__general__","description":"O
        cartão está vencido."}]}'
  recorded_at: Sat, 11 Jan 2025 17:34:35 GMT
recorded_with: VCR 6.3.1
