---
http_interactions:
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/public/v1/vault/cards
    body:
      encoding: UTF-8
      string: >-
        {
          "sellerId": "4195317",
          "number": "****************",
          "holderName": "ADRIANO P SOUZA",
          "securityCode": "321",
          "expirationMonth": "12",
          "expirationYear": "25",
          "brand": "visa"
        }
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTczNjYxNjg2OCwiZXhwIjoxNzM2NjE3MTY4fQ.hp5ZFGc69HlFE_dmfm8knnhVuG_4W2g962Xy4oUGb60
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Sat, 11 Jan 2025 17:34:33 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '185'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Etag:
      - W/"b9-JU+qIeCqlkRR5y7dcMftsSfMgRU"
    body:
      encoding: UTF-8
      string: >-
        {
          "message": "Store card in vault done",
          "data": {
            "id": 2,
            "sellerId": 4195317,
            "partnerId": 794,
            "storeCardId": "a6c112b4-b9d2-4b44-9c51-d6fb276706b4",
            "number": "************0759",
            "holderName": "ADRIANO P SOUZA",
            "brand": "mastercard",
            "createdAt": "2025-04-04T20:57:51.493Z",
            "updatedAt": "2025-04-04T20:57:51.493Z"
          }
        }
  recorded_at: Sat, 11 Jan 2025 17:34:33 GMT
recorded_with: VCR 6.3.1
