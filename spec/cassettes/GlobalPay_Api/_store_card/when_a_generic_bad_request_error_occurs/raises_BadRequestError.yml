---
http_interactions:
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/public/v1/vault/cards
    body:
      encoding: UTF-8
      string: '{"sellerId":"4195317","number":"****************","holderName":"ADRIANO P SOUZA","securityCode":"123","expirationMonth":"11","expirationYear":"77","brand":"123"}'
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTczNjYxNjg3NiwiZXhwIjoxNzM2NjE3MTc2fQ.cSIejAGwNhkorvOwv1WHhgy9Z2rBRe9Na6m76poAVRQ
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Sat, 11 Jan 2025 17:34:38 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '101'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Etag:
      - W/"65-LDCGKihOu3yAfxN/q0MjdUB0Ynk"
    body:
      encoding: UTF-8
      string: '{"status":400,"message":"Bad Request","data":[{"tag":"brand","description":"Bandeira
        inexistente."}]}'
  recorded_at: Sat, 11 Jan 2025 17:34:38 GMT
recorded_with: VCR 6.3.1
