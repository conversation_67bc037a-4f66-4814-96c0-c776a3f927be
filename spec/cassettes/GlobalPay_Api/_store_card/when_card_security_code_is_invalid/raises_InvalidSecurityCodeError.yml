---
http_interactions:
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/public/v1/vault/cards
    body:
      encoding: UTF-8
      string: '{"sellerId":"4195317","number":"****************","holderName":"ADRIANO P SOUZA","securityCode":"12","expirationMonth":"11","expirationYear":"25","brand":"elo"}'
    headers:
      User-Agent:
      - Faraday v2.12.2
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTc0MjQyNzMwNywiZXhwIjoxNzQyNDI3NjA3fQ.I27LZJXpco1MfZ5PvJ6EJ9NhF7qnwGFu9p403QHlXKc
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Wed, 19 Mar 2025 23:35:08 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '172'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Etag:
      - W/"ac-uZvJWG+s8AY5gV0YacjMIWU7t1k"
    body:
      encoding: UTF-8
      string: '{"status":400,"message":"Bad Params","data":{"errors":{"securityCode":{"value":"12","msg":"Parâmetro
        deve ter mínimo 3 e máximo 4 caracteres.","param":"securityCode","location":"body"}}}}'
  recorded_at: Wed, 19 Mar 2025 23:35:08 GMT
recorded_with: VCR 6.3.1
