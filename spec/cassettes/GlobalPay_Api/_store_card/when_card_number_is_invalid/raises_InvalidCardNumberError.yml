---
http_interactions:
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/public/v1/vault/cards
    body:
      encoding: UTF-8
      string: '{"sellerId":"4195317","number":"5067224275805500","holderName":"ADRIANO P SOUZA","securityCode":"123","expirationMonth":"11","expirationYear":"25","brand":"elo"}'
    headers:
      User-Agent:
      - Faraday v2.12.0
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTczNjYxNjg3NSwiZXhwIjoxNzM2NjE3MTc1fQ.lnwVJ89XIwXyv-xc9pGtpuKDyIECi91DLGJLnhMHKdc
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Sat, 11 Jan 2025 17:34:36 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '160'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Etag:
      - W/"a0-IJoziEirc+UcktbvEm3H2G5fQdQ"
    body:
      encoding: UTF-8
      string: '{"status":400,"message":"Bad Params","data":{"errors":{"number":{"value":"5067224275805500","msg":"Parâmetro
        Inválido.","param":"number","location":"body"}}}}'
  recorded_at: Sat, 11 Jan 2025 17:34:36 GMT
recorded_with: VCR 6.3.1
