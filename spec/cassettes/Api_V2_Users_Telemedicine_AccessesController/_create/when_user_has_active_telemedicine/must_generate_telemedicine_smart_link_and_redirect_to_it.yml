---
http_interactions:
- request:
    method: get
    uri: https://hml-api.conexasaude.com.br/integration/enterprise/patients/generate-magiclink-access-app/6413606
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Token:
      - 9f4d3a82de86e8a0d2bc4169c2d9b937
      User-Agent:
      - Faraday v2.12.2
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Fri, 14 Feb 2025 04:40:39 GMT
      Content-Type:
      - application/json;charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      X-Xss-Protection:
      - 1;mode=block
      Strict-Transport-Security:
      - max-age=63072000
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - DENY
      Content-Security-Policy:
      - script-src 'self' ; frame-src 'none'; object-src 'none'
      Referrer-Policy:
      - same-origin
      Cf-Cache-Status:
      - DYNAMIC
      Set-Cookie:
      - __cf_bm=n4yoFrf1Fnyx61dE9eUslTNudpKF8KTlxtTsfmsNJNA-1739508039-*******-Vf6ROjGP5U0Xyve_mltSLn9uwgIVa2OdJpqTjyuT_VLGJP48759bQmQ379IyBLV48.CI_b46rlqXQbEP8ohodQ;
        path=/; expires=Fri, 14-Feb-25 05:10:39 GMT; domain=.conexasaude.com.br; HttpOnly;
        Secure; SameSite=None
      Server:
      - cloudflare
      Cf-Ray:
      - 911a619b2c92f203-GRU
    body:
      encoding: UTF-8
      string: |-
        {
          "status" : 200,
          "msg" : null,
          "object" : {
            "linkMagicoApp" : "https://qa-paciente.conexasaude.com.br/redirecionar/24752676989e4eea9c98facbe0211bca",
            "linkMagicoWeb" : "https://qa-embed-paciente.conexasaude.com.br/redirecionar/11cf570da95d445e8d7e836f2b082ed0",
            "patientId" : null
          },
          "timestamp" : 1739508039413
        }
  recorded_at: Fri, 14 Feb 2025 04:40:39 GMT
recorded_with: VCR 6.3.1
