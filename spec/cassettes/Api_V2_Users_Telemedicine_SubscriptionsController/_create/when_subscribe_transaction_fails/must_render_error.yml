---
http_interactions:
- request:
    method: post
    uri: https://hml-api.conexasaude.com.br/integration/enterprise/patients
    body:
      encoding: UTF-8
      string: '{"name":"<PERSON><PERSON>","mail":"<EMAIL>","dateBirth":"2025-02-12
        00:14:03 -0300","sex":"M","cpf":"36235158840","cellphone":"**********","enterprise":8130,"plan":7}'
    headers:
      Content-Type:
      - application/json
      Authorization:
      - TOKEN
      User-Agent:
      - Faraday v2.12.2
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 422
      message: Unprocessable Entity
    headers:
      Date:
      - Thu, 13 Feb 2025 03:14:02 GMT
      Content-Type:
      - application/json;charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      X-Xss-Protection:
      - 1;mode=block
      Strict-Transport-Security:
      - max-age=63072000
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - DENY
      Content-Security-Policy:
      - script-src 'self' ; frame-src 'none'; object-src 'none'
      Referrer-Policy:
      - same-origin
      Cf-Cache-Status:
      - DYNAMIC
      Set-Cookie:
      - __cf_bm=cVkB0xvJQs.lsMJDN1r_jQ7IAiUZHHpZs9xbNC_NXNs-1739416442-*******-zETPsK4hn12al7dY5oOr8OcMbjyb9LZPpb5rn7AU2XV09bpc2ffgkjKMo3g8Dsin_qZ5dPmy7gZTa9wPiuzF6A;
        path=/; expires=Thu, 13-Feb-25 03:44:02 GMT; domain=.conexasaude.com.br; HttpOnly;
        Secure; SameSite=None
      Server:
      - cloudflare
      Cf-Ray:
      - 9111a55fcd87f1e9-GRU
    body:
      encoding: UTF-8
      string: |-
        {
          "status" : 422,
          "msg" : "SECRET token não informado.",
          "object" : null,
          "timestamp" : 1739416442877
        }
  recorded_at: Thu, 13 Feb 2025 03:14:04 GMT
recorded_with: VCR 6.3.1
