---
http_interactions:
- request:
    method: post
    uri: https://hml-api.conexasaude.com.br/integration/enterprise/patients
    body:
      encoding: UTF-8
      string: '{"name":"<PERSON><PERSON>","mail":"shanta_ger<PERSON><EMAIL>","dateBirth":"2025-02-12
        00:08:51 -0300","sex":"M","cpf":"10780631099","cellphone":"53981245281","enterprise":8097,"plan":7}'
    headers:
      Content-Type:
      - application/json
      Authorization:
      - TOKEN
      User-Agent:
      - Faraday v2.12.2
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 13 Feb 2025 03:08:51 GMT
      Content-Type:
      - application/json;charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      X-Xss-Protection:
      - 1;mode=block
      Strict-Transport-Security:
      - max-age=63072000
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - DENY
      Content-Security-Policy:
      - script-src 'self' ; frame-src 'none'; object-src 'none'
      Referrer-Policy:
      - same-origin
      Cf-Cache-Status:
      - DYNAMIC
      Set-Cookie:
      - __cf_bm=ds6VvJI3MMeGbIZWiFLEqG9tJERVPHLJ9XTjYZEC8vg-1739416131-*******-f9xudewqth2_XMAydjbDuJO481Csd0KFH8sfi45Y5canv.2o2LDC3VMKrlSgUn4Lv6XL9ScCiiWDqfE6aMG9UA;
        path=/; expires=Thu, 13-Feb-25 03:38:51 GMT; domain=.conexasaude.com.br; HttpOnly;
        Secure; SameSite=None
      Server:
      - cloudflare
      Cf-Ray:
      - 91119dc52cc9e026-GRU
    body:
      encoding: UTF-8
      string: |-
        {
          "status" : 200,
          "msg" : "Sucesso",
          "object" : {
            "id": 401,
            "name": "Steve Hopkins",
            "mail": "<EMAIL>",
            "dateBirth": "26/04/1985",
            "sex": "MALE",
            "cpf": "33311155511",
            "cellphone": "21988776554",
            "patientHolderId": 24521,
            "patientHolderCpf": "98354881008",
            "kinshipOfTheHolder": "PAI",
            "healthCardNumber": "1113335556667",
            "additionalInformation": "Matrícula 663399-6",
            "passport": "BR123456",
            "specialist": 199,
            "nationalId": "20112527-9",
            "address": {
            "additionalAddressDetails": "Em frente à clínica Rede Conexa",
            "city": "Rio de Janeiro",
            "country": "Brasil",
            "region": "Ipanema",
            "state": "RJ",
            "streetAddress": "Rua da Passagem",
            "numberAddress": 410,
            "zipCode": 22780070
            },
            "motherName": "Maria Silva",
            "socialName": "Steve H.",
            "idRaceColor": 1,
            "idNationality": 1,
            "naturalizationDate": "26/09/2005",
            "cns": 123456789101213,
            "idCbo": 2,
            "religion": 1,
            "otherReligions": "string",
            "workplace": "Coworking",
            "freeObservations": "Exemplo de texto/observação do paciente",
            "unknowMother": false,
            "idHomeArea": 1,
            "homeSituation": true,
            "idSchooling": 1,
            "socialVulnerability": false,
            "ethnicity": "Indígena",
            "idGender": 1,
            "birthCounty": "Rio de Janeiro",
            "idBirthUF": 1,
            "idPassportIssuingCountry": 1,
            "passportIssuingDate": "17/10/2015",
            "passportExpiryDate": "17/10/2020",
            "idBirthCountry": 1,
            "kinshipProcurator": "PAI",
            "cpfProcurator": "042.858.780-11",
            "nameProcurator": "Luiz da Silva",
            "identityIssuingDate": "17/10/2002",
            "idIdentityUF": 1,
            "identityIssuingBody": "Detran",
            "nisNumber": 72673612,
            "flagAps": false,
            "idPlanType": 1,
            "registration": "MATRICULATESTE",
            "healthPlan": "PLANOTESTE",
            "enterprise": "EMPRESATESTE",
            "department": "DEPARTAMENTOTESTE",
            "patientRole": "CARGOTESTE"
            },
          "timestamp" : 1739416131423
        }
  recorded_at: Thu, 13 Feb 2025 03:08:51 GMT
recorded_with: VCR 6.3.1
