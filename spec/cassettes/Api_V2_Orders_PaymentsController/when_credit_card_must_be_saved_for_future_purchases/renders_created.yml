---
http_interactions:
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/public/v1/vault/cards
    body:
      encoding: UTF-8
      string: >-
        {
          "number": "****************",
          "holderName": "ADRIANOP SOUZA",
          "cvv": "321",
          "expirationMonth": "12",
          "expirationYear": "25",
          "brand": "visa"
        }
    headers:
      User-Agent:
      - Faraday v2.12.2
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTc0MjU5MDEwOCwiZXhwIjoxNzQyNTkwNDA4fQ.8Y7QV5uB0WwkD_5o8DxQm_RbMdqS0hDxTU_3UTYgHfQ
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Fri, 21 Mar 2025 20:48:34 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '185'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Etag:
      - W/"b9-RtSLrnGds1eUPcW7OP9mGNS5ay8"
    body:
      encoding: UTF-8
      string: >-
        {
          "status": 201,
          "message": "Store Card Success",
          "data": {
            "storeCardId": "3cca03b7-062c-44b6-a6a7-a0212e49ef9c",
            "id": "4195317",
            "companyName": "LECUPON S.A.",
            "documentNumber": "26989697000169"
          }
        }
  recorded_at: Fri, 21 Mar 2025 20:48:34 GMT
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/transactions/v1/payments/card
    body:
      encoding: UTF-8
      string: >-
        {
          "amount": "50.21",
          "paymentCardType": "credit",
          "installments": "1",
          "antifraudCode": "fc10b881-d9a0-4ab1-a6fd-a102db188f49",
          "card": {
            "storeCardId": "3cca03b7-062c-44b6-a6a7-a0212e49ef9c"
          },
          "customer": {
            "name": "ADRIANO PESSOA SOUZA",
            "documentType": "PF",
            "documentNumber": "90936034009",
            "email": "<EMAIL>",
            "phoneNumber": "31999855214",
            "cellPhoneNumber": "31999855214",
            "address": {
              "street": "Rua Jose Lacerda",
              "number": "55",
              "complement": "APT 104 BL 02",
              "neighborhood": "Trevo",
              "city": "Belo Horizonte",
              "state": "MG",
              "zipCode": "45810000",
              "country": "BR"
            }
          },
          "itens": [
            {
              "unitPrice": "50.21",
              "productName": "giftcard",
              "quantity": "1"
            }
          ],
          "deviceInfo": {
            "ipAddress": "*************"
          }
        }
    headers:
      User-Agent:
      - Faraday v2.12.2
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTc0MjU5MDExNCwiZXhwIjoxNzQyNTkwNDE0fQ.RdkooCERt6OS8PUpb-Y3RiucoEfE0H8BCLqfc209nnI
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Sat, 11 Jan 2025 17:34:28 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '536'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Etag:
      - W/"218-kiG1fQXOVOupxXpfCa56nP6Y1Dw"
    body:
      encoding: UTF-8
      string: >-
        {
          "status": 201,
          "message": "Pay Credit Card",
          "data": {
            "orderCode": "561cb534a5304",
            "paymentId": "020091487401111734280002361881640000000000",
            "authorizationCode": "955726",
            "statusText": "PAID",
            "amount": "50.21",
            "gateway": {
              "paymentAuthorization": {
                "returnCode": "0",
                "description": "Sucesso",
                "paymentId": "020091487401111734280002361881640000000000",
                "authorizationCode": "955726",
                "orderNumber": "561cb534a5304",
                "amount": 5021,
                "releaseAt": "2025-01-11T14:34:28.2605125-03:00"
              }
            }
          },
          "id": "4195317",
          "companyName": "LECUPON S.A.",
          "documentNumber": "26989697000169"
        }
  recorded_at: Fri, 21 Mar 2025 20:48:35 GMT
recorded_with: VCR 6.3.1
