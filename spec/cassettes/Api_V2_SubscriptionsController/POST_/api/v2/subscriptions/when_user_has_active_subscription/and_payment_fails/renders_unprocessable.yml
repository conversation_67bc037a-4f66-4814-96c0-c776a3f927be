---
http_interactions:
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/transactions/v1/payments/card
    body:
      encoding: UTF-8
      string: '{"amount":"50.21","paymentCardType":"credit","softDescription":"BUSINESS*ASSINATURA","installments":"1","antifraudCode":"fc10b881-d9a0-4ab1-a6fd-a102db188f49","card":{"sellerId":"4195319","number":"****************","holderName":"ADRIANO
        P SOUZA","securityCode":"321","expirationMonth":"09","expirationYear":"24"},"customer":{"name":"ADRIANO
        PESSOA SOUZA","documentType":"PF","documentNumber":"***********","email":"<EMAIL>","phoneNumber":"***********","cellPhoneNumber":"***********","address":{"street":"<PERSON><PERSON>","number":"55","complement":"APT 104 BL 02","neighborhood":"Trevo","city":"Belo
        Horizonte","state":"MG","zipCode":"45810000","country":"BR"}},"splits":[{"sellerId":"4195317","amount":"1.81","items":[{"code":"subscription","description":"subscription","amount":"1.81"}]},{"sellerId":"4195319","amount":"48.4","items":[{"code":"subscription","description":"subscription","amount":"48.4"}]}],"deviceInfo":{"ipAddress":"*************"}}'
    headers:
      User-Agent:
      - Faraday v2.12.2
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTc0NjU0MDgyMSwiZXhwIjoxNzQ2NTQxMTIxfQ.zD1cA0PqqcqTE_y-Fi_NUOGWgwFE6ifK1u3efJv9LYs
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Tue, 06 May 2025 14:13:44 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '185'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Etag:
      - W/"b9-SpNFuIkfegeQKB1+fSiBswd+4RQ"
    body:
      encoding: UTF-8
      string: '{"status":400,"message":"Bad Request","data":[{"tag":"cardInfo","description":"O
        cartão está vencido."}],"id":"4195317","companyName":"LECUPON S.A.","documentNumber":"26989697000169"}'
  recorded_at: Tue, 06 May 2025 14:13:44 GMT
recorded_with: VCR 6.3.1
