---
http_interactions:
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/transactions/v1/payments/card
    body:
      encoding: UTF-8
      string: '{"amount":"50.21","paymentCardType":"credit","softDescription":"BUSINESS*ASSINATURA","installments":"1","antifraudCode":"fc10b881-d9a0-4ab1-a6fd-a102db188f49","card":{"sellerId":"4195319","number":"****************","holderName":"ADRIANOP
        SOUZA","securityCode":"321","expirationMonth":"12","expirationYear":"25"},"customer":{"name":"<PERSON><PERSON>","documentType":"PF","documentNumber":"***********","email":"<EMAIL>","phoneNumber":"***********","cellPhoneNumber":"***********","address":{"street":"<PERSON><PERSON>","number":"55","complement":"APT 104 BL 02","neighborhood":"Trevo","city":"Belo
        Horizonte","state":"MG","zipCode":"45810000","country":"BR"}},"deviceInfo":{"ipAddress":"*************"}}'
    headers:
      User-Agent:
      - Faraday v2.12.2
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTc0NzE2MTE0NCwiZXhwIjoxNzQ3MTYxNDQ0fQ.-XBDgtqTk0rhjXxSnJi19K4XZBz39tuxMqdC4nq5Edk
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Tue, 13 May 2025 18:32:29 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '536'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Etag:
      - W/"218-ogHDWi+oSqA763ynTX+svjDoFVI"
    body:
      encoding: UTF-8
      string: '{"status":201,"message":"Pay Credit Card","data":{"orderCode":"5a65cf9faaf54","paymentId":"020029065305131832290001440726970000000000","authorizationCode":"331065","statusText":"PAID","amount":"50.21","gateway":{"paymentAuthorization":{"returnCode":"0","description":"Sucesso","paymentId":"020029065305131832290001440726970000000000","authorizationCode":"331065","orderNumber":"5a65cf9faaf54","amount":5021,"releaseAt":"2025-05-13T15:32:29.0550166-03:00"}}},"id":"4195317","companyName":"LECUPON
        S.A.","documentNumber":"26989697000169"}'
  recorded_at: Tue, 13 May 2025 18:32:29 GMT
recorded_with: VCR 6.3.1
