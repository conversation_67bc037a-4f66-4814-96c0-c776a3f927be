---
http_interactions:
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/public/v1/vault/cards
    body:
      encoding: UTF-8
      string: '{"sellerId":"4195317","number":"****************","holderName":"ADRIANO
        P SOUZA","securityCode":"321","expirationMonth":"02","expirationYear":"26","brand":"visa"}'
    headers:
      User-Agent:
      - Faraday v2.12.2
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTc0NzM0NjgzNSwiZXhwIjoxNzQ3MzQ3MTM1fQ.D-8Rm2nc8GaZa7GnasaKl5h-zDX_aAvhQfgbsH8-AWo
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Thu, 15 May 2025 22:07:23 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '296'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"128-UKOGYa6hUiXIsrEsWhG254JreUI"
    body:
      encoding: UTF-8
      string: '{"message":"Store card in vault done","data":{"id":100,"sellerId":4195317,"partnerId":935,"storeCardId":"e873f262-1a78-4f40-a8b7-ce9211a85b91","number":"************0036","holderName":"ADRIANO
        P SOUZA","brand":"visa","createdAt":"2025-05-15T22:07:23.683Z","updatedAt":"2025-05-15T22:07:23.683Z"}}'
  recorded_at: Thu, 15 May 2025 22:07:23 GMT
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/transactions/v1/payments/card
    body:
      encoding: UTF-8
      string: '{"amount":"1.5","paymentCardType":"credit","installments":"1","antifraudCode":"62b8fd91-ee45-4ce8-9083-0a48124324c7","card":{"storeCardId":"e873f262-1a78-4f40-a8b7-ce9211a85b91"},"customer":{"name":"ADRIANO
        PESSOA SOUZA","documentType":"PF","documentNumber":"90936034009","email":"<EMAIL>","phoneNumber":"31999855214","cellPhoneNumber":"31999855214","address":{"street":"Rua
        Jose Lacerda","number":"55","complement":"APT 104 BL 02","neighborhood":"Trevo","city":"Belo
        Horizonte","state":"MG","zipCode":"45810000","country":"BR"}},"splits":[{"sellerId":"4195317","amount":"1.5","items":[{"code":"subscription","description":"subscription","amount":"1.5"}]}],"deviceInfo":{"ipAddress":"*******"}}'
    headers:
      User-Agent:
      - Faraday v2.12.2
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTc0NzM0Njg0NCwiZXhwIjoxNzQ3MzQ3MTQ0fQ.HDQBKwRslQ3b-wBwkuuA_IeEkCpCPslDkw2qqFJ2Umw
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Thu, 15 May 2025 22:07:28 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '533'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Etag:
      - W/"215-J/HFFQ24oIZVU8v0TZr8WMz73IE"
    body:
      encoding: UTF-8
      string: '{"status":201,"message":"Pay Credit Card","data":{"orderCode":"a6efc99068b84","paymentId":"020053079905152207280009573972660000000000","authorizationCode":"481117","statusText":"PAID","amount":"1.5","gateway":{"paymentAuthorization":{"returnCode":"0","description":"Sucesso","paymentId":"020053079905152207280009573972660000000000","authorizationCode":"481117","orderNumber":"a6efc99068b84","amount":150,"releaseAt":"2025-05-15T19:07:28.6277949-03:00"}}},"id":"4195317","companyName":"LECUPON
        S.A.","documentNumber":"26989697000169"}'
  recorded_at: Thu, 15 May 2025 22:07:29 GMT
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/transactions/v1/payments/card
    body:
      encoding: UTF-8
      string: '{"amount":"2.8","paymentCardType":"credit","installments":"1","antifraudCode":"eba35dec-8ed1-4d00-a3be-0f233ddf560c","card":{"storeCardId":"e873f262-1a78-4f40-a8b7-ce9211a85b91"},"customer":{"name":"ADRIANO
        PESSOA SOUZA","documentType":"PF","documentNumber":"90936034009","email":"<EMAIL>","phoneNumber":"31999855214","cellPhoneNumber":"31999855214","address":{"street":"Rua
        Jose Lacerda","number":"55","complement":"APT 104 BL 02","neighborhood":"Trevo","city":"Belo
        Horizonte","state":"MG","zipCode":"45810000","country":"BR"}},"splits":[{"sellerId":"4195317","amount":"2.8","items":[{"code":"subscription","description":"subscription","amount":"2.8"}]}],"deviceInfo":{"ipAddress":"*******"}}'
    headers:
      User-Agent:
      - Faraday v2.12.2
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTc0NzM0Njg0OSwiZXhwIjoxNzQ3MzQ3MTQ5fQ.ElS9jL4ypmn772ZIj-LrsaGcC2LF6ssDRzgqG-b2pxk
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Server:
        - nginx/1.22.0 (Ubuntu)
      Date:
        - Mon, 07 Apr 2025 16:05:41 GMT
      Content-Type:
        - application/json; charset=utf-8
      Content-Length:
        - "170"
      Connection:
        - keep-alive
      X-Powered-By:
        - Express
      Vary:
        - Origin
      Etag:
        - W/"aa-xQDLu2dNGFeVfzj4q6OVXgkdz+s"
    body:
      encoding: UTF-8
      string: '{"status":400,"message":"Bad Request","data":[{"tag":"59","description":"59-DNAO AUTORIZADA  #CRT SUSPENSO-59 "}],"id":"874","companyName":"LECUPON S.A.","documentNumber":"26989697000169"}'
  recorded_at: Thu, 15 May 2025 22:07:31 GMT
recorded_with: VCR 6.3.1
