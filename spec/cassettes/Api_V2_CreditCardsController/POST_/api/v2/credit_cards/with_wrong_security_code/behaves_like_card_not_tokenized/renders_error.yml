---
http_interactions:
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/public/v1/vault/cards
    body:
      encoding: UTF-8
      string: '{"sellerId": "4195317","number":"****************","holderName":"ADRIANOP SOUZA","securityCode":"32","expirationMonth":"02","expirationYear":"26","brand":"visa"}'
    headers:
      User-Agent:
      - Faraday v2.12.2
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTc0MjQyNzQxOCwiZXhwIjoxNzQyNDI3NzE4fQ.q4rt1zi0tevEah-xE8ByZFVAt4OLbtdEeDEsBz-XPKQ
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Wed, 19 Mar 2025 23:36:58 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '172'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Etag:
      - W/"ac-b9EIejZRhKaV5SfkKBcP1zX/Cp0"
    body:
      encoding: UTF-8
      string: '{"status":400,"message":"Bad Params","data":{"errors":{"securityCode":{"value":"32","msg":"Parâmetro
        deve ter mínimo 3 e máximo 4 caracteres.","param":"securityCode","location":"body"}}}}'
  recorded_at: Wed, 19 Mar 2025 23:36:58 GMT
recorded_with: VCR 6.3.1
