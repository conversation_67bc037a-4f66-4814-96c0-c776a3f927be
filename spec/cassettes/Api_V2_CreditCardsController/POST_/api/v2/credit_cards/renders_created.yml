---
http_interactions:
  - request:
      method: post
      uri: https://apisandboxpay.globalpaysolucoes.com.br/api/public/v1/vault/cards
      body:
        encoding: UTF-8
        string: >-
          {
            "sellerId": "4195317",
            "number": "****************",
            "holderName": "ADRIANOP SOUZA",
            "securityCode": "321",
            "expirationMonth": "02",
            "expirationYear": "26",
            "brand": "visa"
          }
      headers:
        User-Agent:
          - Faraday v2.12.2
        Authorization:
          - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTc0MTczNzcwOSwiZXhwIjoxNzQxNzM4MDA5fQ.SFXlpjkhmpXnkfHobCfKS0EyiTp0tbEQsrX33--pFdI
        Content-Type:
          - application/json
        Accept-Encoding:
          - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
        Accept:
          - "*/*"
    response:
      status:
        code: 201
        message: Created
      headers:
        Server:
          - nginx/1.22.0 (Ubuntu)
        Date:
          - Wed, 12 Mar 2025 00:01:52 GMT
        Content-Type:
          - application/json; charset=utf-8
        Content-Length:
          - "185"
        Connection:
          - keep-alive
        X-Powered-By:
          - Express
        Vary:
          - Origin
        Etag:
          - W/"b9-0w/PgO+uQ2YOiMnEjqjC+bT7ULg"
      body:
        encoding: UTF-8
        string: >-
          {
            "message": "Store card in vault done",
            "data": {
              "id": 2,
              "sellerId": 4195317,
              "partnerId": 794,
              "storeCardId": "a6c112b4-b9d2-4b44-9c51-d6fb276706b4",
              "number": "************0759",
              "holderName": "ADRIANO P SOUZA",
              "brand": "mastercard",
              "createdAt": "2025-04-04T20:57:51.493Z",
              "updatedAt": "2025-04-04T20:57:51.493Z"
            }
          }
    recorded_at: Wed, 12 Mar 2025 00:01:52 GMT
recorded_with: VCR 6.3.1
