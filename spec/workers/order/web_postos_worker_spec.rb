require "rails_helper"

RSpec.describe Order::WebPostosWorker do
  describe "#perform" do
    context "when order is penging" do
      let(:bucket) { create(:voucher_bucket) }
      let(:voucher) { create(:voucher, bucket:) }
      let(:pending_order) { create(:order, :pending, voucher:) }

      it "cancel order and voucher" do
        Order::WebPostosWorker.new.perform(pending_order.id)

        expect(pending_order.reload).to be_status_canceled
        expect(pending_order.voucher.canceled_at).not_to be_nil
      end
    end

    context "when order is penging and voucher is used" do
      let(:bucket) { create(:voucher_bucket) }
      let(:voucher) { create(:voucher, bucket:, used_at: DateTime.current) }
      let(:pending_order) { create(:order, :pending, voucher:) }

      it "cancel order and voucher" do
        Order::WebPostosWorker.new.perform(pending_order.id)

        expect(pending_order.reload).to be_status_pending
        expect(pending_order.voucher.canceled_at).to be_nil
      end
    end

    context "when order is completed" do
      let(:bucket) { create(:voucher_bucket) }
      let(:voucher) { create(:voucher, bucket:) }
      let(:pending_order) { create(:order, :completed, voucher:) }

      it "cancel order and voucher" do
        Order::WebPostosWorker.new.perform(pending_order.id)

        expect(pending_order.reload).to be_status_completed
        expect(pending_order.voucher.canceled_at).to be_nil
      end
    end

    context "when order is canceled" do
      let(:bucket) { create(:voucher_bucket) }
      let(:voucher) { create(:voucher, bucket:, canceled_at: DateTime.current) }
      let(:pending_order) { create(:order, :canceled, voucher:) }

      it "cancel order and voucher" do
        expect do
          Order::WebPostosWorker.new.perform(pending_order.id)
          pending_order.reload
        end.to not_change { pending_order.status }
          .and not_change { pending_order.voucher.canceled_at }
      end
    end
  end
end
