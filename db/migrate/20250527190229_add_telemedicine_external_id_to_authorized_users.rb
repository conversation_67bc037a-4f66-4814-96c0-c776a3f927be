class AddTelemedicineExternalIdToAuthorizedUsers < ActiveRecord::Migration[7.2]
  def up
    add_column :authorized_users, :telemedicine_external_id, :string, null: true
    execute "UPDATE authorized_users au SET telemedicine_external_id = (SELECT tb.external_id FROM telemedicine_beneficiaries tb WHERE au.id = tb.authorized_user_id);"
  end

  def down
    remove_column :authorized_users, :telemedicine_external_id
  end
end
