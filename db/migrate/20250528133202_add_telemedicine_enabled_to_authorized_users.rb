class AddTelemedicineEnabledToAuthorizedUsers < ActiveRecord::Migration[7.2]
  def up
    add_column :authorized_users, :telemedicine_enabled, :boolean, default: false
    execute "UPDATE authorized_users au SET telemedicine_enabled = (SELECT tb.enabled FROM telemedicine_beneficiaries tb WHERE au.id = tb.authorized_user_id);"
  end

  def down
    remove_column :authorized_users, :telemedicine_enabled
  end
end
