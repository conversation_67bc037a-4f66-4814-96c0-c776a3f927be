class CreateSubscriptionInstallments < ActiveRecord::Migration[7.2]
  def change
    create_table :subscription_installments do |t|
      t.integer :number, null: false, default: 1
      t.decimal :price, null: false, precision: 10, scale: 2
      t.references :subscription, null: false, foreign_key: true
      t.string :status, null: false, default: "panding"
      t.datetime :paid_at, null: false
      t.datetime :due_at, null: false
      t.integer :retry_count, null: false, default: 0

      t.timestamps
    end
  end
end
