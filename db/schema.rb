# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.2].define(version: 2025_05_26_124250) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"
  enable_extension "unaccent"

  # Custom types defined in this database.
  # Note that some types may not work with other database engines. Be careful if changing database.
  create_enum "business_status", ["active", "inactive", "overdue", "suspended", "suspended_by_overdue"]
  create_enum "discount_type", ["percent", "fixed"]
  create_enum "geo_range_type", ["km"]
  create_enum "lead_entry_type", ["shared", "referred"]
  create_enum "lead_status", ["pending", "approved", "disapproved"]
  create_enum "navigations_kind", ["banner", "menu"]
  create_enum "telemedicine_activity_entry_type", ["accessed", "deactivated", "subscribed", "subscribe_error", "deactivated_due_to_project_configuration", "unsubscribe_error", "changed_plan", "reactivated"]
  create_enum "webhook_integration", ["asaas", "magalu", "woocomerce"]

  create_table "aato_taggings", force: :cascade do |t|
    t.bigint "tag_id"
    t.string "taggable_type"
    t.bigint "taggable_id"
    t.string "tagger_type"
    t.bigint "tagger_id"
    t.string "context", limit: 128
    t.datetime "created_at", precision: nil
    t.string "tenant", limit: 128
    t.index ["context"], name: "index_aato_taggings_on_context"
    t.index ["tag_id", "taggable_id", "taggable_type", "context", "tagger_id", "tagger_type"], name: "taggings_idx", unique: true
    t.index ["tag_id"], name: "index_aato_taggings_on_tag_id"
    t.index ["taggable_id", "taggable_type", "context"], name: "taggings_taggable_context_idx"
    t.index ["taggable_id", "taggable_type", "tagger_id", "context"], name: "taggings_idy"
    t.index ["taggable_id"], name: "index_aato_taggings_on_taggable_id"
    t.index ["taggable_type", "taggable_id"], name: "index_aato_taggings_on_taggable_type_and_taggable_id"
    t.index ["taggable_type"], name: "index_aato_taggings_on_taggable_type"
    t.index ["tagger_id", "tagger_type"], name: "index_aato_taggings_on_tagger_id_and_tagger_type"
    t.index ["tagger_id"], name: "index_aato_taggings_on_tagger_id"
    t.index ["tagger_type", "tagger_id"], name: "index_aato_taggings_on_tagger_type_and_tagger_id"
    t.index ["tenant"], name: "index_aato_taggings_on_tenant"
  end

  create_table "aato_tags", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "taggings_count", default: 0
    t.index ["name"], name: "index_aato_tags_on_name", unique: true
  end

  create_table "admin_organizations", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.bigint "organization_id", null: false
    t.bigint "client_employee_id", null: false
    t.boolean "active", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id", "organization_id", "client_employee_id"], name: "admin_organization_profile_to_client_employee_index", unique: true
    t.index ["business_id"], name: "index_admin_organizations_on_business_id"
    t.index ["client_employee_id"], name: "index_admin_organizations_on_client_employee_id"
    t.index ["organization_id"], name: "index_admin_organizations_on_organization_id"
  end

  create_table "allowlisted_jwts", force: :cascade do |t|
    t.string "jti", null: false
    t.string "aud"
    t.datetime "exp", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["jti", "aud", "user_id"], name: "index_allowlisted_jwts_on_jti_and_aud_and_user_id"
    t.index ["user_id"], name: "index_allowlisted_jwts_on_user_id"
  end

  create_table "auth_integrations", force: :cascade do |t|
    t.string "base_url", null: false
    t.string "http_method", null: false
    t.text "params"
    t.text "headers"
    t.string "content_type", default: "application/json", null: false
    t.boolean "use_on_login", default: true, null: false
    t.boolean "use_on_register", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "business_id", null: false
    t.index ["business_id"], name: "index_auth_integrations_on_business_id"
  end

  create_table "authorized_user_export_files", force: :cascade do |t|
    t.string "custom_field_1_filter"
    t.string "custom_field_2_filter"
    t.string "custom_field_3_filter"
    t.string "custom_field_4_filter"
    t.string "csv"
    t.bigint "business_id", null: false
    t.boolean "processed", default: false
    t.boolean "user_registered_filter"
    t.boolean "user_status_filter"
    t.text "user_tags_filter"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "custom_field_5_filter"
    t.string "custom_field_6_filter"
    t.string "custom_field_7_filter"
    t.string "custom_field_8_filter"
    t.index ["business_id"], name: "index_authorized_user_export_files_on_business_id"
  end

  create_table "authorized_user_groups", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.string "name", null: false
    t.string "node_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "slug"
    t.index ["business_id"], name: "index_authorized_user_groups_on_business_id"
    t.index ["slug", "business_id"], name: "index_authorized_user_groups_on_slug_and_business_id", unique: true
  end

  create_table "authorized_user_import_errors", force: :cascade do |t|
    t.bigint "authorized_user_import_file_id", null: false
    t.integer "line"
    t.string "error_details"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["authorized_user_import_file_id"], name: "authorized_user_import_file_errors"
  end

  create_table "authorized_user_import_files", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.bigint "import_temp_file_id", null: false
    t.integer "import_count", default: 0
    t.integer "total_amount", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "error_count", default: 0
    t.string "invalid_list_file"
    t.integer "process_count", default: 0
    t.index ["business_id"], name: "index_authorized_user_import_files_on_business_id"
    t.index ["import_temp_file_id"], name: "index_authorized_user_import_files_on_import_temp_file_id"
  end

  create_table "authorized_users", force: :cascade do |t|
    t.string "name"
    t.string "cpf"
    t.string "cep"
    t.string "email"
    t.string "phone"
    t.boolean "active", default: true, null: false
    t.boolean "synced", default: false
    t.bigint "business_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "authorized_user_group_id"
    t.string "custom_field_1"
    t.string "custom_field_2"
    t.string "custom_field_3"
    t.string "custom_field_4"
    t.datetime "registered_at", precision: nil
    t.boolean "open_signup", default: false, null: false
    t.string "username"
    t.bigint "main_business_id"
    t.boolean "default_auth_flow", default: false, null: false
    t.boolean "telemedicine", default: false, null: false
    t.string "custom_field_5"
    t.string "custom_field_6"
    t.string "custom_field_7"
    t.string "custom_field_8"
    t.index ["authorized_user_group_id"], name: "index_authorized_users_on_authorized_user_group_id"
    t.index ["business_id"], name: "index_authorized_users_on_business_id"
    t.index ["cpf", "business_id"], name: "index_authorized_users_on_cpf_and_business_id", unique: true
    t.index ["cpf", "main_business_id"], name: "index_authorized_users_on_cpf_and_main_business_id", unique: true
    t.index ["cpf"], name: "index_authorized_users_on_cpf"
    t.index ["main_business_id"], name: "index_authorized_users_on_main_business_id"
    t.index ["telemedicine"], name: "index_authorized_users_on_telemedicine"
  end

  create_table "authorized_users_gmv_metrics", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.decimal "amount", precision: 10, scale: 2, null: false
    t.string "period", null: false
    t.date "ordered_on", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id", "period", "ordered_on"], name: "unique_business_period_ordered_on_auth_user_gmv_metrics", unique: true
    t.index ["business_id"], name: "index_authorized_users_gmv_metrics_on_business_id"
  end

  create_table "banned_cpfs", force: :cascade do |t|
    t.string "cpf", null: false
    t.string "reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["cpf"], name: "index_banned_cpfs_on_cpf", unique: true
  end

  create_table "betalabs_clients", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.bigint "authorized_user_group_id", null: false
    t.string "url_subdomain", null: false
    t.string "username", null: false
    t.string "password", null: false
    t.string "client_id", null: false
    t.string "client_secret", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "token"
    t.index ["authorized_user_group_id"], name: "index_betalabs_clients_on_authorized_user_group_id", unique: true
    t.index ["business_id"], name: "index_betalabs_clients_on_business_id", unique: true
  end

  create_table "branches", force: :cascade do |t|
    t.string "name"
    t.string "cnpj"
    t.text "description"
    t.string "telephone"
    t.string "cep"
    t.string "address"
    t.string "number"
    t.string "complement"
    t.string "neighborhood"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "city_id"
    t.float "lat"
    t.float "lng"
    t.string "qrcode"
    t.time "opening_hour", default: "2000-01-01 00:00:00", null: false
    t.time "closing_hour", default: "2000-01-01 23:59:00", null: false
    t.string "timezone", default: "America/Sao_Paulo"
    t.string "batch"
    t.bigint "organization_id", null: false
    t.boolean "active", default: true, null: false
    t.string "contact_name"
    t.string "contact_email"
    t.string "contact_telephone"
    t.string "branch_type", default: "physical", null: false
    t.string "full_address"
    t.boolean "giftcard_redeemable", default: false, null: false
    t.boolean "promotion_redeemable", default: false, null: false
    t.bigint "business_id"
    t.index ["active"], name: "index_branches_on_active"
    t.index ["branch_type"], name: "index_branches_on_branch_type"
    t.index ["business_id"], name: "index_branches_on_business_id"
    t.index ["cep", "cnpj", "number"], name: "index_branches_on_cep_and_cnpj_and_number", unique: true
    t.index ["city_id"], name: "index_branches_on_city_id"
    t.index ["lat", "lng"], name: "index_branches_on_lat_and_lng"
    t.index ["lat"], name: "index_branches_on_lat"
    t.index ["lng"], name: "index_branches_on_lng"
    t.index ["organization_id"], name: "index_branches_on_organization_id"
  end

  create_table "business_client_employees", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.bigint "client_employee_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "active_member_referral", default: false, null: false
    t.index ["business_id", "client_employee_id"], name: "business_id_client_employee_id", unique: true
    t.index ["business_id"], name: "index_business_client_employees_on_business_id"
    t.index ["client_employee_id"], name: "index_business_client_employees_on_client_employee_id"
  end

  create_table "businesses", force: :cascade do |t|
    t.string "name", null: false
    t.string "description", default: "", null: false
    t.boolean "active", default: true
    t.string "api_secret", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "cnpj", null: false
    t.string "subdomain", default: "lecupon", null: false
    t.boolean "cashback", default: false
    t.string "hinova_wallet_id"
    t.boolean "must_approve_organization", default: false
    t.boolean "lbc_giftcard", default: true, null: false
    t.decimal "spread_percent", precision: 10, scale: 2, default: "0.0", null: false
    t.string "cashback_manager"
    t.integer "cashback_requestable_amount"
    t.boolean "send_email_onboarding", default: false
    t.bigint "main_business_id"
    t.string "contact_email"
    t.string "integration_type", default: "app"
    t.boolean "allow_registration", default: true, null: false
    t.integer "user_count", default: 0
    t.integer "authorized_user_count", default: 0
    t.integer "users_with_redeem_count", default: 0
    t.integer "order_count", default: 0
    t.integer "users_with_redeem_last_month_count", default: 0
    t.integer "users_with_redeem_last_week_count", default: 0
    t.integer "amount_saved_total"
    t.integer "amount_saved_last_month"
    t.integer "amount_saved_last_week"
    t.boolean "giftcard", default: false
    t.string "signature_secret"
    t.boolean "prize_draw", default: false, null: false
    t.boolean "telemedicine", default: false, null: false
    t.boolean "biometry", default: true, null: false
    t.boolean "embedded", default: false, null: false
    t.boolean "organization_manage", default: false, null: false
    t.boolean "cashback_manage", default: false, null: false
    t.string "cashback_wallet_destination", default: "cashback", null: false
    t.boolean "user_request_withdrawal", default: false, null: false
    t.string "hubspot_company_id"
    t.boolean "banner", default: false, null: false
    t.enum "status", default: "active", null: false, enum_type: "business_status"
    t.string "wallet_type", default: "cashback", null: false
    t.decimal "fair_value", precision: 5, scale: 4
    t.integer "days_to_expire_points"
    t.boolean "home_banner", default: false, null: false
    t.boolean "azul_withdrawal", default: false, null: false
    t.string "global_pay_external_id"
    t.string "payment_soft_description"
    t.boolean "sync_user_updates", default: false, null: false
    t.string "currency", default: "BRL", null: false
    t.boolean "payment_split", default: true, null: false
    t.index ["active"], name: "index_businesses_on_active"
    t.index ["api_secret"], name: "index_businesses_on_api_secret", unique: true
    t.index ["cnpj"], name: "index_businesses_on_cnpj", unique: true
    t.index ["currency"], name: "index_businesses_on_currency"
    t.index ["main_business_id"], name: "index_businesses_on_main_business_id"
    t.index ["name"], name: "index_businesses_on_name", unique: true
    t.index ["prize_draw"], name: "index_businesses_on_prize_draw"
    t.index ["wallet_type"], name: "index_businesses_on_wallet_type"
  end

  create_table "cards", force: :cascade do |t|
    t.string "title", null: false
    t.text "description", null: false
    t.string "view_type", default: "support", null: false
    t.integer "position", default: 0, null: false
    t.boolean "active", default: true, null: false
    t.string "feature", default: "subscription", null: false
    t.bigint "business_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id"], name: "index_cards_on_business_id"
  end

  create_table "cashback_discount_request_histories", force: :cascade do |t|
    t.bigint "cashback_discount_request_id", null: false
    t.string "status", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["cashback_discount_request_id"], name: "index_cashback_disc_req_histories_on_cashback_disc_req_id"
  end

  create_table "cashback_discount_requests", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "business_id", null: false
    t.decimal "total_amount", precision: 10, scale: 2, default: "0.0", null: false
    t.string "status", default: "in_transfer", null: false
    t.date "requested_on"
    t.datetime "transferred_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.text "reason"
    t.index ["business_id"], name: "index_cashback_discount_requests_on_business_id"
    t.index ["status"], name: "index_cashback_discount_requests_on_status"
    t.index ["user_id", "requested_on"], name: "index_cashback_discount_requests_on_user_id_and_requested_on", unique: true
    t.index ["user_id"], name: "index_cashback_discount_requests_on_user_id"
  end

  create_table "cashback_metrics", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.decimal "amount", precision: 10, scale: 2, null: false
    t.string "period", null: false
    t.date "created_on", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id", "period", "created_on"], name: "unique_business_period_and_date_cashback_metrics", unique: true
    t.index ["business_id"], name: "index_cashback_metrics_on_business_id"
  end

  create_table "cashback_record_histories", force: :cascade do |t|
    t.bigint "cashback_record_id", null: false
    t.string "transaction_status", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["cashback_record_id"], name: "index_cashback_record_histories_on_cashback_record_id"
  end

  create_table "cashback_records", force: :cascade do |t|
    t.bigint "user_id"
    t.string "transaction_status", default: "pending", null: false
    t.decimal "cashback_amount", precision: 10, scale: 2, null: false
    t.decimal "order_amount", precision: 10, scale: 2, null: false
    t.text "transaction_json"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "batch"
    t.decimal "business_spread_amount", precision: 10, scale: 2, default: "0.0", null: false
    t.decimal "total_commission_amount", precision: 10, scale: 2, default: "0.0", null: false
    t.bigint "order_id"
    t.bigint "payout_id"
    t.bigint "cashback_discount_request_id"
    t.string "external_id"
    t.bigint "authorized_user_id"
    t.boolean "computable", default: true, null: false
    t.index ["authorized_user_id"], name: "index_cashback_records_on_authorized_user_id"
    t.index ["cashback_discount_request_id"], name: "index_cashback_records_on_cashback_discount_request_id"
    t.index ["order_id", "external_id"], name: "index_cashback_records_on_order_id_and_external_id", unique: true
    t.index ["order_id"], name: "index_cashback_records_on_order_id"
    t.index ["payout_id"], name: "index_cashback_records_on_payout_id"
    t.index ["user_id"], name: "index_cashback_records_on_user_id"
  end

  create_table "cashback_transfer_frequencies", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.string "recurring_type", null: false
    t.integer "interval", default: 1, null: false
    t.integer "day_of_week"
    t.integer "day_of_month"
    t.integer "month_of_year"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.date "start_date"
    t.index ["business_id"], name: "index_cashback_transfer_frequencies_on_business_id", unique: true
  end

  create_table "categories", force: :cascade do |t|
    t.string "title", null: false
    t.string "deprecated_icon"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "business_id"
    t.boolean "visible", default: true, null: false
    t.string "category_type", default: "organization"
    t.string "short_description"
    t.string "deprecated_image"
    t.string "template"
    t.boolean "home_pinned", default: false, null: false
    t.integer "position"
    t.string "image"
    t.bigint "main_category_id"
    t.boolean "active", default: true, null: false
    t.index ["business_id"], name: "index_categories_on_business_id"
    t.index ["main_category_id"], name: "index_categories_on_main_category_id"
    t.index ["position", "business_id"], name: "index_categories_on_position_and_business_id", unique: true
    t.index ["template"], name: "index_categories_on_template"
  end

  create_table "category_blocklists", force: :cascade do |t|
    t.bigint "category_id"
    t.string "business_name"
    t.datetime "created_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "updated_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.boolean "blocked_by_lecupon", default: false, null: false
    t.bigint "business_id"
    t.index ["business_id"], name: "index_category_blocklists_on_business_id"
    t.index ["category_id", "business_id"], name: "index_category_blocklists_on_category_id_and_business_id", unique: true
    t.index ["category_id"], name: "index_category_blocklists_on_category_id"
  end

  create_table "category_metrics", force: :cascade do |t|
    t.bigint "category_id", null: false
    t.bigint "business_id", null: false
    t.integer "redeem_count", null: false
    t.string "category_name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "redeemed_on"
    t.index ["business_id"], name: "index_category_metrics_on_business_id"
    t.index ["category_id", "business_id", "redeemed_on"], name: "unique_category_business_redeemed_on_category_metrics", unique: true
    t.index ["category_id"], name: "index_category_metrics_on_category_id"
  end

  create_table "cities", force: :cascade do |t|
    t.bigint "federation_unit_id", null: false
    t.string "name", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["federation_unit_id"], name: "index_cities_on_federation_unit_id"
  end

  create_table "client_employees", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "authentication_token", limit: 30
    t.string "role", default: "admin_client"
    t.string "username"
    t.boolean "migrated", default: true, null: false
    t.string "jti"
    t.string "invitation_token"
    t.datetime "invitation_created_at"
    t.datetime "invitation_sent_at"
    t.datetime "invitation_accepted_at"
    t.integer "invitation_limit"
    t.string "invited_by_type"
    t.bigint "invited_by_id"
    t.integer "invitations_count", default: 0
    t.boolean "system_user", default: false, null: false
    t.index ["authentication_token"], name: "index_client_employees_on_authentication_token", unique: true
    t.index ["email"], name: "index_client_employees_on_email", unique: true
    t.index ["invitation_token"], name: "index_client_employees_on_invitation_token", unique: true
    t.index ["invited_by_id"], name: "index_client_employees_on_invited_by_id"
    t.index ["invited_by_type", "invited_by_id"], name: "index_client_employees_on_invited_by"
    t.index ["jti"], name: "index_client_employees_on_jti", unique: true
    t.index ["reset_password_token"], name: "index_client_employees_on_reset_password_token", unique: true
    t.index ["username"], name: "index_client_employees_on_username", unique: true
  end

  create_table "clube_associados_tokens", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.string "base_url", null: false
    t.string "token", null: false
    t.string "application_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id"], name: "index_clube_associados_tokens_on_business_id"
  end

  create_table "credit_cards", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.boolean "main", default: false, null: false
    t.string "token", null: false
    t.string "last4", null: false
    t.string "brand", null: false
    t.string "billing_name", null: false
    t.string "billing_document", null: false
    t.string "billing_email", null: false
    t.string "billing_phone", null: false
    t.string "billing_postal_code", null: false
    t.string "billing_street", null: false
    t.string "billing_number", null: false
    t.string "billing_complement"
    t.string "billing_neighborhood", null: false
    t.string "billing_city", null: false
    t.string "billing_state", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["token"], name: "index_credit_cards_on_token", unique: true
    t.index ["user_id", "main"], name: "index_credit_cards_on_user_id_and_main", unique: true, where: "main"
    t.index ["user_id"], name: "index_credit_cards_on_user_id"
  end

  create_table "cupons", force: :cascade do |t|
    t.string "cuponable_type"
    t.bigint "cuponable_id"
    t.text "description"
    t.text "rules"
    t.string "code"
    t.text "url"
    t.decimal "discount_value", precision: 10, scale: 2
    t.integer "quantity"
    t.integer "redeemed_count", default: 0, null: false
    t.datetime "start_date", precision: nil
    t.datetime "end_date", precision: nil
    t.time "start_hour"
    t.time "end_hour"
    t.boolean "monday"
    t.boolean "tuesday"
    t.boolean "wednesday"
    t.boolean "thursday"
    t.boolean "friday"
    t.boolean "saturday"
    t.boolean "sunday"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "not_cumulative", default: false
    t.boolean "only_physical", default: false
    t.boolean "one_for_cpf", default: false
    t.boolean "one_per_day", default: false
    t.boolean "infinity", default: false, null: false
    t.integer "custom_id"
    t.datetime "deleted_at", precision: nil
    t.float "price", default: 0.0
    t.string "c_type", default: "CLASSIC"
    t.string "exclusive"
    t.decimal "average_ticket", precision: 10, scale: 2, default: "0.0"
    t.integer "frequency_in_days", default: 0, null: false
    t.bigint "business_id"
    t.string "integration_code"
    t.bigint "promotion_id", null: false
    t.string "batch"
    t.bigint "branch_id"
    t.bigint "organization_id"
    t.string "contract_custom_text"
    t.string "integration_partner"
    t.integer "redeems_per_cpf", default: 999
    t.string "title"
    t.boolean "active", default: true, null: false
    t.text "cashback_rules"
    t.enum "discount_type", enum_type: "discount_type"
    t.string "usage_instruction"
    t.index ["active"], name: "index_cupons_on_active"
    t.index ["branch_id"], name: "index_cupons_on_branch_id"
    t.index ["business_id"], name: "index_cupons_on_business_id"
    t.index ["cuponable_id", "cuponable_type"], name: "index_cupons_on_cuponable_id_and_cuponable_type"
    t.index ["cuponable_type", "cuponable_id"], name: "index_cupons_on_cuponable_type_and_cuponable_id"
    t.index ["deleted_at"], name: "index_cupons_on_deleted_at"
    t.index ["organization_id"], name: "index_cupons_on_organization_id"
    t.index ["promotion_id"], name: "index_cupons_on_promotion_id"
  end

  create_table "custom_field_values", force: :cascade do |t|
    t.bigint "user_id"
    t.string "field_value", null: false
    t.bigint "custom_field_id", null: false
    t.bigint "authorized_user_id", null: false
    t.index ["authorized_user_id"], name: "index_custom_field_values_on_authorized_user_id"
    t.index ["custom_field_id", "authorized_user_id"], name: "unique_authorized_user_custom_field_value", unique: true
    t.index ["custom_field_id", "user_id"], name: "unique_user_custom_field_value", unique: true
    t.index ["custom_field_id"], name: "index_custom_field_values_on_custom_field_id"
    t.index ["user_id"], name: "index_custom_field_values_on_user_id"
  end

  create_table "custom_fields", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.string "name", null: false
    t.string "label"
    t.string "description"
    t.string "input_type", default: "text", null: false
    t.string "placeholder"
    t.boolean "sign_in_param", default: true, null: false
    t.boolean "sign_up_param", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "param_name", null: false
    t.boolean "account_update_param", default: true, null: false
    t.string "fill_field"
    t.index ["business_id"], name: "index_custom_fields_on_business_id"
    t.index ["sign_in_param"], name: "index_custom_fields_on_sign_in_param"
    t.index ["sign_up_param"], name: "index_custom_fields_on_sign_up_param"
  end

  create_table "custom_texts", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.string "message_type", null: false
    t.text "message", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["business_id", "message_type"], name: "index_custom_texts_on_business_id_and_message_type", unique: true
    t.index ["business_id"], name: "index_custom_texts_on_business_id"
  end

  create_table "favorite_organizations", force: :cascade do |t|
    t.bigint "organization_id"
    t.bigint "user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["organization_id"], name: "index_favorite_organizations_on_organization_id"
    t.index ["user_id", "organization_id"], name: "index_favorite_organizations_on_user_id_and_organization_id", unique: true
    t.index ["user_id"], name: "index_favorite_organizations_on_user_id"
  end

  create_table "federation_units", force: :cascade do |t|
    t.string "name", null: false
    t.string "initials", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "timezone", default: "America/Sao_Paulo"
  end

  create_table "fund_activities", force: :cascade do |t|
    t.bigint "fund_id", null: false
    t.string "kind", null: false
    t.string "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["fund_id"], name: "index_fund_activities_on_fund_id"
  end

  create_table "fund_debits", force: :cascade do |t|
    t.bigint "fund_id", null: false
    t.integer "amount", null: false
    t.string "kind", null: false
    t.text "description", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["fund_id"], name: "index_fund_debits_on_fund_id"
  end

  create_table "fund_redemption_configs", force: :cascade do |t|
    t.string "service", null: false
    t.decimal "fair_value", precision: 5, scale: 4, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "business_id"
    t.integer "min_amount"
    t.integer "multiple"
    t.string "currency"
    t.index ["business_id", "currency", "service"], name: "idx_on_business_id_currency_service_8041dc9609", unique: true
    t.index ["business_id"], name: "index_fund_redemption_configs_on_business_id"
  end

  create_table "fund_redemptions", force: :cascade do |t|
    t.string "currency", null: false
    t.string "service", null: false
    t.string "product"
    t.decimal "fair_value", precision: 5, scale: 4, null: false
    t.integer "min_amount"
    t.integer "multiple"
    t.bigint "business_id", null: false
    t.bigint "user_id", null: false
    t.integer "amount", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id"], name: "index_fund_redemptions_on_business_id"
    t.index ["user_id"], name: "index_fund_redemptions_on_user_id"
  end

  create_table "funds", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "order_id"
    t.string "idempotency_key"
    t.string "currency", null: false
    t.string "status", default: "pending", null: false
    t.decimal "fair_value", precision: 5, scale: 4
    t.decimal "accrual_parity_value", precision: 4, scale: 1
    t.integer "amount", null: false
    t.integer "original_amount", null: false
    t.integer "monetary_amount", null: false
    t.integer "order_amount"
    t.datetime "credits_at"
    t.datetime "expires_at"
    t.json "metadata"
    t.string "import_batch"
    t.boolean "credited", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "business_id", null: false
    t.boolean "computable", default: true, null: false
    t.integer "remaining_amount"
    t.bigint "subscription_id"
    t.index ["business_id", "idempotency_key"], name: "index_funds_on_business_id_and_idempotency_key", unique: true
    t.index ["business_id"], name: "index_funds_on_business_id"
    t.index ["order_id", "idempotency_key"], name: "index_funds_on_order_id_and_idempotency_key", unique: true
    t.index ["order_id"], name: "index_funds_on_order_id"
    t.index ["subscription_id"], name: "index_funds_on_subscription_id"
    t.index ["user_id"], name: "index_funds_on_user_id"
  end

  create_table "giftcard_branches", force: :cascade do |t|
    t.bigint "giftcard_id", null: false
    t.bigint "branch_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["branch_id"], name: "index_giftcard_branches_on_branch_id"
    t.index ["giftcard_id"], name: "index_giftcard_branches_on_giftcard_id"
  end

  create_table "giftcard_configs", force: :cascade do |t|
    t.bigint "business_id"
    t.integer "limit_by_user", default: 0, null: false
    t.integer "limit_by_user_period_interval", default: 1, null: false
    t.string "limit_by_user_period_type", default: "month", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id"], name: "index_giftcard_configs_on_business_id"
  end

  create_table "giftcards", force: :cascade do |t|
    t.string "title", null: false
    t.decimal "price", precision: 10, scale: 2, null: false
    t.boolean "available", default: false, null: false
    t.datetime "sale_started_at", null: false
    t.datetime "sale_ended_at", null: false
    t.datetime "usage_started_at", null: false
    t.datetime "usage_ended_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "usage_instruction"
    t.boolean "usage_expired", default: false
    t.string "cashback_type"
    t.decimal "cashback_value", precision: 10, scale: 2
    t.bigint "voucher_bucket_id"
    t.index ["voucher_bucket_id"], name: "index_giftcards_on_voucher_bucket_id"
  end

  create_table "gmv_metrics", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.decimal "amount", precision: 10, scale: 2, null: false
    t.string "period", null: false
    t.date "ordered_on", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id", "period", "ordered_on"], name: "unique_business_period_ordered_on_gmv_metrics", unique: true
    t.index ["business_id"], name: "index_gmv_metrics_on_business_id"
  end

  create_table "highlight_organizations", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.bigint "organization_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["business_id", "organization_id"], name: "business_id_organization_id", unique: true
    t.index ["business_id"], name: "index_highlight_organizations_on_business_id"
    t.index ["organization_id"], name: "index_highlight_organizations_on_organization_id"
  end

  create_table "hinova_tokens", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.text "token", null: false
    t.string "group_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "authorized_user_group_id"
    t.index ["authorized_user_group_id"], name: "index_hinova_tokens_on_authorized_user_group_id", unique: true
    t.index ["business_id"], name: "index_hinova_tokens_on_business_id"
    t.index ["group_id"], name: "index_hinova_tokens_on_group_id", unique: true
  end

  create_table "hubsoft_services", force: :cascade do |t|
    t.bigint "hubsoft_token_id", null: false
    t.string "external_id", null: false
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["hubsoft_token_id"], name: "index_hubsoft_services_on_hubsoft_token_id"
  end

  create_table "hubsoft_tokens", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.bigint "authorized_user_group_id"
    t.string "base_url", null: false
    t.string "username", null: false
    t.string "password", null: false
    t.string "client_id", null: false
    t.string "client_secret", null: false
    t.string "bundle_code"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "use_only_cpf", default: false, null: false
    t.index ["authorized_user_group_id"], name: "index_hubsoft_tokens_on_authorized_user_group_id", unique: true
    t.index ["business_id"], name: "index_hubsoft_tokens_on_business_id"
  end

  create_table "ileva_clients", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.bigint "authorized_user_group_id", null: false
    t.string "token", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["authorized_user_group_id"], name: "index_ileva_clients_on_authorized_user_group_id", unique: true
    t.index ["business_id"], name: "index_ileva_clients_on_business_id", unique: true
  end

  create_table "import_file_errors", force: :cascade do |t|
    t.bigint "import_file_id", null: false
    t.json "row"
    t.integer "line"
    t.json "details", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["import_file_id"], name: "index_import_file_errors_on_import_file_id"
  end

  create_table "import_files", force: :cascade do |t|
    t.string "file", null: false
    t.integer "amount_data_rows", default: 0
    t.integer "amount_rows_processed_successfully", default: 0
    t.integer "amount_rows_with_errors", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "status"
    t.bigint "business_id"
    t.bigint "client_employee_id"
    t.string "kind", null: false
    t.json "metadata"
    t.string "batch"
    t.index ["business_id"], name: "index_import_files_on_business_id"
    t.index ["client_employee_id"], name: "index_import_files_on_client_employee_id"
    t.index ["status"], name: "index_import_files_on_status"
  end

  create_table "import_temp_files", force: :cascade do |t|
    t.string "file", null: false
    t.datetime "created_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "updated_at", precision: nil
  end

  create_table "integration_tokens", force: :cascade do |t|
    t.string "service", null: false
    t.string "description", null: false
    t.string "token", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "username"
    t.string "password_digest"
    t.index ["token"], name: "index_integration_tokens_on_token", unique: true
    t.index ["username"], name: "index_integration_tokens_on_username", unique: true
  end

  create_table "ixc_contracts", force: :cascade do |t|
    t.string "external_id"
    t.bigint "ixc_token_id"
    t.string "name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["ixc_token_id"], name: "index_ixc_contracts_on_ixc_token_id"
  end

  create_table "ixc_tokens", force: :cascade do |t|
    t.string "token", null: false
    t.string "base_url", null: false
    t.bigint "business_id", null: false
    t.string "group_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "authorized_user_group_id"
    t.string "allowed_contracts_status", null: false
    t.string "product_id"
    t.index ["authorized_user_group_id"], name: "index_ixc_tokens_on_authorized_user_group_id"
    t.index ["business_id"], name: "index_ixc_tokens_on_business_id"
  end

  create_table "leads", force: :cascade do |t|
    t.enum "entry_type", null: false, enum_type: "lead_entry_type"
    t.enum "status", default: "pending", null: false, enum_type: "lead_status"
    t.string "name", null: false
    t.string "email", null: false
    t.string "phone", null: false
    t.jsonb "config_snapshot", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "referrer_id", null: false
    t.bigint "business_id"
    t.index ["business_id"], name: "index_leads_on_business_id"
    t.index ["referrer_id"], name: "index_leads_on_referrer_id"
  end

  create_table "mailer_configs", force: :cascade do |t|
    t.bigint "business_id"
    t.string "email_sender"
    t.boolean "domain_configured", default: false, null: false
    t.boolean "dns_configured", default: false, null: false
    t.jsonb "dns_records", default: [], null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "logo"
    t.boolean "send_transactional_emails", default: true, null: false
    t.index ["business_id"], name: "index_mailer_configs_on_business_id"
  end

  create_table "maintenance_tasks_runs", force: :cascade do |t|
    t.string "task_name", null: false
    t.datetime "started_at", precision: nil
    t.datetime "ended_at", precision: nil
    t.float "time_running", default: 0.0, null: false
    t.bigint "tick_count", default: 0, null: false
    t.bigint "tick_total"
    t.string "job_id"
    t.string "cursor"
    t.string "status", default: "enqueued", null: false
    t.string "error_class"
    t.string "error_message"
    t.text "backtrace"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "arguments"
    t.integer "lock_version", default: 0, null: false
    t.text "metadata"
    t.index ["task_name", "status", "created_at"], name: "index_maintenance_tasks_runs", order: { created_at: :desc }
  end

  create_table "member_referral_configs", force: :cascade do |t|
    t.string "referrer_benefit"
    t.string "referred_benefit"
    t.text "share_message"
    t.text "register_page_content"
    t.boolean "active", default: false, null: false
    t.bigint "business_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id"], name: "index_member_referral_configs_on_business_id"
  end

  create_table "mk_solution_plans", force: :cascade do |t|
    t.bigint "mk_solution_token_id", null: false
    t.string "name", null: false
    t.string "external_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["mk_solution_token_id"], name: "index_mk_solution_plans_on_mk_solution_token_id"
  end

  create_table "mk_solution_tokens", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.string "base_url", null: false
    t.string "user_token", null: false
    t.string "password", null: false
    t.string "group_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "authorized_user_group_id"
    t.string "auth_strategy", null: false
    t.index ["auth_strategy"], name: "index_mk_solution_tokens_on_auth_strategy"
    t.index ["authorized_user_group_id"], name: "index_mk_solution_tokens_on_authorized_user_group_id"
    t.index ["business_id"], name: "index_mk_solution_tokens_on_business_id"
  end

  create_table "navigation_geolocations", force: :cascade do |t|
    t.string "address", null: false
    t.float "lat", null: false
    t.float "long", null: false
    t.enum "range_type", default: "km", null: false, enum_type: "geo_range_type"
    t.integer "range_value", null: false
    t.bigint "navigation_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["navigation_id"], name: "index_navigation_geolocations_on_navigation_id"
  end

  create_table "navigations", force: :cascade do |t|
    t.bigint "exclusive_business_id"
    t.string "title"
    t.string "subtitle"
    t.text "destination", null: false
    t.string "cpf_param_name"
    t.string "email_param_name"
    t.boolean "fixed", default: false, null: false
    t.string "icon"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "cnpj_param_name"
    t.string "custom_field_1_param_name"
    t.string "custom_field_2_param_name"
    t.string "custom_field_3_param_name"
    t.string "custom_field_4_param_name"
    t.integer "position"
    t.string "header_param_name"
    t.string "header_param_value"
    t.string "http_method", default: "get"
    t.boolean "encrypt_request", default: false, null: false
    t.string "name_param_name"
    t.boolean "active", default: true, null: false
    t.string "feature_flag_rule"
    t.string "device_type", default: "all", null: false
    t.string "disposition"
    t.bigint "business_id", null: false
    t.boolean "open_new_tab_on_web", default: false, null: false
    t.string "custom_field_5_param_name"
    t.string "custom_field_6_param_name"
    t.string "custom_field_7_param_name"
    t.string "custom_field_8_param_name"
    t.enum "kind", null: false, enum_type: "navigations_kind"
    t.datetime "starts_at"
    t.datetime "ends_at"
    t.string "provider"
    t.boolean "georeferenced", default: false, null: false
    t.string "image"
    t.jsonb "metadata"
    t.string "min_version"
    t.string "mobile_navigation_option"
    t.boolean "smart_link", default: false, null: false
    t.string "web_navigation_option", default: "self"
    t.string "max_version"
    t.index ["active"], name: "index_navigations_on_active"
    t.index ["business_id"], name: "index_navigations_on_business_id"
    t.index ["exclusive_business_id"], name: "index_navigations_on_exclusive_business_id"
  end

  create_table "order_metrics", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.integer "redeem_count", null: false
    t.string "period", null: false
    t.date "redeemed_on", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id", "period", "redeemed_on"], name: "unique_business_period_redeemed_on_order_metrics", unique: true
    t.index ["business_id"], name: "index_order_metrics_on_business_id"
  end

  create_table "orders", force: :cascade do |t|
    t.string "number", null: false
    t.bigint "user_id"
    t.text "description"
    t.string "redeem_code"
    t.string "organization_name", null: false
    t.bigint "cupon_id"
    t.bigint "purchase_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "branch_name"
    t.string "user_name"
    t.string "user_cpf"
    t.bigint "business_id"
    t.string "business_name"
    t.string "redeem_type", default: "physical", null: false
    t.datetime "redeemed_at", precision: nil
    t.datetime "used_at", precision: nil
    t.bigint "voucher_id"
    t.decimal "average_saving", precision: 10, scale: 2, default: "0.0"
    t.boolean "business_cashback", default: false, null: false
    t.decimal "cashback_percent", precision: 10, scale: 2, default: "0.0", null: false
    t.string "promotion_provider"
    t.text "deep_link"
    t.bigint "organization_id"
    t.bigint "branch_id"
    t.string "status"
    t.bigint "giftcard_id"
    t.decimal "price", precision: 10, scale: 2
    t.datetime "paid_at"
    t.datetime "usage_ended_at"
    t.text "usage_instruction"
    t.boolean "usage_expired", default: false
    t.string "title"
    t.string "integration_partner"
    t.string "cashback_type"
    t.decimal "cashback_value", precision: 10, scale: 2
    t.string "batch"
    t.json "import_metadata"
    t.enum "discount_type", enum_type: "discount_type"
    t.decimal "amount", precision: 10, scale: 2
    t.bigint "authorized_user_id"
    t.decimal "discount_value", precision: 10, scale: 2
    t.decimal "net_value", precision: 10, scale: 2
    t.string "external_id"
    t.decimal "fair_value", precision: 5, scale: 4
    t.string "wallet_type", default: "cashback", null: false
    t.string "payment_gateway"
    t.decimal "redemption_fair_value", precision: 5, scale: 4
    t.string "currency"
    t.index ["authorized_user_id"], name: "index_orders_on_authorized_user_id"
    t.index ["branch_id"], name: "index_orders_on_branch_id"
    t.index ["business_id", "external_id"], name: "index_orders_on_business_id_and_external_id", unique: true
    t.index ["business_id"], name: "index_orders_on_business_id"
    t.index ["cupon_id"], name: "index_orders_on_cupon_id"
    t.index ["currency"], name: "index_orders_on_currency"
    t.index ["giftcard_id"], name: "index_orders_on_giftcard_id"
    t.index ["number"], name: "index_orders_on_number", unique: true
    t.index ["organization_id"], name: "index_orders_on_organization_id"
    t.index ["purchase_id"], name: "index_orders_on_purchase_id"
    t.index ["user_id"], name: "index_orders_on_user_id"
    t.index ["voucher_id"], name: "index_orders_on_voucher_id"
  end

  create_table "organization_blocklists", force: :cascade do |t|
    t.string "business_name"
    t.bigint "organization_id", null: false
    t.datetime "created_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "updated_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.bigint "business_id"
    t.index ["business_id"], name: "index_organization_blocklists_on_business_id"
    t.index ["organization_id"], name: "index_organization_blocklists_on_organization_id"
  end

  create_table "organization_categories", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.bigint "category_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "position"
    t.index ["category_id", "organization_id"], name: "unique_organization_by_category_index", unique: true
    t.index ["category_id"], name: "index_organization_categories_on_category_id"
    t.index ["organization_id"], name: "index_organization_categories_on_organization_id"
    t.index ["position", "organization_id", "category_id"], name: "index_org_categories_on_position_and_org_and_category_id", unique: true
  end

  create_table "organization_integration_partners", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.string "integration_partner", null: false
    t.string "integration_code"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "integration_hash"
    t.index ["organization_id"], name: "index_organization_integration_partners_on_organization_id"
  end

  create_table "organization_metrics", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.bigint "business_id", null: false
    t.integer "redeem_count", null: false
    t.string "organization_name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "redeemed_on"
    t.index ["business_id"], name: "index_organization_metrics_on_business_id"
    t.index ["organization_id", "business_id", "redeemed_on"], name: "unique_org_business_redeemed_on_org_metrics", unique: true
    t.index ["organization_id"], name: "index_organization_metrics_on_organization_id"
  end

  create_table "organization_profiles", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.bigint "business_id", null: false
    t.text "description"
    t.string "short_description"
    t.string "logo_image"
    t.string "top_background_image"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "cashback_manage", default: false, null: false
    t.decimal "highest_discount_value", precision: 10, scale: 2
    t.string "highest_cashback_type"
    t.decimal "highest_cashback_value", precision: 10, scale: 2, default: "0.0"
    t.enum "highest_discount_type", enum_type: "discount_type"
    t.index ["business_id"], name: "index_organization_profiles_on_business_id"
    t.index ["organization_id", "business_id"], name: "index_organization_profiles_on_organization_id_and_business_id", unique: true
    t.index ["organization_id"], name: "index_organization_profiles_on_organization_id"
  end

  create_table "organization_promotion_providers", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.string "promotion_provider", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["organization_id", "promotion_provider"], name: "organization_id_promotion_provider", unique: true
    t.index ["organization_id"], name: "index_organization_promotion_providers_on_organization_id"
  end

  create_table "organizations", force: :cascade do |t|
    t.string "name", null: false
    t.string "logo_image"
    t.text "description"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "category_id"
    t.text "instagram"
    t.string "facebook"
    t.string "twitter"
    t.integer "cupons_count", default: 0
    t.decimal "highest_discount_value", precision: 10, scale: 2
    t.boolean "active", default: true
    t.boolean "pined", default: false
    t.decimal "highest_cashback_value", precision: 10, scale: 2, default: "0.0"
    t.string "contact_name"
    t.string "contact_email"
    t.string "contact_telephone"
    t.string "banner_image_v1"
    t.string "short_description"
    t.boolean "giftcard_redeemable", default: false, null: false
    t.string "top_background_image"
    t.string "top_background_image_v1"
    t.string "banner_image"
    t.string "banner_without_cashback_image_v1"
    t.string "banner_without_cashback_image"
    t.boolean "online_branches", default: false
    t.boolean "physical_branches", default: false
    t.string "highest_cashback_type"
    t.string "cashback_type"
    t.decimal "cashback_value", precision: 10, scale: 2
    t.string "cnpj", limit: 14
    t.boolean "promotion_redeemable", default: false, null: false
    t.boolean "all_projects", default: true, null: false
    t.bigint "creator_id"
    t.enum "discount_type", enum_type: "discount_type"
    t.enum "highest_discount_type", enum_type: "discount_type"
    t.decimal "giftcard_min_price", precision: 10, scale: 2
    t.index ["active"], name: "index_organizations_on_active"
    t.index ["category_id"], name: "index_organizations_on_category_id"
    t.index ["creator_id"], name: "index_organizations_on_creator_id"
    t.index ["online_branches"], name: "index_organizations_on_online_branches"
    t.index ["physical_branches"], name: "index_organizations_on_physical_branches"
  end

  create_table "payment_history", force: :cascade do |t|
    t.bigint "payment_id", null: false
    t.string "status"
    t.text "json"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["payment_id"], name: "index_payment_history_on_payment_id"
  end

  create_table "payments", force: :cascade do |t|
    t.bigint "order_id"
    t.decimal "value", precision: 10, scale: 2, null: false
    t.string "status", null: false
    t.string "reason_denied"
    t.text "json"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "payment_method"
    t.string "pix_qrcode"
    t.string "idempotency_key"
    t.bigint "subscription_id"
    t.jsonb "split_data"
    t.string "soft_description"
    t.index ["idempotency_key"], name: "index_payments_on_idempotency_key", unique: true
    t.index ["order_id"], name: "index_payments_on_order_id"
    t.index ["subscription_id"], name: "index_payments_on_subscription_id"
    t.check_constraint "num_nonnulls(order_id, subscription_id) = 1", name: "payable_is_not_null"
  end

  create_table "payout_histories", force: :cascade do |t|
    t.bigint "payout_id", null: false
    t.string "status", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["payout_id"], name: "index_payout_histories_on_payout_id"
  end

  create_table "payouts", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "business_id"
    t.decimal "total_amount", precision: 10, scale: 2, default: "0.0", null: false
    t.string "receiver_taxpayer_number", null: false
    t.text "reason"
    t.string "status", default: "open", null: false
    t.datetime "transferred_at", precision: nil
    t.date "due_on"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "locked_at", precision: nil
    t.date "requested_on"
    t.bigint "user_business_id"
    t.string "lock_code"
    t.string "kind", default: "pix", null: false
    t.boolean "reversible", default: false, null: false
    t.string "wallet_destination"
    t.string "wallet_type", default: "cashback", null: false
    t.decimal "fair_value", precision: 5, scale: 4
    t.string "currency"
    t.index ["business_id", "due_on"], name: "index_payouts_on_business_id_and_due_on", unique: true
    t.index ["business_id"], name: "index_payouts_on_business_id"
    t.index ["currency"], name: "index_payouts_on_currency"
    t.index ["kind"], name: "index_payouts_on_kind"
    t.index ["receiver_taxpayer_number"], name: "index_payouts_on_receiver_taxpayer_number"
    t.index ["status"], name: "index_payouts_on_status"
    t.index ["user_business_id"], name: "index_payouts_on_user_business_id"
    t.index ["user_id"], name: "index_payouts_on_user_id"
  end

  create_table "plans", force: :cascade do |t|
    t.string "title", null: false
    t.decimal "price", precision: 10, scale: 2, null: false
    t.boolean "active", default: true
    t.integer "points", default: 0
    t.string "background_color", default: "#ffffff", null: false
    t.string "font_color", default: "#000000", null: false
    t.string "recurrence", default: "monthly", null: false
    t.bigint "business_id", null: false
    t.bigint "subscription_config_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "destination_business_id"
    t.string "benefit_description"
    t.index ["business_id"], name: "index_plans_on_business_id"
    t.index ["destination_business_id"], name: "index_plans_on_destination_business_id"
    t.index ["subscription_config_id"], name: "index_plans_on_subscription_config_id"
  end

  create_table "playhub_clients", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.bigint "authorized_user_group_id", null: false
    t.string "base_url", default: "https://stage3.pca.com.br/playhub_demo/api_lecupom/lecupom", null: false
    t.string "client_id", null: false
    t.string "isp_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["authorized_user_group_id"], name: "index_playhub_clients_on_authorized_user_group_id", unique: true
    t.index ["business_id"], name: "index_playhub_clients_on_business_id", unique: true
  end

  create_table "policies", force: :cascade do |t|
    t.text "content", null: false
    t.string "kind", null: false
    t.bigint "business_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id"], name: "index_policies_on_business_id"
    t.index ["kind"], name: "index_policies_on_kind"
  end

  create_table "prize_draw_entries", force: :cascade do |t|
    t.string "lucky_number", null: false
    t.string "full_name", null: false
    t.string "email", null: false
    t.string "phone", null: false
    t.string "address_zip_code", null: false
    t.string "address_city", null: false
    t.string "address_state", null: false
    t.string "address_street", null: false
    t.string "address_complement"
    t.string "address_number", null: false
    t.string "address_neighborhood", null: false
    t.bigint "user_id", null: false
    t.bigint "prize_draw_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "acceptance_of_terms", default: false, null: false
    t.index ["lucky_number", "prize_draw_id"], name: "index_prize_draw_entries_on_lucky_number_and_prize_draw_id", unique: true
    t.index ["prize_draw_id"], name: "index_prize_draw_entries_on_prize_draw_id"
    t.index ["user_id", "prize_draw_id"], name: "index_prize_draw_entries_on_user_id_and_prize_draw_id", unique: true
    t.index ["user_id"], name: "index_prize_draw_entries_on_user_id"
  end

  create_table "prize_draw_run_histories", force: :cascade do |t|
    t.string "drawn_number", null: false
    t.string "comparison", null: false
    t.bigint "prize_draw_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["drawn_number", "prize_draw_id", "comparison"], name: "idx_pzd_run_hist_search_already_exists", unique: true
    t.index ["prize_draw_id"], name: "index_prize_draw_run_histories_on_prize_draw_id"
  end

  create_table "prize_draw_statuses", force: :cascade do |t|
    t.string "to_state", null: false
    t.json "metadata"
    t.integer "sort_key", null: false
    t.bigint "prize_draw_id", null: false
    t.boolean "most_recent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["prize_draw_id", "most_recent"], name: "index_prize_draw_statuses_parent_most_recent", unique: true
    t.index ["prize_draw_id", "sort_key"], name: "index_prize_draw_statuses_parent_sort", unique: true
  end

  create_table "prize_draw_winners", force: :cascade do |t|
    t.string "drawn_number"
    t.bigint "prize_draw_id", null: false
    t.bigint "owner_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "comparison"
    t.index ["owner_id"], name: "index_prize_draw_winners_on_owner_id"
    t.index ["prize_draw_id"], name: "index_prize_draw_winners_on_prize_draw_id", unique: true
  end

  create_table "prize_drawn_numbers", force: :cascade do |t|
    t.string "number_sequence", null: false
    t.string "caixa_identifier", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["caixa_identifier"], name: "index_prize_drawn_numbers_on_caixa_identifier", unique: true
  end

  create_table "prize_draws", force: :cascade do |t|
    t.string "code", limit: 13, null: false
    t.string "product_image", null: false
    t.string "product_name", null: false
    t.string "sefaz_registration"
    t.datetime "starts_at", null: false
    t.datetime "ends_at", null: false
    t.datetime "run_at", null: false
    t.bigint "business_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "rules"
    t.string "caixa_identifier"
    t.index ["business_id"], name: "index_prize_draws_on_business_id"
    t.index ["code", "business_id"], name: "index_prize_draws_on_code_and_business_id", unique: true
    t.index ["sefaz_registration"], name: "index_prize_draws_on_sefaz_registration", unique: true
  end

  create_table "project_configs", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.string "name"
    t.string "favicon"
    t.string "apple_icon"
    t.string "google_icon"
    t.string "logo"
    t.string "vertical_logo"
    t.string "primary_color"
    t.string "secondary_color"
    t.string "font_color"
    t.string "background_color"
    t.string "term_of_use_url"
    t.string "web_domain"
    t.string "google_identifier"
    t.string "google_key_file"
    t.string "apple_identifier"
    t.string "apple_api_key_id"
    t.string "apple_team_id"
    t.string "apple_issuer_id"
    t.string "apple_key_file"
    t.string "firebase_dynamic_link_domain"
    t.string "firebase_google_file"
    t.string "firebase_apple_file"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "email_sender"
    t.string "sign_up_url"
    t.string "user_update_url"
    t.string "user_manager", default: "default"
    t.boolean "only_exclusive_categories", default: false, null: false
    t.boolean "cashback_auto_discount", default: true
    t.boolean "custom_sign_in", default: false, null: false
    t.boolean "custom_sign_up", default: false, null: false
    t.string "firebase_project_id"
    t.string "android_version"
    t.string "ios_version"
    t.string "apple_store_url"
    t.string "google_store_url"
    t.string "google_jks_file"
    t.boolean "use_google_jks"
    t.string "password_recovery_url"
    t.bigint "open_signup_business_id"
    t.string "horizontal_logo"
    t.string "horizontal_white_logo"
    t.text "single_sign_on_url"
    t.boolean "ux_cam", default: false, null: false
    t.string "single_sign_on_iss"
    t.string "android_min_version"
    t.string "ios_min_version"
    t.boolean "support", default: false, null: false
    t.string "support_url"
    t.string "help_center_url"
    t.string "inngage_token"
    t.string "auth_integration_type"
    t.boolean "custom_user_transfer_account_number", default: false, null: false
    t.string "facebook_sdk_app_id"
    t.string "facebook_sdk_client_token"
    t.boolean "facebook_sdk", default: false, null: false
    t.string "facebook_sdk_app_name"
    t.string "gtm_tag_id"
    t.string "telemedicine_url", default: "https://api.whatsapp.com/send?phone=************&text=Ol%C3%A1!%0ASolicito%20um%20atendimento%20para%20Telemedicina"
    t.boolean "home_search", default: false, null: false
    t.boolean "show_only_organizations_with_coupons", default: true, null: false
    t.string "giftcard_faq_url"
    t.boolean "create_user_on_smart_link", default: false, null: false
    t.string "shorebird_application_id"
    t.string "single_sign_on_logout_url"
    t.integer "telemedicine_plan", default: 11001, null: false
    t.string "freshworks_app_id"
    t.string "freshworks_app_key"
    t.string "freshworks_domain"
    t.string "clever_tap_account_id"
    t.string "clever_tap_project_token"
    t.string "clever_tap_region"
    t.string "policy_url"
    t.string "clever_tap_passcode"
    t.boolean "clever_tap", default: false
    t.string "home_view_mode", default: "online", null: false
    t.boolean "show_my_account", default: true, null: false
    t.boolean "need_email_on_registration", default: true, null: false
    t.boolean "need_cellphone_on_registration", default: true, null: false
    t.index ["business_id"], name: "index_project_configs_on_business_id"
    t.index ["open_signup_business_id"], name: "index_project_configs_on_open_signup_business_id"
    t.index ["shorebird_application_id"], name: "index_project_configs_on_shorebird_application_id", unique: true
    t.index ["web_domain"], name: "index_project_configs_on_web_domain"
  end

  create_table "promotion_categories", force: :cascade do |t|
    t.bigint "promotion_id", null: false
    t.bigint "category_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category_id"], name: "index_promotion_categories_on_category_id"
    t.index ["promotion_id", "category_id"], name: "unique_promotion_by_category_index", unique: true
    t.index ["promotion_id"], name: "index_promotion_categories_on_promotion_id"
  end

  create_table "promotions", force: :cascade do |t|
    t.text "description"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "provider"
    t.text "rules"
    t.string "code"
    t.text "url"
    t.decimal "discount_value", precision: 10, scale: 2
    t.integer "quantity"
    t.integer "redeemed_count", default: 0, null: false
    t.datetime "start_date", precision: nil
    t.datetime "end_date", precision: nil
    t.time "start_hour"
    t.time "end_hour"
    t.boolean "monday"
    t.boolean "tuesday"
    t.boolean "wednesday"
    t.boolean "thursday"
    t.boolean "friday"
    t.boolean "saturday"
    t.boolean "sunday"
    t.boolean "not_cumulative", default: false
    t.boolean "only_physical", default: false
    t.boolean "one_for_cpf", default: false
    t.boolean "one_per_day", default: false
    t.boolean "infinity", default: false, null: false
    t.float "price", limit: 24, default: 0.0
    t.decimal "average_ticket", precision: 10, scale: 2, default: "0.0"
    t.integer "frequency_in_days", default: 0, null: false
    t.string "integration_code"
    t.string "status", default: "pending", null: false
    t.string "redeem_type"
    t.string "picture"
    t.bigint "business_id"
    t.bigint "online_organization_id"
    t.bigint "creator_id"
    t.string "contract_custom_text"
    t.string "integration_partner"
    t.integer "redeems_per_cpf"
    t.string "title"
    t.boolean "dynamic_voucher", default: false, null: false
    t.boolean "closed_interval", default: false, null: false
    t.decimal "cashback_value", precision: 10, scale: 2
    t.string "cashback_type"
    t.boolean "sync_cashback_with_organization", default: true, null: false
    t.string "source", default: "shopkeeper", null: false
    t.bigint "organization_id"
    t.string "usage_type"
    t.text "cashback_rules"
    t.string "provider_type"
    t.string "voucher_type"
    t.bigint "voucher_bucket_id"
    t.enum "discount_type", enum_type: "discount_type"
    t.boolean "web_postos", default: false, null: false
    t.string "usage_instruction"
    t.index ["business_id"], name: "index_promotions_on_business_id"
    t.index ["creator_id"], name: "index_promotions_on_creator_id"
    t.index ["online_organization_id"], name: "index_promotions_on_online_organization_id"
    t.index ["organization_id"], name: "index_promotions_on_organization_id"
    t.index ["status"], name: "index_promotions_on_status"
    t.index ["voucher_bucket_id"], name: "index_promotions_on_voucher_bucket_id"
  end

  create_table "rbxsoft_plans", force: :cascade do |t|
    t.bigint "rbxsoft_token_id", null: false
    t.string "name", null: false
    t.string "external_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["rbxsoft_token_id"], name: "index_rbxsoft_plans_on_rbxsoft_token_id"
  end

  create_table "rbxsoft_tokens", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.bigint "authorized_user_group_id", null: false
    t.string "base_url", null: false
    t.string "token", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["authorized_user_group_id"], name: "index_rbxsoft_tokens_on_authorized_user_group_id"
    t.index ["business_id"], name: "index_rbxsoft_tokens_on_business_id"
  end

  create_table "redeem_rules", force: :cascade do |t|
    t.bigint "giftcard_id"
    t.string "redeem_rule", null: false
    t.string "value", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["giftcard_id"], name: "index_redeem_rules_on_giftcard_id"
  end

  create_table "response_conditions", force: :cascade do |t|
    t.bigint "auth_integration_id", null: false
    t.string "text_matcher"
    t.string "response_type", null: false
    t.integer "http_status_code"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "custom_message"
    t.index ["auth_integration_id"], name: "index_response_conditions_on_auth_integration_id"
  end

  create_table "shopkeeper_branches", force: :cascade do |t|
    t.bigint "shopkeeper_id", null: false
    t.bigint "branch_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["branch_id"], name: "index_shopkeeper_branches_on_branch_id"
    t.index ["shopkeeper_id", "branch_id"], name: "index_shopkeeper_branches_on_shopkeeper_id_and_branch_id", unique: true
    t.index ["shopkeeper_id"], name: "index_shopkeeper_branches_on_shopkeeper_id"
  end

  create_table "shopkeeper_businesses", force: :cascade do |t|
    t.bigint "shopkeeper_id", null: false
    t.bigint "business_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id"], name: "index_shopkeeper_businesses_on_business_id"
    t.index ["shopkeeper_id", "business_id"], name: "index_shopkeeper_businesses_on_shopkeeper_id_and_business_id", unique: true
    t.index ["shopkeeper_id"], name: "index_shopkeeper_businesses_on_shopkeeper_id"
  end

  create_table "shopkeeper_promotion_providers", force: :cascade do |t|
    t.bigint "shopkeeper_id", null: false
    t.string "promotion_provider", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["shopkeeper_id", "promotion_provider"], name: "shopkeeper_id_promotion_provider", unique: true
    t.index ["shopkeeper_id"], name: "index_shopkeeper_promotion_providers_on_shopkeeper_id"
  end

  create_table "shopkeepers", force: :cascade do |t|
    t.string "name", null: false
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "authentication_token", limit: 30
    t.string "role", default: "shopkeeper", null: false
    t.string "telephone"
    t.index ["authentication_token"], name: "index_shopkeepers_on_authentication_token", unique: true
    t.index ["email"], name: "index_shopkeepers_on_email", unique: true
    t.index ["reset_password_token"], name: "index_shopkeepers_on_reset_password_token", unique: true
  end

  create_table "subscription_configs", force: :cascade do |t|
    t.string "title", null: false
    t.text "description", null: false
    t.boolean "active", default: true, null: false
    t.string "image_desktop"
    t.string "image_mobile"
    t.bigint "business_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "title_faq", default: "Vantagens de ter uma assinatura"
    t.string "title_support", default: "Vantagens das assinaturas disponíveis"
    t.bigint "recommended_plan_id"
    t.string "cancellation_description", null: false
    t.bigint "cancel_destination_business_id"
    t.integer "days_to_credit_points", default: 7, null: false
    t.index ["business_id"], name: "index_subscription_configs_on_business_id"
    t.index ["cancel_destination_business_id"], name: "index_subscription_configs_on_cancel_destination_business_id"
    t.index ["recommended_plan_id"], name: "index_subscription_configs_on_recommended_plan_id"
  end

  create_table "subscription_group_plans", force: :cascade do |t|
    t.bigint "plan_id", null: false
    t.bigint "subscription_group_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["plan_id"], name: "index_subscription_group_plans_on_plan_id"
    t.index ["subscription_group_id"], name: "index_subscription_group_plans_on_subscription_group_id"
  end

  create_table "subscription_groups", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "subscription_config_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["subscription_config_id"], name: "index_subscription_groups_on_subscription_config_id"
  end

  create_table "subscription_installments", force: :cascade do |t|
    t.integer "number", default: 1, null: false
    t.decimal "price", precision: 10, scale: 2, null: false
    t.bigint "subscription_id", null: false
    t.string "status", default: "panding", null: false
    t.datetime "paid_at", null: false
    t.datetime "due_at", null: false
    t.integer "retry_count", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["subscription_id"], name: "index_subscription_installments_on_subscription_id"
  end

  create_table "subscriptions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "subscription_config_id", null: false
    t.bigint "plan_id", null: false
    t.bigint "credit_card_id"
    t.boolean "active", default: false, null: false
    t.decimal "price", precision: 10, scale: 2, null: false
    t.integer "points", default: 0
    t.string "recurrence", default: "monthly", null: false
    t.string "title", null: false
    t.text "reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "retries", default: 0, null: false
    t.index ["credit_card_id"], name: "index_subscriptions_on_credit_card_id"
    t.index ["plan_id"], name: "index_subscriptions_on_plan_id"
    t.index ["subscription_config_id"], name: "index_subscriptions_on_subscription_config_id"
    t.index ["user_id"], name: "index_subscriptions_on_user_id"
  end

  create_table "taggings", force: :cascade do |t|
    t.bigint "tag_id", null: false
    t.string "taggable_type", null: false
    t.bigint "taggable_id", null: false
    t.bigint "business_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id"], name: "index_taggings_on_business_id"
    t.index ["tag_id", "taggable_id", "taggable_type"], name: "index_taggings_on_tag_id_and_taggable_id_and_taggable_type"
    t.index ["tag_id"], name: "index_taggings_on_tag_id"
    t.index ["taggable_id", "taggable_type"], name: "index_taggings_on_taggable_id_and_taggable_type"
    t.index ["taggable_id"], name: "index_taggings_on_taggable_id"
    t.index ["taggable_type", "taggable_id"], name: "index_taggings_on_taggable"
    t.index ["taggable_type"], name: "index_taggings_on_taggable_type"
  end

  create_table "tags", force: :cascade do |t|
    t.string "name"
    t.string "slug"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_tags_on_name"
    t.index ["slug"], name: "index_tags_on_slug"
  end

  create_table "telemedicine_activities", force: :cascade do |t|
    t.bigint "authorized_user_id", null: false
    t.jsonb "activity_log", default: {}, null: false
    t.enum "entry_type", null: false, enum_type: "telemedicine_activity_entry_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["authorized_user_id"], name: "index_telemedicine_activities_on_authorized_user_id"
  end

  create_table "telemedicine_beneficiaries", force: :cascade do |t|
    t.string "cpf", null: false
    t.string "sex"
    t.string "name", null: false
    t.string "email", null: false
    t.string "cellphone"
    t.datetime "birthdate"
    t.datetime "last_access_at"
    t.string "external_id"
    t.boolean "enabled", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "authorized_user_id"
    t.index ["authorized_user_id"], name: "index_telemedicine_beneficiaries_on_authorized_user_id", unique: true
    t.index ["cpf"], name: "index_telemedicine_beneficiaries_on_cpf"
  end

  create_table "telemedicine_configs", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.integer "plan", default: 10091, null: false
    t.string "url", default: "https://api.whatsapp.com/send?phone=************&text=Ol%C3%A1!%0ASolicito%20um%20atendimento%20para%20Telemedicina", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "sync_all_users", default: false, null: false
    t.integer "contracted_beneficiaries", default: 500
    t.index ["business_id"], name: "index_telemedicine_configs_on_business_id", unique: true
  end

  create_table "tenex_clients", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.bigint "authorized_user_group_id", null: false
    t.string "token", null: false
    t.string "base_url", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["authorized_user_group_id"], name: "index_tenex_clients_on_authorized_user_group_id", unique: true
    t.index ["business_id"], name: "index_tenex_clients_on_business_id"
  end

  create_table "tudo_azul_ad_hoc_transactions", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "issuer", null: false
    t.string "taxpayer_id", null: false
    t.string "full_name", null: false
    t.string "phone_number", null: false
    t.string "email", null: false
    t.string "tudo_azul_transaction_id"
    t.string "issuer_external_id"
    t.string "comment", null: false
    t.integer "points", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "response_data"
    t.datetime "enqueued_at"
    t.string "costcenter"
    t.index ["issuer", "issuer_external_id"], name: "index_tudo_azul_on_issuer_and_external_id", unique: true
    t.index ["tudo_azul_transaction_id"], name: "index_tudo_azul_on_transaction_id", unique: true
  end

  create_table "unifacisa_clients", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.bigint "authorized_user_group_id", null: false
    t.string "username", null: false
    t.string "password", null: false
    t.string "user_type", null: false
    t.string "base_url", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["authorized_user_group_id"], name: "index_unifacisa_clients_on_authorized_user_group_id", unique: true
    t.index ["business_id"], name: "index_unifacisa_clients_on_business_id"
  end

  create_table "user_destroy_requests", force: :cascade do |t|
    t.bigint "user_id"
    t.text "reason"
    t.string "request_status", default: "unprocessed"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "requester_type"
    t.index ["user_id"], name: "index_user_destroy_requests_on_user_id"
  end

  create_table "user_last_seen_metrics", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.integer "users_count", null: false
    t.string "period", null: false
    t.date "last_seen_on", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id", "period", "last_seen_on"], name: "unique_business_period_last_seen_on_user_metrics", unique: true
    t.index ["business_id"], name: "index_user_last_seen_metrics_on_business_id"
  end

  create_table "user_registration_metrics", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.integer "users_count", null: false
    t.string "period", null: false
    t.date "registered_on", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id", "period", "registered_on"], name: "unique_business_period_registered_on_user_metrics", unique: true
    t.index ["business_id"], name: "index_user_registration_metrics_on_business_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.boolean "allow_password_change", default: false
    t.string "name"
    t.string "cellphone", default: ""
    t.string "cpf", default: "", null: false
    t.string "email"
    t.text "tokens"
    t.text "device_tokens"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.text "apns_tokens"
    t.datetime "deleted_at", precision: nil
    t.datetime "last_seen_at", precision: nil
    t.boolean "deleted", default: false, null: false
    t.bigint "business_id"
    t.boolean "active", default: true, null: false
    t.string "smart_token", default: ""
    t.string "email_status", default: "new", null: false
    t.bigint "main_business_id", null: false
    t.bigint "authorized_user_id"
    t.string "api_token"
    t.string "web_token"
    t.string "full_address"
    t.float "lat"
    t.float "lng"
    t.string "username"
    t.string "jti"
    t.boolean "default_auth_flow", default: false, null: false
    t.string "external_id"
    t.boolean "giftcard", default: false, null: false
    t.boolean "cashback", default: false, null: false
    t.uuid "uuid", default: -> { "gen_random_uuid()" }, null: false
    t.jsonb "external_tokens"
    t.index ["api_token"], name: "index_users_on_api_token", unique: true
    t.index ["authorized_user_id"], name: "index_users_on_authorized_user_id"
    t.index ["business_id"], name: "index_users_on_business_id"
    t.index ["cpf", "main_business_id", "deleted"], name: "index_users_on_cpf_and_main_business_id_and_deleted", unique: true
    t.index ["cpf"], name: "index_users_on_cpf"
    t.index ["created_at"], name: "index_users_on_created_at"
    t.index ["deleted"], name: "index_users_on_deleted"
    t.index ["external_id", "main_business_id"], name: "index_users_on_external_id_and_main_business_id", unique: true
    t.index ["main_business_id"], name: "index_users_on_main_business_id"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["smart_token"], name: "index_users_on_smart_token"
    t.index ["uuid"], name: "index_users_on_uuid"
    t.index ["web_token"], name: "index_users_on_web_token", unique: true
  end

  create_table "users_gmv_metrics", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.decimal "amount", precision: 10, scale: 2, null: false
    t.string "period", null: false
    t.date "ordered_on", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id", "period", "ordered_on"], name: "unique_business_period_ordered_on_user_gmv_metrics", unique: true
    t.index ["business_id"], name: "index_users_gmv_metrics_on_business_id"
  end

  create_table "voalle_plans", force: :cascade do |t|
    t.bigint "voalle_token_id", null: false
    t.string "name", null: false
    t.string "external_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["voalle_token_id"], name: "index_voalle_plans_on_voalle_token_id"
  end

  create_table "voalle_tokens", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.string "base_url", null: false
    t.string "integrator_token", null: false
    t.string "integrator", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "authorized_user_group_id", null: false
    t.index ["authorized_user_group_id"], name: "index_voalle_tokens_on_authorized_user_group_id"
    t.index ["business_id"], name: "index_voalle_tokens_on_business_id"
  end

  create_table "voucher_buckets", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "vouchers", force: :cascade do |t|
    t.bigint "user_id"
    t.string "code", null: false
    t.string "cpf"
    t.string "description"
    t.datetime "began_at", precision: nil, null: false
    t.datetime "expired_at", precision: nil, null: false
    t.text "data_json"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "used_at", precision: nil
    t.bigint "promotion_id"
    t.string "batch"
    t.string "provider"
    t.bigint "cupon_id"
    t.datetime "redeemed_at", precision: nil
    t.datetime "queried_at", precision: nil
    t.bigint "giftcard_id"
    t.bigint "order_id"
    t.bigint "voucher_bucket_id"
    t.datetime "reserved_at"
    t.datetime "canceled_at"
    t.index ["code"], name: "index_vouchers_on_code"
    t.index ["cupon_id"], name: "index_vouchers_on_cupon_id"
    t.index ["giftcard_id"], name: "index_vouchers_on_giftcard_id"
    t.index ["order_id"], name: "index_vouchers_on_order_id"
    t.index ["promotion_id"], name: "index_vouchers_on_promotion_id"
    t.index ["used_at"], name: "index_vouchers_on_used_at"
    t.index ["user_id"], name: "index_vouchers_on_user_id"
    t.index ["voucher_bucket_id"], name: "index_vouchers_on_voucher_bucket_id"
  end

  create_table "wallet_entries", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "wallet_id", null: false
    t.integer "amount", null: false
    t.integer "direction", limit: 2, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "kind", null: false
    t.string "description"
    t.index ["wallet_id"], name: "index_wallet_entries_on_wallet_id"
  end

  create_table "wallet_transactions", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "idempotency_key", null: false
    t.uuid "wallet_id", null: false
    t.string "wallet_type", null: false
    t.integer "amount", null: false
    t.string "currency", null: false
    t.text "description", null: false
    t.string "kind"
    t.json "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "payment_method"
    t.index ["idempotency_key", "wallet_id"], name: "index_wallet_transactions_on_idempotency_key_and_wallet_id", unique: true
    t.index ["wallet_id"], name: "index_wallet_transactions_on_wallet_id"
  end

  create_table "wallets", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "business_id"
    t.bigint "user_id"
    t.string "kind"
    t.bigint "balance", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "inflow", default: false, null: false
    t.boolean "outflow", default: false, null: false
    t.string "wallet_type", default: "cashback", null: false
    t.string "currency"
    t.index ["business_id"], name: "index_wallets_on_business_id"
    t.index ["currency"], name: "index_wallets_on_currency"
    t.index ["kind"], name: "index_wallets_on_kind"
    t.index ["user_id"], name: "index_wallets_on_user_id"
    t.index ["wallet_type"], name: "index_wallets_on_wallet_type"
  end

  create_table "web_applications", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.jsonb "certified_cnames", default: {}
    t.boolean "valid_certificate", default: false, null: false
    t.string "acm_certificate_arn"
    t.string "cloudfront_distribution_id"
    t.string "cloudfront_distribution_url"
    t.boolean "certificate_attached_to_distribution", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id"], name: "index_web_applications_on_business_id"
    t.index ["cloudfront_distribution_url"], name: "index_web_applications_on_cloudfront_distribution_url"
  end

  create_table "webhook_clients", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.string "api_key", null: false
    t.enum "integration", default: "asaas", null: false, enum_type: "webhook_integration"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["business_id"], name: "index_webhook_clients_on_business_id", unique: true
  end

  create_table "webhook_configs", force: :cascade do |t|
    t.bigint "business_id", null: false
    t.string "application_identifier"
    t.string "endpoint"
    t.boolean "user_created_event", default: false, null: false
    t.boolean "user_updated_event", default: false, null: false
    t.boolean "cashback_event", default: false, null: false
    t.boolean "order_created_event", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "active", default: false, null: false
    t.boolean "order_updated_event", default: false, null: false
    t.index ["business_id"], name: "index_webhook_configs_on_business_id"
  end

  add_foreign_key "aato_taggings", "aato_tags", column: "tag_id"
  add_foreign_key "admin_organizations", "businesses"
  add_foreign_key "admin_organizations", "client_employees"
  add_foreign_key "admin_organizations", "organizations"
  add_foreign_key "allowlisted_jwts", "users", on_delete: :cascade
  add_foreign_key "auth_integrations", "businesses"
  add_foreign_key "authorized_user_export_files", "businesses"
  add_foreign_key "authorized_user_import_files", "businesses"
  add_foreign_key "authorized_users", "businesses"
  add_foreign_key "authorized_users", "businesses", column: "main_business_id"
  add_foreign_key "authorized_users_gmv_metrics", "businesses"
  add_foreign_key "betalabs_clients", "authorized_user_groups"
  add_foreign_key "betalabs_clients", "businesses"
  add_foreign_key "branches", "businesses"
  add_foreign_key "branches", "organizations"
  add_foreign_key "business_client_employees", "businesses"
  add_foreign_key "business_client_employees", "client_employees"
  add_foreign_key "businesses", "businesses", column: "main_business_id"
  add_foreign_key "cards", "businesses"
  add_foreign_key "cashback_discount_request_histories", "cashback_discount_requests"
  add_foreign_key "cashback_discount_requests", "businesses"
  add_foreign_key "cashback_discount_requests", "users"
  add_foreign_key "cashback_metrics", "businesses"
  add_foreign_key "cashback_record_histories", "cashback_records"
  add_foreign_key "cashback_records", "authorized_users"
  add_foreign_key "cashback_records", "cashback_discount_requests"
  add_foreign_key "cashback_records", "orders"
  add_foreign_key "cashback_records", "payouts"
  add_foreign_key "cashback_records", "users"
  add_foreign_key "cashback_transfer_frequencies", "businesses"
  add_foreign_key "categories", "businesses"
  add_foreign_key "categories", "categories", column: "main_category_id"
  add_foreign_key "category_blocklists", "businesses"
  add_foreign_key "category_blocklists", "categories"
  add_foreign_key "category_metrics", "businesses"
  add_foreign_key "category_metrics", "categories", on_delete: :cascade
  add_foreign_key "cities", "federation_units"
  add_foreign_key "clube_associados_tokens", "businesses"
  add_foreign_key "credit_cards", "users"
  add_foreign_key "cupons", "branches"
  add_foreign_key "cupons", "businesses"
  add_foreign_key "cupons", "organizations"
  add_foreign_key "cupons", "promotions"
  add_foreign_key "custom_field_values", "custom_fields"
  add_foreign_key "custom_field_values", "users"
  add_foreign_key "custom_fields", "businesses"
  add_foreign_key "custom_texts", "businesses"
  add_foreign_key "fund_activities", "funds"
  add_foreign_key "fund_debits", "funds"
  add_foreign_key "fund_redemption_configs", "businesses"
  add_foreign_key "fund_redemptions", "businesses"
  add_foreign_key "fund_redemptions", "users"
  add_foreign_key "funds", "businesses"
  add_foreign_key "funds", "orders"
  add_foreign_key "funds", "subscriptions"
  add_foreign_key "funds", "users"
  add_foreign_key "giftcard_branches", "branches"
  add_foreign_key "giftcard_branches", "giftcards"
  add_foreign_key "giftcard_configs", "businesses"
  add_foreign_key "giftcards", "voucher_buckets"
  add_foreign_key "gmv_metrics", "businesses"
  add_foreign_key "highlight_organizations", "businesses", on_delete: :cascade
  add_foreign_key "highlight_organizations", "organizations", on_delete: :cascade
  add_foreign_key "hinova_tokens", "businesses"
  add_foreign_key "hubsoft_services", "hubsoft_tokens"
  add_foreign_key "hubsoft_tokens", "authorized_user_groups"
  add_foreign_key "hubsoft_tokens", "businesses"
  add_foreign_key "ileva_clients", "authorized_user_groups"
  add_foreign_key "ileva_clients", "businesses"
  add_foreign_key "import_file_errors", "import_files"
  add_foreign_key "import_files", "businesses"
  add_foreign_key "import_files", "client_employees"
  add_foreign_key "ixc_contracts", "ixc_tokens"
  add_foreign_key "leads", "businesses"
  add_foreign_key "leads", "users", column: "referrer_id"
  add_foreign_key "mailer_configs", "businesses"
  add_foreign_key "member_referral_configs", "businesses"
  add_foreign_key "mk_solution_plans", "mk_solution_tokens"
  add_foreign_key "mk_solution_tokens", "businesses"
  add_foreign_key "navigation_geolocations", "navigations"
  add_foreign_key "navigations", "businesses"
  add_foreign_key "navigations", "businesses", column: "exclusive_business_id", on_delete: :cascade
  add_foreign_key "order_metrics", "businesses"
  add_foreign_key "orders", "authorized_users"
  add_foreign_key "orders", "branches"
  add_foreign_key "orders", "businesses"
  add_foreign_key "orders", "cupons"
  add_foreign_key "orders", "giftcards"
  add_foreign_key "orders", "organizations"
  add_foreign_key "orders", "users"
  add_foreign_key "orders", "vouchers"
  add_foreign_key "organization_blocklists", "businesses"
  add_foreign_key "organization_blocklists", "organizations"
  add_foreign_key "organization_integration_partners", "organizations"
  add_foreign_key "organization_metrics", "businesses"
  add_foreign_key "organization_metrics", "organizations"
  add_foreign_key "organization_profiles", "businesses"
  add_foreign_key "organization_profiles", "organizations"
  add_foreign_key "organization_promotion_providers", "organizations"
  add_foreign_key "organizations", "businesses", column: "creator_id"
  add_foreign_key "payment_history", "payments"
  add_foreign_key "payments", "orders"
  add_foreign_key "payments", "subscriptions"
  add_foreign_key "payout_histories", "payouts"
  add_foreign_key "payouts", "businesses"
  add_foreign_key "payouts", "businesses", column: "user_business_id"
  add_foreign_key "payouts", "users"
  add_foreign_key "plans", "businesses"
  add_foreign_key "plans", "businesses", column: "destination_business_id"
  add_foreign_key "plans", "subscription_configs"
  add_foreign_key "playhub_clients", "authorized_user_groups"
  add_foreign_key "playhub_clients", "businesses"
  add_foreign_key "policies", "businesses"
  add_foreign_key "prize_draw_entries", "prize_draws"
  add_foreign_key "prize_draw_entries", "users"
  add_foreign_key "prize_draw_run_histories", "prize_draws"
  add_foreign_key "prize_draw_statuses", "prize_draws"
  add_foreign_key "prize_draw_winners", "prize_draw_entries", column: "owner_id"
  add_foreign_key "prize_draw_winners", "prize_draws"
  add_foreign_key "prize_draws", "businesses"
  add_foreign_key "project_configs", "businesses"
  add_foreign_key "project_configs", "businesses", column: "open_signup_business_id"
  add_foreign_key "promotion_categories", "categories"
  add_foreign_key "promotion_categories", "promotions"
  add_foreign_key "promotions", "businesses"
  add_foreign_key "promotions", "organizations"
  add_foreign_key "promotions", "organizations", column: "online_organization_id"
  add_foreign_key "promotions", "shopkeepers", column: "creator_id"
  add_foreign_key "promotions", "voucher_buckets"
  add_foreign_key "rbxsoft_plans", "rbxsoft_tokens"
  add_foreign_key "rbxsoft_tokens", "authorized_user_groups"
  add_foreign_key "rbxsoft_tokens", "businesses"
  add_foreign_key "redeem_rules", "giftcards"
  add_foreign_key "response_conditions", "auth_integrations"
  add_foreign_key "shopkeeper_branches", "branches"
  add_foreign_key "shopkeeper_branches", "shopkeepers"
  add_foreign_key "shopkeeper_businesses", "businesses"
  add_foreign_key "shopkeeper_businesses", "shopkeepers"
  add_foreign_key "shopkeeper_promotion_providers", "shopkeepers"
  add_foreign_key "subscription_configs", "businesses"
  add_foreign_key "subscription_configs", "businesses", column: "cancel_destination_business_id"
  add_foreign_key "subscription_configs", "plans", column: "recommended_plan_id"
  add_foreign_key "subscription_group_plans", "plans"
  add_foreign_key "subscription_group_plans", "subscription_groups"
  add_foreign_key "subscription_groups", "subscription_configs"
  add_foreign_key "subscription_installments", "subscriptions"
  add_foreign_key "subscriptions", "credit_cards"
  add_foreign_key "subscriptions", "plans"
  add_foreign_key "subscriptions", "subscription_configs"
  add_foreign_key "subscriptions", "users"
  add_foreign_key "taggings", "businesses"
  add_foreign_key "taggings", "tags"
  add_foreign_key "telemedicine_activities", "authorized_users"
  add_foreign_key "telemedicine_beneficiaries", "authorized_users"
  add_foreign_key "telemedicine_configs", "businesses"
  add_foreign_key "tenex_clients", "authorized_user_groups"
  add_foreign_key "tenex_clients", "businesses"
  add_foreign_key "unifacisa_clients", "authorized_user_groups"
  add_foreign_key "unifacisa_clients", "businesses"
  add_foreign_key "user_destroy_requests", "users"
  add_foreign_key "user_last_seen_metrics", "businesses"
  add_foreign_key "user_registration_metrics", "businesses"
  add_foreign_key "users", "authorized_users"
  add_foreign_key "users", "businesses"
  add_foreign_key "users", "businesses", column: "main_business_id"
  add_foreign_key "users_gmv_metrics", "businesses"
  add_foreign_key "voalle_plans", "voalle_tokens"
  add_foreign_key "voalle_tokens", "businesses"
  add_foreign_key "vouchers", "cupons"
  add_foreign_key "vouchers", "giftcards"
  add_foreign_key "vouchers", "orders", on_delete: :nullify
  add_foreign_key "vouchers", "promotions"
  add_foreign_key "vouchers", "users"
  add_foreign_key "vouchers", "voucher_buckets"
  add_foreign_key "wallet_entries", "wallets"
  add_foreign_key "wallet_transactions", "wallets"
  add_foreign_key "wallets", "businesses"
  add_foreign_key "wallets", "users"
  add_foreign_key "web_applications", "businesses"
  add_foreign_key "webhook_clients", "businesses"
  add_foreign_key "webhook_configs", "businesses"
end
