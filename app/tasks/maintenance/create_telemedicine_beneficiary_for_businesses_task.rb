# frozen_string_literal: true

module Maintenance
  class CreateTelemedicineBeneficiaryForBusinessesTask < MaintenanceTasks::Task
    def collection
      TelemedicineBeneficiary.where(authorized_user_id: nil)
    end

    def process(telemedicine_beneficary)
      active_businesses_ids = Business.active.ids
      authorized_users = AuthorizedUser.where(business_id: active_businesses_ids, cpf: telemedicine_beneficary.cpf)
      authorized_users.each do |authorized_user|
        new_beneficiary = TelemedicineBeneficiary.create!(
          authorized_user:,
          cpf: telemedicine_beneficary.cpf,
          name: telemedicine_beneficary.name,
          email: telemedicine_beneficary.email,
          cellphone: telemedicine_beneficary.cellphone,
          sex: telemedicine_beneficary.sex,
          birthdate: telemedicine_beneficary.birthdate,
          enabled: telemedicine_beneficary.enabled && authorized_user.active
        )
        if telemedicine_beneficary.enabled && authorized_user.active
          Telemedicine::ThirdPartyUpsertWorker.perform_async(new_beneficiary.id)
        else
          Telemedicine::ThirdPartyUpsertWorker.perform_in(1.second, new_beneficiary.id)
          Telemedicine::ThirdPartyDeactivateWorker.perform_in(1.minute, new_beneficiary.id)
        end
      end

      telemedicine_beneficary.delete
    end
  end
end
