# frozen_string_literal: true

require "global_pay/api"

class CreditCard < ApplicationRecord
  attr_accessor :seller_id, :number, :holder, :security_code, :exp_month, :exp_year

  belongs_to :user

  before_validation :set_last4, if: :number
  before_validation :set_brand, if: :number
  before_validation :set_main, if: :user

  validates :token, uniqueness: true
  validates :last4, presence: true, length: {is: 4}, if: -> { number.present? }
  validates :brand, presence: true, if: -> { number.present? }

  validates :main, inclusion: [true, false]
  validates :main, uniqueness: {scope: :user_id}, if: :main

  validates :billing_name, presence: true
  validates :billing_document, presence: true
  validates :billing_email, presence: true
  validates :billing_phone, presence: true
  validates :billing_postal_code, presence: true
  validates :billing_street, presence: true
  validates :billing_number, presence: true
  validates :billing_neighborhood, presence: true
  validates :billing_city, presence: true
  validates :billing_state, presence: true

  validates :security_code, presence: true, on: :create
  validates :exp_month, :exp_year, :holder, :number, presence: true, if: -> { token.blank? }

  before_save :tokenize!, if: -> { token.blank? }

  encrypts :token
  encrypts :billing_name
  encrypts :billing_document
  encrypts :billing_email
  encrypts :billing_phone
  encrypts :billing_postal_code
  encrypts :billing_street
  encrypts :billing_number
  encrypts :billing_complement
  encrypts :billing_neighborhood
  encrypts :billing_city
  encrypts :billing_state

  private

  def set_last4
    self.last4 ||= number.last(4)
  end

  def set_brand
    self.brand ||= CreditCardValidations::Detector.new(number).brand
  end

  def set_main
    self.main = true unless user.credit_cards.exists?
  end

  def tokenize!
    card = GlobalPay::Payload::Card.new(
      seller_id:,
      number:,
      holder_name: holder,
      cvv: security_code,
      expiration_month: exp_month,
      expiration_year: exp_year,
      brand:
    )

    response = GlobalPay::Client.new.store_card(card)

    if response.success
      self.token = response.store_card_id
    else
      errors.add(:base, response.error_message)
      throw :abort
    end
  end
end
