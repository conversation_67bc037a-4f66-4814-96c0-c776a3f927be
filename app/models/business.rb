# frozen_string_literal: true

class Business < ApplicationRecord
  include Business::Project
  include Business::SecretKeys
  include Business::Telemedicine
  include Business::Walletable

  enum :integration_type, Enums::Business::IntegrationType.enum
  enum :status, Business::Status.enum, prefix: true

  has_many :admin_organizations, dependent: :destroy
  has_one :auth_integration
  has_many :cupons
  has_many :custom_texts
  has_many :highlight_organizations, dependent: :destroy
  has_many :menus, -> { where(kind: "menu") }, inverse_of: :business, class_name: "Navigation"
  has_many :banners, -> { where(kind: "banner") }, inverse_of: :business, class_name: "Navigation"
  has_many :payouts
  has_many :business_client_employees
  has_many :client_employees, -> { distinct }, through: :business_client_employees
  has_many :orders
  has_many :cashback_records, -> { distinct }, through: :orders
  has_many :hinova_tokens
  has_one :ixc_token
  has_one :ileva_client
  has_one :unifacisa_client
  has_one :tenex_client
  has_many :ixc_contracts, through: :ixc_token
  has_many :authorized_users, dependent: :destroy
  has_many :authorized_user_groups, dependent: :destroy
  has_many :authorized_user_export_files, dependent: :destroy
  has_many :users
  has_many :categories
  has_one :voalle_token
  has_many :voalle_plans, through: :voalle_token
  has_one :project_config, dependent: :destroy
  has_one :giftcard_config, dependent: :destroy
  has_one :mailer_config, dependent: :destroy
  has_one :webhook_config, dependent: :destroy
  has_one :web_application, dependent: :destroy
  has_one :cashback_transfer_frequency, dependent: :destroy
  has_one :mk_solution_token
  has_many :mk_solution_plans, through: :mk_solution_token
  has_one :rbxsoft_token
  has_many :rbxsoft_plans, through: :rbxsoft_token
  has_many :authorized_user_import_files
  has_one :hubsoft_token
  has_many :hubsoft_services, through: :hubsoft_token
  has_one :webhook_client
  has_one :playhub_client
  has_one :betalabs_client
  has_one :clube_associados_token
  has_many :category_blocklists
  has_many :organization_blocklists
  has_many :shopkeeper_businesses, dependent: :destroy
  has_many :shopkeepers, -> { distinct }, through: :shopkeeper_businesses
  has_many :custom_fields
  has_many :prize_draws
  has_many :organization_profiles
  has_many :organizations, through: :organization_profiles
  has_many :promotions
  has_many :leads
  has_many :policies
  has_one :member_referral_config
  has_many :cards
  # TODO: change has_one to has_many :subscription_configs
  has_one :subscription_config, class_name: "SubscriptionConfig"

  has_many :taggings, dependent: :destroy
  has_many :tags, through: :taggings

  validates :name, uniqueness: true
  validates :cnpj, uniqueness: true
  validates :name, :cnpj, presence: true
  validates :prize_draw, inclusion: [true, false]
  validates :spread_percent,
    format: {with: /\A\d+(?:\.\d{0,2})?\z/},
    numericality: {greater_than_or_equal_to: 0, less_than_or_equal_to: 100}
  validates :contact_email, format: {
    with: /\A[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\z/,
    message: I18n.t("business.invalid_contact_email")
  }, if: -> { contact_email.present? }
  validates :integration_type, presence: true, inclusion: {in: Enums::Business::IntegrationType.all}

  validate :main_business_cannot_be_child, if: :main_business

  before_validation :transliterate_contact_email, if: -> { contact_email.present? }
  before_save :normalize_cnpj
  before_save :update_active_status

  after_commit :generate_jks, if: -> { saved_change_to_cnpj? && Rails.env.production? }, on: :update
  after_create_commit -> { update_policy_url }
  after_update_commit -> { update_policy_url }, if: -> { saved_change_to_name? }

  scope :active, -> { where(active: true) }
  scope :main, -> { where(main_business_id: nil) }
  scope :with_integration_type_app, -> { where(integration_type: "app") }
  scope :by_organization, lambda { |organization|
    left_joins(category_blocklists: {category: %i[organization_categories organizations]})
      .left_joins(:organization_blocklists)
      .and(
        where(category_blocklists: {category_id: nil})
          .or(where.not(category_blocklists: {category_id: [organization.category_id, organization.category_ids]}))
      )
      .and(
        where(organizations: {id: nil})
          .or(where.not(organizations: {id: organization.id}))
      )
      .and(
        where(organization_categories: {organization_id: nil})
          .or(where.not(organization_categories: {organization_id: organization.id}))
      )
      .and(
        where(organization_blocklists: {organization_id: nil})
          .or(where.not(organization_blocklists: {organization_id: organization.id}))
      )
      .distinct
  }
  scope :by_web_domain, lambda { |web_domain|
    joins(:project_config)
      .where(project_configs: {web_domain:})
  }
  scope :by_term, lambda { |term|
    return if term.blank?

    where("cnpj like :term OR name like :term", term: "%#{term}%")
  }
  scope :by_identifier, lambda { |identifier|
    return if identifier.blank?

    where(cnpj: identifier).or(where(id: identifier))
  }

  scope :without_lbc_giftcard, lambda { |project_ids|
    where(id: project_ids)
      .where(lbc_giftcard: false)
  }

  delegate :support, to: :cards
  delegate :faq, to: :cards

  def cashback_status_mailable?
    integration_type == Enums::Business::IntegrationType::APP
  end

  def sicoob?
    /^*sicoob*/i.match?(name)
  end

  def message_by_type(type)
    custom_texts.find_by(message_type: type)&.message ||
      (Enums::CustomTextType.translate(type) || {})[:value]
  end

  def current_payout
    payouts.ongoing
      .where(due_on: Date.current..)
      .order(due_on: :desc)
      .first
  end

  def gateway_external_id(gateway)
    case gateway
    when "global_pay"
      global_pay_external_id
    end
  end

  private

  def update_policy_url
    return unless project_config

    policy_url = "#{name.parameterize.delete("-")}.clubedefidelidade.com"
    project_config.update_columns(policy_url:)
  end

  def main_business_cannot_be_child
    return unless main_business.main_business

    errors.add(:main_business, "Business principal não pode ser sub business")
  end

  def normalize_cnpj
    self.cnpj = cnpj.rjust(14, "0")
  end

  def transliterate_contact_email
    self.contact_email = I18n.transliterate(contact_email.strip)
  end

  def update_active_status
    self.active = [Business::Status::ACTIVE, Business::Status::OVERDUE].include?(status)
  end

  def generate_jks
    project_config.generate_jks
  end
end
