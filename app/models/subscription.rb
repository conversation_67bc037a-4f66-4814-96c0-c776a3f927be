# frozen_string_literal: true

class Subscription < ApplicationRecord
  RETRY_LIMIT = 3

  belongs_to :user
  belongs_to :subscription_config
  belongs_to :plan
  belongs_to :credit_card, optional: true
  has_many :payments
  has_many :subscription_installments

  scope :inactive, -> { where(active: false) }
  scope :active, -> { where(active: true) }

  before_validation :set_attributes

  def complete
    subscription_installments.create!(
      price:,
      status: "paid",
      paid_at: Time.current,
      due_at: Time.current
    )

    if plan.destination_business_id
      user.update_columns(
        business_id: plan.destination_business_id
      )
    end

    update!(active: true)

    user.funds.points.approved.create!(
      subscription: self,
      amount: points,
      credits_at: subscription_config.days_to_credit_points.to_i.days.from_now
    )

    user.subscriptions.active.without(self).each { |sub| sub.cancel!(notify: false) }
  end

  def pay(idempotency_key:, payment_method:, params:)
    increment!(:retries)

    processor = Payment::Processor.new(
      order: self,
      idempotency_key:,
      payment_method:,
      params:
    )

    payment = processor.call

    return payment unless payment.captured?

    update_columns(retries: 0)

    update_credit_card!(processor.payment_data) if processor.payment_data&.persisted?

    send_approved_email

    payment
  end

  def cancel!(notify: true, email_template: :canceled, reason: nil)
    self.active = false
    self.reason = reason
    save!
    send_canceled_email(email_template) if notify
  end

  def update_credit_card!(credit_card)
    transaction do
      update!(credit_card:)
      user.credit_cards.each { |card| card.update!(main: false) }
      credit_card.update!(main: true)
    end
  end

  def gateway_business_external_id
    user.main_business.gateway_external_id(payment_gateway)
  end

  def service
    "subscription"
  end

  def payment_gateway
    "global_pay"
  end

  def self.charge_due_installments
    active
      .eager_load(:subscription_installments)
      .where(retries: ...RETRY_LIMIT)
      .order(:updated_at)
      .select(&:payment_due?)
      .each do |subscription|
        user = subscription.user
        next if user.deleted? || !user.active? || !user.business.active? || !user.main_business.active? || !user.main_business.points_currency?

        payment = subscription.pay(
          idempotency_key: SecureRandom.uuid,
          payment_method: Payment::PaymentMethod::CREDIT_CARD,
          params: {
            credit_card: subscription.user.credit_cards.find_by!(main: true),
            anti_fraud_params: {
              ip: "*******",
              session_id: SecureRandom.uuid
            }
          }
        )

        if !payment.captured? && subscription.retries >= RETRY_LIMIT
          subscription.cancel!(reason: payment.reason_denied, email_template: :payment_failed)
        end
      end
  end

  def payment_due?
    payment_next_date <= Date.current
  end

  def payment_next_date
    first_date = subscription_installments.min_by(&:due_at).due_at.to_date
    last_date = subscription_installments.max_by(&:due_at).due_at.to_date
    next_date = last_date + 1.month
    if next_date.day.in?([30, 31]) || (first_date.day.in?([30, 31]) && next_date.month != 2) || (first_date.month != 2 && last_date.month == 2 && last_date.day.in?([28, 29]))
      next_date = next_date.change(day: 30)
    end
    next_date
  end

  private

  def send_approved_email
    SubscriptionMailer
      .with(business: user.main_business, user:)
      .approved(plan:)
      .deliver_now
  end

  def send_canceled_email(template)
    SubscriptionMailer
      .with(business: user.main_business, user:)
      .send(template, plan:)
      .deliver_now
  end

  def set_attributes
    if plan
      self.price = plan.price
      self.title = plan.title
      self.points = plan.points
      self.recurrence = plan.recurrence
    end
  end
end
