# frozen_string_literal: true

module TelemedicineBeneficiary::ThirdPartyIntegration
  extend ActiveSupport::Concern

  # TODO: passar telemedicine_config.plan como parametro para os workers pra evitar conflitos

  included do
    def create_or_update_third_party_beneficiary
      return if subscribed_to_telemedicine?

      ActiveRecord::Base.transaction do
        update!(telemedicine_enabled: true)
        telemedicine_activities.create(entry_type: :subscribed)
        third_party_beneficiary = Conexa::Client.create_or_update_user(third_party_beneficiary_params, business.telemedicine_config.plan)
        update!(telemedicine_external_id: third_party_beneficiary["id"])
      end
    end

    def reactivate_third_party_beneficiary
      return if active_telemedicine?

      ActiveRecord::Base.transaction do
        update!(telemedicine_enabled: true)
        telemedicine_activities.create!(entry_type: :reactivated)
        Conexa::Client.unblock_user(telemedicine_external_id, business.telemedicine_config.plan)
      rescue Conexa::IntegrationError
        raise ActiveRecord::Rollback
      end
    end

    def deactivate_third_party_beneficiary
      return unless active_telemedicine?

      ActiveRecord::Base.transaction do
        update!(telemedicine_enabled: false)
        telemedicine_activities.create!(entry_type: :deactivated)
        Conexa::Client.block_user(telemedicine_external_id, business.telemedicine_config.plan)
      rescue Conexa::IntegrationError
        raise ActiveRecord::Rollback
      end
    end

    def generate_third_party_smart_link
      if active_telemedicine?
        Conexa::Client.generate_smart_link(telemedicine_external_id, business.telemedicine_config.plan)
      end
    end

    def third_party_beneficiary_params
      {
        id: telemedicine_external_id,
        name:,
        mail: email,
        cpf:,
        enterprise: business_id,
        additionalInformation: business.name
      }.compact_blank
    end
  end
end
