# frozen_string_literal: true

class Business::Setup::FundRedemptionConfig
  def initialize(business)
    @business = business
  end

  def setup
    default_values[business.currency.to_sym].each do |service, values|
      FundRedemptionConfig.create!(business:, currency: business.currency, service:, **values)
    end
  end

  private

  attr_reader :business

  def default_values
    {
      BRL: {
        azul: {
          fair_value: 0.03,
          min_amount: 1,
          multiple: 1
        }
      },
      points: {
        bank_transfer: {
          fair_value: 0.03,
          min_amount: 1000,
          multiple: 100
        },
        giftcard: {
          fair_value: 0.03,
          min_amount: nil,
          multiple: nil
        },
        azul: {
          fair_value: 1.0,
          min_amount: 1,
          multiple: 100
        }
      }
    }
  end
end
