# frozen_string_literal: true

module Business::Telemedicine
  extend ActiveSupport::Concern

  included do
    has_one :telemedicine_config, dependent: :destroy

    after_commit :sync_telemedicine, if: -> { saved_change_to_telemedicine? }, on: :update

    def sync_telemedicine
      return if telemedicine_config.blank?
      telemedicine ? activate_telemedicine_users : deactivate_telemedicine_users
    end

    def telemedicine_slot_available?
      return false unless telemedicine
      authorized_users.subscribed_to_telemedicine.count < telemedicine_config&.contracted_beneficiaries.to_i
    end

    # Para manter compatibilidade com o descontinuado TelemedicineBeneficiary
    def telemedicine_beneficiaries = authorized_users.with_telemedicine_enabled

    private

    def activate_telemedicine_users
      TelemedicineConfig.enable_beneficiaries(authorized_users.with_telemedicine_disabled.pluck(:id))
    end

    def deactivate_telemedicine_users
      telemedicine_config.disable_users_to_telemedicine
    end
  end
end
