# frozen_string_literal: true

module TelemedicineConfig::WorkersHelper
  extend ActiveSupport::Concern

  class_methods do
    def upsert_beneficiaries(elegible_ids)
      enqueue_worker(Telemedicine::ThirdPartyUpsertWorker, elegible_ids)
    end

    def disable_beneficiaries(enabled_ids)
      enqueue_worker(Telemedicine::ThirdPartyDeactivateWorker, enabled_ids)
    end

    def enable_beneficiaries(disabled_ids)
      enqueue_worker(Telemedicine::ThirdPartyReactivateWorker, disabled_ids)
    end

    private

    def enqueue_worker(worker, ids)
      return if ids&.empty?
      ids.each do |auth_user_id|
        worker.perform_async(auth_user_id)
      end
    end
  end
end
