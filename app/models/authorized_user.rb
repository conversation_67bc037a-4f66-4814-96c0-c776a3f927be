# frozen_string_literal: true

class AuthorizedUser < ApplicationRecord
  include Taggable
  include AuthorizedUser::Accountable
  include AuthorizedUser::Groupable
  include AuthorizedUser::Telemedicine
  include AuthorizedUser::TelemedicineIntegration

  belongs_to :business
  belongs_to :main_business, class_name: "Business"
  belongs_to :authorized_user_group, optional: true

  validates :cpf, uniqueness: {scope: :business_id}
  validates :cpf, uniqueness: {scope: :main_business_id}
  validates :cpf, presence: true
  validate :cpf_must_be_valid
  validate :authorized_user_group_on_same_business
  validate :cpf_must_not_be_banned, if: :active

  before_validation :sanitize_cpf
  before_validation :normalize_email
  before_validation :normalize_phone
  before_validation :set_main_business, if: :business_id
  before_validation :normalize_name, if: :name_changed?

  scope :inactive, -> { where(active: false) }
  scope :active, -> { where(active: true) }

  scope :by_term, lambda { |term|
    if term.present?
      cleaned_term = term.delete("^0-9")
      if cleaned_term.present?
        where("unaccent(authorized_users.name) ILIKE unaccent(:term) OR authorized_users.email ILIKE :term OR authorized_users.cpf LIKE :cpf_term",
          term: "%#{term}%", cpf_term: "%#{cleaned_term}%")
      else
        where("unaccent(authorized_users.name) ILIKE unaccent(:term) OR authorized_users.email ILIKE :term", term: "%#{term}%")
      end
    end
  }

  scope :by_status, lambda { |status|
    where(active: status) unless status.nil?
  }

  scope :by_custom_field, lambda { |custom_field, field_value|
    where(custom_field => field_value) if custom_field.present? && field_value.present?
  }

  scope :by_user_tags, lambda { |user_tags|
    joins(:authorized_user_group).where(authorized_user_group: {slug: user_tags}) if user_tags.present?
  }

  scope :by_taxpayer_numbers, lambda { |taxpayer_numbers|
    where(cpf: taxpayer_numbers) if taxpayer_numbers.present?
  }

  scope :by_main_business_id, ->(business_id) { where(main_business_id: business_id) if business_id }

  def project_ids
    [business_id, main_business_id].compact
  end

  private

  def sanitize_cpf
    self.cpf = cpf.delete("^0-9") if cpf.present?
  end

  def cpf_must_be_valid
    errors.add(:cpf, "inválido") unless CPF.valid?(cpf)
  end

  def cpf_must_not_be_banned
    return unless BannedCpf.where(cpf:).exists?

    errors.add(:cpf, I18n.t("user.sign_up.banned_cpf"))
  end

  def authorized_user_group_on_same_business
    return if authorized_user_group_id.blank?

    return unless authorized_user_group.business_id != business_id

    errors.add(:authorized_user_group, I18n.t("authorized_user.unrelated_authorized_user_group"))
  end

  def normalize_email
    if email.blank?
      self.email = nil
    else
      self.email = I18n.transliterate(email.strip)
      self.email = nil unless email.match?(/\A[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\z/)
    end
  end

  def normalize_phone
    self.phone = phone.delete("^0-9") if phone.present?
  end

  def normalize_name
    self.name = Utils::NameNormalizer.call(name)
  end

  def set_main_business
    self.main_business_id = business.main_business_id || business_id
  end
end
