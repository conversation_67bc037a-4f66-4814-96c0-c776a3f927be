# frozen_string_literal: true

class Payment::Gateway::GlobalPay::ResponseParser
  attr_reader :global_pay_response

  def initialize(global_pay_response)
    @global_pay_response = global_pay_response
  end

  def response
    Payment::Gateway::Response.new(
      response_json: @global_pay_response.json,
      status:,
      error_message: @global_pay_response.error_message
    )
  end

  private

  def status
    return Payment::Status::DENIED unless @global_pay_response.success

    case @global_pay_response.status_text.downcase
    when "paid" then Payment::Status::CAPTURED
    else Payment::Status::DENIED
    end
  end
end
