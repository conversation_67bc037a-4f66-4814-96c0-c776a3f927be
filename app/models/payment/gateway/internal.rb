# frozen_string_literal: true

class Payment::Gateway::Internal
  ACCEPTED_PAYMENT_METHODS = %w[wallet_withdrawal].freeze

  def initialize(payment_method:, user:, value:)
    @payment_method = payment_method
    @value = value.to_i
    @wallet = user.wallets.find_by!(currency: :points)
  end

  def process(*)
    unless payment_method.in?(ACCEPTED_PAYMENT_METHODS)
      return Payment::Gateway::Response.new(status: Payment::Status::DENIED, error_message: "Método de pagamento inválido")
    end

    wallet.withdraw!(amount: value)

    Payment::Gateway::Response.new(status: Payment::Status::CAPTURED)
  rescue Wallet::NoBalanceError => e
    Payment::Gateway::Response.new(status: Payment::Status::DENIED, error_message: e.message)
  end

  private

  attr_reader :payment_method, :value, :wallet
end
