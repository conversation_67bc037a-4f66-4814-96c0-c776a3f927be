# frozen_string_literal: true

require "global_pay/api"

class Payment::Gateway::GlobalPay
  ACCEPTED_PAYMENT_METHODS = %w[credit_card].freeze

  attr_reader :split_data

  def initialize(payment_method:, value:, product_name:, params:, business:, business_external_id: nil, soft_description: nil)
    @payment_method = payment_method
    @value = value
    @product_name = product_name
    @business_external_id = business_external_id
    @soft_description = soft_description
    @params = params || {}
    @business = business
    @credit_card = @params[:credit_card]
    @anti_fraud_params = @params[:anti_fraud_params]

    validate_payment_method!
  end

  def process
    request = transform_request
    @split_data = request.to_hash["splits"]
    response = charge(request)
    transform_response(response)
  end

  private

  attr_reader :payment_method, :value, :product_name, :business_external_id, :soft_description, :credit_card, :anti_fraud_params, :business

  def validate_payment_method!
    unless payment_method.in?(ACCEPTED_PAYMENT_METHODS)
      raise Payment::Gateway::InvalidPaymentMethodError, "Método de pagamento inválido"
    end
  end

  def transform_request
    Payment::Gateway::GlobalPay::RequestParser.new(
      value:,
      product_name:,
      credit_card:,
      anti_fraud_params:,
      split: business.payment_split?,
      business_external_id:,
      soft_description:
    ).request
  end

  def charge(data)
    GlobalPay::Client.new.payment(data)
  end

  def transform_response(response)
    Payment::Gateway::GlobalPay::ResponseParser.new(response).response
  end
end
