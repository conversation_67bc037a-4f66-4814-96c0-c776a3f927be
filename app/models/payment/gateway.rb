# frozen_string_literal: true

class Payment::Gateway
  attr_reader :payment_data, :split_data

  def initialize(payment_gateway:, payment_method:, value:, product_name:, params:, user:, business_external_id: nil, soft_description: nil)
    @payment_gateway = payment_gateway
    @payment_method = payment_method
    @value = value
    @product_name = product_name
    @business_external_id = business_external_id
    @soft_description = soft_description
    @params = params
    @payment_data = params[:credit_card]
    @user = user
    @gateway = build_gateway
  end

  def process
    tokenize_credit_card if payment_method == "credit_card" && params[:store_card]

    response = gateway.process

    @split_data = gateway.try(:split_data)

    response
  end

  private

  attr_reader :payment_gateway, :payment_method, :value, :product_name, :business_external_id, :soft_description, :params, :user, :gateway

  def build_gateway
    case payment_gateway
    when "global_pay" then Payment::Gateway::GlobalPay.new(payment_method:, value:, product_name:, business_external_id:, soft_description:, params:, business: user.main_business)
    when "internal" then Payment::Gateway::Internal.new(payment_method:, user:, value:)
    else raise NoMethodError
    end
  end

  def tokenize_credit_card
    @payment_data.save if @payment_data.new_record?
  end
end
