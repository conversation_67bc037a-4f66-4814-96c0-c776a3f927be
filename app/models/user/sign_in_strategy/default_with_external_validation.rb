# frozen_string_literal: true

class User::SignInStrategy::DefaultWithExternalValidation
  include ActiveModel::Validations

  validate :active_user?, if: :user
  validate :custom_auth_must_be_successful, if: :user

  def initialize(sanitized_params:, business:)
    @business = business
    @cpf = CPF.new(sanitized_params[:cpf]).stripped
    @params = sanitized_params
  end

  def find_user
    validate
    if user.present? && user.errors.blank? && custom_auth.business.present?
      user.update(business_id: custom_auth.business.id)
    end

    user
  end

  private

  attr_reader :cpf, :business, :params

  def custom_auth_must_be_successful
    return if business.auth_integration.present? && !business.auth_integration.use_on_login
    return if custom_auth.success?

    user.errors.add(:base, custom_auth.message)
  end

  def custom_auth
    @custom_auth ||=
      if business.project_config.auth_integration_type.present?
        CustomAuth::AuthV2.new(main_business: business.main_project, params:).call
      else
        CustomAuthService
          .new(api_key: business.name.parameterize, params:)
          .sign_in
      end
  end

  def active_user?
    return false if user.active

    user.errors.add(:base, business.message_by_type(Enums::CustomTextType::CONTACT_MANAGER))
  end

  def user
    @user ||= User.find_by(cpf:, deleted: false, business_id: business.all_project_ids)
  end
end
