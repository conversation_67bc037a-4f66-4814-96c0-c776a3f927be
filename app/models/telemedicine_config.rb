# frozen_string_literal: true

class TelemedicineConfig < ApplicationRecord
  include TelemedicineConfig::<PERSON><PERSON><PERSON><PERSON>

  belongs_to :business

  after_commit :sync_telemedicine, if: -> { saved_change_to_plan? || saved_change_to_contracted_beneficiaries? }

  validate :contracted_beneficiaries_must_be_between_plan_limits

  def sync_telemedicine
    nulify_beneficiaries_external_ids if saved_change_to_plan?

    if business.telemedicine?
      apply_contracted_beneficiaries_limit
      enable_subscribed_beneficiaries
      upsert_elegible_beneficiaries
    end
  end

  def apply_contracted_beneficiaries_limit
    current_beneficiaries_count = subscribed_beneficiaries_ids.size

    if contracted_beneficiaries > current_beneficiaries_count
      elegible_ids = select_elegible_users_ids(contracted_beneficiaries - current_beneficiaries_count)
      enable_users_to_telemedicine(elegible_ids) if elegible_ids.any?
    elsif contracted_beneficiaries < current_beneficiaries_count
      exceeded_ids = active_beneficiaries_user_ids.drop(current_beneficiaries_count - contracted_beneficiaries)
      disable_users_to_telemedicine(exceeded_ids)
    end
  end

  def disable_users_to_telemedicine(ids: authorized_users_ids)
    authorized_users.where(id: ids, telemedicine: true).in_batches.update_all(telemedicine: false)
    disable_beneficiaries(active_beneficiaries_ids)
  end

  private

  def contracted_beneficiaries_must_be_between_plan_limits
    return if contracted_beneficiaries.zero?
    available_limits = Telemedicine::Plans.contracted_beneficiaries_limits_for_plan(plan)

    if contracted_beneficiaries < available_limits.first
      errors.add(:contracted_beneficiaries, :below_limit)
    end

    if contracted_beneficiaries > available_limits.last
      errors.add(:contracted_beneficiaries, :above_limit)
    end

    unless contracted_beneficiaries.in?(available_limits)
      errors.add(:contracted_beneficiaries, :unknown_limit)
    end
  end

  def select_elegible_users_ids(limit)
    authorized_users.where.not(id: subscribed_beneficiaries_ids).limit(limit).pluck(:id)
  end

  def enable_users_to_telemedicine(ids)
    authorized_users.where(id: ids, telemedicine_external_id: nil).in_batches.update_all(telemedicine: true)
  end

  def nulify_beneficiaries_external_ids
    authorized_users.subscribed_to_telemedicine.in_batches.update_all(telemedicine_external_id: nil)
  end

  def enable_subscribed_beneficiaries
    enable_beneficiaries(inactive_beneficiaries_ids)
  end

  def upsert_elegible_beneficiaries
    upsert_beneficiaries(elegible_beneficiaries_ids)
  end

  def elegible_beneficiaries_ids
    authorized_users.elegible_to_telemedicine.pluck(:id)
  end

  def inactive_beneficiaries_ids
    authorized_users.with_telemedicine_disabled.pluck(:id)
  end

  def active_beneficiaries_ids
    authorized_users.with_telemedicine_enabled.pluck(:id)
  end

  def subscribed_beneficiaries_ids
    authorized_users.subscribed_to_telemedicine.pluck(:id)
  end

  def authorized_users_ids
    authorized_users.pluck(:id)
  end

  def authorized_users
    @authorized_users ||= business.authorized_users.active
  end
end
