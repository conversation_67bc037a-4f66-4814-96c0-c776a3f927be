# frozen_string_literal: true

class Payment < ApplicationRecord
  enum :status, Payment::Status.enum
  enum :payment_method, Payment::PaymentMethod.enum, prefix: true

  belongs_to :order, optional: true
  belongs_to :subscription, optional: true

  has_many :payment_history, dependent: :delete_all

  before_validation :generate_soft_description, unless: :soft_description

  validates :payment_method, inclusion: {in: Payment::PaymentMethod.all}
  validates :value, :status, :payment_method, presence: true
  validates :idempotency_key, uniqueness: true

  after_commit :create_history, if: :create_history?

  serialize :json, coder: JSON

  def self.create_from_order(order:, value:, idempotency_key:, payment_method:)
    case order.service
    when "giftcard"
      Payment.pending.create!(order:, value:, idempotency_key:, payment_method:)
    when "subscription"
      Payment.pending.create!(subscription: order, value:, idempotency_key:, payment_method:)
    end
  end

  def update_from_gateway_response(response)
    update!(json: response.response_json, status: response.status, reason_denied: response.error_message)
  end

  private

  def generate_soft_description
    business = order&.user&.main_business || subscription&.user&.main_business

    return if !business || !business.payment_soft_description

    self.soft_description = "#{business.payment_soft_description}*#{service_description}".truncate(22, omission: "")
  end

  def service_description
    case (order || subscription).service
    when "giftcard"
      "GIFTCARD"
    when "subscription"
      "ASSINATURA"
    else
      "PAGAMENTO"
    end
  end

  def create_history
    PaymentHistory::CreateWorker.perform_async(id, status, json, DateTime.current.to_s)
  end

  def create_history?
    saved_changes.keys.any? { _1.in?(%w[status json]) }
  end
end
