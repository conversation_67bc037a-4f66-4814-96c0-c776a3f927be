# frozen_string_literal: true

class TelemedicineBeneficiary < ApplicationRecord
  include TelemedicineBeneficiary::ThirdPartyIntegration

  belongs_to :authorized_user
  has_one :business, through: :authorized_user

  delegate :telemedicine_activities, to: :authorized_user

  validates :cpf, :name, :email, presence: true

  scope :enabled, -> { where(enabled: true) }
  scope :disabled, -> { where(enabled: false) }
  scope :subscribed, -> { where.not(external_id: nil) }
  scope :elegible, -> { enabled.where(external_id: nil) }
  scope :active, -> { enabled.subscribed }
  scope :inactive, -> { disabled.subscribed }

  def disabled
    !enabled
  end
end
