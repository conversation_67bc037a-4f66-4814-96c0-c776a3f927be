# frozen_string_literal: true

module AuthorizedUser::Telemedicine
  extend ActiveSupport::Concern

  included do
    has_many :telemedicine_activities, class_name: "TelemedicineActivity"
    has_one :telemedicine_beneficiary

    scope :subscribed_to_telemedicine, -> { where.not(telemedicine_external_id: nil) }
    scope :elegible_to_telemedicine, -> { where(telemedicine: true, telemedicine_external_id: nil) }
    scope :with_telemedicine_enabled, -> { subscribed_to_telemedicine.where(telemedicine_enabled: true) }
    scope :with_telemedicine_disabled, -> { subscribed_to_telemedicine.where(telemedicine_enabled: false) }

    after_commit :sync_telemedicine, if: -> { saved_change_to_telemedicine? }, on: :update

    def sync_telemedicine
      return unless in_telemedicine_project?
      telemedicine ? enable_telemedicine : disable_telemedicine
    end

    def subscribed_to_telemedicine?
      telemedicine_external_id.present?
    end

    def in_telemedicine_project?
      business.telemedicine?
    end

    def active_telemedicine?
      telemedicine_enabled && telemedicine_external_id.present?
    end

    def inactive_telemedicine?
      !telemedicine_enabled && telemedicine_external_id.present?
    end

    def track_telemedicine_activity!
      ActiveRecord::Base.transaction do
        telemedicine_activities.create!(entry_type: :accessed)
      end
    end

    def generate_telemedicine_smart_link = generate_third_party_smart_link

    private

    def enable_telemedicine
      if telemedicine_external_id.blank? && business.telemedicine_slot_available?
        subscribe_to_telemedicine
      else
        reactive_telemedicine
      end
    end

    def subscribe_to_telemedicine
      TelemedicineConfig.upsert_beneficiaries([id])
    end

    def reactive_telemedicine
      TelemedicineConfig.enable_beneficiaries([id])
    end

    def disable_telemedicine
      return if telemedicine_external_id.blank? || !telemedicine_enabled
      TelemedicineConfig.disable_beneficiaries([id])
    end
  end
end
