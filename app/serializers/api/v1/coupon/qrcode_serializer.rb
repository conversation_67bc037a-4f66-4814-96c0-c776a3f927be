class Api::V1::Coupon::QrcodeSerializer < ActiveModel::Serializer
  include ActionView::Helpers::NumberHelper

  attribute :id
  attributes :branch_id,
    :cashback_text,
    :categories,
    :description,
    :discount,
    :end_date,
    :infinity,
    :organization_cover_image,
    :organization_id,
    :organization_name,
    :picture_large_url,
    :picture_small_url,
    :rules,
    :start_date,
    :tags,
    :template,
    :title,
    :usage_instruction,
    :working_days

  def discount
    if object.promotion.discount_type == Promotion::DiscountType::PERCENT
      object.promotion.discount_value.to_i
    else
      0
    end
  end

  def template
    template_hash[object.c_type.downcase.to_sym] || template_hash[:default]
  end

  def organization_name
    (object.organization || object.branch.organization).name
  end

  def organization_cover_image
    (object.organization || object.branch.organization).logo_image.url || ""
  end

  def working_days
    (0..6).map { |n|
      {
        week_day: n,
        is_available: available(n),
        start_hour: object.start_hour.strftime("%H:%M"),
        end_hour: object.end_hour.strftime("%H:%M")
      }
    }
  end

  def template_hash
    @instance_options[:template]
  end

  def rules
    text = ""
    text += "Desconto não cumulativo. " if object.not_cumulative
    text += "Promoção exclusiva para lojas físicas. " if object.only_physical
    text += "Limite de resgate por pessoa: #{object.redeems_per_cpf} " if object.redeems_per_cpf && object.redeems_per_cpf < 100
    text += "Um resgate por #{object.frequency_in_days} dia(s)." if object.frequency_in_days && object.frequency_in_days > 0
    text += object.rules&.gsub("{{client_name}}", current_business.name) || ""
    text += "\n#{object.cashback_rules}" if cashback? && object.cashback_rules.present?
    text + "\n#{current_contract_text}"
  end

  def cashback_text
    return unless cashback?
    if object.promotion.cashback_type == Cashback::Type::PERCENT
      "#{"%g" % cashback_value}% de cashback"
    else
      "#{cashback_value.to_brl_currency} de cashback"
    end
  end

  def picture_small_url
    object.promotion.picture&.url(:small) || ""
  end

  def picture_large_url
    object.promotion.picture&.url(:large) || ""
  end

  def description
    object.description&.gsub("{{client_name}}", current_business.name)
  end

  def categories
    ActiveModelSerializers::SerializableResource.new(
      object.promotion.categories
        .where(visible: true)
        .where.not(id: @instance_options[:blocklisted_category_ids]),
      each_serializer: @instance_options[:include_categories] ? Api::V1::Categories::SubcategorySerializer : NestedResourceIdSerializer,
      use_original_images: @instance_options[:original_category_images]
    )
  end

  private

  def current_business = @instance_options[:current_business]

  def current_contract_text
    return object.contract_custom_text if object.contract_custom_text.present?

    current_business.message_by_type(Enums::CustomTextType::PROMOTION_CUSTOM_TEXT)
  end

  def available(day_of_week)
    case day_of_week
    when 0
      object.sunday
    when 1
      object.monday
    when 2
      object.tuesday
    when 3
      object.wednesday
    when 4
      object.thursday
    when 5
      object.friday
    when 6
      object.saturday
    end
  end

  def cashback?
    @instance_options[:business_cashback] && cashback_value&.positive?
  end

  def cashback_value
    Cashback::Spread.new(
      organization_cashback_value: object.promotion.cashback_value || 0,
      business_spread_percent: @instance_options[:spread_percent]
    ).user_cashback_value.to_f
  end
end
