# frozen_string_literal: true

class VouchersMakeAvailableWorker
  include Sidekiq::Worker

  def perform(import_file_id, promotion_id, email, import_time)
    import_temp_file = ImportTempFile.find(import_file_id)
    service = VouchersMakeAvailableService.new(file_path: import_temp_file.tempfile.path, resource_id: promotion_id)
    service.call
    if service.error_messages.blank?
      Admin::VoucherMailer.make_available_success(email, import_time).deliver_now
    else
      Admin::VoucherMailer.make_available_error(
        email,
        import_time,
        service.error_messages,
        service.vouchers_with_error_csv
      ).deliver_now
    end
  rescue CsvInvalidHeadersError => e
    Admin::VoucherMailer.make_available_error(email, import_time, [e.message]).deliver_now
  rescue => e
    Admin::VoucherMailer.make_available_error(email, import_time, [e.message]).deliver_now
  end
end
