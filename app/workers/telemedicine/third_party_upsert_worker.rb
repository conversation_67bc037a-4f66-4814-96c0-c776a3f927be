# frozen_string_literal: true

class Telemedicine::ThirdPartyUpsertWorker
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options retry: 3, retry_queue: "low"
  sidekiq_throttle(
    concurrency: {limit: 3},
    threshold: {limit: 5, period: 1.second}
  )

  sidekiq_retries_exhausted do |job, _ex|
    # TODO: log and notify failure
  end

  def perform(id)
    auth_user = AuthorizedUser.find(id)
    auth_user.create_or_update_third_party_beneficiary
  end
end
