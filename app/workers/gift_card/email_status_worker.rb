# frozen_string_literal: true

class GiftCard::EmailStatusWorker
  include Sidekiq::Worker

  def perform(params)
    params = params.deep_symbolize_keys

    # OAB SP
    return if params.fetch(:business_id).to_i == 472

    business = Business.find(params.fetch(:business_id))
    user = User.find(params.fetch(:user_id))

    case params[:transaction_status]
    when "success"
      GiftCardMailer.with(business:, user:).success_feedback.deliver_now
    when "error"
      GiftCardMailer.with(business:, user:).error_feedback.deliver_now
    end
  end
end
