# frozen_string_literal: true

class Payment::RateLimitWorker
  include Sidekiq::Worker
  sidekiq_options queue: "critical"

  DENIED_PAYMENTS_LIMIT_TIME_RANGE = 10.minutes
  DENIED_PAYMENTS_LIMIT = 3

  def perform(user_id, payment_id)
    payment = Payment.find(payment_id)
    return unless payment.status == Payment::Status::DENIED

    user = User.find(user_id)
    time_to_check = payment.created_at
    recent_denied_payments =
      user.payments
        .where(created_at: (time_to_check - DENIED_PAYMENTS_LIMIT_TIME_RANGE)..time_to_check)
        .denied

    if recent_denied_payments.count > DENIED_PAYMENTS_LIMIT
      BannedCpf.create(cpf: user.cpf, reason: BannedCpf::Reasons::PAYMENT_RATE_LIMIT)
      PaymentMailer.with(business: user.business, user:).rate_limit_ban.deliver_now
    end
  end
end
