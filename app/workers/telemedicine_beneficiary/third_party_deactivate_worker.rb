# frozen_string_literal: true

class TelemedicineBeneficiary::ThirdPartyDeactivateWorker
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker

  sidekiq_options retry: 3, retry_queue: "low"
  sidekiq_throttle(
    concurrency: {limit: 5},
    threshold: {limit: 20, period: 1.second}
  )

  sidekiq_retries_exhausted do |job, _ex|
    # TODO: log and notify failure
  end

  def perform(id)
    auth_user = AuthorizedUser.find_by(id: id)
    return unless auth_user
    auth_user.deactivate_third_party_beneficiary
  end
end
