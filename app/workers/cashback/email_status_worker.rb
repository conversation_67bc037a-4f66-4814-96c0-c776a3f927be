# frozen_string_literal: true

class Cashback::EmailStatusWorker
  include Sidekiq::Worker

  def perform(params)
    params = params.deep_symbolize_keys

    # OAB SP
    return if params[:business_id].to_i == 472

    user = User.find(params[:user_id])
    business = Business.find(params[:business_id])

    case params[:transaction_status]
    when Enums::CashbackRecordStatus::PENDING
      CashbackMailer.with(business:, user:).pending.deliver_now
    when Enums::CashbackRecordStatus::APPROVED
      CashbackMailer.with(business:, user:).pending.deliver_now
    when Enums::CashbackRecordStatus::AVAILABLE
      CashbackMailer.with(business:, user:).available.deliver_now
    when Enums::CashbackRecordStatus::IN_TRANSFER
      CashbackMailer.with(business:, user:).in_transfer.deliver_now
    when Enums::CashbackRecordStatus::TRANSFERRED
      CashbackMailer.with(business:, user:).transferred.deliver_now
    end
  end
end
