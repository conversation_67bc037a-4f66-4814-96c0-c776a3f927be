# frozen_string_literal: true

class VouchersImportWorker
  include Sidekiq::Worker

  def perform(import_file_id, promotion_id, email, import_time)
    import_temp_file = ImportTempFile.find(import_file_id)
    promotion = Promotion.find(promotion_id)
    service = VouchersImportService.new(file_path: import_temp_file.tempfile.path, resource: promotion)
    service.call
    if service.error_messages.blank?
      promotion
        .refresh_status
        .update_columns(
          voucher_type: Promotion::VoucherType::LIST,
          dynamic_voucher: false,
          code: nil
        )
      promotion
        .refresh_coupons
        .propagate_changes
      Admin::VoucherMailer.import_success(email, import_time).deliver_now
    else
      Admin::VoucherMailer.import_error(
        email,
        import_time,
        service.error_messages,
        service.vouchers_with_error_csv
      ).deliver_now
    end
  rescue => e
    Admin::VoucherMailer.import_error(email, import_time, [e.message]).deliver_now
  end
end
