# frozen_string_literal: true

class Shopkeeper::V2::BaseController < ApplicationController
  include ::ErrorHandler
  class ShopkeeperRoleAdminError < StandardError; end

  before_action :authenticate_shopkeeper!

  protected

  def authenticate_shopkeeper!
    render json: {error: "Acesso negado"}, status: :unauthorized unless current_shopkeeper
  end

  def current_shopkeeper
    @current_shopkeeper ||= Shopkeeper.find_by(
      authentication_token: request.headers["X-Shopkeeper-Token"],
      email: request.headers["X-Shopkeeper-Email"]
    )
  end

  rescue_from ShopkeeperRoleAdminError do
    render json: {error: "Acesso negado"}, status: :forbidden
  end

  def guard_shopkeeper_role_admin_lecupon
    raise ShopkeeperRoleAdminError unless current_shopkeeper.admin_lecupon_role?
  end

  def guard_shopkeeper_role_admin_shopkeeper
    raise ShopkeeperRoleAdminError unless current_shopkeeper.admin_shopkeeper_role?
  end

  def guard_shopkeeper_role_admin
    raise ShopkeeperRoleAdminError if !current_shopkeeper.admin_lecupon_role? && !current_shopkeeper.admin_shopkeeper_role?
  end

  def authorize_default_password
    raise ArgumentError, "Senha incorreta" if params[:password] != "8K&]kb[#<PQXPC{e"
  end
end
