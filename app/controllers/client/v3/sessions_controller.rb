# frozen_string_literal: true

class Client::V3::SessionsController < Devise::SessionsController
  before_action :set_client_employee, only: :create
  protect_from_forgery with: :null_session

  def create
    return render_unauthorized unless valid_current_password?

    generate_admin_jwt

    render json: @client_employee,
      serializer: Client::V3::SessionSerializer
  end

  private

  def set_client_employee
    @client_employee = ClientEmployee.find_by("email = :user_term OR username = :user_term",
      user_term: params[:email])

    render_unauthorized if @client_employee.nil? || !@client_employee.system_user
  end

  def generate_admin_jwt
    scope = Devise::Mapping.find_scope!(@client_employee)
    token, payload = Warden::JWTAuth::UserEncoder.new.call(@client_employee, scope, nil)
    @client_employee.update(jti: payload["jti"])
    response.headers.merge!(Warden::JWTAuth::HeaderParser.to_headers({}, token))
  end

  def valid_current_password?
    @client_employee.valid_password?(params[:password])
  end

  def render_unauthorized
    render json: {error: "Credenciais inválidas"}, status: :unauthorized
  end
end
