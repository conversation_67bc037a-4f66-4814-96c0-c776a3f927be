# frozen_string_literal: true

class Client::V2::CategoriesController < Client::V2::BaseController
  include Client::V2::Concerns::CurrentBusiness

  before_action :checky_if_choosing_business_is_mandatory,  if: -> { current_user.admin_client_role? }
  before_action :guard_not_found_current_business
  before_action :guard_admin_client_current_business

  def index
    render json: categories,
      each_serializer: Client::V2::Categories::IndexSerializer,
      blocklisted_category_ids:,
      with_sub_categories: include_sub_categories?
  end

  def show
    category =
      if alloyal_categories?
        Category.find(params[:id])
      else
        current_business.categories.find(params[:id])
      end

    render json: category,
      serializer: Client::V2::Categories::ShowSerializer
  end

  def create
    if alloyal_categories?
      Category.create!(category_params)
    else
      current_business.categories.create!(category_params)
    end

    head :created
  end

  def update
    category =
      if alloyal_categories?
        Category.find(params[:id])
      else
        current_business.categories.find(params[:id])
      end

    category.update!(category_params)

    head :ok
  end

  private

  def category_params
    permited_params = params
      .permit(:active, :category_type, :main_category_id, :short_description, :template, :title, :visible)

    if params[:image_resolution] == "264x140" && params[:image].present?
      permited_params[:deprecated_image] = params[:image]
    elsif params[:image].present?
      permited_params[:image] = params[:image]
    end

    permited_params
  end

  def categories
    Category
      .all
      .by_visible(params[:visible])
      .by_exclusive(exclusive:, business_ids: [current_business.id])
      .by_blocklisted(blocklisted:, business_ids: [current_business.id])
      .by_title(params[:title])
      .by_category_type(params[:category_type])
      .by_template(params[:template])
      .then { |relation| params[:only_main_categories] ? relation.main_categories : relation }
      .then { |relation| include_sub_categories? ? relation.main_categories.includes(:sub_categories) : relation }
      .order(:title)
      .page(params[:page])
  end

  def exclusive
    ActiveModel::Type::Boolean.new.cast(params[:exclusive])
  end

  def blocklisted
    ActiveModel::Type::Boolean.new.cast(params[:blocklisted])
  end

  def blocklisted_category_ids
    CategoryBlocklist.where(business_id: current_business.project_ids).pluck(:category_id)
  end

  def include_sub_categories?
    ActiveRecord::Type::Boolean.new.cast(params[:with_sub_categories])
  end

  def alloyal_categories?
    current_user.admin_lecupon_role? && tenant_id.blank?
  end
end
