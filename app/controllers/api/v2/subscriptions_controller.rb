# frozen_string_literal: true

class Api::V2::SubscriptionsController < Api::V2::BaseController
  before_action :set_plan, only: :create
  before_action :set_request_params, only: :create
  before_action :validate_request_params, only: :create

  def index
    subscription_config = current_business.subscription_config
    supports = Card.support.where(business_id: current_business.id).load_async
    faqs = Card.faq.where(business_id: current_business.id).load_async
    subscription_groups = SubscriptionGroup.includes(:plans).where(subscription_config_id: subscription_config.id, plans: {active: true}).load_async

    subscription = Api::V2::BffSubscriptionPresenter.new(
      subscription_config:,
      supports:,
      faqs:,
      group_sections: subscription_groups,
      business: current_business,
      user: current_user
    )

    render json: subscription, serializer: Api::V2::SubscriptionSerializer
  end

  def create
    subscription = current_user.subscriptions.create!(
      plan: @plan,
      subscription_config: current_business.subscription_config,
      active: false
    )

    payment = subscription.pay(
      idempotency_key: request.headers["Idempotency-Key"],
      payment_method: "credit_card",
      params: @request_params.to_hash
    )

    if subscription.active
      render json: {message: I18n.t("subscriptions.create.success", count: current_business.subscription_config.days_to_credit_points)}, status: :created
    else
      render json: {error: payment.reason_denied}, status: :unprocessable_entity
    end
  end

  private

  def set_plan
    @plan = Plan.find_by!(id: params[:plan_id], subscription_config: current_business.subscription_config)
  end

  def set_request_params
    @request_params = Api::V2::PaymentParams.new(
      ip: request.remote_ip,
      user: current_user,
      payment_method: "credit_card",
      payment_details: params[:payment_details]
    )
  end

  def validate_request_params
    return if @request_params.valid?

    render json: {error: @request_params.errors.full_messages.join(", ")}, status: :unprocessable_entity
  end
end
