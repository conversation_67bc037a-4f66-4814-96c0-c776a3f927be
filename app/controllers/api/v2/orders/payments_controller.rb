# frozen_string_literal: true

class Api::V2::Orders::PaymentsController < Api::V2::BaseController
  before_action :set_order
  before_action :set_request_params
  before_action :validate_request_params

  def create
    payment = @order.pay(
      idempotency_key: request.headers["Idempotency-Key"],
      payment_method: params[:payment_method],
      params: @request_params.to_hash
    )

    message = I18n.t(payment.status, scope: "payment.#{current_business.currency}.create")

    if payment.captured?
      send_email_success_feedback

      render json: {message:}, status: :created
    else
      render_error(error: payment.reason_denied || message, status_code: :unprocessable_entity)
    end
  end

  private

  def send_email_success_feedback
    GiftCardMailer
      .with(business: current_business, user: current_user)
      .success_feedback
      .deliver_now
  end

  def set_order
    @order = Order.payable.find(params[:order_id])
  end

  def set_request_params
    @request_params = Api::V2::PaymentParams.new(
      user: current_user,
      ip: request.remote_ip,
      payment_method: params[:payment_method],
      payment_details: params[:payment_details]
    )
  end

  def validate_request_params
    return if @request_params.valid?

    render json: {error: @request_params.errors.full_messages.join(", ")}, status: :unprocessable_entity
  end
end
