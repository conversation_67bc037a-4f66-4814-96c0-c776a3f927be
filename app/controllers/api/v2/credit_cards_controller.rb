# frozen_string_literal: true

class Api::V2::CreditCardsController < Api::V2::BaseController
  def index
    @credit_cards = current_user.credit_cards

    render json: @credit_cards, each_serializer: Api::V2::CreditCards::IndexSerializer
  end

  def create
    @credit_card = current_user.credit_cards.create(credit_card_params)

    if @credit_card.persisted?
      render json: @credit_card, serializer: Api::V2::CreditCards::CreateSerializer, status: :created
    else
      render_error(error: @credit_card.errors.full_messages.join(", "), status_code: :unprocessable_entity)
    end
  end

  def destroy
    credit_card = current_user.credit_cards.where(main: false).find(params[:id])

    credit_card.destroy!

    head :no_content
  end

  private

  def credit_card_params
    card_params = {
      seller_id: current_business.global_pay_external_id,
      number: params.dig(:card, :number).to_s.delete("^0-9"),
      holder: params.dig(:card, :holder).to_s.strip,
      security_code: params.dig(:card, :security_code).to_s.delete("^0-9"),
      billing_name: params.dig(:user, :name).to_s.strip,
      billing_document: params.dig(:user, :document).to_s.delete("^0-9"),
      billing_email: params.dig(:user, :email).to_s.strip,
      billing_phone: params.dig(:user, :phone).to_s.delete("^0-9"),
      billing_postal_code: params.dig(:address, :postal_code).to_s.strip,
      billing_street: params.dig(:address, :street).to_s.strip,
      billing_number: params.dig(:address, :number).to_s.strip,
      billing_complement: params.dig(:address, :complement).to_s.strip,
      billing_neighborhood: params.dig(:address, :neighborhood).to_s.strip,
      billing_city: params.dig(:address, :city).to_s.strip,
      billing_state: params.dig(:address, :state).to_s.strip
    }
    card_params[:exp_month] = params.dig(:card, :exp_month).to_s.rjust(2, "0") if params.dig(:card, :exp_month)
    card_params[:exp_year] = params.dig(:card, :exp_year).to_s.rjust(2, "0") if params.dig(:card, :exp_year)
    card_params
  end
end
