# frozen_string_literal: true

class Api::V2::Users::Telemedicine::AccessesController < Api::V2::BaseController
  before_action -> { authorize! with: Api::V2::Business::TelemedicinePolicy }
  before_action -> { authorize! with: Api::V2::Users::Telemedicine::AccessPolicy }

  def create
    smart_link = current_user.generate_telemedicine_smart_link
    if smart_link.present?
      current_user.track_telemedicine_activity!
      response.set_header("Location", smart_link)
      render json: {url: smart_link}

    else
      render_error_to_generate_smart_link
    end
  rescue Conexa::IntegrationError # TODO: remover dependencia direta Conexa
    render_error_to_generate_smart_link
  end

  private

  def render_error_to_generate_smart_link
    render json: {errors: I18n.t("telemedicine.errors.couldnt_create_smart_link")}, status: :unprocessable_entity
  end
end
