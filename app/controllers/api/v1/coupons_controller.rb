# frozen_string_literal: true

class Api::V1::CouponsController < Api::V1::BaseController
  before_action :set_coupon

  def show
    render json: @coupon,
      serializer: serializer(@coupon.c_type),
      template:,
      lat: params[:lat],
      lng: params[:lng],
      business_cashback: current_user.cashback?,
      user_id: current_user.id,
      current_business:,
      spread_percent: current_business.spread_percent,
      include_categories: include_categories?,
      original_category_images: current_business.id == (Rails.env.homologation? ? 8 : 414)
  end

  private

  def set_coupon
    @coupon = Cupon
      .active
      .left_joins(promotion: :base_tags)
      .merge(
        Promotion.where(business_id: nil)
          .or(Promotion.where(taggings: {id: nil}))
          .or(Promotion.where(taggings: {business_id: current_business.all_project_ids}, tags: {name: current_user.tags}))
      )
      .distinct
      .merge(Promotion.status_available)
      .available_for_business(current_user.project_ids)
      .joins(:organization)
      .merge(
        Organization
          .active
          .not_blocked(Organization::Blocklisted.new(business_ids: current_user.project_ids).ids)
          .and(
            Organization
              .where(all_projects: true)
              .or(
                Organization
                  .where(id: OrganizationProfile.where(business: current_user.project_ids).pluck(:organization_id))
              )
          )
      )
      .joins(:branch)
      .includes(:branch)
      .merge(Branch.active)
      .find(params[:id])
  end

  def serializer(c_type)
    Enums::CuponSerializerTemplate::TEMPLATE[c_type.downcase.to_sym]&.dig(1) ||
      Api::V1::Coupon::DefaultSerializer
  end

  def template
    Enums::CuponSerializerTemplate::TEMPLATE.transform_values { |v| v[0] }
  end

  def include_categories?
    Array(params[:include]).include?("categories")
  end
end
