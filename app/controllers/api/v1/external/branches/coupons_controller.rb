# frozen_string_literal: true

class Api::V1::External::Branches::CouponsController < Api::V1::BaseController
  before_action :set_branch

  def index
    @coupons =
      @branch
        .cupons
        .active
        .left_joins(promotion: :base_tags)
        .includes(:organization, promotion: :base_tags)
        .available_for_business(current_user.project_ids)
        .distinct
        .merge(Promotion.status_available)
        .joins(:branch)
        .merge(
          Branch
            .active
            .order_by_distance(branch_distance)
        )
        .select("cupons.*")
        .order_by(params[:order_by])
        .paginate(page: params[:page], per_page: params[:per_page])

    @coupons = @coupons.merge(Promotion.joins(:base_tags).where(tags: {name: params[:tags]})) if params[:tags]

    render json: @coupons,
      each_serializer: Api::V1::CouponSerializer,
      template:,
      lat: params[:lat],
      lng: params[:lng],
      business_cashback: business_cashback?,
      spread_percent: current_business.spread_percent,
      current_business:
  end

  private

  def set_branch
    @branch =
      Branch
        .active
        .where
        .not(organization_id: blocklisted_organization_ids)
        .find(params[:branch_id])
  end

  def template
    Enums::CuponSerializerTemplate::TEMPLATE.transform_values { |v| v[0] }
  end

  def business_cashback?
    @branch.online? && current_user.cashback?
  end
end
