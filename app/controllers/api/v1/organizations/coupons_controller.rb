# frozen_string_literal: true

class Api::V1::Organizations::CouponsController < Api::V1::BaseController
  before_action :set_organization

  def index
    raise ArgumentError, "Paramêtro de tipo de uso obrigatório" unless usage_type_valid?

    render json: coupons_with_cashback_filter,
      each_serializer: Api::V1::CouponSerializer,
      template:,
      lat: params[:lat],
      lng: params[:lng],
      business_cashback: current_user.cashback?,
      business_name: current_business.name,
      spread_percent: current_business.spread_percent,
      current_business:
  end

  def show
    @coupon = @organization.cupons
      .active
      .find(params[:id])

    if @coupon.c_type == Enums::CouponType::DYNAMIC_ONLINE
      Voucher.force_primary_database do
        voucher_redeem
      end
    end

    render json: @coupon,
      serializer: serializer(@coupon.c_type),
      template:,
      lat: params[:lat],
      lng: params[:lng],
      business_cashback: current_user.cashback?,
      dynamic_coupon_code: @voucher&.code,
      user_id: current_user.id,
      current_business:,
      spread_percent: current_business.spread_percent,
      include_categories: include_categories?,
      original_category_images: current_business.id == (Rails.env.homologation? ? 8 : 414)
  rescue Voucher::NotAvailableError => e
    render json: {error: e.message}, status: :unprocessable_entity
  end

  private

  def blocklisted_organization_ids
    Organization::Blocklisted.new(business_ids: current_user.project_ids).ids
  end

  def set_organization
    @organization =
      Organization
        .active
        .not_blocked(blocklisted_organization_ids)
        .and(
          Organization
            .where(all_projects: true)
            .or(
              Organization
                .where(id: OrganizationProfile.where(business:  current_user.project_ids).pluck(:organization_id))
            )
        )
        .find(params[:organization_id])
  end

  def branch_distance
    Branch::Distance.new(
      km_range: distance_km,
      lat: params[:lat],
      lng: params[:lng]
    )
  end

  def coupons_with_cashback_filter
    available_coupons.reject do |c|
      (c.title.downcase.include?("cashback") || c.description.downcase.include?("cashback")) &&
        !current_user.cashback?
    end
  end

  def available_coupons
    Cupon
      .where(organization: @organization)
      .left_joins(promotion: :base_tags)
      .merge(
        Promotion.where(business_id: nil)
          .or(Promotion.where(taggings: {id: nil}))
          .or(Promotion.where(taggings: {business_id: current_business.all_project_ids}, tags: {name: current_user.tags}))
      )
      .merge(Promotion.status_available)
      .active
      .available_for_business(current_user.project_ids)
      .by_usage_type(params[:usage_type], branch_distance)
      .where.not(promotions: {provider: Promotion::Provider::REGIONALIZED})
      .distinct
      .select("cupons.*")
      .includes(promotion: :base_tags)
      .paginate(page: params[:page], per_page: 15)
  end

  def voucher_redeem
    ActiveRecord::Base.transaction do
      @voucher = voucher_bucket.issue(resource: @coupon.promotion)
      if @voucher
        @order = Order.create!(
          user: current_user,
          cupon: @coupon,
          voucher: @voucher
        )
      end
    end

    call_order_hook(@order.id) if @order
  end

  def voucher_bucket
    @coupon.promotion.voucher_bucket
  end

  def usage_type_valid?
    %w[online physical].include?(params[:usage_type]) && params[:usage_type].present?
  end

  def serializer(c_type)
    Enums::CuponSerializerTemplate::TEMPLATE[c_type.downcase.to_sym]&.dig(1) ||
      Api::V1::Coupon::DefaultSerializer
  end

  def template
    Enums::CuponSerializerTemplate::TEMPLATE.transform_values { |v| v[0] }
  end

  def call_order_hook(order_id)
    WebhookSender::EventWorker.perform_async("order.created", order_id)
  end

  def include_categories?
    Array(params[:include]).include?("categories")
  end
end
