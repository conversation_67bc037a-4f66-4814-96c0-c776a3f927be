# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2025-03-20 13:50:00 UTC using RuboCop version 1.69.1.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 27
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle, NonImplicitAssociationMethodNames.
# SupportedStyles: explicit, implicit
FactoryBot/AssociationStyle:
  Exclude:
    - 'spec/factories/cupon_factory.rb'
    - 'spec/factories/custom_text_factory.rb'
    - 'spec/factories/lead_factory.rb'
    - 'spec/factories/order_factory.rb'
    - 'spec/factories/payout_factory.rb'
    - 'spec/factories/user_factory.rb'

# Offense count: 3
# Configuration parameters: Include.
# Include: **/*_spec.rb, **/spec/**/*, **/test/**/*, **/features/support/factories/**/*.rb
FactoryBot/FactoryAssociationWithStrategy:
  Exclude:
    - 'spec/factories/authorized_user_factory.rb'
    - 'spec/factories/business_factory.rb'
    - 'spec/factories/telemedicine_beneficiary_factory.rb'

# Offense count: 288
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: with_first_element, with_fixed_indentation
Layout/ArrayAlignment:
  Enabled: false

# Offense count: 91
# This cop supports safe autocorrection (--autocorrect).
Layout/EmptyLineAfterGuardClause:
  Enabled: false

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowAliasSyntax, AllowedMethods.
# AllowedMethods: alias_method, public, protected, private
Layout/EmptyLinesAroundAttributeAccessor:
  Exclude:
    - 'lib/ileva/api.rb'
    - 'lib/ixc/api.rb'

# Offense count: 59
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowForAlignment, AllowBeforeTrailingComments, ForceEqualSignAlignment.
Layout/ExtraSpacing:
  Enabled: false

# Offense count: 412
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: IndentationWidth.
# SupportedStyles: special_inside_parentheses, consistent, align_brackets
Layout/FirstArrayElementIndentation:
  EnforcedStyle: consistent

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: aligned, indented
Layout/LineEndStringConcatenationIndentation:
  Exclude:
    - 'lib/shopee/deep_link.rb'
    - 'lib/shopee/transactions.rb'
    - 'spec/models/playhub_client_spec.rb'

# Offense count: 6
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBraces.
# SupportedStyles: space, no_space, compact
# SupportedStylesForEmptyBraces: space, no_space
Layout/SpaceInsideHashLiteralBraces:
  Exclude:
    - 'db/migrate/20231109162141_create_maintenance_tasks_runs.maintenance_tasks.rb'
    - 'db/migrate/20231109162147_add_index_on_task_name_and_status_to_runs.maintenance_tasks.rb'

# Offense count: 107
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowedMethods, AllowedPatterns.
Lint/AmbiguousBlockAssociation:
  Enabled: false

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
Lint/AmbiguousOperatorPrecedence:
  Exclude:
    - 'app/serializers/api/v1/branch_serializer.rb'
    - 'app/services/payout/history/notification_service.rb'
    - 'lib/ixc/token/validator.rb'

# Offense count: 35
# Configuration parameters: IgnoreLiteralBranches, IgnoreConstantBranches, IgnoreDuplicateElseBranch.
Lint/DuplicateBranch:
  Enabled: false

# Offense count: 27
# Configuration parameters: AllowComments, AllowEmptyLambdas.
Lint/EmptyBlock:
  Enabled: false

# Offense count: 43
# Configuration parameters: AllowedParentClasses.
Lint/MissingSuper:
  Enabled: false

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Lint/NonAtomicFileOperation:
  Exclude:
    - 'app/workers/project_config/jks/generate_worker.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedMethods.
# AllowedMethods: instance_of?, kind_of?, is_a?, eql?, respond_to?, equal?, presence, present?
Lint/RedundantSafeNavigation:
  Exclude:
    - 'app/services/branch_bulk_import_service.rb'

# Offense count: 3
Lint/ShadowingOuterLocalVariable:
  Exclude:
    - 'app/controllers/api/v2/concerns/versioned_serializer.rb'
    - 'app/serializers/client/v2/organizations/promotions/show_serializer.rb'
    - 'config/initializers/devise.rb'

# Offense count: 2
# Configuration parameters: AllowComments, AllowNil.
Lint/SuppressedException:
  Exclude:
    - 'app/models/web_application/certificatable.rb'
    - 'lib/admitad/authenticator.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
Lint/ToJSON:
  Exclude:
    - 'lib/slack/message_template/reply.rb'
    - 'lib/slack/message_template/simple.rb'

# Offense count: 1
# Configuration parameters: Methods.
Lint/UnexpectedBlockArity:
  Exclude:
    - 'spec/services/mk_solution/user_import_service_spec.rb'

# Offense count: 12
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, IgnoreEmptyBlocks, AllowUnusedKeywordArguments.
Lint/UnusedBlockArgument:
  Exclude:
    - 'app/workers/authorized_user_import_file/process_file_worker.rb'
    - 'config/initializers/devise.rb'
    - 'spec/factories/giftcard_factory.rb'
    - 'spec/models/authorized_user_import_file_spec.rb'
    - 'spec/models/promotion/integration/import_shoppe_spec.rb'
    - 'spec/services/ixc/users_import_service_spec.rb'
    - 'spec/workers/authorized_user_import_file/export_errors_worker_spec.rb'
    - 'spec/workers/authorized_user_import_file/process_file_worker_spec.rb'

# Offense count: 6
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, AllowUnusedKeywordArguments, IgnoreEmptyMethods, IgnoreNotImplementedMethods, NotImplementedExceptions.
# NotImplementedExceptions: NotImplementedError
Lint/UnusedMethodArgument:
  Exclude:
    - 'app/services/promotion/update_service.rb'
    - 'app/services/vouchers_make_available_service.rb'
    - 'app/uploaders/avatar_uploader.rb'
    - 'app/uploaders/organization/banner_image_v1_uploader.rb'
    - 'app/uploaders/organization/logo_image_uploader.rb'

# Offense count: 353
# Configuration parameters: AllowedMethods, AllowedPatterns, CountRepeatedAttributes.
Metrics/AbcSize:
  Max: 86

# Offense count: 42
# Configuration parameters: CountComments, CountAsOne, AllowedMethods, AllowedPatterns, inherit_mode.
# AllowedMethods: refine
Metrics/BlockLength:
  Max: 76
  Exclude:
    - 'app/models/data_generator/business/coupon.rb'

# Offense count: 58
# Configuration parameters: CountComments, CountAsOne.
Metrics/ClassLength:
  Max: 413

# Offense count: 1
# Configuration parameters: LengthThreshold.
Metrics/CollectionLiteralLength:
  Exclude:
    - 'lib/custom_auth/crea_sp/bypass.rb'

# Offense count: 66
# Configuration parameters: AllowedMethods, AllowedPatterns.
Metrics/CyclomaticComplexity:
  Max: 19

# Offense count: 518
# Configuration parameters: CountComments, CountAsOne, AllowedMethods, AllowedPatterns.
Metrics/MethodLength:
  Max: 81
  Exclude:
    - 'app/models/data_generator/business/coupon.rb'

# Offense count: 2
# Configuration parameters: CountComments, CountAsOne.
Metrics/ModuleLength:
  Max: 676

# Offense count: 20
# Configuration parameters: CountKeywordArgs, MaxOptionalParameters.
Metrics/ParameterLists:
  Max: 12
  Exclude:
    - 'app/queries/promotions_query.rb'

# Offense count: 44
# Configuration parameters: AllowedMethods, AllowedPatterns.
Metrics/PerceivedComplexity:
  Max: 19

# Offense count: 2
Naming/AccessorMethodName:
  Exclude:
    - 'lib/firebase/messenger/authenticator.rb'
    - 'lib/hubsoft/api.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, BlockForwardingName.
# SupportedStyles: anonymous, explicit
Naming/BlockForwarding:
  Exclude:
    - 'app/mailers/application_mailer.rb'

# Offense count: 23
# Configuration parameters: AllowedNames.
# AllowedNames: module_parent
Naming/ClassAndModuleCamelCase:
  Enabled: false

# Offense count: 10
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyleForLeadingUnderscores.
# SupportedStylesForLeadingUnderscores: disallowed, required, optional
Naming/MemoizedInstanceVariableName:
  Exclude:
    - 'app/models/user/registration_strategy/default.rb'
    - 'app/models/user/registration_strategy/open_signup.rb'
    - 'app/serializers/api/v1/coupon/coupon_code_serializer.rb'
    - 'lib/awin/transactions.rb'
    - 'lib/custom_auth/oabmg/auth.rb'
    - 'lib/ileva/users.rb'
    - 'lib/ixc/contract_users.rb'
    - 'lib/ixc/product_contracts.rb'
    - 'lib/ixc/users.rb'
    - 'lib/rbxsoft/user_sign_in.rb'

# Offense count: 9
# Configuration parameters: NamePrefix, ForbiddenPrefixes, AllowedMethods, MethodDefinitionMacros.
# NamePrefix: is_, has_, have_
# ForbiddenPrefixes: is_, has_, have_
# AllowedMethods: is_a?
# MethodDefinitionMacros: define_method, define_singleton_method
Naming/PredicateName:
  Exclude:
    - 'app/models/user/device_manager.rb'
    - 'app/serializers/api/v2/v3_4_999/cashbacks/balance_serializer.rb'
    - 'app/validations/cashback/record/validate.rb'
    - 'lib/admitad/promotions.rb'
    - 'lib/admitad/transactions.rb'
    - 'lib/ileva/users.rb'
    - 'lib/ixc/contract_users.rb'
    - 'lib/ixc/product_contracts.rb'
    - 'lib/ixc/users.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: PreferredName.
Naming/RescuedExceptionsVariableName:
  Exclude:
    - 'app/models/promotion/integration/import/create_coupon.rb'

# Offense count: 484
# Configuration parameters: EnforcedStyle, CheckMethodNames, CheckSymbols, AllowedIdentifiers, AllowedPatterns.
# SupportedStyles: snake_case, normalcase, non_integer
# AllowedIdentifiers: capture3, iso8601, rfc1123_date, rfc822, rfc2822, rfc3339, x86_64
Naming/VariableNumber:
  Enabled: false

# Offense count: 1
# Configuration parameters: MinSize.
Performance/CollectionLiteralInLoop:
  Exclude:
    - 'app/controllers/api/v1/sessions_controller.rb'

# Offense count: 1
Performance/MethodObjectAsBlock:
  Exclude:
    - 'lib/mk_solution/client.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Performance/StringInclude:
  Exclude:
    - 'spec/requests/api/v2/custom_messages_request_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect.
RSpec/BeEmpty:
  Exclude:
    - 'spec/requests/api/v1/organizations/nearest_request_spec.rb'

# Offense count: 409
# This cop supports unsafe autocorrection (--autocorrect-all).
RSpec/BeEq:
  Enabled: false

# Offense count: 141
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: NegatedMatcher.
RSpec/ChangeByZero:
  Enabled: false

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: be_a, be_kind_of
RSpec/ClassCheck:
  Exclude:
    - 'spec/lib/ixc/api_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
RSpec/ContextMethod:
  Exclude:
    - 'spec/models/payout_spec.rb'

# Offense count: 424
# Configuration parameters: Prefixes, AllowedPatterns.
# Prefixes: when, with, without
RSpec/ContextWording:
  Enabled: false

# Offense count: 253
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: SkipBlocks, EnforcedStyle, OnlyStaticConstants.
# SupportedStyles: described_class, explicit
RSpec/DescribedClass:
  Enabled: false

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AutoCorrect.
RSpec/EmptyExampleGroup:
  Exclude:
    - 'spec/services/hinova/users_import_service_spec.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowConsecutiveOneLiners.
RSpec/EmptyLineAfterExample:
  Exclude:
    - 'spec/models/cupon_spec.rb'
    - 'spec/requests/client/v2/base_request_spec.rb'

# Offense count: 59
# This cop supports safe autocorrection (--autocorrect).
RSpec/EmptyLineAfterExampleGroup:
  Enabled: false

# Offense count: 78
# This cop supports safe autocorrection (--autocorrect).
RSpec/EmptyLineAfterFinalLet:
  Enabled: false

# Offense count: 25
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowConsecutiveOneLiners.
RSpec/EmptyLineAfterHook:
  Enabled: false

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
RSpec/EmptyLineAfterSubject:
  Exclude:
    - 'spec/lib/firebase/messenger/api_spec.rb'
    - 'spec/lib/firebase/messenger/client_spec.rb'
    - 'spec/services/authorized_user/csv_builder_service_spec.rb'
    - 'spec/services/firebase/push_notification_service_spec.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
RSpec/Eq:
  Exclude:
    - 'spec/lib/hinova/users_spec.rb'
    - 'spec/lib/ixc/contract_users_spec.rb'
    - 'spec/lib/ixc/product_contracts_spec.rb'
    - 'spec/lib/ixc/users_spec.rb'

# Offense count: 2074
# Configuration parameters: CountAsOne.
RSpec/ExampleLength:
  Max: 108

# Offense count: 3
# Configuration parameters: EnforcedStyle.
# SupportedStyles: always_allow, single_line_only, disallow
RSpec/ExampleWithoutDescription:
  Exclude:
    - 'spec/models/prize_draw_spec.rb'
    - 'spec/requests/client/v2/prize_draws/winner_request_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: CustomTransform, IgnoredWords, DisallowedExamples.
# DisallowedExamples: works
RSpec/ExampleWording:
  Exclude:
    - 'spec/models/order/import/csv/processor_spec.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
RSpec/ExcessiveDocstringSpacing:
  Exclude:
    - 'spec/models/organization_spec.rb'
    - 'spec/models/project_config_spec.rb'

# Offense count: 18
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: method_call, block
RSpec/ExpectChange:
  Exclude:
    - 'spec/models/order/trackable_spec.rb'
    - 'spec/services/admitad/transactions_process_service_spec.rb'
    - 'spec/services/awin/transactions_process_service_spec.rb'
    - 'spec/services/user_activate_service_spec.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: implicit, each, example
RSpec/HookArgument:
  Exclude:
    - 'spec/spec_helper.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect.
RSpec/HooksBeforeExamples:
  Exclude:
    - 'spec/requests/shopkeeper/v2/users_request_spec.rb'

# Offense count: 5
RSpec/IdenticalEqualityAssertion:
  Exclude:
    - 'spec/models/user/sign_in_strategy/default_spec.rb'
    - 'spec/models/user/sign_in_strategy/default_with_external_validation_spec.rb'
    - 'spec/models/voucher/make_available_spec.rb'
    - 'spec/services/order_destroy_service_spec.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: single_line_only, single_statement_only, disallow, require_implicit
RSpec/ImplicitSubject:
  Exclude:
    - 'spec/policies/admin/v1/purchase_import/branch_policy_spec.rb'
    - 'spec/policies/admin/v1/purchase_import/organization_policy_spec.rb'

# Offense count: 19
# Configuration parameters: Max, AllowedIdentifiers, AllowedPatterns.
RSpec/IndexedLet:
  Exclude:
    - 'spec/lib/ixc/contract_users_spec.rb'
    - 'spec/lib/ixc/product_contracts_spec.rb'
    - 'spec/lib/ixc/users_spec.rb'
    - 'spec/models/authorized_user_group_spec.rb'
    - 'spec/models/business_spec.rb'
    - 'spec/models/wallet_entry_spec.rb'
    - 'spec/requests/client/v2/project_configs/bulk_request_spec.rb'
    - 'spec/requests/shopkeeper/v2/organizations/organization_integration_partners_request_spec.rb'

# Offense count: 116
# Configuration parameters: AssignmentOnly.
RSpec/InstanceVariable:
  Exclude:
    - 'spec/models/promotion/integration/import_awin_spec.rb'
    - 'spec/models/promotion/integration/import_rakuten_spec.rb'
    - 'spec/requests/client/v2/authorized_user_groups_request_spec.rb'
    - 'spec/requests/shopkeeper/v2/base_spec.rb'
    - 'spec/requests/shopkeeper/v2/promotions/create_request_spec.rb'
    - 'spec/requests/shopkeeper/v2/promotions/promotions_request_spec.rb'
    - 'spec/services/user_sign_up_service_spec.rb'

# Offense count: 18
# This cop supports safe autocorrection (--autocorrect).
RSpec/LeadingSubject:
  Exclude:
    - 'spec/models/authorized_user_spec.rb'
    - 'spec/models/business_spec.rb'
    - 'spec/models/cupon_spec.rb'
    - 'spec/models/order/coupon_redeem_limit_per_period_validator_spec.rb'
    - 'spec/models/organization_spec.rb'
    - 'spec/models/promotion_spec.rb'
    - 'spec/models/user_spec.rb'
    - 'spec/policies/admin/v1/business_policy_spec.rb'
    - 'spec/policies/admin/v1/purchase_import/branch_policy_spec.rb'
    - 'spec/policies/admin/v1/purchase_import/import_file_policy_spec.rb'
    - 'spec/policies/admin/v1/purchase_import/organization_policy_spec.rb'
    - 'spec/validations/cashback/record/validate_spec.rb'

# Offense count: 9
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect.
RSpec/LetBeforeExamples:
  Exclude:
    - 'spec/requests/admin/v1/finances/payout_batches/index_request_spec.rb'
    - 'spec/requests/admin/v1/finances/payouts/failed_request_spec.rb'
    - 'spec/requests/admin/v1/finances/payouts/requested_request_spec.rb'
    - 'spec/requests/admin/v1/finances/payouts/transferred_request_spec.rb'
    - 'spec/requests/admin/v1/navigations/banners/index_request_spec.rb'
    - 'spec/requests/api/v2/banners/index_request_spec.rb'
    - 'spec/requests/shopkeeper/v2/users_request_spec.rb'

# Offense count: 1083
RSpec/LetSetup:
  Enabled: false

# Offense count: 288
# This cop supports safe autocorrection (--autocorrect).
RSpec/MatchArray:
  Enabled: false

# Offense count: 6
RSpec/MessageChain:
  Exclude:
    - 'spec/lib/webhook/handler_spec.rb'
    - 'spec/lib/webhook/woocomerce/event_handler_spec.rb'
    - 'spec/services/hinova/users_import_service_spec.rb'
    - 'spec/services/ixc/users_import_service_spec.rb'

# Offense count: 80
# Configuration parameters: EnforcedStyle.
# SupportedStyles: have_received, receive
RSpec/MessageSpies:
  Enabled: false

# Offense count: 2441
RSpec/MultipleExpectations:
  Max: 27

# Offense count: 2517
# Configuration parameters: AllowSubject.
RSpec/MultipleMemoizedHelpers:
  Max: 46
  Exclude:
    - 'spec/requests/api/v1/organizations/coupons_request_spec.rb'

# Offense count: 15
# Configuration parameters: EnforcedStyle, IgnoreSharedExamples.
# SupportedStyles: always, named_only
RSpec/NamedSubject:
  Exclude:
    - 'spec/lib/firebase/messenger/api_spec.rb'
    - 'spec/lib/firebase/messenger/client_spec.rb'
    - 'spec/models/authorized_user_export_file_spec.rb'
    - 'spec/policies/admin/v1/purchase_import/branch_policy_spec.rb'
    - 'spec/policies/admin/v1/purchase_import/import_file_policy_spec.rb'
    - 'spec/policies/admin/v1/purchase_import/organization_policy_spec.rb'
    - 'spec/services/authorized_user/csv_builder_service_spec.rb'

# Offense count: 1057
# Configuration parameters: AllowedGroups.
RSpec/NestedGroups:
  Max: 8

# Offense count: 10
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Strict, EnforcedStyle, AllowedExplicitMatchers.
# SupportedStyles: inflected, explicit
RSpec/PredicateMatcher:
  Exclude:
    - 'spec/file_validator/apple_key_file_spec.rb'
    - 'spec/file_validator/firebase_apple_file_spec.rb'
    - 'spec/file_validator/firebase_google_file_spec.rb'
    - 'spec/file_validator/google_key_file_spec.rb'
    - 'spec/models/project_config_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
RSpec/ReceiveCounts:
  Exclude:
    - 'spec/lib/awin/promotions_spec.rb'

# Offense count: 8
# This cop supports unsafe autocorrection (--autocorrect-all).
RSpec/ReceiveMessages:
  Exclude:
    - 'spec/models/integration_partner_spec.rb'
    - 'spec/requests/api/v1/project_configs_requests_spec.rb'
    - 'spec/requests/api/v2/project_configs_requests_spec.rb'
    - 'spec/tasks/maintenance/projects/create_mailer_configs_task_spec.rb'

# Offense count: 29
RSpec/RepeatedDescription:
  Exclude:
    - 'spec/models/order/import/csv/row_spec.rb'
    - 'spec/requests/admin/v1/finances/payouts/failed_request_spec.rb'
    - 'spec/requests/admin/v1/finances/payouts/requested_request_spec.rb'
    - 'spec/requests/admin/v1/finances/payouts/transferred_request_spec.rb'
    - 'spec/requests/api/v1/cashback_records_request_spec.rb'
    - 'spec/requests/api/v1/organizations/coupons_request_spec.rb'
    - 'spec/requests/api/v2/banners/index_request_spec.rb'
    - 'spec/requests/api/v2/coupons_request_spec.rb'
    - 'spec/requests/client/v2/organizations/promotions/provider_type_request_spec.rb'
    - 'spec/requests/client/v2/organizations/promotions/vouchers/index_request_spec.rb'
    - 'spec/requests/client/v2/organizations/promotions/work_schedule_request_spec.rb'
    - 'spec/requests/shopkeeper/v2/branches_request_spec.rb'
    - 'spec/requests/shopkeeper/v2/promotions/create_request_spec.rb'

# Offense count: 10
RSpec/RepeatedExample:
  Exclude:
    - 'spec/lib/custom_auth/crea_rs/auth_spec.rb'
    - 'spec/lib/custom_auth/oab_ba/auth_spec.rb'
    - 'spec/lib/custom_auth/oab_ro/auth_spec.rb'
    - 'spec/models/promotion/integration/import_admitad_spec.rb'
    - 'spec/models/promotion/integration/import_awin_spec.rb'

# Offense count: 8
RSpec/RepeatedExampleGroupBody:
  Exclude:
    - 'spec/models/wallet/payment_spec.rb'
    - 'spec/requests/api/v2/base_request_spec.rb'
    - 'spec/requests/api/v2/navigations/menus/index_request_spec.rb'
    - 'spec/requests/client/v2/wallets/payments_request_spec.rb'

# Offense count: 4
RSpec/RepeatedExampleGroupDescription:
  Exclude:
    - 'spec/lib/custom_auth/custom_no_signup_integration_auth_spec.rb'
    - 'spec/requests/api/v1/organizations/coupons_request_spec.rb'

# Offense count: 19
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect.
RSpec/ScatteredLet:
  Exclude:
    - 'spec/lib/hinova/users_spec.rb'
    - 'spec/lib/ixc/users_spec.rb'
    - 'spec/models/payout/export_spec.rb'
    - 'spec/policies/admin/v1/purchase_import/import_file_policy_spec.rb'
    - 'spec/requests/admin/v1/finances/payout_batches/index_request_spec.rb'
    - 'spec/requests/admin/v1/finances/payouts/failed_request_spec.rb'
    - 'spec/requests/admin/v1/finances/payouts/requested_request_spec.rb'
    - 'spec/requests/admin/v1/finances/payouts/transferred_request_spec.rb'
    - 'spec/requests/admin/v1/navigations/banners/index_request_spec.rb'
    - 'spec/requests/shopkeeper/v2/users_request_spec.rb'
    - 'spec/services/project_config/verify_external_urls_service_spec.rb'
    - 'spec/services/project_config/verify_google_key_files_service_spec.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect.
RSpec/ScatteredSetup:
  Exclude:
    - 'spec/requests/shopkeeper/v2/promotions/create_request_spec.rb'
    - 'spec/services/qrcode_coupon_make_available_service_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
RSpec/SingleArgumentMessageChain:
  Exclude:
    - 'spec/lib/webhook/handler_spec.rb'

# Offense count: 390
# Configuration parameters: Include, CustomTransform, IgnoreMethods, IgnoreMetadata.
# Include: **/*_spec.rb
RSpec/SpecFilePathFormat:
  Enabled: false

# Offense count: 3
# Configuration parameters: Include.
# Include: **/*_spec*rb*, **/spec/**/*
RSpec/SpecFilePathSuffix:
  Exclude:
    - 'spec/lib/click_ads/deep_link.rb'
    - 'spec/models/organization_integration_partner.rb'
    - 'spec/requests/api/v1/cashback_records/histories_request_spect.rb'

# Offense count: 3
RSpec/StubbedMock:
  Exclude:
    - 'spec/lib/webhook/handler_spec.rb'
    - 'spec/lib/webhook/woocomerce/event_handler_spec.rb'

# Offense count: 1
RSpec/SubjectDeclaration:
  Exclude:
    - 'spec/lib/hubsoft/contract_spec.rb'

# Offense count: 192
# Configuration parameters: IgnoreNameless, IgnoreSymbolicNames.
RSpec/VerifiedDoubles:
  Enabled: false

# Offense count: 1
RSpec/VoidExpect:
  Exclude:
    - 'spec/lib/webhook/actions/create_user_action_spec.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
RSpecRails/AvoidSetupHook:
  Exclude:
    - 'spec/tasks/maintenance/metrics/generate_daily_from_task_spec.rb'
    - 'spec/tasks/maintenance/metrics/generate_monthly_from_task_spec.rb'
    - 'spec/tasks/maintenance/metrics/generate_weekly_from_task_spec.rb'

# Offense count: 421
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Inferences.
RSpecRails/InferredSpecType:
  Enabled: false

# Offense count: 109
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AutoCorrect, EnforcedStyle.
# SupportedStyles: not_to, be_invalid
RSpecRails/NegationBeValid:
  Enabled: false

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/ApplicationController:
  Exclude:
    - 'app/controllers/maintenance_tasks_controller.rb'

# Offense count: 70
# Configuration parameters: Database, Include.
# SupportedDatabases: mysql, postgresql
# Include: db/**/*.rb
Rails/BulkChangeTable:
  Enabled: false

# Offense count: 5
# Configuration parameters: Include.
# Include: db/**/*.rb
Rails/CreateTableWithTimestamps:
  Exclude:
    - 'db/migrate/20180320121752_add_devise_to_shopkeepers.rb'
    - 'db/migrate/20200213210813_create_vidalink_table.rb'
    - 'db/migrate/20200331203531_create_multimed_table.rb'
    - 'db/migrate/20220207193746_create_custom_field_values.rb'
    - 'db/migrate/20220909132702_create_import_files.rb'

# Offense count: 15
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforceForPrefixed.
Rails/Delegate:
  Exclude:
    - 'app/models/business/project.rb'
    - 'app/serializers/api/integration/branch_serializer.rb'
    - 'app/serializers/api/v2/organizations/branch_serializer.rb'
    - 'app/serializers/api/v2/redeem_serializer.rb'
    - 'app/serializers/api/v2/v3_2_1/redeem_serializer.rb'
    - 'app/serializers/integration/v1/lead_config/show_serializer.rb'
    - 'app/serializers/shopkeeper/v2/coupon_serializer.rb'
    - 'app/serializers/shopkeeper/v2/organizations/show_serializer.rb'
    - 'app/services/custom_auth_service.rb'
    - 'lib/firebase/messenger/client.rb'

# Offense count: 5
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Whitelist, AllowedMethods, AllowedReceivers.
# Whitelist: find_by_sql, find_by_token_for
# AllowedMethods: find_by_sql, find_by_token_for
# AllowedReceivers: Gem::Specification, page
Rails/DynamicFindBy:
  Exclude:
    - 'app/serializers/api/v1/order_serializer.rb'
    - 'app/workers/user/smart_token_expire_worker.rb'
    - 'lib/devise/strategies/smart_link_authenticatable.rb'
    - 'spec/models/ileva_client_spec.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedMethods, AllowedPatterns.
# AllowedMethods: order, limit, select, lock
Rails/FindEach:
  Exclude:
    - 'app/models/data_generator/business/organization.rb'

# Offense count: 86
# Configuration parameters: Include.
# Include: app/models/**/*.rb
Rails/HasManyOrHasOneDependent:
  Enabled: false

# Offense count: 37
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: numeric, symbolic
Rails/HttpStatus:
  Exclude:
    - 'app/controllers/api/v1/base_controller.rb'
    - 'app/controllers/api/v1/cashback_records/histories_controller.rb'
    - 'app/controllers/api/v1/cashback_transfer_requests_controller.rb'
    - 'app/controllers/api/v1/organizations/orders_controller.rb'
    - 'app/controllers/api/v1/sessions_controller.rb'
    - 'app/controllers/api/v1/users/token_controller.rb'
    - 'app/controllers/api/v1/users_controller.rb'
    - 'app/controllers/api/v2/concerns/business_authentication.rb'
    - 'app/controllers/api/v2/users/addresses_controller.rb'
    - 'app/controllers/api/v2/users/registrations_controller.rb'
    - 'app/controllers/client/v2/payouts/export_controller.rb'
    - 'app/controllers/client/v2/users/smart_link_controller.rb'
    - 'app/controllers/health_checks_controller.rb'
    - 'app/controllers/webhook_handler_controller.rb'
    - 'spec/requests/rack_attack_spec.rb'

# Offense count: 5
# Configuration parameters: Include.
# Include: app/controllers/**/*.rb, app/mailers/**/*.rb
Rails/LexicallyScopedActionFilter:
  Exclude:
    - 'app/controllers/api/v2/users/passwords_controller.rb'
    - 'app/controllers/api/v2/users/registrations_controller.rb'

# Offense count: 4
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/NegateInclude:
  Exclude:
    - 'app/controllers/client/v2/client_employees_controller.rb'
    - 'app/models/organization.rb'

# Offense count: 13
# Configuration parameters: Database, Include.
# SupportedDatabases: mysql
# Include: db/**/*.rb
Rails/NotNullColumn:
  Exclude:
    - 'db/migrate/20180320121752_add_devise_to_shopkeepers.rb'
    - 'db/migrate/20180424195027_create_branch_categories.rb'
    - 'db/migrate/20180425190431_create_cities.rb'
    - 'db/migrate/20180426172126_add_city_to_user.rb'
    - 'db/migrate/20180515173135_add_geolocation_to_branches.rb'
    - 'db/migrate/20181022123144_add_code_to_subscription.rb'
    - 'db/migrate/20190830012158_add_business_custom_flag.rb'
    - 'db/migrate/20200730004648_add_transaction_at_to_wallet_transactions.rb'
    - 'db/migrate/20230803153246_add_allowed_contracts_status_to_ixc_token.rb'
    - 'db/migrate/20231101172730_add_kind_to_import_files.rb'
    - 'db/migrate/20240311224455_add_kind_to_wallet_entries.rb'
    - 'db/migrate/20240604230307_add_referrer_id_to_lead.rb'

# Offense count: 16
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/Pluck:
  Exclude:
    - 'app/models/user/device_manager.rb'
    - 'spec/lib/shopee/transactions_spec.rb'
    - 'spec/requests/api/v2/custom_messages_request_spec.rb'
    - 'spec/requests/api/v2/prize_draws/oldests_request_spec.rb'
    - 'spec/requests/api/v2/prize_draws/recents_request_spec.rb'
    - 'spec/requests/api/v2/prize_draws/wons_request_spec.rb'
    - 'spec/requests/client/v2/custom_texts_types_resquest_spec.rb'
    - 'spec/requests/client/v2/prize_draws/index_request_spec.rb'

# Offense count: 45
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: conservative, aggressive
Rails/PluckInWhere:
  Enabled: false

# Offense count: 24
# This cop supports safe autocorrection (--autocorrect).
Rails/PluralizationGrammar:
  Exclude:
    - 'app/tasks/maintenance/metrics/generate_daily_from_task.rb'
    - 'spec/factories/cupon_factory.rb'
    - 'spec/factories/prize_draws_factory.rb'
    - 'spec/factories/promotion_factory.rb'
    - 'spec/models/prize_draw_spec.rb'
    - 'spec/models/promotion/integration/import_shoppe_spec.rb'
    - 'spec/models/redeem_rule/kind_spec.rb'
    - 'spec/requests/shopkeeper/v2/redeem/import_request_spec.rb'

# Offense count: 6
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: NotNilAndNotEmpty, NotBlank, UnlessBlank.
Rails/Present:
  Exclude:
    - 'app/models/client_employee.rb'
    - 'app/models/prize_draw.rb'
    - 'app/models/shopkeeper.rb'
    - 'app/models/user/registration_strategy/default.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedReceivers.
# AllowedReceivers: ActionMailer::Preview, ActiveSupport::TimeZone
Rails/RedundantActiveRecordAllMethod:
  Exclude:
    - 'app/tasks/maintenance/user_cashback_and_giftcard_task.rb'

# Offense count: 442
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Include.
# Include: spec/controllers/**/*.rb, spec/requests/**/*.rb, test/controllers/**/*.rb, test/integration/**/*.rb
Rails/ResponseParsedBody:
  Enabled: false

# Offense count: 113
# Configuration parameters: Include.
# Include: db/**/*.rb
Rails/ReversibleMigration:
  Enabled: false

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/SelectMap:
  Exclude:
    - 'app/queries/promotions_query.rb'

# Offense count: 282
# Configuration parameters: ForbiddenMethods, AllowedMethods.
# ForbiddenMethods: decrement!, decrement_counter, increment!, increment_counter, insert, insert!, insert_all, insert_all!, toggle!, touch, touch_all, update_all, update_attribute, update_column, update_columns, update_counters, upsert, upsert_all
Rails/SkipsModelValidations:
  Enabled: false

# Offense count: 63
# Configuration parameters: Include.
# Include: db/**/*.rb
Rails/ThreeStateBooleanColumn:
  Enabled: false

# Offense count: 10
# Configuration parameters: Include.
# Include: app/models/**/*.rb
Rails/UniqueValidationWithoutIndex:
  Exclude:
    - 'app/models/allowlisted_jwt.rb'
    - 'app/models/hinova_token.rb'
    - 'app/models/ileva_client.rb'
    - 'app/models/integration_token.rb'
    - 'app/models/ixc_token.rb'
    - 'app/models/mk_solution_token.rb'
    - 'app/models/organization_integration_partner.rb'
    - 'app/models/user.rb'
    - 'app/models/voalle_token.rb'
    - 'app/models/voucher.rb'

# Offense count: 2
# Configuration parameters: Severity, Environments.
# Environments: development, test, production
Rails/UnknownEnv:
  Exclude:
    - 'config/initializers/carrierwave.rb'
    - 'spec/spec_helper.rb'

# Offense count: 32
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: exists, where
Rails/WhereExists:
  Enabled: false

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/ArrayIntersect:
  Exclude:
    - 'app/models/shopkeeper.rb'

# Offense count: 137
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, ProceduralMethods, FunctionalMethods, AllowedMethods, AllowedPatterns, AllowBracesOnProceduralOneLiners, BracesRequiredMethods.
# SupportedStyles: line_count_based, semantic, braces_for_chaining, always_braces
# ProceduralMethods: benchmark, bm, bmbm, create, each_with_object, measure, new, realtime, tap, with_object
# FunctionalMethods: let, let!, subject, watch
# AllowedMethods: lambda, proc, it
Style/BlockDelimiters:
  Enabled: false

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: MinBranchesCount.
Style/CaseLikeIf:
  Exclude:
    - 'app/serializers/client/v2/metrics/order_serializer.rb'
    - 'app/serializers/client/v2/metrics/users/registration_serializer.rb'
    - 'lib/auth_integration/generic_api.rb'

# Offense count: 257
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: nested, compact
Style/ClassAndModuleChildren:
  Enabled: false

# Offense count: 6
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/CombinableLoops:
  Exclude:
    - 'app/tasks/maintenance/metrics/generate_daily_from_task.rb'
    - 'app/tasks/maintenance/metrics/generate_monthly_from_task.rb'
    - 'app/tasks/maintenance/metrics/generate_weekly_from_task.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: Keywords, RequireColon.
# Keywords: TODO, FIXME, OPTIMIZE, HACK, REVIEW, NOTE
Style/CommentAnnotation:
  Exclude:
    - 'lib/webhook/woocomerce/event_handler.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/CommentedKeyword:
  Exclude:
    - 'config/routes/api_v1.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/ExpandPathArguments:
  Exclude:
    - 'config/puma.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowedVars.
Style/FetchEnvVar:
  Exclude:
    - 'config/initializers/carrierwave.rb'

# Offense count: 8
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: format, sprintf, percent
Style/FormatString:
  Exclude:
    - 'app/serializers/api/v1/coupon/coupon_code_serializer.rb'
    - 'app/serializers/api/v1/coupon/default_serializer.rb'
    - 'app/serializers/api/v1/coupon/link_serializer.rb'
    - 'app/serializers/api/v1/coupon/qrcode_serializer.rb'
    - 'app/serializers/api/v1/coupon_serializer.rb'
    - 'app/serializers/api/v1/organization/giftcard_index_serializer.rb'
    - 'app/serializers/api/v1/organization/index_serializer.rb'
    - 'app/serializers/api/v1/organization_show_serializer.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: MaxUnannotatedPlaceholdersAllowed, AllowedMethods, AllowedPatterns.
# SupportedStyles: annotated, template, unannotated
# AllowedMethods: redirect
Style/FormatStringToken:
  EnforcedStyle: template

# Offense count: 1616
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: always, always_true, never
Style/FrozenStringLiteralComment:
  Enabled: false

# Offense count: 102
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: MinBodyLength, AllowConsecutiveConditionals.
Style/GuardClause:
  Enabled: false

# Offense count: 151
# This cop supports safe autocorrection (--autocorrect).
Style/IfUnlessModifier:
  Enabled: false

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: line_count_dependent, lambda, literal
Style/Lambda:
  Exclude:
    - 'app/mailers/dynamic_devise_mailer.rb'
    - 'spec/lib/shopee/promotions/category_promotions_spec.rb'

# Offense count: 9
Style/MultilineBlockChain:
  Exclude:
    - 'app/controllers/api/v2/custom_messages_controller.rb'
    - 'app/controllers/client/v2/custom_texts_controller.rb'
    - 'app/controllers/client/v2/promotions_controller.rb'
    - 'app/models/business/cache.rb'
    - 'app/models/metrics/category_order_count.rb'
    - 'app/models/metrics/organization_order_count.rb'
    - 'lib/rakuten/authenticator.rb'
    - 'lib/rakuten/transactions.rb'

# Offense count: 46
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: literals, strict
Style/MutableConstant:
  Enabled: false

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, MinBodyLength.
# SupportedStyles: skip_modifier_ifs, always
Style/Next:
  Exclude:
    - 'lib/ixc/contract_users.rb'
    - 'lib/ixc/users.rb'

# Offense count: 5
# Configuration parameters: EnforcedStyle.
# SupportedStyles: allow_single_line, disallow
Style/NumberedParameters:
  Exclude:
    - 'app/controllers/client/v2/custom_text_types_controller.rb'
    - 'app/controllers/client/v2/custom_texts_controller.rb'
    - 'app/controllers/client/v2/users/sync_controller.rb'
    - 'lib/hubsoft/contract.rb'
    - 'lib/rbxsoft/client.rb'

# Offense count: 2
Style/NumberedParametersLimit:
  Max: 2

# Offense count: 223
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: Strict, AllowedNumbers, AllowedPatterns.
Style/NumericLiterals:
  MinDigits: 10

# Offense count: 8
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle, AllowedMethods, AllowedPatterns.
# SupportedStyles: predicate, comparison
Style/NumericPredicate:
  Exclude:
    - 'app/serializers/api/v1/coupon/coupon_code_serializer.rb'
    - 'app/serializers/api/v1/coupon/default_serializer.rb'
    - 'app/serializers/api/v1/coupon/link_serializer.rb'
    - 'app/serializers/api/v1/coupon/qrcode_serializer.rb'
    - 'app/serializers/api/v1/promotions/index_serializer.rb'
    - 'app/serializers/client/v2/promotions/index_serializer.rb'
    - 'lib/custom_auth/oabmg/auth.rb'

# Offense count: 6
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Methods.
Style/RedundantArgument:
  Exclude:
    - 'lib/rakuten/promotions.rb'
    - 'spec/services/cashback_record/history/notification_service_spec.rb'
    - 'spec/services/payout/history/notification_service_spec.rb'

# Offense count: 35
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantConstantBase:
  Exclude:
    - 'config/environments/dev_hmg.rb'
    - 'config/environments/homologation.rb'
    - 'config/environments/production.rb'
    - 'config/environments/staging.rb'
    - 'config/environments/testing.rb'
    - 'spec/lib/custom_auth/auth_v2_spec.rb'
    - 'spec/lib/custom_auth/hubsoft_auth_spec.rb'
    - 'spec/lib/custom_auth/voalle_auth_spec.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: SafeForConstants.
Style/RedundantFetchBlock:
  Exclude:
    - 'config/environments/test.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/RedundantFilterChain:
  Exclude:
    - 'app/models/user/device_manager.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/RedundantSelfAssignment:
  Exclude:
    - 'app/workers/authorized_user_import_file/process_line_worker.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, AllowInnerSlashes.
# SupportedStyles: slashes, percent_r, mixed
Style/RegexpLiteral:
  Exclude:
    - 'lib/core_ext/string/remove_protocol.rb'
    - 'lib/rakuten/promotions.rb'
    - 'spec/support/webmock.rb'

# Offense count: 8
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: implicit, explicit
Style/RescueStandardError:
  Exclude:
    - 'app/services/vouchers_import_service.rb'
    - 'app/services/vouchers_make_available_service.rb'
    - 'app/workers/branch_bulk_import_worker.rb'
    - 'app/workers/branch_export_worker.rb'
    - 'app/workers/vouchers_import_worker.rb'
    - 'app/workers/vouchers_make_available_worker.rb'
    - 'config/puma.rb'
    - 'lib/custom_auth/rbxsoft_auth.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedMethods, AllowedPatterns.
Style/ReturnNilInPredicateMethodDefinition:
  Exclude:
    - 'lib/custom_auth/betalabs_auth.rb'

# Offense count: 10
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: only_raise, only_fail, semantic
Style/SignalException:
  Exclude:
    - 'db/migrate/20220120175451_drop_cupon_codes.rb'
    - 'db/migrate/20220221141044_drop_enterprises.rb'
    - 'db/migrate/20220317201114_drop_notifications.rb'
    - 'db/migrate/20220317201207_drop_searches.rb'
    - 'db/migrate/20220317201851_drop_tickets.rb'
    - 'db/migrate/20220826220515_drop_sessions.rb'
    - 'db/migrate/20220831185237_drop_admins.rb'
    - 'db/migrate/20221006150426_drop_events.rb'
    - 'db/migrate/20230217135007_drop_authorized_user_histories.rb'
    - 'db/migrate/20240229185639_drop_wallet_transaction.rb'

# Offense count: 32
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/SingleArgumentDig:
  Exclude:
    - 'lib/awin/promotions.rb'
    - 'lib/custom_auth/betalabs_auth.rb'
    - 'lib/devise/strategies/single_sign_on_authenticatable.rb'
    - 'lib/hinova/users.rb'
    - 'lib/shopee/api.rb'
    - 'spec/models/user/registration_strategy/default_spec.rb'
    - 'spec/models/user/registration_strategy/default_with_external_validation_spec.rb'
    - 'spec/requests/client/v2/business_request_spec.rb'
    - 'spec/requests/client/v2/hinova_tokens_spec.rb'
    - 'spec/requests/client/v2/ixc_contracts_spec.rb'
    - 'spec/requests/client/v2/mk_solution_plans_spec.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowModifier.
Style/SoleNestedConditional:
  Exclude:
    - 'lib/ixc/contract_users.rb'
    - 'lib/mk_solution/client.rb'

# Offense count: 9
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Mode.
Style/StringConcatenation:
  Exclude:
    - 'app/serializers/client/v2/users/user_smart_link_serializer.rb'
    - 'lib/admitad/authenticator.rb'
    - 'lib/shopee/api.rb'
    - 'lib/shopee/deep_link.rb'
    - 'lib/shopee/transactions.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, ConsistentQuotesInMultiline.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiterals:
  Exclude:
    - 'spec/rails_helper.rb'

# Offense count: 84
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiteralsInInterpolation:
  Enabled: false

# Offense count: 6
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/StructInheritance:
  Exclude:
    - 'lib/geocoding_api/geocoding/result.rb'
    - 'lib/hubsoft/user.rb'
    - 'lib/mk_solution/user.rb'
    - 'lib/voalle/contract.rb'
    - 'lib/voalle/customer.rb'
    - 'lib/voalle/token.rb'

# Offense count: 213
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, MinSize.
# SupportedStyles: percent, brackets
Style/SymbolArray:
  Enabled: false

# Offense count: 4
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowMethodsWithArguments, AllowedMethods, AllowedPatterns, AllowComments.
# AllowedMethods: define_method, mail, respond_to
Style/SymbolProc:
  Exclude:
    - 'app/services/business/update_service.rb'
    - 'app/workers/authorized_user/bulk_import_worker.rb'
    - 'db/migrate/20180713163643_create_signatures.rb'
    - 'db/migrate/20240227184132_create_voucher_buckets.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyleForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, no_comma
Style/TrailingCommaInArguments:
  Exclude:
    - 'db/migrate/20231109162145_add_lock_version_to_maintenance_tasks_runs.maintenance_tasks.rb'
    - 'db/migrate/20231109162147_add_index_on_task_name_and_status_to_runs.maintenance_tasks.rb'

# Offense count: 8
# This cop supports safe autocorrection (--autocorrect).
Style/WhileUntilModifier:
  Exclude:
    - 'lib/admitad/transactions.rb'
    - 'lib/devise/models/smart_link_authenticatable.rb'
    - 'lib/ileva/users.rb'
    - 'lib/ixc/contract_users.rb'
    - 'lib/ixc/product_contracts.rb'
    - 'lib/ixc/users.rb'

# Offense count: 196
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, MinSize, WordRegex.
# SupportedStyles: percent, brackets
Style/WordArray:
  Enabled: false

# Offense count: 1402
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, AllowedPatterns, SplitStrings.
# URISchemes: http, https
Layout/LineLength:
  Max: 7238
